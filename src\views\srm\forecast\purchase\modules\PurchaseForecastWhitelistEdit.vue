<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    
  </div>
</template> 

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'PurchaseForecastWhitelistEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            pageData: {
                form: {},
                // validateRules: {
                //     toElsAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXeyxOLVWW_bee7400e`, '供应商ELS账号不能为空')}]
                // },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基本信息'),
                        groupCode: 'baseForm',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: '单据编号',
                                    fieldLabelI18nKey: 'i18n_baseForm5ef8_infoNumber',
                                    fieldName: 'whitelistNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus `, '单据状态'),
                                    fieldLabelI18nKey: 'i18n_PurchaseMassProdHeadList_documentsStatus',
                                    fieldName: 'whitelistStatus',
                                    dictCode: 'whitelistStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    fieldName: 'factory',
                                    dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"',
                                    disabled: false,
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectFactoryTips`, '请选择工厂')
                                },
                                {
                                    groupCode: 'baseForm',
                                    fieldType: 'remoteSelect',
                                    fieldLabel: '供应商ELS账号',
                                    fieldLabelI18nKey: 'i18n_field_toElsAccount',
                                    fieldName: 'toElsAccount',
                                    dictCode: '',
                                    defaultValue: '',
                                    dataFormat: '',
                                    helpText: '',
                                    alertMsg: '',
                                    alertMsgI18nKey: '',
                                    required: '1',
                                    bindFunction: (Vue, data) => {
                                        this.$set(Vue.form, 'toElsAccount', data[0].toElsAccount)
                                        this.$set(Vue.form, 'supplierCode', data[0].supplierCode)
                                        this.$set(Vue.form, 'supplierName', data[0].supplierName)
                                        // Vue.form.supplierCode = data[0].supplierCode
                                        // Vue.form.supplierName = data[0].supplierName
                                    },
                                    extend: {
                                        modalColumns: [{
                                            field: 'toElsAccount',
                                            fieldLabelI18nKey: 'i18n_title_supplierAccount',
                                            title: '供应商ELS账号',
                                            with: 150
                                        },
                                        {
                                            field: 'supplierName',
                                            fieldLabelI18nKey: 'i18n__RdXRL_8e11f650',
                                            title: '供应商名称',
                                            with: 150
                                        },
                                        {
                                            field: 'supplierCode',
                                            fieldLabelI18nKey: 'i18n_field_RdXey_8e18ba5e',
                                            title: '供应商编码',
                                            with: 150
                                        }
                                       
                                        ],
                                        modalUrl: '/supplier/supplierMaster/list',
                                        modalParams: {frozenFunctionValue: '0'},
                                        afterClearCallBack: function (form, pageData, col){
                                            form.toElsAccount = ''
                                            form.supplierCode = ''
                                            form.supplierName = ''
                                        }

                                    },
                                    placeholder: ''
                                },
                                {
                                    groupCode: 'baseForm',
                                    fieldType: 'input',
                                    fieldLabel: '供应商ERP编码',
                                    fieldLabelI18nKey: 'i18n_field_supplierCode',
                                    fieldName: 'supplierCode',
                                    dictCode: '',
                                    defaultValue: '',
                                    dataFormat: '',
                                    helpText: '',
                                    alertMsg: '',
                                    alertMsgI18nKey: '',
                                    disabled: true,
                                    placeholder: ''
                                },
                                {
                                    groupCode: 'baseForm',
                                    fieldType: 'input',
                                    fieldLabel: '供应商名称',
                                    fieldLabelI18nKey: 'i18n_field_supplierName',
                                    fieldName: 'supplierName',
                                    dictCode: '',
                                    defaultValue: '',
                                    dataFormat: '',
                                    helpText: '',
                                    alertMsg: '',
                                    alertMsgI18nKey: '',
                                    disabled: true,
                                    placeholder: ''
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
                                    fieldName: 'materialGroup',
                                    dictCode: 'materialGroup',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注'),
                                    fieldLabelI18nKey: '',
                                    fieldName: 'remark'
                                }
                            ],
                            validateRules: {
                                factoryCode: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_factoryCodeCantEmpty`, '工厂不能为空')}],
                                toElsAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RdXeyxOLVW_e6a8e73`, '供应商ELS账号不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), authorityCode: 'forecastWhitelist#purchaseForecastWhitelist:add', type: 'primary', click: this.saveEvent, showCondition: this.showSaveBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), authorityCode: 'forecastWhitelist#purchaseForecastWhitelist:submit', type: 'primary', click: this.submitEvent, showCondition: this.showSubmitBtn },
                    // { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), type: 'primary', click: this.publishEvent, showCondition: this.showPublishCondition},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/forecast/purchaseForecastWhitelist/add',
                edit: '/forecast/purchaseForecastWhitelist/edit',
                detail: '/forecast/purchaseForecastWhitelist/queryById',
                public: '/forecast/purchaseForecastWhitelist/publish',
                submitAudit: '/forecast/purchaseForecastWhitelist/submit'
            }
        }
    },
    computed: {
        
    },
    methods: {
        showSaveBtn () {
            if (this.currentEditRow.id) {
                return false
            }
            return true
        },
        showSubmitBtn () {
            if (this.currentEditRow.id) {
                return true
            }
            return false
        },
        goBack () {
            this.$emit('hide')
        },
        saveEvent () {
            // this.$refs.queryForm.validate(valid => {
            //     if (valid) {
            //         let params = this.$refs.editPage.getPageData()
            //         postAction(this.url.edit, params).then(res => {
            //             const msgType = res.success ? 'success' : 'error'
            //             this.$message[msgType](res.message)
            //             if (res.success) {
            //                 this.goBack()
            //             }
            //         })
            //     } 
            // })
            // this.handValidate(this.url.edit, params)
            this.$refs.editPage.handleSend('add', () => {this.goBack()})
        },
        submitEvent () {
            // this.$refs.editPage.postData()
            let params = this.$refs.editPage.getPageData()
            postAction(this.url.submitAudit, params).then(res => {
                const msgType = res.success ? 'success' : 'error'
                this.$message[msgType](res.message)
                if (res.success) {
                    this.goBack()
                }
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        downloadEvent (row) {
            this.$refs.editPage.handleDownload(row)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        }
    }
}
</script>
