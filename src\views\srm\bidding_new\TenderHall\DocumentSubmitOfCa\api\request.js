import axios from 'axios'
import { notification } from 'ant-design-vue'

// 创建 axios 实例
const service = axios.create({
    baseURL: 'http://127.0.0.1:18455' // api base_url
    // timeout: 600000 // 请求超时时间
})

const err = (error) => {
    console.log(error)
    notification.error({
        message: '系统提示',
        description: '加密驱动没有响应，请检查是否启动加密工具',
        duration: 4
    })
    return Promise.reject(error)
}
const resErr = (code) => {
    if (code !== 0) {
        notification.error({ message: '系统提示', description: 'UK操作失败', duration: 4 })
    }
}
// request interceptor
service.interceptors.request.use(config => {
    return config
}, (error) => {
    return Promise.reject(error)
})

// response interceptor
service.interceptors.response.use((response) => {
    console.log(response)
    let data = response.data
    let code = data.code
    resErr(code)
    return data
}, err)

//post
export function postAction (url, parameter, config) {
    let params = {
        url: url,
        method: 'POST',
        data: parameter
    }
    if (config) {
        Object.assign(params, config)
    }
    return service(params)
}
