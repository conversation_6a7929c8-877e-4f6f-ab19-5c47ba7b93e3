<template>
  <a-modal
    centered
    v-drag
    :mask-closable="false"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_SelectData`, '选择数据')"
    :visible="visible"
    :confirmLoading="loading"
    :width="960">
    <a-radio-group
      button-style="solid"
      style="margin-bottom: 12px"
      v-model="inputType">
      <a-radio-button value="sysDictCode">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_systemDictionary`, '系统字典') }}
      </a-radio-button>
      <a-radio-button value="dictCode">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_companyDictionary`, '企业字典') }}
      </a-radio-button>
      <a-radio-button value="manual">
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_manualInput`, '手动输入') }}
      </a-radio-button>
    </a-radio-group>
    <div v-if="inputType === 'manual'">
      <a-input
        v-model="dataSource"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterDataSourceAddressDictionaryCode`, '请输入数据源地址或字典编码')" />
    </div>
    <div v-else>
      <a-input-search
        enterButton
        style="margin-bottom: 8px"
        v-model="keyWord"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
        @search="onSearch"/>
      <vxe-grid
        border
        highlight-hover-row
        max-height="320"
        ref="selectGrid"
        resizable
        row-id="id"
        show-overflow
        size="mini"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pager-config="tablePage"
        :radio-config="{highlight: true, reserve: true, trigger: 'row'}"
        :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
        @page-change="handlePageChange">
        <template #empty>
          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
        </template>
      </vxe-grid>
    </div>
    <template #footer>
      <a-button @click="visible = false">
        {{ $srmI18n(`${$getLangAccount()}#`, '取消') }}
      </a-button>
      <a-button
        type="danger"
        @click="handleClear">
        {{ $srmI18n(`${$getLangAccount()}#`, '清除') }}
      </a-button>
      <a-button
        type="primary"
        @click="selectedOk">
        {{ $srmI18n(`${$getLangAccount()}#`, '确认') }}
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="jsx">
import {getAction} from '@api/manage'

export default {
    data (){
        return{
            columns: [
                {type: 'radio', width: 40},
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 60},
                {field: 'dictCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dictionaryCoding`, '字典编码')},
                {field: 'dictName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dictionaryName`, '字典名称')},
                {field: 'description', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dictionaryDescription`, '字典描述')}
            ],
            dataSource: '',
            inputType: 'sysDictCode',
            keyWord: '',
            loading: false,
            tableData: [],
            tablePage: {
                align: 'left',
                currentPage: 1,
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                pageSize: 500,
                pageSizes: [20, 50, 100, 200, 500],
                perfect: true,
                total: 0
            },
            visible: false
        }
    },
    methods: {
        handleClear (){
            this.visible = false
            this.$emit('ok', {dictCode: null})
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            this.loadData()
        },
        loadData (){
            this.loading = true
            const url = this.inputType === 'sysDictCode' ? '/base/dict/listSys' : '/base/dict/list'
            getAction(url, {keyWord: this.keyWord, pageNo: this.tablePage.currentPage, pageSize: this.tablePage.pageSize}).then((res) => {
                if(res.success) {
                    this.tableData = res.result.records || []
                    this.tablePage.total = res.result.total
                    this.$refs.selectGrid && this.$refs.selectGrid.clearRadioReserve()
                }
            }).finally(() => {
                this.loading = false
            })
        },
        onSearch (keyWord) {
            this.keyWord = keyWord
            this.loadData()
        },
        open (){
            this.dataSource = ''
            this.keyWord = ''
            this.loadData()
            this.visible = true
        },
        selectedOk (){
            this.loading = true
            let selectedData = null
            if (this.inputType === 'manual' && this.dataSource) {
                selectedData = {dictCode: this.dataSource}
            } else {
                selectedData = this.$refs.selectGrid.getRadioRecord()
            }
            if (selectedData) {
                this.visible = false
                this.$emit('ok', selectedData)
                this.loading = false
            } else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        }
    },
    name: 'DictSelectModal'
}
</script>
