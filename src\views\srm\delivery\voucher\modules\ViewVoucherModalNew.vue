<template>
  <div class="els-page-comtainer">
    <business-layout
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      modelLayout="collapse"
      pageStatus="detail"
      v-on="businessHandler"
    />
    <RelationGraphModal
      v-if="modalVisibleDocket && currentEditRow.documentId"
      :modalVisibleDocket="modalVisibleDocket"
      :id="documentId"
      :rootId="rootId"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    />
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK, ROWDEMO } from '@/utils/constant.js'
import { cloneDeep } from 'lodash'
import RelationGraphModal from '@comp/RelationGraphModal'
export default {
  name: 'ViewVoucherModal',
  mixins: [businessUtilMixin],
  components: {
    BusinessLayout,
    RelationGraphModal
  },
  props: {
    currentEditRow: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      documentId: '',
      rootId: '',
      modalVisibleDocket: false,
      requestData: {
        detail: {
          url: '/delivery/purchaseVoucherHead/queryById',
          args: (that) => {
            return { id: that.currentEditRow.id }
          }
        }
      },
      pageHeaderButtons: [
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'),
          show: () => this.currentEditRow.documentId,
          click: this.viewDocket
        },
        BUTTON_BACK
      ]
    }
  },
  methods: {
    handleBeforeRemoteConfigData() {
      // 头信息
      let ViewVoucherModalHeader = [
        {
          fieldLabel: '凭证号',
          fieldLabelI18nKey: 'i18n_title_voucherNo',
          fieldName: 'voucherNumber'
        },
        {
          fieldLabel: '供应商ELS账号',
          fieldLabelI18nKey: 'i18n_title_supplierAccount',
          fieldName: 'toElsAccount'
        },
        {
          fieldLabel: '供应商ERP编码',
          fieldLabelI18nKey: 'i18n_massProdHead88b_supplierErpCode',
          fieldName: 'supplierCode'
        },
        {
          fieldLabel: '供应商名称',
          fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName',
          fieldName: 'supplierName'
        },
        {
          fieldLabel: '工厂',
          fieldLabelI18nKey: 'i18n_massProdHead4e3_factoryCode',
          fieldName: 'factory_dictText'
        },
        {
          fieldLabel: '库存地点',
          fieldLabelI18nKey: 'i18n_field_storageLocation',
          fieldName: 'storageLocation_dictText'
        },
        {
          fieldLabel: '供应商公司代码',
          fieldLabelI18nKey: '',
          fieldName: 'toCompany_dictText'
        }
      ]
      let formFields = ViewVoucherModalHeader.map((n, index) => {
        let cloneDemo = cloneDeep(ROWDEMO)
        return { ...cloneDemo, ...n, groupCode: 'baseForm', sortOrder: index + 1 }
      })
      // 行信息
      let ViewVoucherModalItem = [
        {
          title: '订单号',
          fieldLabelI18nKey: 'i18n_field_orderNumber',
          field: 'orderNumber',
          width: '130',
          extend: {
            linkConfig: {
              primaryKey: 'orderNumber',
              actionPath: '/srm/order/purchase/PurchaseOrderHeadList',
              bindKey: 'orderNumber',
              otherQuery: { linkFilter: true }
            }
          }
        },
        {
          title: '补充价格记录号',
          fieldLabelI18nKey: 'i18n_field_infoRecordNumberExtend',
          field: 'infoRecordNumberExtend',
          width: 120
        },
        {
          title: '批次号',
          fieldLabelI18nKey: 'i18n_field_batchNumber',
          field: 'batchNumber',
          width: '100'
        },
        {
          title: '凭证日期',
          fieldLabelI18nKey: 'i18n_field_voucherDate',
          field: 'voucherDate',
          width: '100'
        },
        {
          title: '移动类型',
          fieldLabelI18nKey: 'i18n_title_movementType',
          field: 'moveType',
          width: '100'
        },
        {
          title: '移动类型描述',
          fieldLabelI18nKey: 'i18n_title_movementTypeDesc',
          field: 'moveType_dictText',
          width: '100'
        },
        {
          title: '借贷方向',
          fieldLabelI18nKey: 'i18n_field_loanDirection',
          field: 'loanDirection',
          width: '100'
        },
        {
          title: '订单行号',
          fieldLabelI18nKey: 'i18n_field_orderItemNumber',
          field: 'orderItemNumber',
          width: '80'
        },
        {
          title: '采购组织',
          fieldLabelI18nKey: 'i18n_massProdHeadc98_purchaseOrgCode',
          field: 'purchaseOrg_dictText',
          width: '200'
        },
        {
          title: '采购组',
          fieldLabelI18nKey: 'i18n_field_purchaseGroupCode',
          field: 'purchaseGroup_dictText',
          width: '200'
        },
        {
          title: '公司',
          fieldLabelI18nKey: 'i18n_field_company',
          field: 'company_dictText',
          width: '200'
        },
        {
          title: '供应商公司代码',
          fieldLabelI18nKey: '',
          field: 'toCompany_dictText',
          width: '200'
        },
        {
          title: '物料编码',
          fieldLabelI18nKey: 'i18n_title_materialCode',
          field: 'materialNumber',
          width: '120'
        },
        {
          title: '物料名称',
          fieldLabelI18nKey: 'i18n_title_materialName',
          field: 'materialName',
          width: '150'
        },
        // {
        //   title: '物料描述',
        //   fieldLabelI18nKey: 'i18n_title_materialDesc',
        //   field: 'materialDesc',
        //   width: '150'
        // },
        {
          title: '物料规格',
          fieldLabelI18nKey: 'i18n_title_materialSpec',
          field: 'materialSpec',
          width: '150'
        },
        {
          title: '物料组',
          fieldLabelI18nKey: 'i18n_field_materialGroup',
          field: 'materialGroup',
          width: 80
        },
        {
          title: '物料组名称',
          fieldLabelI18nKey: 'i18n_title_materialGroupName',
          field: 'materialGroupName',
          width: 100
        },
        {
          title: '物料分类',
          fieldLabelI18nKey: 'i18n_title_cateCode',
          field: 'cateCode',
          width: 80
        },
        {
          title: '物料分类名称',
          fieldLabelI18nKey: 'i18n_title_cateName',
          field: 'cateName',
          width: 100
        },
        {
          title: '订单数量',
          fieldLabelI18nKey: 'i18n_field_order_quantity',
          field: 'quantity',
          width: 120
        },
        {
          title: '采购单位',
          fieldLabelI18nKey: 'i18n_field_purchaseUnit',
          field: 'purchaseUnit_dictText',
          width: 120
        },

        {
          title: '税码',
          fieldLabelI18nKey: 'i18n_title_enterTaxCode',
          field: 'taxCode',
          width: 100
        },
        {
          title: '税率',
          fieldLabelI18nKey: 'i18n_title_taxRate',
          field: 'taxRate',
          width: 100
        },
        {
          title: '净价',
          fieldLabelI18nKey: 'i18n_title_netPrice',
          field: 'netPrice',
          width: 100,
          formatter: function({ cellValue }) {
            if(!!cellValue) return parseFloat(cellValue).toFixed(6);
            return cellValue
          }
        },
        {
          title: '含税价',
          fieldLabelI18nKey: 'i18n_title_price',
          field: 'price',
          width: 100,
          formatter: function({ cellValue }) {
            if(!!cellValue) return parseFloat(cellValue).toFixed(6);
            return cellValue
          }
        },
        {
          title: '币别',
          fieldLabelI18nKey: 'i18n_field_currency',
          field: 'currency',
          width: 100
        },
        {
          title: '本位币',
          fieldLabelI18nKey: 'i18n_field_vLl_18d4480',
          field: 'chargeCurrency',
          width: 100
        },
        {
          title: '汇率',
          fieldLabelI18nKey: 'i18n_title_exchange',
          field: 'exchange',
          width: 100
        },
        {
          title: '凭证数量',
          fieldLabelI18nKey: 'i18n_field_voucherQuantity',
          field: 'voucherQuantity',
          width: 120
        },
        {
          title: '不足结算数量',
          fieldLabelI18nKey: 'i18n_field_voucherBalanceLackQuantity',
          field: 'voucherBalanceLackQuantity',
          width: 120
        },
        {
          title: '来源',
          fieldLabelI18nKey: 'i18n_title_source',
          field: 'sourceType_dictText',
          width: 120
        },
        {
          title: '换算率',
          fieldLabelI18nKey: 'i18n_title_conversionRate',
          field: 'conversionRate',
          width: 120
        },
        {
          title: '辅数量',
          fieldLabelI18nKey: 'i18n_title_secondaryQuantity',
          field: 'secondaryQuantity',
          width: 120
        }
      ]
      let itemColumns = ViewVoucherModalItem.map((n, index) => {
        let cloneDemo = cloneDeep(ROWDEMO)
        return { ...cloneDemo, ...n, groupCode: 'purchaseVoucherItemList', align: 'center' }
      })
      return {
        groups: [
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
            groupCode: 'baseForm',
            groupType: 'head',
            sortOrder: '1'
          },
          {
            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
            groupCode: 'purchaseVoucherItemList',
            groupType: 'item',
            sortOrder: '2'
          }
        ],
        formFields,
        itemColumns
      }
    },
    clickNode() {
      this.$store.dispatch('SetTabConfirm', false)
      this.modalVisibleDocket = false
    },
    closeModalDocket() {
      this.modalVisibleDocket = false
    },
    // 单据联查
    viewDocket() {
      let row = this.currentEditRow
      this.documentId = row.documentId
      this.rootId = row.id
      this.modalVisibleDocket = true
    }
  }
}
</script>
