import { srmI18n, getLangAccount } from '@/utils/util'

// 英式 - 打包报价
export const rankGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    stripe: true,
    height: 276,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        {
            field: 'netTotalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'),
            width: 150,
            slots: {
                default: 'netTotalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'totalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
            width: 150,
            require: true,
            slots: {
                default: 'totalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'confirmQuantity',
            title: srmI18n(`${getLangAccount()}#i18n_field_RLWR_38d795d5`, '确认数量'),
            width: 120,
            slots: {
                default: 'confirmQuantity_default'
            }
        },
        {
            field: 'supplierRemark',
            title: srmI18n(`${getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
            width: 120,
            slots: {
                default: 'supplierRemark_default'
            }
        }
    ],
    data: []
}

// 英式 - 打包报价 物料列表
export const materialPackGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        // { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'), width: 80, showOverflow: true },
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialName', title: srmI18n(`${getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialSpec', title: srmI18n(`${getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120, align: 'center', showOverflow: true },
        { field: 'factory_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 120, align: 'center', showOverflow: true },
        { field: 'storageLocation_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'purchaseUnit_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true }
    ],
    data: []
}

// 英式 - 逐条、批量报价
export const materialGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        // { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'), width: 80, showOverflow: true },
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialName', title: srmI18n(`${getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialSpec', title: srmI18n(`${getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120, align: 'center', showOverflow: true },
        { field: 'factory_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 120, align: 'center', showOverflow: true },
        { field: 'storageLocation_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'purchaseUnit_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true },
        { field: 'startPrice', title: srmI18n(`${getLangAccount()}#i18n_field_AAu_21f5181`, '起拍价'), width: 120, showOverflow: true },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            width: 150,
            slots: {
                default: 'netPrice_default',
                header: 'price_header'
            }
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 150,
            require: true,
            slots: {
                default: 'price_default',
                header: 'price_header'
            }
        },
        {
            field: 'confirmQuantity',
            title: srmI18n(`${getLangAccount()}#i18n_field_RLWR_38d795d5`, '确认数量'),
            width: 120,
            slots: {
                default: 'confirmQuantity_default'
            }
        },
        {
            field: 'supplierRemark',
            title: srmI18n(`${getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
            width: 120,
            slots: {
                default: 'supplierRemark_default'
            }
        },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_title_quotationTimes`, '报价次数'), width: 150 }
    ],
    data: []
}

// 一次性报价
export const materialOnceGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        // { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'), width: 80, showOverflow: true },
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialName', title: srmI18n(`${getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialSpec', title: srmI18n(`${getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120, align: 'center', showOverflow: true },
        { field: 'factory_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 120, align: 'center', showOverflow: true },
        { field: 'storageLocation_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'purchaseUnit_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true },
        { field: 'startPrice', title: srmI18n(`${getLangAccount()}#i18n_field_AAu_21f5181`, '起拍价'), width: 120, showOverflow: true },
        {
            field: 'netPrice',
            title: '不报价',
            width: 150,
            slots: {
                default: 'notQuote_default',
                header: 'notQuote_header'
            }
        },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            width: 150,
            slots: {
                default: 'netPrice_default',
                header: 'price_header'
            }
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 150,
            require: true,
            slots: {
                default: 'price_default',
                header: 'price_header'
            }
        },
        {
            field: 'confirmQuantity',
            title: srmI18n(`${getLangAccount()}#i18n_field_RLWR_38d795d5`, '确认数量'),
            width: 120,
            slots: {
                default: 'confirmQuantity_default'
            }
        },
        {
            field: 'supplierRemark',
            title: srmI18n(`${getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
            width: 120,
            slots: {
                default: 'supplierRemark_default'
            }
        },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_title_quotationTimes`, '报价次数'), width: 150 }
    ],
    data: []
}

export const chartGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    columns: [
        {
            field: 'supplierName',
            title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
            align: 'center',
            showOverflow: true
        },
        {
            field: 'quoteRank',
            title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'),
            width: 84,
            align: 'center',
            showOverflow: true
        },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            align: 'center',
            showOverflow: true
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            align: 'center',
            showOverflow: true
        },
        {
            field: 'netTotalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'),
            align: 'center',
            showOverflow: true
        },
        {
            field: 'totalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
            align: 'center',
            showOverflow: true
        },
        {
            field: 'quoteCount',
            title: srmI18n(`${getLangAccount()}#i18n_title_quotationTimes`, '报价次数'),
            width: 150,
            align: 'center',
            showOverflow: true
        }
    ],
    data: []
}

export const saleChartGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    columns: [
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_offer`, '报价'),
            width: 84,
            align: 'center',
            slots: { default: 'price_default' }
        },
        {
            field: 'quoteTime',
            title: srmI18n(`${getLangAccount()}#i18n_title_quotationTime`, '报价时间'),
            width: 156,
            align: 'center',
            slots: { default: 'quoteTime_default' }
        }
    ],
    data: []
}

export const quoteGridOptions = {
    size: 'mini',
    border: true,
    showOverflow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            width: 150,
            slots: {
                default: 'netPrice_default',
                header: 'price_header'
            }
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 150,
            require: true,
            slots: {
                default: 'price_default',
                header: 'price_header'
            }
        },
        { field: 'taxRate', title: srmI18n(`${getLangAccount()}#i18n_title_taxRate`, '税率'), width: 150 },
        {
            field: 'effectiveDate',
            title: srmI18n(`${getLangAccount()}#i18n_field_effectiveTime`, '生效时间'),
            width: 150,
            require: true,
            editRender: {name: 'mDatePicker'}
        },
        {
            field: 'expiryDate',
            title: srmI18n(`${getLangAccount()}#i18n_field_expiryTime`, '失效时间'),
            width: 150,
            require: true,
            editRender: {name: 'mDatePicker'}
        },
        { field: 'priceUnit', title: srmI18n(`${getLangAccount()}#i18n_title_unit`, '价格基数'), width: 150 },
        { field: 'currency_dictText', title: srmI18n(`${getLangAccount()}#i18n_title_currency`, '币别'), width: 150 },
        { field: 'remark', title: srmI18n(`${getLangAccount()}#i18n_title_remark`, '备注'), width: 200, slots: { default: 'remark_default' } },
        { title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'), width: 100, align: 'center', slots: { default: 'operate' }, fixed: 'right' }
    ],
    data: []
}
export const batchMaterialGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    loading: false,
    headerAlign: 'center',
    columns: [
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true }
    ],
    data: []
}
export const batchSaleChartGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showHeaderOverflow: true,
    showOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    columns: [
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_offer`, '报价'),
            width: 84,
            align: 'center',
            slots: { default: 'price_default' }
        },
        {
            field: 'quoteTime',
            title: srmI18n(`${getLangAccount()}#i18n_title_quotationTime`, '报价时间'),
            width: 156,
            align: 'center',
            slots: { default: 'quoteTime_default' }
        }
    ],
    data: []
}
export const batchRankGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    stripe: true,
    height: 276,
    loading: false,
    headerAlign: 'center',
    columns: [
        { field: 'supplierName', title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), align: 'center', showOverflow: true },
        { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'), align: 'center', showOverflow: true },
        { field: 'price', title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), visible: true, align: 'center', showOverflow: true },
        { field: 'totalAmount', title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'), align: 'center', showOverflow: true },
        // { field: 'netTotalAmount', title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'), align: 'center', showOverflow: true },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_title_quotationTimes`, '报价次数'), align: 'center', showOverflow: true },
        { field: 'quoteTime', title: srmI18n(`${getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), align: 'center', showOverflow: true }
    ],
    data: []
}

export const batchQuoteGridOptions = {
    size: 'mini',
    border: true,
    showOverflow: true,
    loading: false,
    headerAlign: 'center',
    editConfig: {
        trigger: 'click',
        mode: 'row'
    },
    columns: [
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_needCout`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), width: 120, align: 'center', showOverflow: true },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            width: 150,
            slots: {
                default: 'netPrice_default',
                header: 'price_header'
            }
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            width: 150,
            require: true,
            slots: {
                default: 'price_default',
                header: 'price_header'
            }
        },
        { field: 'taxRate', title: srmI18n(`${getLangAccount()}#i18n_title_taxRate`, '税率'), width: 150 },
        {
            field: 'effectiveDate',
            title: srmI18n(`${getLangAccount()}#i18n_field_effectiveTime`, '生效时间'),
            width: 150,
            require: true,
            editRender: {name: 'mDatePicker'}
        },
        {
            field: 'expiryDate',
            title: srmI18n(`${getLangAccount()}#i18n_field_expiryTime`, '失效时间'),
            width: 150,
            require: true,
            editRender: {name: 'mDatePicker'}
        },
        { field: 'priceUnit', title: srmI18n(`${getLangAccount()}#i18n_title_unit`, '价格基数'), width: 150 },
        { field: 'currency_dictText', title: srmI18n(`${getLangAccount()}#i18n_title_currency`, '币别'), width: 150 },
        { field: 'remark', title: srmI18n(`${getLangAccount()}#i18n_title_remark`, '备注'), width: 200, slots: { default: 'remark_default' } }
    ],
    data: []
}