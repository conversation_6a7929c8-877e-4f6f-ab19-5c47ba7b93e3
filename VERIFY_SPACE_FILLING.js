// 🚀 验证联系人信息页签空间填充效果的脚本
// 请在浏览器控制台中执行此脚本

console.log('🔍 开始验证联系人信息页签空间填充效果...');

// 1. 检查窗口尺寸
const windowHeight = document.documentElement.clientHeight;
const windowWidth = document.documentElement.clientWidth;
console.log(`📐 窗口尺寸: ${windowWidth} × ${windowHeight}`);

// 2. 查找关键元素
const elements = {
    tabsContentHolder: document.querySelector('.ant-tabs-content-holder'),
    tabPane: document.querySelector('.ant-tabs-tabpane'),
    contactsGrid: document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]'),
    contactsGridByRef: document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]'),
    vxeTable: null,
    vxeBodyWrapper: null
};

// 查找表格元素
if (elements.contactsGrid || elements.contactsGridByRef) {
    const gridElement = elements.contactsGrid || elements.contactsGridByRef;
    elements.vxeTable = gridElement.querySelector('.vxe-table');
    elements.vxeBodyWrapper = gridElement.querySelector('.vxe-table--body-wrapper');
}

console.log('🎯 关键元素查找结果:');
Object.keys(elements).forEach(key => {
    const element = elements[key];
    console.log(`- ${key}: ${element ? '✅ 找到' : '❌ 未找到'}`);
    if (element) {
        console.log(`  尺寸: ${element.offsetWidth} × ${element.offsetHeight}`);
    }
});

// 3. 检查样式应用情况
console.log('🎨 样式应用检查:');

if (elements.tabsContentHolder) {
    const styles = window.getComputedStyle(elements.tabsContentHolder);
    console.log('Tab内容容器样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

if (elements.tabPane) {
    const styles = window.getComputedStyle(elements.tabPane);
    console.log('Tab页签样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

if (elements.vxeTable) {
    const styles = window.getComputedStyle(elements.vxeTable);
    console.log('VXE表格样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

if (elements.vxeBodyWrapper) {
    const styles = window.getComputedStyle(elements.vxeBodyWrapper);
    console.log('表格体样式:');
    console.log(`- height: ${styles.height}`);
    console.log(`- max-height: ${styles.maxHeight}`);
    console.log(`- min-height: ${styles.minHeight}`);
}

// 4. 计算空间利用率
const gridElement = elements.contactsGrid || elements.contactsGridByRef;
if (gridElement) {
    const gridHeight = gridElement.offsetHeight;
    const utilization = (gridHeight / windowHeight * 100).toFixed(1);
    
    console.log('📊 空间利用分析:');
    console.log(`- 表格高度: ${gridHeight}px`);
    console.log(`- 窗口高度: ${windowHeight}px`);
    console.log(`- 空间利用率: ${utilization}%`);
    
    // 计算理想高度
    const idealHeight = windowHeight - 120;
    const heightDiff = idealHeight - gridHeight;
    
    console.log(`- 理想高度: ${idealHeight}px`);
    console.log(`- 高度差异: ${heightDiff}px`);
    
    if (heightDiff > 50) {
        console.warn('⚠️ 仍有较大空白区域，需要进一步优化');
        
        // 提供强制调整方案
        console.log('🔧 强制调整方案:');
        console.log(`
// 执行以下代码强制调整高度
const gridElement = document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]') || 
                   document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]');
if (gridElement) {
    gridElement.style.height = '${idealHeight}px';
    gridElement.style.minHeight = '${idealHeight}px';
    
    const table = gridElement.querySelector('.vxe-table');
    if (table) {
        table.style.height = '${idealHeight}px';
        table.style.minHeight = '${idealHeight}px';
    }
    
    const bodyWrapper = gridElement.querySelector('.vxe-table--body-wrapper');
    if (bodyWrapper) {
        bodyWrapper.style.maxHeight = '${idealHeight - 50}px';
        bodyWrapper.style.minHeight = '${idealHeight - 100}px';
    }
    
    console.log('✅ 已强制调整表格高度');
}
        `);
    } else {
        console.log('✅ 空间利用良好！');
    }
} else {
    console.error('❌ 未找到联系人信息表格元素');
}

// 5. 检查是否在正确的页签
const activeTab = document.querySelector('.ant-tabs-tab-active');
if (activeTab) {
    const tabText = activeTab.textContent;
    console.log(`📋 当前活动页签: ${tabText}`);
    
    if (!tabText.includes('联系人') && !tabText.includes('联系人信息')) {
        console.warn('⚠️ 当前不在联系人信息页签，请切换到联系人信息页签后重新执行此脚本');
    }
} else {
    console.warn('⚠️ 未找到活动页签');
}

// 6. 提供实时监控功能
console.log('🔄 启用实时监控...');
let monitorInterval;

function startMonitoring() {
    monitorInterval = setInterval(() => {
        const currentGrid = document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]') || 
                           document.querySelector('[ref*="supplierContactsInfoList"], [ref*="contactsInfo"]');
        if (currentGrid) {
            const currentHeight = currentGrid.offsetHeight;
            const currentUtilization = (currentHeight / document.documentElement.clientHeight * 100).toFixed(1);
            console.log(`📊 实时监控 - 高度: ${currentHeight}px, 利用率: ${currentUtilization}%`);
        }
    }, 5000); // 每5秒检查一次
}

function stopMonitoring() {
    if (monitorInterval) {
        clearInterval(monitorInterval);
        console.log('⏹️ 已停止实时监控');
    }
}

// 自动启动监控
startMonitoring();

// 提供停止监控的方法
window.stopSpaceMonitoring = stopMonitoring;

console.log('🎉 验证完成！');
console.log('💡 如需停止实时监控，请执行: stopSpaceMonitoring()');

// 7. 检查控制台是否有相关的调试信息
console.log('🔍 查找相关调试信息...');
setTimeout(() => {
    console.log('请查看控制台中是否有以下调试信息:');
    console.log('- "🎯 联系人信息表格极致空间优化"');
    console.log('- "🎯 联系人信息表格在tab模式下的最终高度"');
    console.log('- "[联系人信息页签] 已启用极致填充空间策略"');
}, 1000);
