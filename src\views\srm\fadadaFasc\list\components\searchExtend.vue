<template>
  <transition
    name="component-fade"
    mode="out-in"
    v-if="visible">
    <div
      class="search-extend">
      <!-- <template slot="footer">
      <div style="float: left">
        <a-button
          :loading="loading"
          @click="handleReset"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}
        </a-button>
        <a-button
          :loading="loading"
          @click="handleSave"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_saveSearchListItem`, '保存查询条件') }}
        </a-button>
      </div>
      <a-button
        :loading="loading"
        @click="handleCancel"
      >
        {{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}
      </a-button>
      <a-button
        :loading="loading"
        type="primary"
        @click="handleOk"
      >
        {{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }} 
      </a-button>
    </template> -->

      <a-spin :spinning="loading">
        <a-row class="search-condition-wrap">
          <div class="arraw-box">
            <img src="@assets/img/common/arraw-box.png" />
          </div>
          <div class="scheme-wrap">
            <a-dropdown placement="bottomRight">
              <a
                class="ant-dropdown-link"
                @click="(e) => e.preventDefault()">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_MjCp_2e5db782`, '我的方案') }} <a-icon type="down" /></a>
              <!-- 查询记录 -->
              <a-card
                slot="overlay"
                class="j-super-query-history-card"
                :bordered="true">
                <a-empty
                  v-if="treeData.length === 0"
                  class="j-super-query-history-empty"
                  :description="$srmI18n(`${$getLangAccount()}#i18n_title_noSavedEveryQuery`, '没有保存任何查询')" />
                <a-tree
                  v-else
                  ref="treeRef"
                  class="j-super-query-history-tree"
                  show-icon
                  :selectedKeys="selectedKeys"
                  :tree-data="treeData"
                  @select="handleTreeSelect"
                  @rightClick="handleTreeRightClick" />
              </a-card>
            </a-dropdown>
          </div>
          <a-col :span="24">
            <a-empty v-if="queryParamsModel.length === 0">
              <div slot="description">
                <span> {{ $srmI18n(`${$getLangAccount()}#i18n_title_noSaveSearchListItem`, '没有任何查询条件') }} </span>
                <a-divider type="vertical" />
                <a @click="handleAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_title_clickNewAdd`, '点击新增') }}</a>
              </div>
            </a-empty>
            <a-form
              v-else
              layout="inline">
              <a-form-item
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_filterConditionMatching`, '过滤条件匹配')">
                <div class="inline-btn">
                  <a-select
                    v-model="selectValue"
                    class="filter-select">
                    <a-select-option value="AND"> AND {{ $srmI18n(`${$getLangAccount()}#i18n_title_allConditionsRequireMatching`, '（所有条件都要求匹配）') }} </a-select-option>
                    <a-select-option value="OR"> OR {{ $srmI18n(`${$getLangAccount()}#i18n_title_anyOneConditionsMatches`, '（条件中的任意一个匹配）') }} </a-select-option>
                  </a-select>
                  <div class="search-opts">
                    <a-button @click="handleAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_SuTI_33418a7a`, '添加条件') }}</a-button>
                    <a-button @click="handleSave">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_sMTI_25b18010`, '保存条件') }}</a-button>
                    <a-button @click="handleReset">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_VVTI_33dbaf8a`, '清空条件') }}</a-button>
                    <a-button
                      type="primary"
                      @click="handleOk">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_RLmh_38d7dc13`, '确认查询') }}</a-button>
                    <a-button @click="handleCancel">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
                  </div>
                </div>
              </a-form-item>
              <div class="query-params-box">
                <a-row
                  type="flex"
                  style="margin-top: 10px"
                  :gutter="16"
                  v-for="(item, index) in queryParamsModel"
                  :key="index">
                  <a-col :span="8">
                    <a-select
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectQueryField`, '选择查询字段')"
                      v-model="item.fieldCode"
                      show-search
                      :filter-option="filterOption"
                      @select="(val, option) => handleSelected(option, item)">
                      <a-select-option
                        v-for="(f, fIndex) in fieldList"
                        :key="'field' + fIndex"
                        :value="f.fieldValue"
                        :data-idx="fIndex"
                        :data-itemid="f.id">
                        {{ f.text }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="4">
                    <a-select
                      :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_matchingRules`, '匹配规则')"
                      v-model="item.logicSymbol"
                      @change="changeLogicSymbol(item.logicSymbol, item)">
                      <a-select-option value="isnull">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_alert_LV_9f380`, '为空') }}
                      </a-select-option>
                      <a-select-option value="eq">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_beEqualTo`, '等于') }}
                      </a-select-option>
                      <a-select-option value="ne">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_NoneBeEqualTo`, '不等于') }}
                      </a-select-option>
                      <a-select-option value="gt">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_greaterThan`, '大于') }}
                      </a-select-option>
                      <a-select-option value="ge">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_greaterThanOrEqualTo`, '大于等于') }}
                      </a-select-option>
                      <a-select-option value="lt">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_lessThan`, '小于') }}
                      </a-select-option>
                      <a-select-option value="le">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_lessThanEqualTo`, '小于等于') }}
                      </a-select-option>
                      <a-select-option value="right_like">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_startSewith`, '以..开始') }}
                      </a-select-option>
                      <a-select-option value="left_like">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_endSewith`, '以..结尾') }}
                      </a-select-option>
                      <a-select-option value="like">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_seacrhContain`, '包含') }}
                      </a-select-option>
                      <a-select-option value="in">
                        {{ $srmI18n(`${$getLangAccount()}#i18n_title_InContain`, '在...中') }}
                      </a-select-option>
                    </a-select>
                  </a-col>
                  <a-col :span="12">
                    <a-row
                      type="flex"
                      :gutter="16">
                      <a-col flex="auto">
                        <template v-if="item.dictCode">
                          <template v-if="item.fieldType === 'table-dict'">
                            <j-popup
                              v-model="item.fieldValue"
                              :code="item.dictTable"
                              :field="item.dictCode"
                              :org-fields="item.dictCode"
                              :dest-fields="item.dictCode"
                              :disabled="item.logicSymbol === 'isnull'" />
                          </template>
                          <j-dict-select-tag
                            v-else
                            v-model="item.fieldValue"
                            :dict-code="item.dictCode"
                            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelect`, '请选择')"
                            :disabled="item.logicSymbol === 'isnull'" />
                        </template>
                        <!-- 加入远程来写组件就可以 -->
                        <m-remote-select
                          v-else-if="item.fieldType === 'remoteSelect'"
                          v-model="item.showValue"
                          :config="fieldList[fieldListIndex]"
                          :disabled="item.disabled"
                          @afterClearCallBack="()=> handleSelectModalAfterClear(item.afterClearCallBack)"
                          @ok="(rows) => handleSelectModalAfterSelect({queryParams: item, rows, fieldListItem: fieldList[fieldListIndex]})"
                        />
                        <j-select-multi-user
                          v-else-if="item.fieldType === 'select-user'"
                          v-model="item.fieldValue"
                          :buttons="false"
                          :multiple="false"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectUser`, '请选择用户')"
                          :return-keys="['id', item.customReturnField || 'username']"
                          :disabled="item.logicSymbol === 'isnull'" />
                        <j-select-depart
                          v-else-if="item.fieldType === 'select-depart'"
                          v-model="item.fieldValue"
                          :multi="false"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectDepartment`, '请选择部门')"
                          :custom-return-field="item.customReturnField || 'id'"
                          :disabled="item.logicSymbol === 'isnull'" />
                        <j-date
                          v-else-if="item.fieldType == 'date'"
                          v-model="item.fieldValue"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectADate`, '请选择日期')"
                          style="width: 100%"
                          :disabled="item.logicSymbol === 'isnull'" />
                        <j-date
                          v-else-if="item.fieldType == 'datetime'"
                          v-model="item.fieldValue"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectATimes`, '请选择时间')"
                          :show-time="true"
                          date-format="YYYY-MM-DD HH:mm:ss"
                          style="width: 100%" />
                        <a-input-number
                          v-else-if="item.fieldType == 'int' || item.fieldType == 'number'"
                          style="width: 100%"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterNumbers`, '请输入数值')"
                          v-model="item.fieldValue"
                          :disabled="item.logicSymbol === 'isnull'" />
                        <a-input
                          v-else
                          v-model="item.fieldValue"
                          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterVaules`, '请输入值')"
                          :disabled="item.logicSymbol === 'isnull'" />
                      </a-col>
                      <a-col flex="45px">
                        <!-- <a-button
                      @click="handleAdd"
                      icon="plus"
                    />&nbsp; -->
                        <a-button
                          @click="handleDel(index)"
                          icon="minus" />
                      </a-col>
                    </a-row>
                  </a-col>
                </a-row>
              </div>
            </a-form>
          </a-col>

        </a-row>
      </a-spin>

      <a-modal
        v-drag
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterSaveNames`, '请输入保存的名称')"
        :visible="prompt.visible"
        @cancel="prompt.visible = false"
        @ok="handlePromptOk">
        <a-input v-model="prompt.value" />
      </a-modal>
    </div>

  </transition>
  
</template>

<script lang="jsx">
import * as utils from '@/utils/util'
import JDate from '@/components/els/JDate.vue'
import JSelectDepart from '@/components/elsbiz/JSelectDepart'
import JSelectMultiUser from '@/components/elsbiz/JSelectMultiUser'
import { getAction, postAction } from '@api/manage'
import { Empty, Tree } from 'ant-design-vue'
export default {
    name: 'JSuperQuery',
    components: { JDate, JSelectDepart, JSelectMultiUser, AEmpty: Empty, ATree: Tree },
    props: {
        tableCode: {
            type: String,
            default: ''
        },
        callback: {
            type: String,
            required: false,
            default: 'handleSuperQuery'
        },
        synQueryParamsCallback: {
            type: String,
            required: false,
            default: 'synQueryParams'
        },
        saveCode: {
            type: String,
            default: null
        }
    },
    data () {
        return {
            prompt: {
                visible: false,
                value: ''
            },
            loading: false,
            fieldList: [],
            visible: false,
            queryParamsModel: [{ logicSymbol: 'like' }],
            treeIcon: () => <a-icon type="file-text" />,
            treeData: [],
            selectValue: 'AND',
            selectedKeys: [],
            fieldListIndex: 0
        }
    },
    computed: {
        izMobile () {
            return this.device === 'mobile'
        },
        tooltipProps () {
            return this.izMobile ? { visible: false } : {}
        },
        fullSaveCode () {
            let saveCode = this.saveCode
            if (saveCode == null || saveCode === '') {
                saveCode = this.$route.fullPath
            }
            return this.saveCodeBefore + saveCode
        }
    },
    watch: {
        // 当 saveCode 变化时，重新查询已保存的条件
        fullSaveCode: {
            immediate: true,
            handler () {
                let list = this.$ls.get(this.fullSaveCode)
                if (list instanceof Array) {
                    this.saveTreeData = list.map((i) => this.renderSaveTreeData(i))
                }
            }
        }
    },
    methods: {
        formatTitle ( {rows, queryParams, fieldListItem}) {
            let columns = fieldListItem?.extend?.modalColumns
            if (!columns) return ''
            let leng = columns.length
            return columns.reduce((acc, obj, i) => {
                acc += (rows[0][obj.field] || '') // 单选
                if (leng > 1 && i !== columns.length - 1) {
                    acc += '_'
                }
                return acc
            }, '')
        },
        handleSelectModalAfterSelect (info) {
            const {queryParams, rows, fieldListItem} = info
            if (fieldListItem?.extend?.bindFunction) {
                fieldListItem?.extend?.bindFunction.call(null, this, {rows, queryParams, fieldListItem})
            } else {
                queryParams.fieldValue = this.formatTitle({rows, queryParams, fieldListItem})
            }
        },
        handleSelectModalAfterClear (cb) {
            cb && cb(this.form, this.pageData)
        },
        //查询逻辑条件改变时
        changeLogicSymbol (value, item) {
            if (value === 'isnull') {
                item.fieldValue = ''
            }
        },
        show (flag = true) {
            if (!this.queryParamsModel || this.queryParamsModel.length == 0) {
                this.queryParamsModel = [{}]
            }
            this.requestFieldList()
            this.requestQueryHeader()
            this.visible = flag
        },
        handleOk ({type}) {
       
            if(type!='reset'){
                this.visible=false 
            }
            if (!this.isNullArray(this.queryParamsModel)) {
                let that = this
                let params = this.removeEmptyObject(utils.cloneObject(this.queryParamsModel))
                params.forEach((item) => {
                    item.joiner = that.selectValue
                })
                this.$emit(this.callback, params, that.selectValue)
            } else {
                this.$emit(this.callback, null, null)
            }
            let queryParamsModels=this.queryParamsModel.filter(val=> !!val.fieldCode )
            this.$emit('queryParamsModelLength', queryParamsModels.length)
        },
        // 广播高级查询条件，同步外面的查询条件
        emitSearchParams () {
            if (!this.isNullArray(this.queryParamsModel)) {
                let that = this
                let params = this.removeEmptyObject(utils.cloneObject(this.queryParamsModel))
                params.forEach((item) => {
                    item.joiner = that.selectValue
                })
                this.$emit(this.synQueryParamsCallback, params, that.selectValue)
            } else {
                this.$emit(this.synQueryParamsCallback, null, null)
            }
            let queryParamsModels=this.queryParamsModel.filter(val=> !!val.fieldCode )
            this.$emit('queryParamsModelLength', queryParamsModels.length)
        },
        handleCancel () {
            this.close()
        },
        close () {
            this.$emit('close')
            this.visible = false
        },
        handleAdd () {
            this.queryParamsModel.push({ logicSymbol: 'like' })
            this.emitSearchParams()
        },
        handleDel (index) {
            this.queryParamsModel.splice(index, 1)
            this.emitSearchParams()
        },
        handleSelected (option, item) {
            let index = option.data.attrs['data-idx']
            this.fieldListIndex = index
            let itemId = option.data.attrs['data-itemid']
            let { fieldType, dictCode, dictTable, customReturnField } = this.fieldList[index]
            item['fieldType'] = fieldType
            item['dictCode'] = dictCode
            item['dictTable'] = dictTable
            item['customReturnField'] = customReturnField
            item['queryItemId'] = itemId
            this.$set(item, 'fieldValue', '')
            if (fieldType == 'remoteSelect') { // 动态下拉的
                this.$set(item, 'showValue', '')
            }
        },
        handleReset () {
            this.queryParamsModel = [{ logicSymbol: 'like' }]
            this.handleOk({type: 'reset'})
        },
        
        filterOption (input, option) {
            return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        },
        handleSave () {
            let queryParams = this.removeEmptyObject(utils.cloneObject(this.queryParamsModel))
            if (this.isNullArray(queryParams)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emptyConditionCannotSaved`, '请输入保存的名称'))
            } else {
                this.prompt.value = ''
                this.prompt.visible = true
            }
        },
        // handlePromptOk () {
        //     let { value } = this.prompt
        //     if(!value) {
        //         this.$message.warning('请输入查询方案名称')
        //         return
        //     }
        //     // 判断有没有重名
        //     let filterList = this.treeData.filter(i => i.title === value)
        //     if (filterList.length > 0) {
        //         this.$confirm({
        //             content: `${value} 已存在，是否覆盖？`,
        //             onOk: () => {
        //                 this.saveQueryCondition(filterList[0].id)
        //             }
        //         })
        //     } else {
        //         this.saveQueryCondition()

        //     }
        // },
        handlePromptOk () {
            let { value } = this.prompt
            if (!value) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_emptyConditionCannotSavedName`, '保存名称不能为空'))
                return
            }
            this.saveQueryCondition()
            // 取出查询条件
            // let records = this.removeEmptyObject(this.queryParamsModel)
            // // 判断有没有重名的
            // let filterList = this.treeData.filter(i => i.originTitle === value)
            // if (filterList.length > 0) {
            //     this.$confirm({
            //         content: `${value} 已存在，是否覆盖？`,
            //         onOk: () => {
            //             this.prompt.visible = false
            //             filterList[0].records = records
            //             this.saveQueryCondition()
            //             //this.$message.success('保存成功')
            //         }
            //     })
            // } else {
            //     // 没有重名的，直接添加
            //     this.prompt.visible = false
            //     // 添加到树列表中
            //     // this.treeData.push(this.renderSaveTreeData({
            //     //     title: value,
            //     //     matchType: this.matchType,
            //     //     records: records
            //     // }))
            //     // 保存到服务器
            //     this.saveQueryCondition()
            //     //this.$message.success('保存成功')
            // }
        },
        /** 渲染保存查询条件的 title（加个删除按钮） */
        renderSaveTreeData (item) {
            item.icon = this.treeIcon
            item.originTitle = item['title']
            item.title = (arg1, arg2) => {
                let vNode
                // 兼容旧版的Antdv
                if (arg1.dataRef) {
                    vNode = arg1
                } else if (arg2.dataRef) {
                    vNode = arg2
                } else {
                    return <span style="color:red;">{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_AntdvVersionIsNotSupported`, 'Antdv版本不支持')}</span>
                }
                let { originTitle } = vNode.dataRef
                return (
                    <div class="j-history-tree-title">
                        <span>{originTitle}</span>

                        <div class="j-history-tree-title-closer" onClick={(e) => this.handleRemoveSaveTreeItem(e, vNode)}>
                            <a-icon type="close-circle" />
                        </div>
                    </div>
                )
            }
            return item
        },
        handleRemoveSaveTreeItem (event, vNode) {
            // 阻止事件冒泡
            event.stopPropagation()
            this.$confirm({
                zIndex: 1051,
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteCurrentQuery`, '是否删除当前查询？'),
                onOk: () => {
                    let { eventKey } = vNode
                    //this.treeData.splice(Number.parseInt(eventKey.substring(2)), 1)
                    this.deleteQueryCondition(vNode.id, Number.parseInt(eventKey.substring(2)))
                    //this.saveToLocalStore()
                }
            })
        },
        handleTreeSelect (idx, event) {
            if (event.selectedNodes[0]) {
                this.selectedKeys = idx
                this.requestQueryItem(event.selectedNodes[0].data.props.id)
            } else {
                if (this.selectedKeys && this.selectedKeys.length) {
                    if (this.selectedKeys[0] && this.selectedKeys[0].length) {
                        let getIndex = this.selectedKeys[0].substring(2, 3)
                        let currentData = this.treeData[getIndex]
                        this.requestQueryItem(currentData.id)
                    }
                }
            }
        },
        handleTreeRightClick (args) {
            this.$confirm({
                mask: false,
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteCurrentQuery`, '是否删除当前查询？'),
                onOk: () => {
                    let {
                        node: { eventKey, dataRef }
                    } = args
                    this.deleteQueryCondition(dataRef.id, Number.parseInt(eventKey.substring(2)))
                }
            })
        },
        deleteQueryCondition (id, index) {
            getAction('/base/queryHead/delete', { id: id }).then((res) => {
                if (res.success) {
                    this.treeData.splice(index, 1)
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_deleteSuccess`, '删除成功'))
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        requestQueryHeader () {
            let that = this
            getAction('/base/queryHead/queryByTableCode', { tableCode: this.tableCode }).then((res) => {
                if (res.success) {
                    let list = res.result
                    if (list.length) {
                        let itemList = list.map((item) => {
                            return {
                                id: item.id,
                                title: item.queryName,
                                icon: that.treeIcon
                            }
                        })

                        that.treeData = itemList.map((i) => this.renderSaveTreeData(i))
                    }
                }
            })
        },
        requestQueryItem (id) {
            let that = this
            this.loading = true
            getAction('/base/queryHead/queryById', { id: id }).then((res) => {
                if (res.success) {
                    let itemList = res.result.elsQueryItemList
                    let records = itemList.map((item) => {
                        return {
                            dictCode: item.dictCode,
                            fieldCode: item.fieldCode,
                            logicSymbol: item.logicSymbol,
                            fieldType: item.fieldType,
                            fieldValue: item.fieldValue  
                            //queryItemId: item.queryItemId
                        }
                    }) || {}
                    that.selectValue = res.result.matchType
                    that.queryParamsModel = utils.cloneObject(records)
                    that.loading = false
                }
            })
        },
        // 查询方案保存到 服务器
        saveQueryCondition (queryId) {
            let that = this
            let url = '/base/queryHead/add'
            if (queryId) {
                url = '/base/queryHead/edit'
            }
            let { value } = this.prompt
            let detailList = this.removeEmptyObject(utils.cloneObject(this.queryParamsModel))
            let elsQueryItemList = detailList.map((item) => {
                return {
                    dictCode: item.dictCode,
                    fieldCode: item.fieldCode,
                    fieldType: item.fieldType,
                    logicSymbol: item.logicSymbol,
                    fieldValue: item.fieldValue
                }
            })
            let params = { tableCode: this.tableCode, matchType: that.selectValue, id: queryId, queryName: value, elsQueryItemList: elsQueryItemList }
            this.prompt.visible = false
            postAction(url, params).then((res) => {
                if (res.success) {
                    if (!queryId) {
                        that.treeData.push({
                            title: value,
                            icon: that.treeIcon,
                            records: that.removeEmptyObject(utils.cloneObject(this.queryParamsModel))
                        })
                    }
                    that.requestQueryHeader()
                    that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_savaSuccess`, '保存成功'))
                }
            })
        },
        isNullArray (array) {
            //判断是不是空数组对象
            if (!array || array.length === 0) {
                return true
            }
            if (array.length === 1) {
                let obj = array[0]
                if (obj.logicSymbol === 'isnull') {
                    if (!obj.fieldCode) {
                        return true
                    }
                } else {
                    if (obj.fieldValue == '0') {
                        return false
                    }
                    if (!obj.fieldCode || !obj.fieldValue || !obj.logicSymbol) {
                        return true
                    }
                }
            }
            return false
        },
        // 去掉数组中的空对象
        removeEmptyObject (array) {
            for (let i = 0; i < array.length; i++) {
                let item = array[i]
                if (item == null || Object.keys(item).length <= 0) {
                    array.splice(i--, 1)
                }
            }
            return array
        },
        //获取字段选项列表
        requestFieldList () {
            let that = this
            getAction('/base/columnDefine/queryListByTableCode', { tableCode: this.tableCode }).then((res) => {
                if (res.success) {
                    that.fieldList = res.result.map((item) => {
                        // extendLink: {
                        //       modalParams: {},
                        //       modalUrl: '/account/elsSubAccount/list',
                        //       modalColumns: [{field: 'elsAccount', title: '子账号', with: 150}, {field: 'realname', title: '姓名', with: 150} ],
                        //       bindFunction: 'function(Vue,{rows, item}){debugger;item.fieldValue = rows[0].deleted_dictText + "_" + "测试文字"}"}'
                        //     }
                        //  item.extendLink = '{"modalParams":{},"modalUrl":"/account/elsSubAccount/list","modalColumns":[{"field":"elsAccount","title":"子账号","with":150},{"field":"realname","title":"姓名","with":150}],"bindFunction":"function(Vue,{rows, item}){console.log(item)}"}'
                        // if (item.fieldType == 'remoteSelect') {
                        // item.extendLink = '{"modalParams":{},"modalUrl":"/account/elsSubAccount/list","modalColumns":[{"field":"subAccount","title":"子账号","with":150},{"field":"realname","title":"姓名","with":150}],"bindFunction":"function(Vue,{queryParams, rows, fieldListItem}){queryParams.fieldValue=rows[0].subAccount;queryParams.showValue = rows[0].realname}"}'
                        // }
                        const extendLink = item.extendLink && JSON.parse(item.extendLink, function (k, v) {
                            if (v.indexOf && v.indexOf('function') > -1) { // 返回方法
                                return eval('(function (){ return ' + v + ' }) ()')
                            }
                            return v
                        }) || {}
                        return {
                            id: item.id,
                            fieldValue: item.fieldCode,
                            text: item.fieldName,
                            fieldType: item.fieldType,
                            dictCode: item.dictCode,
                            extend: extendLink
                            // extend: {
                            //     modalParams: {},
                            //     modalUrl: '/account/elsSubAccount/list',
                            //     modalColumns: [{field: 'elsAccount', title: '子账号', with: 150}, {field: 'realname', title: '姓名', with: 150} ]
                            // }
                        }
                    })
                }
            })
        }
    }
}
</script>

<style lang="less" scoped>
.search-extend {
    position: relative;
    background-color: #fff;
    padding-left: 14px;
    padding-right: 14px;
    padding-bottom: 14px;
    transition: all 0.5s ease-out;
    .search-condition-wrap {
        background-color: #fff;
        padding: 8px;
        margin-top: 6px;
        border: solid 1px #adcdff;
        .scheme-wrap {
            position: absolute;
            right: 24px;
            top: 24px;
            z-index: 10;
        }
        .arraw-box {
            position: absolute;
            right: 148px;
            top: -19px;
            z-index: 20;
            img {
                height: 13px;
                width: 26px;
            }
        }
        .query-params-box {
            max-height: 130px;
            overflow-y: auto;
            overflow-x: hidden;
        }
    }
    .search-opts {
        text-align: center;
         margin-left: 20px;
        .ant-btn {
            margin-right: 10px;
        }
    }
    .filter-select{
      width: 220px;
    }
    :deep(.j-super-query-history-card) {
        .ant-card-body,
        .ant-card-head-title {
            padding: 0;
        }
        .ant-card-head {
            padding: 4px 8px;
            min-height: initial;
        }
    }
    :deep(.j-super-query-history-empty) {
        .ant-empty-image {
            height: 80px;
            line-height: 80px;
            margin-bottom: 0;
        }
        img {
            width: 80px;
            height: 65px;
        }
        .ant-empty-description {
            color: #afafaf;
            margin: 8px 0;
        }
    }
}
.j-super-query-history-tree {
    min-width: 180px;
    z-index: 1;
    .j-history-tree-title {
        width: calc(100% - 24px);
        position: relative;
        display: inline-block;
        &-closer {
            color: #999999;
            position: absolute;
            top: 0;
            right: 0;
            width: 24px;
            height: 24px;
            text-align: center;
            opacity: 0;
            transition: opacity 0.3s, color 0.3s;
            &:hover {
                color: #a1c6ff;
            }
            &:active {
                color: #7a7171;
            }
        }
        &:hover {
            .j-history-tree-title-closer {
                opacity: 1;
            }
        }
    }
    :deep(.ant-tree-switcher) {
        display: none;
    }
    :deep(.ant-tree-node-content-wrapper) {
        width: 100%;
    }
}
.component-fade-enter-active, .component-fade-leave-active {
  transition: opacity .1s ease;
}
.component-fade-enter, .component-fade-leave-to
/* .component-fade-leave-active for below version 2.1.8 */ {
  opacity: 0;
}
.inline-btn{
  display: flex;
  align-items: center;
}
</style>
