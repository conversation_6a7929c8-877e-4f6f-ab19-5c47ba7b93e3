<template>
  <div class="render-html">
    <div class="content">
      <slot :openFunc="openSelectModal">
        <div
          class="open-modal"
          :disabled="config.disabled"
          @click="openSelectModal"
          :style="{color: defaultColor }">
          <span class="txt" >{{ $srmI18n(`${$getLangAccount()}#i18n_btn_CtmA_347e73a8`, '点击查看') }}</span>
          <a-icon type="eye" />
        </div>
      </slot>
    </div>
    <a-modal
    v-drag    
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_see`, '查看')"
      width="90%"
      :footer="null"
      class="render-html-modal">
      <div
        v-html="content"
        class="content"></div>
    </a-modal>
  </div>
</template>

<script>
import { DEFAULT_COLOR } from '@/store/mutation-types'

export default {
    name: 'RenderHtmlModal',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String,
            default: ''
        },
        config: {
            type: Object,
            default () {
                return {}
            }
        },
        content: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            visible: false
        }
    },
    computed: {
        // 获取项目的主题色
        defaultColor () {
            return this.$ls.get(DEFAULT_COLOR)
        }
    },
    methods: {
        //打开选择弹窗
        openSelectModal () {
            this.visible = true
        }
    }
}
</script>
<style lang="less" scoped>
.render-html{
     :deep(.open-modal){
        display: flex;
        justify-content: center;
        cursor: pointer;
        line-height: 100%;
        .txt{
            margin-right: 8px;
        }
    }
}
.render-html-modal{
        :deep(.ant-modal-body){
            height: 600px;
            overflow: scroll;
        }
}
</style>
