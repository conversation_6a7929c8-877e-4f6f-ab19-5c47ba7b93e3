<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <div
        ref="determineTheWinner"
        @reset="getData"
        @resetCurrentSubPackage="resetCurrentSubPackage"
        :is="componentsName"
        :resultData="resultData"
      />
    </a-spin>
  </div>
</template>
<script lang="jsx">
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import TotalDetermineTheWinner from './components/TotalDetermineTheWinner'
import SubDetermineTheWinner from './components/SubDetermineTheWinner'
export default {
    name: 'DetermineTheWinner',
    inject: [
        'subpackageId',
        'resetCurrentSubPackage'
    ],
    components: {
        TotalDetermineTheWinner,
        SubDetermineTheWinner
    },
    data () {
        return {
            componentsName: '',
            show: false,
            confirmLoading: false,
            resultData: {}
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
        }
    },
    methods: {
        getData () {
            let params = {
                subpackageId: this.subId,
                affirmType: '0'
            }
            this.confirmLoading = true
            getAction('/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInfoBySubpackage', params).then(res => {
                console.log(res)
                if (res.code == 200 && res.result) {
                    this.resultData = res.result || {}
                    console.log(this.resultData)
                    this.resultData.bidWinningAffirmPriceItemVoList.forEach(item2=>{
                        item2.affirm = parseInt(item2.affirm) || 0
                    })
                    this.componentsName = this.resultData.quoteType == '1' ? 'SubDetermineTheWinner' : 'TotalDetermineTheWinner'
                    this.$nextTick(() => {
                        console.log(this.$refs)
                        console.log(this.$refs.determineTheWinner)
                        this.$refs.determineTheWinner.init(this.resultData)
                    })
                }
                this.confirmLoading = false
            }, (err) => {
                this.confirmLoading = false
            })
        },
        resetCurrentSubPackage () {
            // this.$emit('resetCurrentSubPackage')
            this.resetCurrentSubPackage()
        }
    },
    mounted () {
        this.getData()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
</style>




