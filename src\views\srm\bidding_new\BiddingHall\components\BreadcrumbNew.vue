<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item
      v-for="(item, index) in breadList"
      :key="index"
    >
      <router-link
        v-if="item.name != name"
        :to="{ path: item.path }"
      >
        {{ handleTitle(item.meta) }}
      </router-link>
      <span v-else>{{ handleTitle(item.meta) }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script>
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    data () {
        return {
            name: '',
            breadList: []
        }
    },
    created () {
        this.getBreadcrumb()
    },
    props: {
        breadDefault: {
            type: Object,
            default: () => {
                return {
                    path: '/biddingHall/MarginManagerment/MarginManagermentHome',
                    name: 'MarginManagermentHome',
                    meta: {
                        title: srmI18n(`${getLangAccount()}#i18n_field_siHRv_a7253812`, '保证金管理'),
                        titleI18nKey: '',
                        keepAlive: false
                    }
                }
            }
        }
    },
    methods: {
        getBreadcrumb () {
            this.breadList = [this.breadDefault]

            this.name = this.$route.name
            console.log(this.$route.matched)
            this.$route.matched.forEach((item, index) => {
                this.breadList.splice((index * 2), 0, item)
            })
            console.log(this.breadList)
        },
        handleTitle (meta) {
            let t = meta.title
            if (meta.titleI18nKey) {
                t = srmI18n(`${getLangAccount()}#${meta.titleI18nKey}`, meta.title)
            }
            return t
        }
    },
    watch: {
        $route () {
            this.getBreadcrumb()
        }
    }
}
</script>

<style scoped>

</style>