<template>
  <span>
    <template v-if="field.type === 'input'">
      <input-com :field="field" />
    </template>
    <template v-if="field.type === 'inputNumber'">
      <input-number-com :field="field" />
    </template>
    <template v-if="field.type === 'radio'">
      <radio-com :field="field" />
    </template>
    <template v-if="field.type === 'checkbox'">
      <checkbox-com :field="field" />
    </template>
    <template v-if="field.type === 'select'">
      <select-com :field="field" />
    </template>
    <template v-if="field.type === 'score'">
      <score-com
        :field="field" />
    </template>
    <template v-if="field.type === 'file'">
      <upload-com
        :field="field"
        :answerId="answerId"
        :answerAllData="answerAllData"
        :isPre="isPre"
        :index="index"
        :isDownload="isDownload"
      />
    </template>
  </span>
</template>

<script>
import InputCom from './fromItem/InputCom'
import RadioCom from './fromItem/RadioCom'
import CheckboxCom from './fromItem/CheckboxCom'
import SelectCom from './fromItem/SelectCom'
import InputNumberCom from './fromItem/InputNumberCom'
import ScoreCom from './fromItem/scoreCom'
import UploadCom from './fromItem/UploadCom'
export default {
    name: 'FieldCom',
    components: {
        InputCom,
        RadioCom,
        CheckboxCom,
        SelectCom,
        InputNumberCom,
        ScoreCom,
        UploadCom
    },
    provide () {
        return {
            index: (this.index + 1)
        }
    },
    props: {
        field: {
            type: Object,
            required: true
        },
        index: {
            type: Number,
            required: true
        },
        answerId: {
            type: String,
            default: ''
        },
        answerAllData: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        },
        isPre: {
            type: Number,
            default: 0
        },
        isDownload: {
            type: Number,
            default: 0
        }
    },
    watch: {
        field: {
            handler: function (val, oldVal) {
                // console.log('watch', val, oldVal)
            },
            deep: true
        }
    },
    created () {
        this.options = this.field.options
    }
}
</script>

<style scoped>

</style>
