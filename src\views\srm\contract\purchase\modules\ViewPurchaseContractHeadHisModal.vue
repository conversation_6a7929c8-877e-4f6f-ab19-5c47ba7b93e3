<template>
  <div class="PurchaseEightDisciplinesHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="!showViewDiffPage && !showHisContractItemPage"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :current-edit-row="currentEditRow"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :collapseHeadCode="['baseForm','busRule','personFrom']"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag
        centered
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
        :width="360"
        v-model="templateVisible"
        @ok="selectedTemplateAfter">
        <template slot="footer">
          <a-button
            key="back"
            @click="handleTempCancel">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
          </a-button>
          <a-button
            key="submit"
            type="primary"
            :loading="submitLoading"
            @click="selectedTemplateAfter">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
          </a-button>
        </template>
        <m-select
          v-model="templateNumber"
          :options="templateOpts"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"/>
      </a-modal>
      <!-- <a-modal
          v-drag
              centered
              :width="960"
              :maskClosable="false"
              :visible="flowView"
              @ok="closeFlowView"
              @cancel="closeFlowView">
              <iframe
                style="width:100%;height:560px"
                title=""
                :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                frameborder="0"></iframe>
            </a-modal> -->
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
      <a-modal
        v-drag
        forceRender
        :visible="editRowModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
        :width="800"
        @ok="confirmEdit"
        @cancel="closeEditModal">
        <j-editor
          v-if="editRowModal"
          v-model="currentItemContent"></j-editor>
      </a-modal>
      <a-modal
        v-drag
        v-model="previewModal"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
        :footer="null"
        :width="1000">
        <div
          style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
          v-html="previewContent"></div>
      </a-modal>
      <view-item-diff-modal ref="viewDiffModal"/>
      <His-Contract-Item-Modal ref="hisContractItemModal"/>
    </a-spin>
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import {getAction, httpAction, postAction} from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import {axios} from '@/utils/request'

export default {
    name: 'PurchaseContractHeadModal',
    mixins: [businessUtilMixin],
    components: {
        flowViewModal,
        BusinessLayout,
        ViewItemDiffModal,
        HisContractItemModal,
        JEditor
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            isView: false,
            labelCol: {span: 4},
            wrapperCol: {span: 15},
            templateVisible: false,
            refresh: true,
            submitLoading: false,
            nextOpt: true,
            currentRow: {},
            templateNumber: undefined,
            templateOpts: [],
            businessType: 'order',
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            orderAllow: true,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            requestData: {
                detail: {
                    url: '/contract/purchaseContractHeadHis/queryById', args: (that) => {
                        return {id: that.currentEditRow.id}
                    }
                }
            },
            externalToolBar: {
                purchaseContractItemList: [],
                purchaseContractContentItemList: [],
                purchaseContractPromiseList: [],
                contractItemCustom1List: [],
                contractItemCustom2List: [],
                contractItemCustom3List: [],
                contractItemCustom4List: [],
                contractItemCustom5List: [],
                purchaseAttachmentList: []
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'contract#purchaseContractHead:getPreviewData',
                    click: this.previewPdf
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.showFlowConditionBtn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                    attrs: {
                        type: 'primary'
                    },
                    authorityCode: 'contract#purchaseContractHead:download',
                    click: this.downloadFile
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                detail: '/contract/purchaseContractHeadHis/queryById',

                download: '/contract/purchaseContractHeadHis/download'
            }
        }
    },

    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            debugger
            if (this.currentEditRow.contractType === '3') {
                return `${elsAccount}/purchase_contractSimple_${templateNumber}_${templateVersion}`
            }
            return `${elsAccount}/purchase_contract_${templateNumber}_${templateVersion}`
        }
    },
    mounted () {
        const that = this
        getAction('/contract/purchaseContractHeadHis/queryById', {id: this.currentEditRow.id}).then(res => {
            if (res.success) {
                if (res.result) {
                    debugger
                    that.currentEditRow.templateNumber = res.result.templateNumber
                    that.currentEditRow.templateVersion = res.result.templateVersion
                    that.currentEditRow.templateAccount = res.result.templateAccount
                    that.currentEditRow.contractType = res.result.contractType
                    that.isView = true
                } else {
                    this.$message.error('查询失败')
                }
            }
        })
    },
    methods: {
        confirmEdit () {
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                {
                                    key: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    click: this.downloadEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    click: this.preViewEvent
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLibrary`, '合同条款库'),
                        groupNameI18nKey: '',
                        groupCode: 'purchaseContractContentItemHisList',
                        groupType: 'item',
                        sortOrder: '4',
                        show: true,
                        extend: {
                            optColumnList: []
                        }
                    },
                    {
                        groupName: '关联订单',
                        groupNameI18nKey: 'i18n_field_RKIt_26f94cf4',
                        groupCode: 'orderItemList',
                        groupType: 'item',
                        sortOrder: '5',
                        show: true,
                        extend: {
                            optColumnList: []
                        }
                    }
                ],
                itemColumns: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderNumber`, '订单号'),
                        groupCode: 'orderItemList',
                        field: 'orderNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_orderItemNumber`, '订单行号'),
                        groupCode: 'orderItemList',
                        field: 'itemNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseType`, '采购类型'),
                        field: 'purchaseType_dictText',
                        groupCode: 'orderItemList',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ItczE_d79d4484`, '订单行状态'),
                        field: 'itemStatus_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_factory`, '工厂'),
                        groupCode: 'orderItemList',
                        field: 'factory_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageLocation`, '库存地点'),
                        groupCode: 'orderItemList',
                        field: 'storageLocation_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                        groupCode: 'orderItemList',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialDesc`, '物料描述'),
                        groupCode: 'orderItemList',
                        field: 'materialDesc',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'),
                        groupCode: 'orderItemList',
                        field: 'materialSpec',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroup`, '物料组'),
                        groupCode: 'orderItemList',
                        field: 'materialGroupCode',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialGroupName`, '物料组名称'),
                        groupCode: 'orderItemList',
                        field: 'materialGroupName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'),
                        groupCode: 'orderItemList',
                        field: 'purchaseCycle',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_requiredDeliveryDate`, '采购周期'),
                        groupCode: 'orderItemList',
                        field: 'requireDate',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unitQuantity`, '数量单位'),
                        groupCode: 'orderItemList',
                        field: 'quantityUnit',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_order_quantity`, '数量单位'),
                        groupCode: 'orderItemList',
                        field: 'quantity',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'),
                        groupCode: 'orderItemList',
                        field: 'price',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价'),
                        groupCode: 'orderItemList',
                        field: 'netPrice',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xfku_2818dc9f`, '含税总价'),
                        groupCode: 'orderItemList',
                        field: 'taxAmount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xxfku_73f95c2c`, '含税总价'),
                        groupCode: 'orderItemList',
                        field: 'netAmount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_wjneEy_ad073b7a`, '来源合同行号'),
                        groupCode: 'orderItemList',
                        field: 'sourceItemNumber',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        dictCode: '',
                        required: '0',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectNumber`, '项目编号'),
                        fieldLabelI18nKey: 'i18n_title_projectNumber',
                        field: 'itemId',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        dictCode: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                        fieldLabelI18nKey: 'i18n_title_projectName',
                        field: 'itemName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                        fieldLabelI18nKey: 'i18n_title_itemType',
                        field: 'itemType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemVersion`, '项目版本'),
                        fieldLabelI18nKey: 'i18n_title_projectVersion',
                        field: 'itemVersion',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
                        fieldLabelI18nKey: 'i18n_title_changeIdentification',
                        field: 'changeFlag',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        cellRender: {
                            name: '$switch',
                            type: 'visible',
                            props: {closeValue: '0', openValue: '1', disabled: true}
                        },
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sourceType`, '来源类型'),
                        fieldLabelI18nKey: 'i18n_title_sourceType',
                        field: 'sourceType_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'purchaseContractContentItemHisList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: 'i18n_title_operation',
                        field: 'sourceType_dictText',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        align: 'left',
                        slots: {
                            default: ({row}) => {
                                let resultArray = []
                                resultArray.push(<a
                                    title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                    onClick={() => this.viewDetail(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}</a>)
                                if (row.changeFlag == '1') {
                                    resultArray.push(<a title="比对" style="margin-left:8px"
                                        onClick={() => this.viewDiff(row)}>比对</a>)
                                }
                                return resultArray
                            }
                        }
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: 120
                    },
                    {
                        groupCode: 'purchaseAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {default: 'grid_opration'}
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
            // this.externalToolBar['purchaseAttachmentList'][0].args.headId = resultData.id || ''
            // let itemInfo = pageConfig.groups
            //     .map(n => ({ label: n.groupName, value: n.groupCode }))
            // this.externalToolBar['purchaseAttachmentList'][0].args.itemInfo = itemInfo
            if (resultData.showCustom1 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom1List', false)
            }
            if (resultData.showCustom2 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom2List', false)
            }
            if (resultData.showCustom3 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom3List', false)
            }
            if (resultData.showCustom4 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom4List', false)
            }
            if (resultData.showCustom5 == '0') {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', true)
            } else {
                this.hideSingleGroup(this.businessRefName, 'contractItemCustom5List', false)
            }
            if (resultData.showItem == '1') {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractItemList', true)
            }
            if ((resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6') && (resultData.promiseType == 'promiseSale' || resultData.promiseType == 'promisePurchase')) {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'purchaseContractPromiseList', true)
            }

            if (resultData.promiseType == 'order' && (resultData.contractStatus == '3' || resultData.contractStatus == '8' || resultData.contractStatus == '6')) {
                this.hideSingleGroup(this.businessRefName, 'orderItemList', false)
            } else {
                this.hideSingleGroup(this.businessRefName, 'orderItemList', true)
            }
        },
        showCreateOrderBtn ({pageData, pageConfig}) {
            //①	如果合同启用电子签章，则“合同状态”必须为“已归档”，才可点击。
            //②	如果合同不启用电子签章，则“合同状态”必须为“已归档”或者“供应商已确认
            let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
            let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
            if ((signFlag || notSignFlag) && pageData.promiseType == 'order') {
                return true
            } else {
                return false
            }

        },
        showCreatePromiseBtn ({pageData, pageConfig}) {
            //①	如果合同启用电子签章，则“合同状态”必须为“已归档”，才可点击。
            //②	如果合同不启用电子签章，则“合同状态”必须为“已归档”或者“供应商已确认
            let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
            let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
            if ((signFlag || notSignFlag) && pageData.promiseType == 'promisePurchase') {
                return true
            } else {
                return false
            }
        },
        showCreatePromiseItemBtn ({pageData, pageConfig}) {
            let signFlag = pageData.sign == '1' && pageData.contractStatus == '6'
            let notSignFlag = pageData.sign !== '1' && (pageData.contractStatus == '6' || pageData.contractStatus == '3')
            if ((signFlag || notSignFlag) && pageData.promiseType == 'promisePurchase') {
                return true
            } else {
                return false
            }
        },
        showCncelConditionBtn ({pageData}) {
            console.log(pageData)
            if (pageData.auditStatus == '1') {
                return true
            } else {
                return false
            }
        },
        createOrder () {
            this.nextOpt = true
            this.serachTemplate('order')
        },
        createPromise () {
            this.nextOpt = true
            this.serachTemplate('contractPromise')
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        preview () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemHisList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            getAction('/contract/purchaseContractHeadHis/getPreviewData', {id: this.currentEditRow.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },

        previewPdf () {
            debugger
            let contentGrid = this.getItemGridRef('purchaseContractContentItemHisList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: '/contract/purchaseContractHeadHis/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    debugger
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.auditPostData(that.url.cancelAudit)

                }
            })/*.finally(() => {
                that.init()
            })*/
        },
        serachTemplate (businessType) {
            this.currentRow = {}
            if (businessType == 'order') {
                let selectedRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                if (selectedRows.length <= 0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                    return
                } else {
                    selectedRows.forEach((item, i) => {
                        if (item.sourceType == 'order') {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                            this.nextOpt = false
                            return
                        }
                    })
                    if (this.nextOpt) {
                        this.businessType = businessType
                        this.openModal()
                    }
                }
            } else {
                let parm = this.getAllData()
                let selectedItemRows = []
                if (parm.showCustom1 == '1') {
                    selectedItemRows = this.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                }
                if (parm.showItem == '1' && selectedItemRows.length == 0) {
                    selectedItemRows = this.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                }

                if (selectedItemRows.length <= 0) {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
                    return
                } else {
                    /*selectedRows.forEach((item, i) => {
                        if (item.sourceType == 'order') {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPage`, '选中的第') + (i + 1) + '行来源为订单行')
                            this.nextOpt = false
                            return
                        }
                    })*/
                    if (this.nextOpt) {
                        this.businessType = businessType
                        this.openModal()
                    }
                }
            }

        },
        queryTemplateList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: this.businessType}
            return getAction('/template/templateHead/getListByType', params)
        },
        openModal () {
            this.queryTemplateList(this.$ls.get('Login_elsAccount')).then(res => {
                if (res.success) {
                    if (res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                value: item.templateNumber,
                                title: item.templateName,
                                version: item.templateVersion,
                                account: item.elsAccount
                            }
                        })
                        this.templateOpts = options
                        // 只有单个模板直接新建
                        if (this.templateOpts && this.templateOpts.length === 1) {
                            this.templateNumber = this.templateOpts[0].value
                            this.selectedTemplateAfter()
                        } else {
                            // 有多个模板先选择在新建
                            this.templateVisible = true
                        }
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        handleTempCancel () {
            this.templateVisible = false
        },
        selectedTemplateAfter () {
            if (this.templateNumber) {
                const that = this
                let parm = that.getAllData()
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    templateName: template[0].title,
                    templateVersion: template[0].version,
                    templateAccount: template[0].account,
                    purchaseContractItemList: [],
                    contractItemCustom1List: []
                }
                if (this.businessType == 'order') {
                    this.url.temp = this.url.createOrderByItems
                }
                if (this.businessType == 'contractPromise') {
                    this.url.temp = this.url.createPromiseByItems
                }
                if (parm.showItem == '1') {
                    params.purchaseContractItemList = that.getItemGridRef('purchaseContractItemList').getCheckboxRecords()
                }
                if (parm.showCustom1 == '1') {
                    params.contractItemCustom1List = that.getItemGridRef('contractItemCustom1List').getCheckboxRecords()
                }
                that.templateVisible = false
                that.submitLoading = false
                if (this.url.temp == '') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseMaintainAddressFirst`, '请先维护地址'))
                    return
                }
                that.postUpdateData(this.url.temp, params)
            }
        },
        postUpdateData (url, row) {
            this.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.init()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        paramIntegrate () {
            let pageData = this.getAllData() || {}
            let {busRule = {}, personFrom = {}} = pageData || {}

            // pageData = { ...pageData, ...busRule, ...personFrom}
            delete pageData.busRule
            delete pageData.personFrom

            pageData = Object.assign({}, pageData, busRule, personFrom)
            return pageData
        },
        auditPostData (invokeUrl) {
            this.confirmLoading = true
            let formData = this.paramIntegrate()
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contract'
            param['auditSubject'] = '合同编号：' + formData.contractNumber
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        downloadFile () {
            debugger
            let params = {id: this.paramIntegrate().id}
            this.confirmLoading = true
            axios({
                url: this.url.download,
                responseType: 'blob',
                params: params
            }).then(res => {
                this.confirmLoading = false
                let fieldName = this.currentEditRow.contractNumber + '_' + this.currentEditRow.contractVersion + '.pdf'
                //console.log(res)
                const blob = new Blob([res])
                const blobUrl = window.URL.createObjectURL(blob)
                const a = document.createElement('a')
                a.style.display = 'none'
                a.href = blobUrl
                a.download = fieldName
                a.click()
            })
        },
        closeEditModal () {
            this.editRowModal = false
        },
        showFlow () {
            let params = this.paramIntegrate()
            this.flowId = params.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        handleCancel () {
            this.visible = false
        },
        downloadEvent (Vue, row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction(this.url.downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    }
}
</script>