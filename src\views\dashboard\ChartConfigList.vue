<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <chartConfig-modal
      v-if="showEditPage"
      ref="modalForm"
      :current-edit-row="currentEditRow"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import ChartConfigModal from './modules/ChartConfigModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        ChartConfigModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeName`, '请输入编码或名称')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, primary: true},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDel},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/report/dashboard/chartConfig/list',
                delete: '/report/dashboard/chartConfig/delete',
                deleteBatch: '/report/dashboard/chartConfig/deleteBatch',
                exportXlsUrl: 'dashboard/chartConfig/exportXls',
                importExcelUrl: 'dashboard/chartConfig/importExcel',
                columns: 'chartConfigList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(srmI18n(`${getLangAccount()}#i18n_title_chartConfigure`, '图表配置'))
        }
    }
}
</script>