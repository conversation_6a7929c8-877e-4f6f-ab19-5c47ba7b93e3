<template>
  <!-- 2022-03-08 新版本采购竞价大厅 英式《QQT SRM V5-PRD-竞价管理优化方案-v2.1-********》 -->
  <div class="buyBid">
    <a-spin :spinning="spinning">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
            <!-- <breadcrumb></breadcrumb> -->
          </div>
          <div class="menu">
            <a-button
              type="primary"
              v-if="showStartBid"
              :disabled="!activeStartBid"
              @click="startOrEndBid(true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_openBiding`, '开启竞价') }}</a-button>
            <a-button
              type="primary"
              v-if="showEndBid"
              :disabled="startEndDisabled"
              @click="startOrEndBid(false)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_overBiding`, '结束竞价') }}</a-button>
            <a-button
              type="primary"
              @click="refresh(true)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_refresh`, '刷新') }}</a-button>
            <a-button
              type="primary"
              @click="windowOpenClose">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
          </div>
        </div>
        refresh
        <div class="content">
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_field_OuyO_391539d8`, '竞价阶段') }}</b>
                  <b class="value">{{ getCurrentTitle }}</b>
                </span>
                <span class="inline">
                  <b class="value">{{ getCurrentMaterialStatus }}</b>
                </span>
                <span
                  v-if="form.ebiddingStatus === '3' || form.ebiddingStatus === '1' || form.ebiddingStatus === '4'"
                  class="inline"
                  style="float: right">
                  <b class="label">{{ countdownText }}</b>
                  <b class="value red">
                    <span
                      class="icon"
                      v-if="deadline">
                      <a-icon
                        type="clock-circle"
                        theme="outlined"
                        :style="{ fontSize: '22px', color: '#f41616', marginRight: '8px' }" />
                    </span>
                    <countdown
                      :time="deadline"
                      v-if="deadline"
                      :style="valueStyle"
                      @end="handleFinish">
                      <template slot-scope="props">{{ props.days ? `${props.days} ${$srmI18n(`${$getLangAccount()}#i18n_dict_S_5929`, '天')} ` : '' }}{{ props.hours }} : {{ props.minutes }} : {{ props.seconds }}</template>
                    </countdown>
                  </b>
                </span>
              </div>
            </div>
          </div>
          <div class="gutter">
            <div class="item price">
              <div class="info">
                <span class="inline">
                  <b class="label">{{ $srmI18n(`${$getLangAccount()}#i18n_title_bidDocumentNo`, '竞价单号') }}</b>
                  <b class="value m-l-24">{{ form.ebiddingNumber }} </b>
                  <b
                    class="value inline-flot"
                    @click="changeToggle">{{ getToggleStatu }} <a-icon :type="resizeHeight == 0 ? 'down' : 'up'" /></b>
                </span>
                <a-descriptions
                  bordered
                  v-if="resizeHeight == 0"
                  class="m-t-12"
                  id="description"
                  size="small">
                  <!-- :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }" -->
                  <a-descriptions-item
                    v-for="item in baseInfo"
                    :key="item.key"
                    :label="item.label"
                    :span="item.span || 1">
                    {{ form[item.key] }}
                  </a-descriptions-item>
                </a-descriptions>
              </div>
            </div>
          </div>
          <div class="gutter">
            <multipane
              class="custom-resizer"
              layout="vertical"
              @paneResizeStop="reSizeChart">
              <div class="item material">
                <div class="info">
                  <a-card
                    :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')"
                    :bordered="false"
                    :headStyle="headStyle"
                    :bodyStyle="bodyStyle_2">
                    <div class="cardContent">
                      <div class="table">
                        <vxe-grid
                          ref="materialGrid"
                          :height="365 + resizeHeight"
                          show-overflow
                          v-bind="materialGridOptions"
                          :row-class-name="handleRowClass"
                          @cell-click="cellClickEvent">
                          <template slot="empty">
                            <a-empty />
                          </template>
                        </vxe-grid>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
              <multipane-resizer></multipane-resizer>
              <div class="item material marerial-right">
                <div class="info">
                  <a-tabs
                    type="card"
                    v-model="activeKey"
                    default-active-key="1">
                    <a-tab-pane
                      key="1"
                      :tab="$srmI18n(`${$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息')">
                      <vxe-grid
                        ref="rankGrid"
                        :height="350 + resizeHeight"
                        v-bind="rankGridOptions">
                        <template slot="empty">
                          <a-empty />
                        </template>
                        <template #online="{ row }">
                          <span>{{ suppliers.includes(row.toElsAccount) ? '在线' : '离线' }}</span>
                        </template>
                      </vxe-grid>
                    </a-tab-pane>
                    <a-tab-pane
                      key="2"
                      :tab="$srmI18n(`${$getLangAccount()}#i18n_title_trendChartQuotation`, '报价趋势图')">
                      <div class="cardContent">
                        <a-select
                          v-model="xasDeaultVal"
                          style="width: 120px"
                          @change="handleXasChange">
                          <a-select-option value="length">
                            {{ $srmI18n(`${$getLangAccount()}#i18n_field_quoteCount`, '报价次数') }}
                          </a-select-option>
                          <a-select-option value="quoteTime">
                            {{ $srmI18n(`${$getLangAccount()}#i18n_field_suKI_2e0cbb30`, '报价时间') }}
                          </a-select-option>
                        </a-select>

                        <div class="chartInfo">
                          <div
                            class="m-t-12"
                            :class="['echart', isShowEchart ? '' : 'unShow']">
                            <line-chart
                              v-if="isShowEchart"
                              ref="echart"
                              :height="320+resizeHeight+'px'"
                              :chartData="chartData"> </line-chart>
                            <div
                              v-if="!isShowEchart"
                              class="red">{{ $srmI18n(`${$getLangAccount()}#i18n_title_doesDownNotAllow`, '当前配置不允许查看供方报价信息') }}</div>
                          </div>
                        </div>
                      </div>
                    </a-tab-pane>
                  </a-tabs>
                </div>
              </div>
              <div class="item"></div>
            </multipane>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script lang='jsx'>
const resizeChartMethod = '$__resizeMethod'
import countdown from '@/components/countdown/index.js'

import { groupBy, debounce } from 'lodash'
import { apiQueryBidLobbyDetail, apiQueryBidLobbyQuote, apiHisQueryBidLobbyDetail, apiManualStartBid, apiManualEndBid, apiQueryOnlineAccount } from '@/api/apiBidding.js'
import { currency } from '@/filters'
import { materialGridOptions, chartGridOptions, rankGridOptions } from '../gridConfig/purchase/index'
import { getAction } from '@/api/manage'
import LineChart from '../components/LineChart'
import { USER_INFO } from '@/store/mutation-types'
import { mapActions, mapGetters } from 'vuex'

import { closeWS, createSocket } from '../websocket.js'
import { nominalEdgePullWhiteBlack } from '@/utils/util.js'
import { Multipane, MultipaneResizer } from 'vue-multipane'
const HEART_BEAT_CONFIG = {
    time: 3 * 1000, // time：心跳时间间隔
    timeout: 3 * 1000, // timeout：心跳超时间隔
    reconnect: 10 * 1000 // reconnect：断线重连时
}

const INTERVAL = 1000 * 30 // 30秒刷新页面

export default {
    inject: ['closeCurrent'],
    components: {
        LineChart,
        countdown,
        Multipane,
        MultipaneResizer
        // Breadcrumb
    },
    filters: {
        currency
    },
    data () {
        return {
            xasDeaultVal: 'length',
            timer: null,
            activeKey: '1',
            materialGridOptions,
            chartGridOptions,
            rankGridOptions,
            current: -1,
            currentItemNumber: -1, // 点击物料行当前行号
            currentMaterialNumName: null, // 点击物料行当前行物料名称
            currentMaterialStatus: null, // 点击物料行当前行竞价状态
            deadline: 0,
            countdownText: '',
            gt1900: false,
            spinning: false,
            delayTime: 300,
            headStyle: {
                borderBottom: 'none',
                padding: '0 6px',
                minHeight: '34px',
                lineHeight: '34px',
                height: '40px'
            },
            valueStyle: {
                fontSize: '24px',
                color: '#f41616'
            },
            bodyStyle: {
                padding: '0 24px 24px'
            },
            bodyStyle_2: {
                padding: '0 6px 24px 6px'
            },
            progress: {
                percent: 78,
                status: 'active',
                strokeColor: {
                    from: '#4892ff',
                    to: '#596fe1'
                }
            },
            // endTime: 0,
            minPrice: 1200,
            form: {
                ebiddingNumber: '', // 竞价单号
                currentItemNumber: '',
                ebiddingType: '', // 竞价方式
                changeRange: '', // 价格调整幅度
                rangeUnit: '', // 幅度单位
                beginTime: '', // 竞价开始时间
                endTime: '', // 竞价结束时间
                keepMinute: '', // 每轮持续时间
                intervalMinute: '', // 每轮间隔时间
                currentRound: '',
                ebiddingWay: '',
                id: '',
                itemNumber: ''
            },
            ebiddingDetailData: {},
            chartData: {},
            // websocket: null,
            socket: null,
            lockReconnect: false, //是否真正建立连接
            timeout: 30 * 1000, //30秒一次心跳
            timeoutObj: null, //心跳心跳倒计时
            serverTimeoutObj: null, //心跳倒计时
            timeoutnum: null, //断开 重连倒计时
            visible: false,
            heartBeatConfig: HEART_BEAT_CONFIG,
            wsOnlineUrl: '',
            httpOnlineSuppliers: '',
            suppliers: '',
            resizeHeight: 0,
            chartXAxisData: [],
            currentEndMaterialItemNumber: 1
        }
    },
    computed: {
        ...mapGetters(['getOnlineSuppliers', 'getOnlineID', 'getPageRefreshTime']),

        showStartBid () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:manualStartBid')
        },
        activeStartBid () {
            let flag = false
            // "1", "待应标", "3", "待竞价"
            let status = this.form.ebiddingStatus
            console.log('this.form.ebiddingStatus', this.form.ebiddingStatus)
            if (status === '1') {
                flag = true
            }
            if (status === '3' && !this.isIntervalCountdown) {
                flag = true
            }
            return flag
        },
        // 竞价结束、已授标、已流标、已建新轮次、已作废、已悔标  时，竞价大厅中，开始竞价、结束竞价按钮置灰不可操作
        startEndDisabled () {
            return ['5', '6', '7', '10', '11', '12'].includes(this.form.ebiddingStatus)
        },
        showEndBid () {
            return this.$hasOptAuth('ebidding#purchaseEbiddingHead:manualEndBid')
        },
        getCurrentTitle () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) {
                // 逐条报价、竞价中
                return this.currentMaterialNumName ? `【 ${this.currentMaterialNumName} 】` : ''
            } else {
                return ''
            }
        },
        getToggleStatu () {
            let lable = this.resizeHeight == 0 ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_putAway`, '收起') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expand`, '展开')
            return lable
        },
        getCurrentMaterialStatus () {
            if (this.form.ebiddingWay === '1' && ['3', '4'].includes(this.form.ebiddingStatus)) {
                // 逐条报价、竞价中
                return this.currentMaterialStatus ? `${this.currentMaterialStatus}` : ''
            } else {
                return this.form.ebiddingStatus_dictText
            }
        },
        baseInfo () {
            const { ebiddingWay = '', rangeUnit_dictText} = this.form || {}
            if (ebiddingWay === '0') {
                return [
                    // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                    { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_endTime`, '结束时间'), key: 'endTime', show: true },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWKIPzs_1ed46e2b`, '结束时间前(分钟)'), key: 'beforeEndMinute', show: true }, // 分钟内，有报价
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OAKIzs_ca46caaf`, '延期时间(分钟)'), key: 'delayMinute', show: true }, // 分钟
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageStartingUnitPrice`, '打包起拍价'), key: 'startTotalAmount' } // 打包起拍价 - 打包竞价显示
                ]
            }
            return [
                // { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingNumber`, '竞价单号'), key: 'ebiddingNumber', show: true, span: '4' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'), key: 'ebiddingStatus_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ebiddingType`, '竞价类型'), key: 'ebiddingType_dictText', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quotePriceWay`, '报价方式'), key: 'ebiddingWay_dictText', show: true },
                { label: (this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingRange`, '竞价幅度'))+`(${rangeUnit_dictText})`, key: 'changeRange', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AOKI_2787c0d7`, '启动时间'), key: 'startTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_endTime`, '结束时间'), key: 'endTime', show: true },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIzs_63db992`, '持续时间(分钟)'), key: 'keepMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ImKIzs_cd425da6`, '间隔时间(分钟)'), key: 'intervalMinute' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yWKIPzs_1ed46e2b`, '结束时间前(分钟)'), key: 'beforeEndMinute', show: true }, // 分钟内，有报价
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OAKIzs_ca46caaf`, '延期时间(分钟)'), key: 'delayMinute', show: true } // 分钟
            ]
        },
        isShowEchart () {
            // 需方可见, 0: 排名, 1: 价格, 2: 供方名称
            let { purchaseShow = '', ebiddingStatus } = this.form
            // serviceTime, endTime, 
            // endTime = endTime ? new Date(endTime).getTime() : Date.now()
            // const now = serviceTime || Date.now()
            // if (now > endTime) {
            //     return true
            // }
            // 竞价结束、已授标、已流标、已悔标、作废 可查看
            if (['5', '6', '7', '12', '11'].includes(ebiddingStatus)) {
                return true
            }
            let arr = purchaseShow.split(',')
            return arr.includes('0') && arr.includes('1') && arr.includes('2')
        },
        // 反否反向竞价
        isReverse () {
            const { ebiddingType = '' } = this.form || {}
            return ebiddingType === '1'
        },
        // 是否打包状态
        isPack () {
            const { ebiddingWay = '' } = this.form || {}
            return ebiddingWay === '0'
        }
    },
    methods: {
        ...mapActions(['SetOnlineWS', 'CloseOnlineWS']),
        //拖动的时候重置图表
        //关闭页面
        windowOpenClose () {
            window.close()
        },
        reSizeChart: debounce(function () {
            this.$refs.echart.resizeChart()
        }, 500),
 
        //收取和展开逻辑
        changeToggle () {
            if (document.getElementById('description')) {
                let domHeight = document.getElementById('description').clientHeight
                this.resizeHeight = domHeight
            } else {
                this.resizeHeight = 0
            }
            
            this.$refs.echart && this.$refs.echart.resizeChart()
        },
        setApiQueryOnlineAccount () {
            this.setOnlineAccountWesocketPush && clearInterval(this.setOnlineAccountWesocketPush)
            this.setOnlineAccountWesocketPush = setInterval(() => {
                this.setApiQueryOnlineAccount()
                apiQueryOnlineAccount({ headId: this.$route.query.id }).then((res) => {
                    // console.log('res', res)
                    this.$nextTick(() => {
                        this.httpOnlineSuppliers = res.message
                        this.suppliers = res.message
                    })
                })
            }, 3000)
        },
        sendRefresh (time = INTERVAL) {
            console.log('this.startEndDisabled :>> ', this.startEndDisabled)
            // if (this.startEndDisabled) {
            //     this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            //     return
            // }
            this.setIntervalWesocketPush && clearInterval(this.setIntervalWesocketPush)
            this.setIntervalWesocketPush = setInterval(() => {
                this.sendRefresh()
                // apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
                //     console.log('res', res)
                //     this.$nextTick(() => {
                //         this.httpOnlineSuppliers = res.message
                //         this.suppliers = res.message
                //         if (!this.startEndDisabled) { // 仅竞价中可刷新
                //             this.refresh(false)
                //         }
                //     })
                // })
                if (!this.startEndDisabled) {
                    // 仅竞价中可刷新
                    this.refresh(false)
                }
            }, time)
            // clearTimeout(this.timer)
            // this.timer = setTimeout(() => {
            //     this.sendRefresh()
            //     apiQueryOnlineAccount({ headId: this.$route.query.id }).then(res => {
            //         console.log('res', res)
            //         this.$nextTick(() => {
            //             this.httpOnlineSuppliers = res.message
            //             this.suppliers = res.message
            //             this.refresh()
            //         })
            //     })
            // }, time)
        },
        getSocketData (newVal) {
            console.log('getSocketData newVal.detail', newVal.detail.data)
            const message = newVal.detail.data
            // 报价更新
            if (message === this.form.id) {
                this.refresh(false)
                return
            }
        },
        getOnlineWebsocketUrl () {
            let { serivceUrl = '', elsAccount = '' } = this.$ls.get(USER_INFO) || {}
            let url = serivceUrl.replace('https://', 'wss://').replace('http://', 'ws://')
            this.wsOnlineUrl = `${url}/els/websocket/online/${this.form.id}/${elsAccount}`
            createSocket(this.wsOnlineUrl)
        },
        cellClickEvent ({ row }) {
            if (this.isPack) return
            const { itemNumber = '', materialNumber, materialName, itemStatus_dictText } = row || {}
            // if (itemNumber === this.current) return
            this.$nextTick(() => {
                this.xasDeaultVal = 'length'
            })
            this.current = itemNumber
            this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            this.getItemData()
        },
        setCurrentMaterialInfo (materialNumber = null, materialName = null, itemStatus_dictText = null) {
            this.currentMaterialNumName = `${materialNumber} - ${materialName}`
            this.currentMaterialStatus = itemStatus_dictText
        },
        goBack () {
            closeWS()
            // this.CloseOnlineWS()
            this.closeCurrent()
            this.timer && clearTimeout(this.timer)
        },
        startOrEndBid (flag = false) {
            // 参与数量 供应商信息
            const { participateQuantity, saleHeadList = [] } = this.ebiddingDetailData
            // 待竞价的供应商
            let saleEbiddingNumbers = saleHeadList.filter((item) => item.ebiddingStatus == '3')
            if (flag) {
                if (saleEbiddingNumbers.length == 0) return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__BjqsUOujRdX_dcf49d55`, '没有可参与竞价的供应商'))
            }
            const title = flag ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_openBiding`, '开启竞价') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overBiding`, '结束竞价')

            const content = flag
                ? `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eRsUWRL_7e095f3e`, '最少参与数量为')}${participateQuantity}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WqsUOujRdXWRL_f5dc0f6e`, '现可参与竞价的供应商数量为')}${saleEbiddingNumbers.length}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VRLKQvAOuW_98f84593`, '请确认是否开启竞价？')}`
                : this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLKQyWOu_50fee892`, '确认是否结束竞价')

            const callback = () => {
                const params = {
                    id: this.form.id
                }
                const request = flag ? apiManualStartBid : apiManualEndBid
                request(params).then((res) => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    this.getData()
                })
            }
            this.$confirm({
                title,
                content,
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        refresh: debounce(function (spinning = true) {
            this.sendRefresh()
            this.setApiQueryOnlineAccount()
            this.getData(spinning)
            this.$nextTick(() => {
                this.xasDeaultVal = 'length'
            })
        }, 500),
        handleFinish () {
            this.refresh()
        },
        getData (spinning = true) {
            console.log('spinning', spinning)
            this.getEbiddingDetailData().then((res) => {
                this.currentItemNumber = res.currentItemNumber || '1'
                // if (this.currentMaterialItemNumber === -1) {
                this.current = this.currentItemNumber
                this.form.currentItemNumber = this.currentItemNumber
                if (res.ebiddingStatus === '5'&&!this.endEbidd) {
                    this.endEbidd=true
                    this.$notification.open({
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                        description: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')}`,
                        icon: <a-icon type="sound" style="color: #108ee9" />,
                        duration: 10
                    })
                    this.getItemData()
                } else {
                    if (this.currentEndMaterialItemNumber != this.currentItemNumber) {
                        this.currentSupplierAccount = -1
                        this.currentSupplierStatus = -1
                        this.currentSupplierItemId = ''
                        if (this.currentMaterialNumName && this.currentItemNumber != '1') {
                            let materNumber = this.materialGridOptions.data.find((val) => this.currentEndMaterialItemNumber == val.itemNumber)
                            this.$notification.open({
                                message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示'),
                                description: `${materNumber.materialNumber}-${materNumber.materialDesc}${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_biddingOver`, '竞价结束')},切换到下一个物料`,
                                icon: <a-icon type="sound" style="color: #108ee9" />,
                                duration: 10
                            })
                        }
                        this.currentEndMaterialItemNumber = this.currentItemNumber
                    }
                }
                if (spinning) this.spinning = true
                const handlePromise = (list = []) =>
                    list.map((promise) =>
                        promise.then(
                            (res) => ({ status: res.success ? 'success' : 'error', res }),
                            (err) => ({ status: 'error', err })
                        )
                    )
                const { id } = this.form
                const params = {
                    id,
                    itemNumber: this.current !== -1 ? this.current : this.currentItemNumber
                }
                const promiseList = [apiQueryBidLobbyDetail(params)]
                Promise.all(handlePromise(promiseList)).then((res) => {
                    const [infoRes] = res || []
                    if (infoRes && infoRes.status === 'success') {
                        this.fixInfoData(infoRes.res)
                    }
                    this.getItemData()
                })
            })
        },
        // 查竞价详情接口 根据开启方式以及待竞价供应商数量判断是否自动开启竞价
        getEbiddingDetailData () {
            return new Promise((resolve, reject) => {
                getAction('/ebidding/purchaseEbiddingHead/queryById', { id: this.$route.query.id }).then((res) => {
                    if (res.success) {
                        this.ebiddingDetailData = res.result
                        // 供应商信息 启动方式
                        const { saleHeadList = [], startWay = '0' } = this.ebiddingDetailData
                        // 自动开启竞价
                        if (startWay == '1') {
                            // 过滤出待应标 || 不参与 的数据
                            let result = saleHeadList.filter((item) => item.ebiddingStatus == 1 || item.ebiddingStatus == 2)
                            // 与参与供应商数组长度相同 即没有供应商为待竞价状态
                            if (result.length == saleHeadList.length) {
                                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n__BjqsUOujRdXWVRLdBRdX_8d298784`, '没有可参与竞价的供应商，请确认应标供应商'))
                            }
                        }
                    }
                    resolve(res.result)
                })
            })
        },
        getItemData () {
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({ status: res.success ? 'success' : 'error', res }),
                        (err) => ({ status: 'error', err })
                    )
                )
            const { id } = this.form
            const params = {
                id,
                itemNumber: this.current !== -1 ? this.current : this.currentItemNumber
            }
            const promiseList = [apiQueryBidLobbyQuote(params), apiHisQueryBidLobbyDetail(params)]
            Promise.all(handlePromise(promiseList)).then((res) => {
                this.spinning = false
                const [itemRes, hisRes] = res || []
                if (itemRes && itemRes.status === 'success') {
                    this.fixItemData(itemRes.res)
                }
                if (hisRes && hisRes.status === 'success') {
                    this.fixHisPriceData(hisRes.res)
                }
            })
        },
        // 倒计时计算
        checkTime () {
            let { beginTime, beginTime_DateMaps, endTime, endTime_DateMaps, ebiddingStatus } = this.form
            const { serviceTime } = this.form
            const now = serviceTime || Date.now()
            beginTime = beginTime ? beginTime_DateMaps : Date.now()
            endTime = endTime ? endTime_DateMaps : Date.now()
            if (ebiddingStatus === '3' || ebiddingStatus === '1') {
                //待竞价状态
                if (now < beginTime) {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuvKutK_bc29df23`, '竞价开始倒计时')
                    this.deadline = beginTime - now
                } else {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EolOvAOu_2f8860e1`, '等待手动开启竞价')
                    this.deadline = 0
                }
            } else if (ebiddingStatus === '4') {
                //竞价中状态
                if (now < endTime) {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_uTKIutK_b713bdfd`, '持续时间倒计时')
                    this.deadline = endTime - now
                } else {
                    this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EolOyWOu_3e47d35e`, '等待手动结束竞价')
                    this.deadline = 0
                }
            } else {
                this.countdownText = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SutK_2f8d3027`, '无倒计时')
                this.deadline = 0
            }
        },
        fixInfoData (res) {
            const { purchaseEbiddingItemList = [], ebiddingWay, ...others } = res.result || {}
            let { currentItemNumber } = res.result || {}

            this.materialGridOptions.data = purchaseEbiddingItemList
            this.currentItemNumber = currentItemNumber || '1'
            // 初始时，高亮竞价物料表格第一行
            if (this.current === -1 || !this.current) this.current = this.currentItemNumber
            const itemNumber = this.current === -1 ? this.currentItemNumber : this.current
            const currentMaterial = purchaseEbiddingItemList.find((i) => i.itemNumber === itemNumber)
            if (currentMaterial) {
                const { materialNumber, materialName, itemStatus_dictText } = currentMaterial
                this.setCurrentMaterialInfo(materialNumber, materialName, itemStatus_dictText)
            }
            this.form = {
                ebiddingWay,
                currentItemNumber: this.currentItemNumber,
                ...others,
                serviceTime: res.timestamp
            }
            this.sendRefresh()
            // 逐条，获取起拍价格
            if (ebiddingWay === '1') {
                let i = this.currentItemNumber - 1
                this.form.startPrice = purchaseEbiddingItemList[i].startPrice
            }
            this.checkTime()
        },
        fixItemData ({ result }) {
            const { purchaseEbiddingItemList = [] } = result || {}
            this.rankGridOptions.data = purchaseEbiddingItemList
        },
        fixHisPriceData ({ result = [] }) {
            this.chartGridOptions.data = result
            this.setRankTableColumn()
            this.resetChart()
            this.getEchartData(result)
        },
        setRankTableColumn () {
            this.rankGridOptions.columns.forEach((item) => {
                if (item.field === 'price') {
                    item.visible = !this.isPack
                }
            })
        },
        getEchartData (arr) {
            let legend = {
                data: []
            }
            let xAxis = {
                type: 'category',
                boundaryGap: false,
                data: []
            }
            
            let result = arr ? arr.sort((a, b) => {
                const timeA = new Date(a.quoteTime).getTime()
                const timeB = new Date(b.quoteTime).getTime()
                return timeA - timeB
            }) : []

            const group = groupBy(result, 'toElsAccount')
   
            legend.data = Object.keys(group).map((key) => {
                const arr = group[key]
                const { toElsAccount, supplierName } = arr[0] || {}
                return `${toElsAccount}_${supplierName}`
            })
            let length = 0
            // console.log(group)
            const series = Object.keys(group).map((key) => {
                // let prop = this.isPack ? 'totalAmount' : this.form.quoteType == '0'?'price':'netPrice'
                let prop = 'totalAmount'
                // 报价方式：打包、批量 -- 取打包金额或者打包未税金额， 逐条 -- 取含税单价或未税单价
                if (!this.isPack && (this.form.ebiddingWay == '1' || this.form.ebiddingWay == '3')) { // 逐条
                    prop = this.form.quoteType == '0'?'price':'netPrice'
                }
                if (['0', '2'].includes(this.form.ebiddingWay)) { // 打包、批量
                    prop = this.form.quoteType == '0'?'totalAmount':'netTotalAmount'
                }
                const arr = group[key]
                const data = arr.map((n) => n[prop])
                // quoteTime
                const { toElsAccount, supplierName } = arr[0] || {}
                const name = `${toElsAccount}_${supplierName}`

                length = arr.length > length ? arr.length : length

                this.chartXAxisData = this.chartXAxisData.length > arr.length ? this.chartXAxisData : arr

                return {
                    name,
                    data,
                    type: 'line'
                }
            })
            for (let i = 0; i < length; i++) {
                xAxis.data.push(i + 1)
            }
            let title = ''
            if (!this.isPack && (this.form.ebiddingWay == '1' || this.form.ebiddingWay == '3')) {
                title = this.form.quoteType == '0'?this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价(元)'):this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价(元)')
            }
            if (['0', '2'].includes(this.form.ebiddingWay)) { // 打包、批量
                title = this.form.quoteType == '0'?this.$srmI18n(`${this.$getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'):this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额')
            }
            this.chartData = {
                legend,
                xAxis,
                series,
                yAxis: {
                    name: title
                }
            }
            //缓存x轴和报价次数的数据
            this.chartLengthDataXAxis={...xAxis}
            this.chartLengthDataSeries=[...series]
        },
        setHandleQutpXasChange (e){
            let arr = this.chartGridOptions.data
            let result = arr ? arr.sort((a, b) => {
                const timeA = new Date(a.quoteTime).getTime()
                const timeB = new Date(b.quoteTime).getTime()
                return timeA - timeB
            }) : []
            const group = groupBy(result, 'toElsAccount')
            console.log('group', group)
            let kesa=Object.keys(group)
            let prop=''
            console.log('kesa', kesa)
            if (!this.isPack && (this.form.ebiddingWay == '1' || this.form.ebiddingWay == '3')) { // 逐条
                prop = this.form.quoteType == '0' ? 'price' : 'netPrice'
            }
            if (['0', '2'].includes(this.form.ebiddingWay)) { // 打包、批量
                prop = this.form.quoteType == '0' ? 'totalAmount' : 'netTotalAmount'
            }
            let xAxis=[]
            let series=[]
            for(let i of kesa){
                for(let j of group[i]){
                    let time = new Date(j[e]).getHours() <= 9 ? '0' + new Date(j[e]).getHours() : new Date(j[e]).getHours() == '24' ? '00' : new Date(j[e]).getHours()
                    let mins = new Date(j[e]).getMinutes() <= 9 ? '0' + new Date(j[e]).getMinutes() : new Date(j[e]).getMinutes()
                    let seconds = new Date(j[e]).getSeconds() <= 9 ? '0' + new Date(j[e]).getSeconds() : new Date(j[e]).getSeconds()
                    if(!xAxis.includes[`${time}:${mins}:${seconds}`]){
                        xAxis.push(`${time}:${mins}:${seconds}`)
                    }
                }
            }
            for(let i of kesa){
                if (this.isPack|| this.form.ebiddingWay == '1') { // 逐条
                    let seriesItem=new Array(xAxis.length).fill('')
                    for(let  j of group[i]){
                        let time = new Date(j[e]).getHours() <= 9 ? '0' + new Date(j[e]).getHours() : new Date(j[e]).getHours() == '24' ? '00' : new Date(j[e]).getHours()
                        let mins = new Date(j[e]).getMinutes() <= 9 ? '0' + new Date(j[e]).getMinutes() : new Date(j[e]).getMinutes()
                        let seconds = new Date(j[e]).getSeconds() <= 9 ? '0' + new Date(j[e]).getSeconds() : new Date(j[e]).getSeconds()
                        // console.log('xAxis.indexOf[`${time}:${mins}:${seconds}`]', xAxis)
                        if(xAxis.indexOf(`${time}:${mins}:${seconds}`)>-1){
                            seriesItem[xAxis.indexOf(`${time}:${mins}:${seconds}`)]=j[prop]         
                        }
                    }
                    series.push({
                        data: seriesItem,
                        type: 'line'
                    })
                } else {
                    let seriesItem=new Array(xAxis.length).fill('')   
                    let curIndex=0
                    let setup=this.materialGridOptions.data.length
                    for(let  [jidx, j] of group[i].entries()){
                        let time = new Date(j[e]).getHours() <= 9 ? '0' + new Date(j[e]).getHours() : new Date(j[e]).getHours() == '24' ? '00' : new Date(j[e]).getHours()
                        let mins = new Date(j[e]).getMinutes() <= 9 ? '0' + new Date(j[e]).getMinutes() : new Date(j[e]).getMinutes()
                        let seconds = new Date(j[e]).getSeconds() <= 9 ? '0' + new Date(j[e]).getSeconds() : new Date(j[e]).getSeconds()
                        // console.log('xAxis.indexOf[`${time}:${mins}:${seconds}`]', xAxis)
                      
                        if(xAxis.indexOf(`${time}:${mins}:${seconds}`)>-1){
                            curIndex= xAxis.indexOf(`${time}:${mins}:${seconds}`)

                            console.log(i, j[e], `${time}:${mins}:${seconds}`, j[prop], curIndex, jidx, xAxis.indexOf(`${time}:${mins}:${seconds}`))
                            let curIndexs = curIndex <= jidx ? jidx : curIndex+(jidx%setup)
                            seriesItem[curIndexs]=j[prop] 
                        }
                    }
                    series.push({
                        data: seriesItem,
                        type: 'line'
                    })

                }

            }


            return {
                xAxis, series
            }
        },
        //报价趋势X切换
        handleXasChange (e) {
            console.log('legth', e)
            this.xasDeaultVal = e
            if (e == 'length') {
                console.log('1231', this.chartLengthDataXAxis)
                this.chartData.xAxis.data =[...this.chartLengthDataXAxis.data]
                this.chartData.xAxis.axisLabel ={rotate: 0, interval: 0}
                this.chartData.series=[...this.chartLengthDataSeries]
            } else {
                let {xAxis, series}=this.setHandleQutpXasChange(e)
                this.chartData.xAxis.data = xAxis
                this.chartData.series=series
                this.chartData.xAxis.axisLabel = { rotate: 50, interval: 0 }
            }
       
        },
        getScreenWidth () {
            const clientWidth = document.documentElement.clientWidth
            this.gt1900 = clientWidth >= 1900
        },
        getQueryData () {
            this.activeKey = '1' // 初始供应商信息、报价趋势图 tab 为 1
            const { ebiddingNumber, currentItemNumber, id } = this.$route.query || {}
            this.currentItemNumber = currentItemNumber || '1'
            this.current = this.currentItemNumber
            this.form = Object.assign({}, this.form, {
                ebiddingNumber,
                currentItemNumber,
                id
            })
        },
        handleRowClass ({ row }) {
            if (this.isPack) return ''
            const { itemNumber = '' } = row || {}
            if (itemNumber === this.current) {
                return 'row--current'
            }
        },
        resetChart () {
            this.$refs.echart && this.$refs.echart.chart.clear()
        },
        getOnline () {
            apiQueryOnlineAccount({ headId: this.$route.query.id }).then((res) => {
                console.log('res', res)
                this.httpOnlineSuppliers = res.message
                this.suppliers = res.message
            })
        },
        // 通过lodash的防抖函数来控制resize的频率
        [resizeChartMethod]: debounce(function () {
            this.getScreenWidth()
        }, 100)
    },
    watch: {
        $route: {
            handler ({ path }) {
                if (path === '/ebidding/buyLobbyNew') {
                    this.getQueryData()
                    this.getData()
                    this.setApiQueryOnlineAccount()
                    // this.getEbiddingDetailData()
                }
            },
            immediate: true
        },
        getOnlineID: {
            handler (newVal, oldVal) {
                console.log('getOnlineID newVal', newVal)
                if (newVal && oldVal && newVal.id === this.$route.query.id && oldVal.time !== newVal.time) {
                    // && oldVal.time !== newVal.time
                    this.refresh()
                }
            },
            immediate: true,
            deep: true
        },
        getPageRefreshTime: {
            handler (newVal) {
                console.log('getPageRefreshTime newVal', newVal)
                if (newVal && newVal.id === this.$route.query.id) {
                    this.refresh()
                }
                this.getOnline()
            },
            immediate: true,
            deep: true
        }
    },
    created () {
        window.addEventListener('resize', this[resizeChartMethod])
        this.getScreenWidth()
        this.getOnlineWebsocketUrl()
        // this.getData(),
        // this.getEbiddingDetailData()
        nominalEdgePullWhiteBlack()
        this.$nextTick(() => {
            this.changeToggle()
        })
    },
    // activated () {
    //     this.SetOnlineWS({ wsOnlineUrl: this.wsOnlineUrl, id: this.$route.query.id }) // 是否在线WS
    // },
    activated () {
        // this.SetOnlineWS({ wsOnlineUrl: this.wsOnlineUrl, id: this.form.id }) // 是否在线WS
        console.log('activated ------------------------- activated')
        this.getOnlineWebsocketUrl()
        this.sendRefresh()
        window.addEventListener('resize', this[resizeChartMethod])
    },
    mounted () {
        console.log('mounted ------------------------- mounted')
        window.addEventListener('onmessageWS', this.getSocketData)
    },
    beforeDestroy () {
        console.log('beforeDestroy ------------------------- beforeDestroy')
        window.removeEventListener('onmessageWS', this.getSocketData)
        window.removeEventListener('reisze', this[resizeChartMethod])
    }
}
</script>

<style lang="less" scoped>
@red: #f41616;
@blue: #1690ff;
.m-l-24 {
    margin-left: 36px !important;
}
.m-t-12 {
    margin-top: 12px;
}
.inline-flot {
    display: inline-block;
    float: right;
    font-size: 14px;
}
.buyBid {
    background-color: #eaeaea;
    ul {
        list-style: none;
        margin: 0;
        padding: 0;
    }
    .redTxt {
        color: @red;
    }
    .blue {
        color: #1890ff;
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px;
        background: #fff;
        position: fixed;
        width: 100%;
        position: absolute;
        height: 44px;
        z-index: 99;
        .menu {
            text-align: right;
            .ant-btn {
                & + .ant-btn {
                    margin-left: 10px;
                }
            }
        }
    }
    .content {
        padding: 8px;
        padding-top: 32px;
        .gutter:last-child {
            margin-right: 8px;
        }
        .gutter {
            display: flex;
            & + .gutter {
                margin-top: 8px;
            }
            .price {
                max-width: 100%;
                flex: 1;
            }
            .history,
            .compare {
                overflow-y: auto;
                flex: 1;
                max-width: 100%;
                .ant-btn {
                    & + .ant-btn {
                        margin-left: 10px;
                    }
                }
            }
            .material {
                width: 50%;
            }
            .marerial-right {
                flex: 1;
            }
        }
        .item {
            padding: 12px;
            background: #fff;
            border-radius: 4px;
            & + .item {
                margin-left: 8px;
            }
        }
        .info {
            font-size: 20px;
            color: #000;
            .inline {
                .tit {
                    margin-right: 8px;
                    &::after {
                        content: ':';
                    }
                }
                &:first-of-type {
                    border-left: 4px solid @blue;
                }
                & + .inline {
                    margin-left: 24px;
                }
                .label {
                    margin-left: 12px;
                    &::after {
                        content: ':';
                    }
                }
                .value {
                    margin-left: 12px;
                    color: @blue;
                }
                .red {
                    color: @red;
                }
            }
        }
        .currentTrendEchart {
            width: 100%;
            height: 400px;
        }
        .hisPriceEchart {
            width: 100%;
            height: 446px;
        }
        .table {
            height: 420px;
        }
        .material-table {
            height: 480px;
        }
    }
    .chartInfo {
        display: flex;
        margin-top: 30px;
        .echart {
            flex: 1;
            min-height: 320px;
        }
        .echart.unShow {
            // background-size: 100px 100px;
            position: relative;
            background-color: #fff;
            background-image: url(~@/assets/img/ebidding/x1.png);
            background-repeat: no-repeat;
            background-position: center;
        }
        .chartTable {
            flex: 0 0 260px;
            margin-left: 20px;
        }
        .red {
            color: @red;
            position: absolute;
            left: 50%;
            bottom: 20px;
            transform: translateX(-50%);
        }
    }
}
:deep(.ant-descriptions-item-content) {
    width: 20%;
}
:deep(.ant-card-head-title) {
    padding: 0;
}
.custom-resizer {
    width: 100vw;
    overflow: hidden;
    > .multipane-resizer {
        margin: 0;
        left: 0;
        // margin-top: 20%;
        position: relative;
        &:before {
            display: block;
            content: '';
            width: 3px;
            height: 40px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -20px;
            margin-left: -4px;
            border-left: 1px solid #ccc;
            border-right: 1px solid #ccc;
        }
        &:hover {
            &:before {
                border-color: #999;
            }
        }
    }
}
</style>
