<template>
  <div>
    <div class="action-header">
      <preview :formHd="formHd"/>
      <a-popconfirm
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_clearListMsg`, '确定要清空列表吗？')"
        @confirm="reset"
        :okText="$srmI18n(`${$getLangAccount()}#i18n_title_clear`, '清空')"
        :cancelText="$srmI18n(`${$getLangAccount()}#i18n_title_close`, '取消')"
      >
        <a href="#">{{ $srmI18n(`${$getLangAccount()}#i18n_title_clearList`, '清空列表') }}</a>
      </a-popconfirm>
      <a-button
        @click="saveData"
        type="primary"
        style="margin-right: 11px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
      <a-button
        @click="goBack"
        style="margin-right: 11px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
    </div>
    <!-- {{ list }} -->
    <!-- 表头数据 -->
    <a-form-model
      v-if="false"
      :model="formHd"
      :label-col="6"
      :wrapper-col="16">
      <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_questionnaireTitle`, '问卷标题')">
        <a-input v-model="formHd.surveyName" />
      </a-form-model-item>
      <a-form-model-item :label="$srmI18n(`${$getLangAccount()}#i18n_title_questionnaireDes`, '问卷描述')">
        <a-input
          v-model="formHd.surveyDesc"
          type="textarea"/>
      </a-form-model-item>
    </a-form-model>
    <a-form :layout="formLayout">
      
      <draggable
        v-model="list"
        class="view-wrap"
        :group="{ name: 'form', pull: true, put: true }"
        ghostClass="ghost"
        :animation="300"
        :move="move"
        @sort="sortFn"
        @change="onChange"
      >
        <row-view
          v-for="(item, index) in list"
          :key="item.key"
          :index="index"
          :data="item"
          class="row-view"
        />
      </draggable>
    </a-form>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import RowView from './RowView'
import Preview from '../common/Preview'
import GenerateJson from '../common/GenerateJson'
import { mapState, mapMutations } from 'vuex'
import { postAction } from '@/api/manage'
import '../../index.css'
export default {
    name: 'FormView',
    inject: ['initData'],
    components: {
        draggable,
        RowView,
        Preview,
        GenerateJson
    },
    data () {
        return {
            group: {
                name: 'form',
                pull: true,
                put: true
            },
            formLayout: 'vertical',
            formHeader: {},
            formHd: {
                surveyName: '',
                surveyDesc: ''
            }
        }
    },
    created () {
        document.body.ondrop = function (event) {
            event.preventDefault()
            event.stopPropagation()
        }
        // 更新初始化表数据
        this.formHd.surveyName = this.initData.surveyName
        this.formHd.surveyDesc = this.initData.surveyDesc
        let data = this.initData.purchaseSurveyLibraryList.map(rs => JSON.parse(rs.libraryContent))
        this.updateFormData(data)
    },
    mounted () {
    },
    watch: {
        formDataHeader (v) {
            this.formHd = v
        }
    },
    computed: {
        list: {
            get () {
                return this.formData
            },
            set (value) {
                this.updateFormData(value)
            }
        },
        ...mapState({
            formData: state => state.formDesigner.formData,
            formDataHeader: state => state.formDesigner.formDataHeader
        })
    },
    methods: {
        goBack () {
            this.$parent.$parent.$parent.hideDesignPage()
            this.updateFormData([])
        },
        transData () {
            let purchaseSurveyLibraryList = []
            for (let i = 0; i < this.list.length; i++) {
                let row = this.list[i]
                let obj = {}
                obj.itemName = row.attr.label
                obj.itemType = row.type
                obj.fbk1 = row.key  // fbk1 记录当前id
                obj.initialValue = row.attr.initialValue  // 记录默认值
                obj.libraryContent = JSON.stringify(row) // 
                obj.purchaseSurveyOptionList = []
                if (row.attr.options) {
                    for (let j = 0; j < row.attr.options.length; j++) {
                        let options = row.attr.options[j]
                        let opt = {}
                        opt.optionType = row.type
                        opt.optionName = options.label
                        opt.optionComment = options.value
                        obj.purchaseSurveyOptionList.push(opt)
                    }
                } else { // 输入框、评分 添加purchaseSurveyOptionList
                    let optObj = {}
                    optObj.optionType = row.type
                    optObj.optionName = row.attr.label
                    optObj.optionComment = row.attr.initialValue
                    obj.purchaseSurveyOptionList[0] = optObj
                }
                purchaseSurveyLibraryList.push(obj)
            }
            return purchaseSurveyLibraryList
        },
        saveData (){
            let elsAccount = this.$ls.get('Login_elsAccount')
            let purchaseSurveyLibraryList = this.transData() // 转换成接口使用字段
            let url = '/survey/purchaseSurveyHead/edit'
            let params = {
                elsAccount,
                id: this.initData.id,
                surveyDesc: this.formHd.surveyDesc,
                surveyName: this.formHd.surveyName,
                purchaseSurveyLibraryList
            }
            postAction(url, params).then(res => {
                if(res.success) {
                    this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveSuccess`, '保存成功'))
                    window.setTimeout(() => {
                        this.goBack()
                    }, 1000)
                }else{
                  this.$message.warning(res.message)
                }
            })
        },
        move (e, o) {
            if (e.draggedContext.element.type === 'grid' && e.to.className.indexOf('grid-control-wrap') >= 0) return false // 不允许grid嵌套grid
            return true
        },
        sortFn (e){
            console.log(this.list)
        },
        onChange (e) {
            const eKey = Object.keys(e)[0]
            if (eKey === 'added') {
                this.updateActiveKey(e[eKey].element.key)
            }
        },
        reset () {
            this.updateFormData([])
        },
        ...mapMutations({
            updateFormData: 'setFormData',
            updateActiveKey: 'setActiveKey'
        })
    }
}
</script>

<style lang="less" scoped>
  .view-wrap {
    min-height: 300px;
  }
  .row-view {
    cursor: move;
    margin-bottom: 10px;
    /*&:hover {*/
    /*  background-color: rgba(0,0,0,0.1);*/
    /*}*/
  }
  .ghost {
    background: #cf1322;
    height: 3px;
    overflow: scroll;
  }
  .action-header {
    text-align: right;
    margin-bottom: 20px;
    a {
      margin-right: 20px;
    }
  }
</style>
