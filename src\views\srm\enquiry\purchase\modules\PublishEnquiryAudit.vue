<template>
  <div>
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      isAudit
      :pageData="pageData"
      @loadSuccess="handleLoadSuccess"
    />
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <!-- 查看流程 -->
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterapprovalComments`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <view-ladder-price-modal
      :current-edit-row="currentEditRow"
      ref="ladderPage"
    />
  </div>
</template>

<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import { httpAction } from '@/api/manage'
import ViewLadderPriceModal from '../../modules/ViewLadderPriceModal'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PublishEnquiryAudit',
    mixins: [DetailMixin],
    components: {
        flowViewModal,
        ViewLadderPriceModal
    },
    data () {
        return {
            ladderSlots: {
                default: ({ row, column }) => {
                    const detailTpl = (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                            <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                <template slot="title">
                                    <div>
                                        <vxe-table auto-resize border row-id="id" size="mini" data={this.initRowLadderJson(row[column.property])}>
                                            <vxe-table-column type="seq" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                            <vxe-table-column field="ladder" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                            <vxe-table-column field="price" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                            <vxe-table-column field="netPrice" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                        </vxe-table>
                                    </div>
                                </template>
                                {this.defaultRowLadderJson(row[column.property])}
                            </a-tooltip>
                        </div>)
                    if(row && row.quotePriceWay == 1){
                        // let label = row['ladderPriceJson'] ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看') : ''
                        // return [
                        //     <a onClick={() => this.setLadder(row)}>{label}</a>
                        // ]
                        return detailTpl
                    }else{
                        return ''
                    }
                    // if (row && row.quotePriceWay == 1) {
                    //     let label = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                    //     if (row.ladderPriceJson) {
                    //         let itemList = JSON.parse(row.ladderPriceJson)
                    //         if (itemList[0].price) {
                    //             label = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价')
                    //         }
                    //     }
                    //     return [<a onClick={() => this.setLadder(row)}>{label}</a>]
                    // } else {
                    //     return ''
                    // }
                }
            },
            costSlots: {
                default: ({ row }) => {
                    if (row && row.quotePriceWay == 2) {
                        let label = row.price ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        return [<a onClick={() => this.openCost(row)}>{label}</a>]
                    } else {
                        return ''
                    }
                }
            },
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQReleaseApproval`, '询价发布审批'),
            confirmLoading: false,
            flowId: 0,
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            currentRow: {},
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEnquiryItemList',
                        columns: [],
                        buttons: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    // { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                    //         ref: 'enquirySupplierListList',
                    //         columns: [
                    //             { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                    //             { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 },
                    //             { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), field: 'supplierCode', width: 150 },
                    //             { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 }
                    //         ],
                    //         buttons: []
                    // } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'), width: 130 },
                            { field: 'businessType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#`, '业务类型'), width: 130 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), click: this.auditPass, showCondition: this.showAuditBtn},
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), click: this.auditReject, showCondition: this.showAuditBtn},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), click: this.showFlow},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/enquiry/purchaseEnquiryHead/queryById'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentRow.templateNumber
            let templateVersion = this.currentRow.templateVersion
            let elsAccount = this.currentRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_enquiry_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            console.log(jsonData)
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                if (Array.isArray(arr)) {
                    arr.forEach((item, index)=> {
                        let ladder = item.ladder
                        let price = item.price || ''
                        let str = `${ladder},${price}`
                        let separator = index===arr.length-1? '': ';'
                        arrString +=str+ separator
                    })
                }
            }
            return arrString
        },
        beforeHandleData (data) {
            data.itemColumns.forEach((item) => {
                if (item.field == 'ladderPriceJson') {
                    item.slots = this.ladderSlots
                }
                if (item.field == 'costFormJson') {
                    item.slots = this.costSlots
                }

                //询价发布审批详情 - 询价行信息字段排序
                // let needSortKeyList = ["materialNumber","price","requireQuantity","quantityUnit","secondaryQuantity","purchaseUnit","netPrice","quotaQuantity","quotaTaxAmount","quotaNetAmount","taxAmount","netAmount"];
                // if (needSortKeyList.includes(item.field)) {
                //     item.sortable = true;
                // }
                if(item.field!==''){
                    item.sortable = true;
                }
            })
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId

            // 设备部询价需要展示供应商列表
            if(this.$TemplateUtil.isDeviceTemplate_enquiry(this.currentRow) == true) {
                let configSupplierList = this.pageData.groups.find(item => item.groupCode == "supplierInfo")
                if(!configSupplierList || configSupplierList == null) {
                    this.pageData.groups.splice(1, 1, { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'enquirySupplierListList',
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 }
                        ]
                    }})
                }
            }
        },
        goBack () {
            this.$parent.hideController()
        },
        setLadder (row){
            this.$refs.ladderPage.open(row)
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText=this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText=this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        }
    }
}
</script>
