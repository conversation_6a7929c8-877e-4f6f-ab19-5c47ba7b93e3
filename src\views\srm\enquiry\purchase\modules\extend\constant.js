export const CHECKBOX_COLUMN = { align: 'center', fixed: 'left', type: 'checkbox', width: 50 }

export const RADIO_COLUMN = { align: 'center', fixed: 'left', type: 'radio', width: 50 }

export const SEQ_COLUMN = { align: 'center', fixed: 'left', type: 'seq', width: 50 }

export const MATERIAL_COLUMN = { align: 'center', field: 'materialNumber', title: '物料信息', width: 200 }

export const COMPARE_HEAD_COLUMN = { align: 'center', title: '对比项' }

export const COMPARE_COLUMN = { align: 'center', field: 'columnName', title: '对比项', width: 118 }

export const LADDER_COLUMN = { align: 'center', field: 'ladderLabel', title: '阶梯级', width: 120 }

export const COST_COLUMN = { align: 'center', field: 'costLabel', title: '成本项', width: 120 }

export const OPERATION_COLUMN = { align: 'center', fixed: 'right', slots: { default: 'grid_operation' }, title: '操作', width: 60 }

export const CURRENT_PRICE = { columnCode: 'currentPrice', columnName: '当前含税价', hidden: '0' }

export const CURRENT_NET_PRICE = { columnCode: 'currentNetPrice', columnName: '当前未税价', hidden: '0' }

export const QUOTA = { columnCode: 'acceptQuota', columnName: '操作', hidden: '0' }

export const STATUS = { columnCode: 'itemStatus_dictText', columnName: '状态', hidden: '0', align: 'center', field: 'itemStatus_dictText', title: '状态', width: 120 }

export const QUOTA_QUANTITY = { columnCode: 'quotaQuantity', columnName: '拆分数量', hidden: '0' }

export const QUOTA_SCALE = { columnCode: 'quotaScale', columnName: '拆分比例', hidden: '0' }

export const BARGAIN_REMARK = { columnCode: 'bargainRemark', columnName: '议价备注', hidden: '0' }

export const IS_OFFER = { columnCode: 'quotePrice_dictText', columnName: '是否报价', hidden: '0', align: 'center', field: 'quotePrice_dictText', title: '是否报价', width: 100 }

export const LAST_TIME_PRICE = { columnCode: 'lastTimePrice', columnName: '上期价格', hidden: '0', align: 'center', field: 'lastTimePrice', title: '上期价格', width: 120 }

export const PACKAGE_COLUMNS = [
  { align: 'center', field: 'supplierName', title: '授权供应商', width: 120 },
  { align: 'center', field: 'netAmount', title: '未税价', width: 100 },
  { align: 'center', field: 'taxAmount', title: '含税价', width: 100 }
]

export const MATERIAL_COLUMNS = [
  { align: 'center', field: 'materialNumber', title: '物料编码', width: 120 },
  { align: 'center', field: 'materialName', title: '物料名称', width: 120 },
  { align: 'center', field: 'supplierName', title: '授权供应商', width: 120 },
  { align: 'center', field: 'requireQuantity', title: '需求数量', width: 120 },
  { align: 'center', field: 'netPrice', title: '未税价', width: 120 },
  { align: 'center', field: 'price', title: '含税价', width: 120 },
  { align: 'center', field: 'effectiveDate', title: '价格生效时间', width: 120 },
  { align: 'center', field: 'expiryDate', title: '价格失效时间', width: 120 },
  { align: 'center', field: 'auditStatus_dictText', title: '审批状态', width: 120 }
]

export const PAGE_HEADER_TITLE = [
  { label: '询价单号', value: 'enquiryNumber', i18n: 'i18n_title_inquirySheetNo' },
  { label: '询价单状态', value: 'enquiryStatus_dictText', i18n: 'i18n_field_enquiryStatus' },
  { label: '截止时间', value: 'quoteEndTime', i18n: 'i18n_title_deadline' }
]
