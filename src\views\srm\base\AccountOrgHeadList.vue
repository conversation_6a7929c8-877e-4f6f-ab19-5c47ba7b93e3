<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <accountOrgHead-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import AccountOrgHeadModal from './modules/AccountOrgHeadModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    mixins: [listPageMixin],
    components: {
        AccountOrgHeadModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, primary: true},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDel}
                ],
                optColumnList: [
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditInner},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle}          
                ]
            },
            url: {
                list: '/base/accountOrgHead/list',
                delete: '/base/accountOrgHead/delete',
                deleteBatch: '/base/accountOrgHead/deleteBatch',
                exportXlsUrl: 'base/accountOrgHead/exportXls',
                importExcelUrl: 'base/accountOrgHead/importExcel',
                columns: 'accountOrgHeadList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterpriseAccountInfoTable`, '企业组织子账号信息表'))
        },
        handleEditInner (row) {
            let itemRow ={id: row.headId}
            this.currentEditRow = itemRow
            this.$refs.listPage.handleEdit(itemRow)
            this.showEditPage = true
        }      
    }
}
</script>