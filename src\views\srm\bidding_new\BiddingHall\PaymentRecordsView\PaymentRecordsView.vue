<template>
  <div class="payment-records-view">
    <ContentHeaderNew :btns="btns"></ContentHeaderNew>
    <div v-if="!inShowEditPage && !otShowEditPage && !inShowDetailPage && !otShowDetailPage">
      <a-tabs
        default-active-key="1"
        @change="callback">
        <a-tab-pane
          key="1"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_sWsxJp_b516bfeb`, '保险保函缴纳')">
          <InsuranceGuaranteeView
            @changePageStatus="inChangePageStatus"
            @operationPage="operationPage" />
        </a-tab-pane>
        <a-tab-pane
          key="2"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_AvCKJp_88896d15`, '其他方式缴纳')"
          force-render>
          <OtherMethodsView
            @changePageStatus="inChangePageStatus"
            @operationPage="operationPage" />
        </a-tab-pane>
      </a-tabs>
    </div>
    <InsuranceGuaranteeEdit 
      v-if="inShowEditPage"
      :currentEditRow="currentEditRow" />
    <OtherMethodsEdit 
      v-if="otShowEditPage"
      :currentEditRow="currentEditRow" />
    <InsuranceGuaranteeDetail 
      v-if="inShowDetailPage"
      :currentEditRow="currentEditRow" />
    <OtherMethodsDetail 
      v-if="otShowDetailPage"
      :currentEditRow="currentEditRow" />
  </div>
</template>

<script>
import ContentHeaderNew from '../components/content-header-new'
import InsuranceGuaranteeView from './modules/InsuranceGuaranteeView'
import OtherMethodsView from './modules/OtherMethodsView'
import InsuranceGuaranteeEdit from './modules/InsuranceGuaranteeEdit'
import OtherMethodsEdit from './modules/OtherMethodsEdit'
import InsuranceGuaranteeDetail from './modules/InsuranceGuaranteeDetail'
import OtherMethodsDetail from './modules/OtherMethodsDetail'

export default {
    components: {
        ContentHeaderNew,
        InsuranceGuaranteeView,
        OtherMethodsView,
        InsuranceGuaranteeEdit,
        OtherMethodsEdit,
        InsuranceGuaranteeDetail,
        OtherMethodsDetail
    },
    data () {
        return {
            inShowEditPage: false,
            otShowEditPage: false,
            inShowDetailPage: false,
            otShowDetailPage: false,
            currentEditRow: {},
            btns: [
                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}
            ]
        }
    },
    watch: {
        inShowEditPage (val) {
            if (val) {
                this.btns = []
            } else {
                this.btns = [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
            }
        },
        otShowEditPage (val) {
            if (val) {
                this.btns = []
            } else {
                this.btns = [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
            }
        },
        inShowDetailPage (val) {
            if (val) {
                this.btns = []
            } else {
                this.btns = [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
            }
        },
        otShowDetailPage (val) {
            if (val) {
                this.btns = []
            } else {
                this.btns = [{title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.$router.go(-1)}}]
            }
        }
    },
    methods: {
        callback (key) {
            console.log(key)
        },
        inChangePageStatus (type) {
            console.log(type)
            this.currentEditRow = {}
            this.$route.meta && ((type) => {
                if (type == '2') {
                    // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHVasxJp_20241c86`, '保证金管理缴纳记录新增（保函缴纳）')
                    this.inShowEditPage = true
                }
                if (type == '1') {
                    // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHVaAvCKJp_530a66f0`, '保证金管理缴纳记录新增（其他方式缴纳）')
                    this.otShowEditPage = true
                }
            })(type)
        },
        operationPage (type, row, pageStatus) {
            this.currentEditRow = row
            this.$route.meta && ((type, row, pageStatus) => {
                if (type == '2') {
                    if (pageStatus == 'edit') {
                        // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHAtsxJp_4847b913`, '保证金管理缴纳记录编辑（保函缴纳）')
                        this.inShowEditPage = true
                    } else {
                        // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHdVsxJp_b739bc77`, '保证金管理缴纳记录详情（保函缴纳）')
                        this.inShowDetailPage = true
                    }
                }
                if (type == '1') {
                    if (pageStatus == 'edit') {
                        // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHAtAvCKJp_b9143d`, '保证金管理缴纳记录编辑（其他方式缴纳）')
                        this.otShowEditPage = true
                    } else {
                        // this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRvJptHdVAvCKJp_7b37cea1`, '保证金管理缴纳记录详情（其他方式缴纳）')
                        this.otShowDetailPage = true
                    }
                }
            })(type, row, pageStatus)
        }
    }
}
</script>

<style lang="less" scoped>
.payment-records-view {
  background-color: #fff;
  height: 100%;
}
</style>