<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showSingerEditPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <ContractAcceptanceEsignEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <ContractAcceptanceEsignDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 签章人维护 -->
    <ContractAcceptanceSingerEdit
      v-if="showSingerEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import ContractAcceptanceEsignEdit from './modules/ContractAcceptanceEsignEdit'
import ContractAcceptanceSingerEdit from './modules/ContractAcceptanceSingerEdit'
import ContractAcceptanceEsignDetail from './modules/ContractAcceptanceEsignDetail'
import {getAction, postAction} from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        ContractAcceptanceEsignEdit,
        ContractAcceptanceEsignDetail,
        ContractAcceptanceSingerEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            showSingerEditPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '(采方ELS号/名称/合同主题)'
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#contractAcceptanceEsign:detail'},
                    {type: 'edit', title: '维护签章人', clickFn: this.handleSingerEdit, allow: this.allowEdit, authorityCode: 'esign#contractAcceptanceEsign:signer'},
                    {type: 'edit', title: '签署文件维护', clickFn: this.handleEdit, allow: this.allowSignUpload, authorityCode: 'esign#contractAcceptanceEsign:signDoc'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signingInitiation`, '一步签署发起'), clickFn: this.createFlowOneStep, allow: this.allowCreateFlowOneStep, authorityCode: 'esign#contractAcceptanceEsign:initiate'},
                    {type: 'edit', title: '流程文档下载', clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'esign#contractAcceptanceEsign:down'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/elsContractAcceptanceEsign/list',
                flowFileDownload: '/esign/elsContractAcceptanceEsign/signFileDownload',
                columns: 'ContractAcceptanceEsignList',
                createFlowOneStep: '/esign/elsContractAcceptanceEsign/createFlowOneStep'
            }
        }
    },
    methods: {
        allowEdit (row){
            //线上，签章人未维护或者供方签章人未维护
            if(row.onlineSealed==='1' && (row.signerVindicateStatus==='1' || row.signerVindicateStatus==='0') && row.sendBack!=='1'){
                return false
            }
            return true
        },
        allowCreateFlowOneStep (row){
            console.log('row.onlineSealed:', row.onlineSealed)
            if(row.onlineSealed==='1'){
                return true
            }
            //已退回的单不可编辑
            if(row.sendBack==='1'){
                return true
            }
            //维护完成，没有发起
            if(row.signerVindicateStatus==='3' && row.launch!=='1'){
                return false
            }
            //撤销未退回
            if(row.esignStatus==='3' && row.sendBack!=='1'){
                return false
            }
            return true
        },
        createFlowOneStep (row){
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmationStepSignInitiate`, '确认一步签署发起'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherToInitiateAStepSignature`, '是否发起一步签署?'),
                onOk: function () {
                    postAction(that.url.createFlowOneStep, row).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            row.launch_dictText = '是'
                            row.launch = '1'
                            row.esignStatus = '0'
                            row.esignStatus_dictText = '未完成'
                            row.flowId = res.result.flowId
                        }
                    })
                }
            })
        },
        allowSignUpload (row){
            if(row.onlineSealed!=='1' && row.signFileUploaded =='1' && row.sendBack !=='1' && row.signerVindicateStatus !=='3'){
                return false
            }
            return true
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    window.open(res.result[0].fileUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showSingerEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleSingerEdit (row){
            this.showSingerEditPage = true
            this.currentEditRow = row
        }
    }
}
</script>