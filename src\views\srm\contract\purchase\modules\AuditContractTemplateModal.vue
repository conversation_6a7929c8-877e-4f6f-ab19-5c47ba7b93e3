<template>
  <div class="els-page-comtainer">
    <a-spin :spinning="confirmLoading">
      <a-page-header
        :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
        title="合同模板详情"
      >
        <template
          v-if="taskInfo.taskId"
          slot="extra">
          <taskBtn
            :currentEditRow="currentEditRow"
            :pageHeaderButtons="[{type: 'back', click: this.goBack, label: '返回'}]"/>
        </template>
        <template
          slot="extra"
          v-else>
          <a-button
            type="primary"
            v-show="form.auditStatus!='2' && form.auditStatus!='3'"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="auditPass">{{ $srmI18n(`${$getLangAccount()}#i18n_title_approved`, '审批通过') }}
          </a-button>
          <a-button
            type="primary"
            v-show="form.auditStatus!='2' && form.auditStatus!='3'"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="auditReject">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝') }}
          </a-button>
          <a-button
            type="default"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="showFlow">{{ $srmI18n(`${$getLangAccount()}#i18n_title_viewProcess`, '查看流程') }}
          </a-button>
          <a-button
            type="default"
            style="margin-left:8px"
            slot="tabBarExtraContent"
            @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </template>
      </a-page-header>
      <a-collapse
        v-model="activeKey"
        expandIconPosition="right">
        <a-collapse-panel
          key="collapse_panel_1"
          :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
        >
          <a-row
            style="margin-bottom:-12px;color:#222"
            :gutter="24">
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateNo`, '合同模板编号')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.contractTemplateNumber }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateVersion`, '合同模板版本')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.contractTemplateVersion }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateName`, '合同模板名称')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.contractTemplateName }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_templateType`, '模板类型') }}:
                </a-col>
                <a-col :span="15">{{ form.templateType_dictText }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">{{
                    $srmI18n(`${$getLangAccount()}#i18n_title_contractTemplateDesc`, '合同模板描述')
                  }}:
                </a-col>
                <a-col :span="15">{{ form.templateDesc }}</a-col>
              </a-row>
            </a-col>
            <a-col
              style="margin-bottom:12px"
              :span="8">
              <a-row :gutter="24">
                <a-col
                  :span="9"
                  style="text-align:right">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态') }}:
                </a-col>
                <a-col :span="15">{{ form.auditStatus_dictText }}</a-col>
              </a-row>
            </a-col>
          </a-row>
        </a-collapse-panel>
        <a-collapse-panel
          key="collapse_panel_2"
          :header="$srmI18n(`${$getLangAccount()}#i18n_title_rowIitem`, '行项目')"
        >
          <vxe-grid
            border
            ref="scoreGrid"
            show-overflow
            size="small"
            height="300"
            :edit-config="{trigger: 'click', mode: 'cell'}"
            :columns="itemColumns"
            :data="itemTableData">
          </vxe-grid>
        </a-collapse-panel>
        <a-collapse-panel
          key="collapse_panel_3"
          :header="$srmI18n(`${$getLangAccount()}#i18n_title_templatePreview`, '模板预览')"
        >
          <div
            style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
            v-html="contractTemplate"></div>
        </a-collapse-panel>
      </a-collapse>
    </a-spin>
    <!-- <a-modal
            v-drag
              centered
              :width="960"
              :maskClosable="false"
              :visible="flowView"
              @ok="closeFlowView"
              @cancel="closeFlowView">
              <iframe
                style="width:100%;height:560px"
                title=""
                :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
                frameborder="0"></iframe>
            </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
  </div>
</template>
<script lang="jsx">
import {getAction, httpAction} from '@/api/manage'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import flowViewModal from '@comp/flowView/flowView'
import {mapGetters} from 'vuex'
import taskBtn from '@/views/srm/bpm/components/taskBtn'

export default {
    name: 'ViewContractTemplate',
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    computed: {
        ...mapGetters([
            'taskInfo'
        ])
    },
    components: {
        taskBtn,
        flowViewModal,
        ViewItemDiffModal,
        HisContractItemModal
    },
    data () {
        return {
            flowId: '',
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentUrl: '',
            flowView: false,
            cancelAuditShow: false,
            showFlowShow: false,
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            activeKey: ['collapse_panel_1', 'collapse_panel_2', 'collapse_panel_3'],
            fixPageHeader: false,
            contractTemplate: '',
            form: {},
            itemTableData: [],
            itemColumns: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                    field: 'itemNumber',
                    width: 80
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                    field: 'itemType_dictText',
                    width: 100
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    field: 'itemName',
                    align: 'left',
                    width: 300
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_dIrv_471c1a39`, '项目版本'),
                    field: 'itemVersion',
                    width: 100
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isChangeFlag`, '是否更改'),
                    field: 'changeFlag',
                    width: 120,
                    editRender: {
                        name: 'mSwitch',
                        type: 'visible',
                        props: {closeValue: '0', openValue: '1', disabled: true}
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 100,
                    align: 'left',
                    slots: {
                        default: ({row}) => {
                            let resultArray = []
                            resultArray.push(<a
                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                onClick={() => this.viewDetail(row)}>
                                <a-icon type="profile"></a-icon>
                            </a>)
                            if (row.changeFlag == '1') {
                                resultArray.push(<a title="比对" style="margin-left:8px"
                                    onClick={() => this.viewDiff(row)}>
                                    <a-icon type="diff"></a-icon>
                                </a>)
                            }
                            return resultArray
                        }
                    }
                }
            ]
        }
    },
    created () {
        this.getContractTemplateDetail()
    },
    methods: {
        goBack () {
            this.$parent.hideController()
        },
        getContractTemplateDetail () {
            let url = '/contract/purchaseContractTemplateHead/queryById'
            getAction(url, {id: this.currentEditRow.id}).then(res => {
                if (res.success) {
                    Object.assign(this.form, res.result)
                    let itemList = res.result.purchaseContractTemplateItemList
                    let content = res.result.purchaseContractTemplateItemList.map(item => {
                        return item.itemContent
                    })
                    this.$refs.scoreGrid.loadData(itemList)
                    this.contractTemplate = content.join('')
                }
            })
        },
        showFlow () {
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        auditPass (row) {
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (row) {
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        handleOk () {
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        }
    }
}
</script>