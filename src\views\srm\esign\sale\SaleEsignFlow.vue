<template>
  <div style="height:100%">
    <!-- 列表界面 -->
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showSingerEditPage "
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 编辑界面 -->
    <SaleEsignFlowEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
    <!-- 详情界面 -->
    <SaleEsignFlowDetail 
      v-if="showDetailPage" 
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />  
    <!-- 签章人维护 -->
    <SaleEsignFlowSingerEdit
      v-if="showSingerEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"/>
  </div>

</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import SaleEsignFlowEdit from './modules/SaleEsignFlowEdit'
import SaleEsignFlowSingerEdit from './modules/SaleEsignFlowSingerEdit'
import SaleEsignFlowDetail from './modules/SaleEsignFlowDetail'
import { getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        SaleEsignFlowEdit,
        SaleEsignFlowDetail,
        SaleEsignFlowSingerEdit
    },
    data () {
        return {
            showAddPage: false,
            showNewRoundPage: false,
            showSingerEditPage: false,
            pageData: {
                form: {
                    keyWord: '',
                    regulationType: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '(采方ELS号/名称/合同主题)'
                    }
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'esign#saleEsignFlow:detail'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LDPeL_2bb593a8`, '维护签章人'), clickFn: this.handleSingerEdit, allow: this.allowEdit, authorityCode: 'esign#saleEsignFlow:signer'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQILD_ee657e73`, '签署文件维护'), clickFn: this.handleEdit, allow: this.allowSignUpload, authorityCode: 'esign#saleEsignFlow:signDoc'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentDownload`, '流程文档下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload, authorityCode: 'esign#saleEsignFlow:down'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/esign/elsEsign/saleList',
                flowFileDownload: '/esign/elsEsign/signFileDownload',
                columns: 'SaleElsEsignList'
            }
        }
    },
    methods: {
        allowEdit (row){
            //线上，签章人未维护或者供方签章人未维护
            if(row.onlineSealed==='1' && (row.signerVindicateStatus==='2' || row.signerVindicateStatus==='0') && row.sendBack!=='1'){
                return false
            }
            return true
        },
        allowSignUpload (row){
            //线下，文件未上传
            if(row.onlineSealed!=='1' && row.signFileUploaded!=='1' && row.sendBack!=='1'){
                return false
            }
            return true
        },
        showFlowFileDownload (row){
            //流程已经发起
            if(row.launch==='1' && row.archiving==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            getAction(this.url.flowFileDownload, {id: row.id}).then(res => {
                if(res.success){
                    window.open(res.result[0].fileUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        hideEditPage () {
            this.showEditPage = false
            this.showDetailPage = false
            this.showSingerEditPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        handleSingerEdit (row){
            this.showSingerEditPage = true
            this.currentEditRow = row
        }
    }
}
</script>