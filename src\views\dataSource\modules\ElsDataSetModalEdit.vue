<template>
  <div class="business-container">
    <business-layout
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageFooterButtons="pageFooterButtons"
      :externalToolBar="externalToolBar"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      :handleAfterDealSource="handleAfterDealSource"
      modelLayout="masterSlave"
      pageStatus="edit"
      v-on="businessHandler"
    >
      <template #dataSetTransformDtoListTab="{slotProps}" >
        <div class="data-set-transform-inner">
          <template v-for="(el, index) of dataTypeArr">
            <a-tag
              closable 
              color="#108ee9"
              class="data-set-transform"
              :key="el.id"
              @close="(e) => {return handleClose(index)}">
              <a-icon
                v-if="index > 0"
                type="arrow-right"
                class="edit-icon-arrow"/>
              <a-icon
                class="edit-icon-data"
                :style="{visibility: el.transformType ? 'initial' : 'hidden'}"
                @click="editDataSet(el)"
                type="edit" />
              <!-- default-value="lucy" -->
              <a-select
                @change="(val) => { return selectChangeEvent(el, 'selectChangeEvent', val)}"
                v-model="el.transformType"
                style="width: 120px">
                <a-select-option
                  :key="item.value"
                  :value="item.value"
                  v-for="item of transFormType">
                  {{ item.title }}
                </a-select-option>
              </a-select>
            </a-tag>
          </template>
          <a-button
            class="data_set_add_button"
            type="primary"
            icon="plus"
            @click="addDataType">
          </a-button>
        </div>
      </template>
    </business-layout>
    <!-- 查询参数 高级规则弹窗 -->
    <a-modal
      v-drag    
      destroyOnClose
      :visible="queryEditorModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_srmCodeEditor`, 'SRM代码编辑器')"
      :width="800"
      @ok="handleQueryClick"
      @cancel="queryEditorModalFlag=false">
      <codemirror
        v-if="queryEditorModalFlag"
        v-model="queryCodeEditorValue" 
        :options="editorOption"
        @change="queryChangeCodeMirror">
      </codemirror>
    </a-modal>

    <!-- 测试预览 -->
    <a-modal
      v-drag    
      destroyOnClose
      :visible="previewEditorModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_srmCodeEditor`, 'SRM代码编辑器')"
      :width="800"
      @ok="previewEditorModalFlag=false"
      @cancel="previewEditorModalFlag=false">
      <codemirror
        v-if="previewEditorModalFlag"
        v-model="previewCodeEditorValue" 
        :options="editorOption2"
        @change="previewChangeCodeMirror">
      </codemirror>
    </a-modal>
    <!-- 数据转换弹窗 -->
    <a-modal
      v-drag    
      forceRender
      :visible="editorModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_srmCodeEditor`, 'SRM代码编辑器')"
      :width="800"
      @ok="handleSureClick"
      @cancel="editorModalFlag=false">
      <codemirror
        v-if="editorModalFlag"
        v-model="codeEditorValue"
        :options="editorOption"
        @change="changeCodeMirror">
      </codemirror>
    </a-modal>

    <!-- 数据字典弹窗 -->
    <a-modal
      v-drag    
      destroyOnClose
      centered
      :visible="dictModalFlag"
      :maskClosable="false"
      :title="$srmI18n(`${$getLangAccount()}#i18n_alert_WJJCtk_4a7e0157`, '数字字典操作')"
      :width="800"
      @ok="handleDictClick"
      @cancel="dictModalFlag=false">
      <div class="all-dict-tabledata">
        <div
          class="all-dict-tabledata-group"
          v-for="(el, index) of allDictTableData"
          :key="index">
          <a-form-model
            v-if="el.dictForm"
            layout="inline"  
            :model="dictForm">
            <a-form-model-item label="字典项名称">
              <a-input
                v-model="el.dictForm.name"
                placeholder="字典项名称">
              </a-input>
            </a-form-model-item>
            <a-form-model-item>
              <a-button
                type="primary"
                @click="delectGroupDict(el, index)"
              >
                删除
              </a-button>
            </a-form-model-item>
          </a-form-model>

          <vxe-table
            :edit-config="{trigger: 'click', mode: 'cell'}"
            rowId="id"
            :data="el.dictTableData">
            <vxe-table-column
              type="seq"
              :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details1`, '序号')}`"
              width="80"></vxe-table-column>
            <vxe-column
              :edit-render="{name: '$input'}"
              field="key"
              title="key"></vxe-column>
            <vxe-column
              :edit-render="{name: 'input'}"
              field="value"
              title="值"></vxe-column>
            <vxe-column
              field="option"
              title="操作">
              <template #default="{ row, $rowIndex }">
                <a-button
                  @click="addDictRow(el, row, $rowIndex)"
                  type="primary">添加</a-button>
                <a-button
                  v-if="$rowIndex > 0"
                  @click="delectDictRow(el, row, $rowIndex)">删除</a-button>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
        <a-button @click="addDictGroup">新增</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang='jsx'>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import {getLangAccount, srmI18n, composePromise} from '@/utils/util.js'
import {ajaxFindDictItems, duplicateReportCheck} from '@/api/api'
import { codemirror }  from 'vue-codemirror'
import { validationRulesContent, javaScriptContent, javaBeanContent }  from './sample.js'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import 'codemirror/lib/codemirror.css'
import {
    BUTTON_SAVE,
    BUTTON_BACK
} from '@/utils/constant.js'
export default {
    name: 'ElsDataSetModal',
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: null
        },
        dataSource: {
            type: Array,
            default: () => []
        }
    },
    components: {
        BusinessLayout,
        codemirror,
        fieldSelectModal
    },
    data () {
        return {
            dictForm: {
                name: ''
            },
            allDictTableData: [
                // {
                //     dictForm: {
                //         name: ''
                //     },
                //     dictTableData: [ {  _isNew: true, key: '', value: '', option: '' }]
                // }
            ],
            dictTableData: [
                {  _isNew: true, key: '', value: '', option: '' }
            ],
            codeEditorValue: '',
            queryCodeEditorValue: '',
            currentCardInfo: {},
            currentQueryRow: {},
            codeEditorTitle: '',
            transFormTypeSelect: '',
            editorModalFlag: false,
            queryEditorModalFlag: false,
            dictModalFlag: false,
            previewEditorModalFlag: false,
            previewCodeEditorValue: '',
            dataTypeArr: [],
            transFormType: [],
            testPreviewList: '',
            requestData: {
                detail: {
                    url: '/report/dataSource/elsReportChartDataSet/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            pageFooterButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/report/dataSource/elsReportChartDataSet/edit'
                    },
                    click: this.saveEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save66`, '测试预览'),
                    key: 'preview',
                    click: this.previewEvent
                },
                BUTTON_BACK
            ],
            externalToolBar: {
                dataSetParamDtoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addSetParamItemRow,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ],
                dataSetTransformDtoList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.addTransformItemRow,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            url: {
                add: '/report/dataSource/elsReportChartDataSet/add',
                edit: '/report/dataSource/elsReportChartDataSet/edit'
            },
            hasTransfromDictToRequest: false,
            hasTransfromDictToResponse: false
        }
    },
    mounted () {
        // 加载数据转换下拉数据字典
        this.getDataTransForm()
        console.log(this.currentEditRow)
    },
    methods: {
        transformDictParams (data, type) {
            if (type == 'request') {
                let allDictTableData = {}
                data.forEach(rs => {
                    let obj = {}
                    rs.dictTableData.forEach(dict => {
                        obj[dict.key] =  dict.value
                    })
                    allDictTableData[rs.dictForm.name] = {...obj}
                })
                this.hasTransfromDictToRequest = true
                return allDictTableData
            } else {
                let original = JSON.parse(data)
                let allDictTableData = []
                Object.keys(original).forEach(rs => {
                    let from = {
                        dictForm: {
                            name: rs
                        }
                    }
                    let table = {
                        dictTableData: []
                    }
                    let child = original[rs]
                    Object.keys(child).forEach(it => {
                        let tableObj = {
                            key: it,
                            value: child[it] 
                        }
                        table.dictTableData.push(tableObj)

                    })
                    allDictTableData.push({...from, ...table})
                })
                this.hasTransfromDictToResponse = true
                return allDictTableData
            }
            
        },
        handleDictClick () {
            this.currentCardInfo.transformScript = this.allDictTableData
            this.dictModalFlag = false
            console.log(this.currentCardInfo.transformScript)
        },
        addDictGroup () {
            this.allDictTableData.push(
                {
                    dictForm: {
                        name: ''
                    },
                    dictTableData: [ {  _isNew: true, key: '', value: '' }],
                    _isNew: true
                }
            )
        },
        addDictRow (item, row, index) {
            item.dictTableData.splice(index+1, 0, { id: new Date().getTime(), _isNew: true, key: '', value: '' })
        },
        delectDictRow (item, row, index) {
            item.dictTableData.splice(index, 1)
        },
        delectGroupDict (item, index) {
            this.allDictTableData.splice(index, 1)
        },
        handleCurrentCode (val) {
            this.testPreviewList = val
        },
        // 新增保存参数不满足，重写
        composeBusinessSave (args) {
            const allData = this.getAllData()
            // 处理 http 参数
            if (this.currentEditRow.setType == 'http') {            
                let mergeArr = ['body', 'header', 'method', 'apiUrl']
                let dynSentenceObj = {}
                mergeArr.forEach(rs => {
                    dynSentenceObj[rs] = allData[rs] || ''
                })
                allData.dynSentence = JSON.stringify(dynSentenceObj)
            }
            // 加入slot页面参数
            this.dataTypeArr.forEach(rs => { //  转换数字字典值变为json字符串
                if (rs.transformType == 'dict' && typeof rs.transformScript == 'object') {
                    let data = this.transformDictParams(rs.transformScript, 'request')
                    rs.transformScript = JSON.stringify(data)
                }
            })
            allData.dataSetTransformDtoList = this.dataTypeArr
            allData.caseResult = this.previewCodeEditorValue
            allData.setType = this.currentEditRow.setType
            // 需求: 新增不保存逻辑
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge
            let steps = [
                flag ? this.stepBusinessAdd : this.stepBusinessSave
            ]
            const handleCompose = composePromise(...steps)
            this.confirmLoading = true
            handleCompose({ ...args, allData, _isAdd: flag })
                .then(res => {
                    console.log('all save success', res)
                    this.businessHide()
                    // if (!hasId) { // 新增成功后返回
                    // }
                    // 成功-刷新
                    if (flag || this.refresh) {
                        this.refreshPageData()
                    }
                }, err => {
                    console.log('all save error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        handleClose (removedTag) {
            const tags = this.dataTypeArr.filter((tag, idx) => idx !== removedTag)
            this.dataTypeArr = tags
        },
        handleSureClick () {
            this.editorModalFlag=false
            this.currentCardInfo.transformScript = this.codeEditorValue
        },
        handleQueryClick () {
            if (!this.currentQueryRow?.sampleItem) {
                this.$message.warning('参数示例值不能为空！')
                this.queryEditorModalFlag=false
                return
            }
            // 校验js
            try {
                eval(this.queryCodeEditorValue)
                let url = '/report/dataSource/elsReportChartDataSetParam/verification'
                // 发请求二次校验
                postAction(url, {sampleItem: this.currentQueryRow.sampleItem, validationRules: this.queryCodeEditorValue}).then(res => { 
                    if (res.success) {
                        this.currentQueryRow.validationRules = this.queryCodeEditorValue
                        this.queryEditorModalFlag=false
                    }
                })
            } catch (error) {
                this.$message.error('当前输入语法异常！')
                return false
            }
        },
        getDataTransForm () {
            let postData = {
                busAccount: this.busAccount,
                dictCode: 'srmTransFormType '
            }
            ajaxFindDictItems(postData).then(res => {
                if(res.success) {
                    let options = res.result.map((dictItem) => {
                        return {
                            value: dictItem.value,
                            label: dictItem.text,
                            title: dictItem.title
                        }
                    })
                    this.transFormType = options
                    // if (column.editRender) {
                    //     column.editRender.options = options
                    // }
                    // // dictOptions初始化数据字典的字段会用到
                    // column.dictOptions= options
                    // that.$forceUpdate()
                }
            })
        },
        selectChangeEvent (el, action, val) {
            el.transformScript = ''
        },
        editDataSet (el, action, val) {
            this.currentCardInfo = el
            switch (el.transformType) {
            case 'js':
                this.codeEditorValue = el.transformScript ? el.transformScript : javaScriptContent
                this.editorModalFlag = true
                break
            case 'javaBean':
                this.codeEditorValue = el.transformScript ? el.transformScript : javaBeanContent
                this.editorModalFlag = true
                break
            case 'dict':
                if (this.currentEditRow.id && el.transformScript && typeof el.transformScript == 'string') {
                    
                    this.allDictTableData = this.transformDictParams(el.transformScript, 'response')
                } else{
                    this.allDictTableData = el.transformScript ? el.transformScript : []
                }
                this.dictModalFlag = true
                this.editorModalFlag = false
                break
            
            default:
                break
            }
            
        },
        addDataType () {
            const data = {
                transformType: '',
                transformScript: '',
                id: new Date().getTime()
            }
            this.dataTypeArr.push(data)
        },
        showValidationRules (row) {
            this.queryCodeEditorValue = row.validationRules
            this.currentQueryRow = row
            this.queryEditorModalFlag = true
        },
        handleAfterDealSource (pageConfig, resultData) {
            if (this.currentEditRow?.id) {
                this.dataTypeArr = resultData?.dataSetTransformDtoList || []
                let mergeArr = ['body', 'header', 'method', 'apiUrl']
                let fromGroup = pageConfig.groups.find(rs => rs.groupCode == 'baseForm')
                let dynSentence = resultData.dynSentence ? JSON.parse(resultData.dynSentence) : {}
                mergeArr.forEach(rs => {
                    fromGroup.formModel[rs] = dynSentence[rs] || ''
                })
            }
        },
        previewEvent (args) {
            this.stepValidate(args).then(()=>{
                const allData = this.getAllData()
                // 处理 http 参数
                if (this.currentEditRow.setType == 'http') {            
                    let mergeArr = ['body', 'header', 'method', 'apiUrl']
                    // mergeArr = mergeArr.map(rs => {return {[rs]: allData[rs] || ''}})
                    // allData.sourceConfig = JSON.stringify(mergeArr)
                    let dynSentenceObj = {}
                    mergeArr.forEach(rs => {
                        dynSentenceObj[rs] = allData[rs] || ''
                    })
                    // let sourceConfig = {'apiUrl': 'https://v5poc.51qqt.com/els/notice/purchaseNotice/noToken/getPublicInquiryNotice', 'method': 'GET', 'header': '{"Content-Type":"application/json;charset=UTF-8"}', 'body': ''}
                    allData.dynSentence = JSON.stringify(dynSentenceObj)
                }
                // 加入slot页面参数
                this.dataTypeArr.forEach(rs => { //  转换数字字典值变为json字符串
                    if (rs.transformType == 'dict' && typeof rs.transformScript == 'object') {
                        let data = this.transformDictParams(rs.transformScript, 'request')
                        rs.transformScript = JSON.stringify(data)
                    }
                })
                allData.dataSetTransformDtoList = this.dataTypeArr
                // allData.caseResult = this.testPreviewList
                allData.setType = this.currentEditRow.setType
                postAction('/report/dataSource/elsReportChartDataSet/testTransform', {...allData}).then(res => { 
                    if (res.success) {
                        this.previewEditorModalFlag = true
                        this.previewCodeEditorValue = JSON.stringify(res, null, '\t')
                    }
                })
            })
        },
        changeCodeMirror (val) {
            this.codeEditorValue = val
        },
        testPreviewChange (val) {
            this.testPreviewList = val
        },
        queryChangeCodeMirror (val) {
            this.queryCodeEditorValue = val
        },
        handleBeforeRemoteConfigData (data) { 
            const that = this
            const sqlFormFields = [
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'select', // 接口给
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFj_1894252`, '数据源'),
                    fieldName: 'sourceCode',
                    options: this.dataSource
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtAo_c4e8c433`, '数据集编码'),
                    fieldName: 'setCode',
                    fieldValidator: {
                        validateMethod: function (that, form, currentField, rule, value, callback) {
                            if (!value) {
                                callback(that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_WFtAoxOLV_31e76c3`, '数据集编码不能为空'))
                                return
                            }
                            // 重复校验
                            var params = {
                                tableName: 'els_report_chart_data_set',
                                fieldName: 'set_code',
                                fieldVal: value,
                                dataId: that.currentEditRow?.id || ''
                            }
                            duplicateReportCheck(params).then((res) => {
                                if (res.success) {
                                    callback()
                                } else {
                                    callback(res.message)
                                }
                            })
                        },
                        required: true,
                        trigger: 'blur'
                    } 
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b`, '数据集名称'),
                    fieldName: 'setName'
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'textArea', // 文本域
                    fieldLabel: 'Sql',
                    fieldName: 'dynSentence',
                    required: '1'
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'hiddenField',
                    required: '0',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yRpv_3b388d2c`, '结果案例'),
                    fieldName: 'caseResult'
                }
            ]
            const httpFormFields = [
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtAo_c4e8c433`, '数据集编码'),
                    fieldName: 'setCode',
                    required: '1',
                    fieldValidator: {
                        validateMethod: function (that, form, currentField, rule, value, callback) {
                            if (!value) {
                                callback(that.$srmI18n(`${that.$getLangAccount()}#i18n_alert_WFtAoxOLV_31e76c3`, '数据集编码不能为空'))
                                return
                            }
                            // 重复校验
                            var params = {
                                tableName: 'els_report_chart_data_set',
                                fieldName: 'set_code',
                                fieldVal: value,
                                dataId: that.currentEditRow?.id || ''
                            }
                            duplicateReportCheck(params).then((res) => {
                                if (res.success) {
                                    callback()
                                } else {
                                    callback(res.message)
                                }
                            })
                        },
                        required: true,
                        trigger: 'blur'
                    } 
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b`, '数据集名称'),
                    fieldName: 'setName'
                },
                // start
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b6`, '请求路径'),
                    fieldName: 'apiUrl',
                    required: '0'
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b00`, '请求方式'),
                    fieldName: 'method',
                    required: '0'
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b8`, '请求头'),
                    fieldName: 'header',
                    required: '0'
                },
                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'input',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtRL_c4e3900b8`, '请求体'),
                    fieldName: 'body',
                    required: '0'
                },
                // end

                {
                    groupCode: 'baseForm',
                    sortOrder: '5',
                    fieldType: 'hiddenField',
                    required: '0',
                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yRpv_3b388d2c`, '结果案例'),
                    fieldName: 'caseResult'
                }
            ]
            let formFields = []
            if (this.currentEditRow.setType == 'sql') {
                formFields = sqlFormFields
            } else {
                formFields = httpFormFields
            }
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFtdV_c4ea3a27`, '数据集信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mhsW_3151f74b`, '查询参数'),
                        groupCode: 'dataSetParamDtoList',
                        groupType: 'item',
                        sortOrder: '2',
                        extend: {

                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFsS_2fa365f4`, '数据转换'),
                        groupCode: 'dataSetTransformDtoList',
                        groupType: 'item',
                        sortOrder: '3',
                        extend: {

                        }
                    }
                    // {
                    //     groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iKUB_33ce772e`, '测试预览'),
                    //     groupCode: 'testPreviewList',
                    //     groupType: 'item',
                    //     sortOrder: '3',
                    //     extend: {

                    //     }
                    // }
                ],
                formFields,
                itemColumns: [
                    {
                        groupCode: 'dataSetParamDtoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sWRL_279a6df1`, '参数名称'),
                        field: 'paramName',
                        width: 150,
                        required: '1',
                        editRender: {name: '$input'}
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sWMW_279c6c6f`, '参数描述'),
                        field: 'paramDesc',
                        width: 150,
                        required: '1',
                        editRender: {name: '$input'}
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        required: '1',
                        width: 150,
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sWAc_279f30de`, '参数类型'),
                        field: 'paramType',
                        editRender: {name: '$input' }
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        field: 'sampleItem',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sWKvR_cc3a0c1d`, '参数示例值'),
                        editRender: {name: '$input' },
                        required: '1',
                        width: 150
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        field: 'requiredFlag',
                        fieldType: 'switch',
                        dictCode: 'yn',
                        defaultValue: '0',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_required`, '是否必填'),
                        width: 150
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        field: 'validationRules', // 弹窗 代码编辑器
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JIIJOLF_2ef18179`, '自定义校验规则'),
                        width: 150,
                        defaultValue: validationRulesContent,
                        slots: {
                            default: ({row}) => {
                                const button =( <a-button onClick={(e) => {return that.showValidationRules(row)}}>  <a-icon type="plus" />高级规则 </a-button>)
                                return [button]
                            }
                        }
                    },
                    {
                        groupCode: 'dataSetParamDtoList',
                        field: 'orderNum',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sort`, '排序'),
                        width: 150
                    },
                    {
                        groupCode: 'dataSetTransformDtoList',
                        field: 'transformType',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WFsSAc_d4712364`, '数据转换类型'),
                        width: 120
                    },
                    {
                        groupCode: 'dataSetTransformDtoList',
                        field: 'transformScript',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GvWt_2a3c69b8`, '处理逻辑'),
                        width: 120,
                        align: 'center',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'dataSetTransformDtoList',
                        field: 'orderNum',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sort`, '排序'),
                        width: 120,
                        align: 'center',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        addStandardItem ({groupCode}) {
            const data = this.getAllData()
            const inputType = data.inputType
            let itemGrid = this.getItemGridRef(groupCode)
            let itemData = {}
            itemGrid.insert([itemData])
        },
        deleteItemEvent ({groupCode}) {
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.getItemGridRef('attachmentList')
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent ({groupCode}) {
            const fileGrid = this.getItemGridRef(groupCode)
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        saveEvent (args) {
            this.stepValidate(args).then(()=>{
                this.composeBusinessSave(args)
            })
        }
    },
    computed: {
        editorOption () {
            let opt =  {
                mode: 'text/javascript',
                // mode: 'application/json',
                theme: 'monokai',
                indentUnit: 4,
                lineNumbers: true,
                styleActiveLine: true,
                autofocus: true,
                foldGutter: true,
                lineWrapping: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
                // 代码提示功能
                hintOptions: {
                    // 避免由于提示列表只有一个提示信息时，自动填充
                    completeSingle: false
                },
                keyMap: 'sublime'
                
            }
            // let editorOpt = Object.assign({}, opt, this.options)
            return opt
        },
        editorOption2 () {
            let opt =  {
                // mode: 'text/javascript',
                mode: 'javascript',
                indentUnit: 4,
                lineNumbers: true,
                styleActiveLine: true,
                autofocus: true,
                foldGutter: true,
                lineWrapping: true,
                gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter', 'CodeMirror-lint-markers'],
                // 代码提示功能
                hintOptions: {
                    // 避免由于提示列表只有一个提示信息时，自动填充
                    completeSingle: false
                },
                readOnly: true,
                keyMap: 'sublime'
                
            }
            // let editorOpt = Object.assign({}, opt, this.options)
            return opt
        },
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow) {
                if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
                    account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
                }
            }
            return account
        }
    }
}
</script>
<style lang="less" scoped>
.data-set-transform-inner{
    margin-top: -20px;
    .data-set-transform {
        padding: 15px;
        position: relative;
        margin-top: 20px;
        :deep(.edit-icon-data){
            margin-right: 8px;
            padding: 3px;
            cursor: pointer;
        }
    }
    .data-set-transform:not(:first-child) {
        margin-left: 90px;
    }
    .data_set_add_button{
        height: 64px;
        width: 64px;
        text-align: center;
         margin-top: 20px;
    }
    .edit-icon-arrow {
        font-size: 30px;
        position: absolute;
        left: -65px;
        top: 20px;
        color: #108ee9;
    }
}
.all-dict-tabledata{
    height: 500px;
    overflow: auto;
    .all-dict-tabledata-group{
        margin-bottom: 20px;
    }
}
    
</style>