<template>
  <div class="page-container">
   <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="tab"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
    <!-- 加载配置文件 -->
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction } from '@/api/manage'
export default {
    name: 'SaleAddCostDetail',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    data () {
        return {
            showRemote: false,
            businessRefName: 'businessRef',
                requestData: {
                    detail: {
                        url: '/finance/saleAddCost/queryById',
                        args: (that) => {
                            return { id: that.currentEditRow.id }
                        }
                    }
                },
            pageHeaderButtons:[
                {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                        key: 'goBack'
                }
            ],
            url: {
                detail: '/finance/saleAddCost/queryById',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            debugger
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let busAccount = this.currentEditRow.templateAccount|| this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${busAccount}/sale_addCost_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.sourceType = res.result.sourceType
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true
        }
    },
    methods: {
         handleBeforeRemoteConfigData(){
            return {
                    groups: [
                        {
                            groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                            groupNameI18nKey: '',
                            groupCode: 'saleAttachmentList',
                            groupType: 'item',
                            sortOrder: '6',
                            extend: {
                                optColumnList: [
                                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent   },
                                    { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                ]
                            }
                        }
                    ],
                    formFields: [],
                    itemColumns: [
                        { 
                            groupCode: 'saleAttachmentList',
                            field: 'fileType_dictText',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                            fieldLabelI18nKey: '',
                        },
                        { 
                            groupCode: 'saleAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                            fieldLabelI18nKey: '',
                            field: 'fileName',
                            
                        },
                        { 
                            groupCode: 'saleAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                            fieldLabelI18nKey: '',
                            field: 'uploadTime'
                        },
                        { 
                            groupCode: 'saleAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                            fieldLabelI18nKey: '',
                            field: 'uploadElsAccount_dictText'
                        },
                        { 
                            groupCode: 'saleAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                            fieldLabelI18nKey: '',
                            field: 'uploadSubAccount'
                        },
                        {
                            groupCode: 'saleAttachmentList',
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                            fieldLabelI18nKey: '',
                            field: 'grid_opration',
                            width: '100',
                            slots: { default: 'grid_opration' }
                        }
                    ]
                }
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        beforeHandleData (data) {
            data.formFields.forEach(item => {
                if(item.fieldName == 'toElsAccount'){
                    item.fieldLabel = '采购方els账号'
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
    }
}
</script>
