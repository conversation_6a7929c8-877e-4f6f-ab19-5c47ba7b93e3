<template>
  <div style="font-size: 12px; text-align: left;">
    <a-row>
      <a-col class="text-title" :span="24">{{ $srmI18n(`${$getLangAccount()}#i18n_title_materialCode`, '物料编码') }}：{{ row.materialNumber }}</a-col>
        <a-col class="text-title" :span="24">{{ $srmI18n(`${$getLangAccount()}#i18n_title_materialName`, '物料名称') }}：{{ row.materialName }}</a-col>
        <a-col class="text-title" :span="24">{{ $srmI18n(`${$getLangAccount()}#i18n_title_materialSpec`, '物料规格') }}：{{ row.materialSpec }}</a-col>
        <a-col class="text-title" :span="24">{{ $srmI18n(`${$getLangAccount()}#i18n_title_needCout`, '需求数量') }}：{{ row.requireQuantity }}</a-col>
        <a-col style="text-align: center;" :span="6">{{ $srmI18n(`${$getLangAccount()}#i18n_field_quantityUnit`, '主单位') }}：{{ row.quantityUnit_dictText }}</a-col>
        <a-col style="text-align: center;" :span="3">|</a-col>
        <a-col style="text-align: center;" :span="6">换算率：{{ row.conversionRate }}</a-col>
        <a-col style="text-align: center;" :span="3">|</a-col>
        <a-col style="text-align: center;" :span="6">{{ $srmI18n(`${$getLangAccount()}#i18n_field_purchaseUnit`, '辅单位') }}：{{ row.purchaseUnit_dictText }}</a-col>
    </a-row>
  </div>
</template>
<script>

export default {
    name: 'ReportModalMaterial',
    props: {
        row: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.text-title {
  //text-align: right;
  //overflow: hidden;
  //text-overflow: ellipsis;
  // white-space: nowrap;
  white-space: normal;
}
</style>
