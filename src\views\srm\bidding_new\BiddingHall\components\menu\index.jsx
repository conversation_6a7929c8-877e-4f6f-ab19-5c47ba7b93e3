import Menu from 'ant-design-vue/es/menu'
import Icon from 'ant-design-vue/es/icon'

const { Item, SubMenu } = Menu
import { REPORT_ADDRESS, REPORT_JMREPORT_ADDRESS_SUFFIX } from '@/utils/const.js'
import { mapGetters } from 'vuex'
import { once } from 'lodash'
export default {
    name: 'SMenu',
    props: {
        menu: {
            type: Array,
            required: true
        },
        theme: {
            type: String,
            default: 'dark'
        },
        mode: {
            type: String,
            default: 'inline'
        },
        collapsed: {
            type: Boolean,
            default: false
        },
        userIconFont: {
            type: Boolean,
            default: false
        },
        searchOpenKeys: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            openKeys: [],
            selectedKeys: [],
            cachedOpenKeys: []
        }
    },
    computed: {
        rootSubmenuKeys: vm => {
            const keys = []
            vm.menu.forEach(item => keys.push(item.key))
            return keys
        }
    },
    mounted () {
        this.updateMenu()
    },
    watch: {
        collapsed (val) {
            if (val) {
                this.cachedOpenKeys = this.openKeys.concat()
                this.openKeys = []
            } else {
                this.openKeys = this.cachedOpenKeys
            }
        },
        $route: function () {
            this.updateMenu()
        },
        menu (menu) {
            for (let i = 0; i < menu.length; i++) {
                for(let k = 0; k < menu[i].children.length; k++) {
                    let currentNode = menu[i].children[k]
                    if(currentNode.purchaseLink == this.$route.path || currentNode.saleLink == this.$route.path) {
                        this.openKeys = [menu[i].key]
                        currentNode.extend = currentNode.extend ? JSON.parse(currentNode.extend) : { checkType: null, processType: null }
                        sessionStorage.setItem('tender_currentNode', JSON.stringify(currentNode))
                        this.$emit('getSessionStorageOfCurrentNode')
                        break
                    }
                }
            }
        },
        searchOpenKeys: {
            handler (val) {
                if (val && val.length > 0) {
                    this.openKeys = this.searchOpenKeys
                }
            },
            deep: true
        }
    },
    methods: {
    // select menu item
        onOpenChange (openKeys) {
            // 非水平模式时
            const latestOpenKey = openKeys.find(key => !this.openKeys.includes(key))
            if (!this.rootSubmenuKeys.includes(latestOpenKey)) {
                this.openKeys = openKeys
            } else {
                this.openKeys = latestOpenKey ? [latestOpenKey] : []
            }
            window.sessionStorage.setItem('HALL_OPENKEYS', JSON.stringify(this.openKeys))
        },
        updateMenu () {
            const routes = this.$route.matched.concat()
            const { hidden } = this.$route.meta
            if (routes.length >= 3 && hidden) {
                routes.pop()
                this.selectedKeys = [routes[routes.length - 1].path]
            } else {
                this.selectedKeys = [routes.pop().path]
            }
            let OPENKEYS = window.sessionStorage.getItem('HALL_OPENKEYS')
            if (OPENKEYS) {
                this.openKeys = JSON.parse(OPENKEYS)
            }
            console.log(this.openKeys)
        },
        // render
        renderItem (menu) {
            if (!menu.hidden) {
                return menu.children && !menu.alwaysShow ? this.renderSubMenu(menu) : this.renderMenuItem(menu)
            }
            return null
        },
        renderMenuItem (menu) {
            let click = data => {
                this.$emit('setCurrentMenuTender', data)
            }
            return (
                <Item {...{ key: menu.path, menuData: menu }} onClick={() => click(menu)}>
                    {/* <tag {...{ props, attrs }}> */}
                    {this.renderIcon(menu.meta.icon)}
                    <span>{menu.meta.title}</span>
                    {/* </tag> */}
                </Item>
            )
        },
        renderSubMenu (menu) {
            const itemArr = []
            if (!menu.alwaysShow) {
                menu.children.forEach(item => itemArr.push(this.renderItem(item)))
            }
            return (
                <SubMenu {...{ key: menu.key }}>
                    <span slot="title">
                        {this.renderIcon(menu.meta.icon)}
                        <span>{menu.meta.title}</span>
                    </span>
                    {itemArr}
                </SubMenu>
            )
        },
        renderIcon (icon) {
            const { userIconFont } = this
            if (icon === 'none' || icon === undefined) {
                return null
            }
            const props = {}
            typeof icon === 'object' ? (props.component = icon) : (props.type = icon)
            return userIconFont ? <icon-font {...{ props }} /> : <Icon {...{ props }} />
        }
    },
    render () {
        const { mode, theme, menu } = this
        const props = {
            mode: mode,
            theme: theme,
            openKeys: this.openKeys
        }
        const on = {
            select: obj => {
                this.selectedKeys = obj.selectedKeys
                this.$emit('select', obj)
            },
            openChange: this.onOpenChange
        }

        const menuTree = menu.map(item => {
            if (item.hidden) {
                return null
            }
            return this.renderItem(item)
        })
        // {...{ props, on: on }}
        return (
            <Menu vModel={this.selectedKeys} {...{ props, on: on }}>
                {menuTree}
            </Menu>
        )
    }
}
