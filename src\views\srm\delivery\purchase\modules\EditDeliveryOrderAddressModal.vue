<template>
  <div>
    <tile-edit-page
      ref="editPage"
      :pageData="pageData"
      :url="url"
      :options="options"
      :orgType="orgType"
      @getOrgType="getOrgType"
      @goBack="goBack" />
    <a-modal
      v-drag    
      forceRender
      :visible="editRowModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
      :width="800"
      @ok="confirmEdit"
      @cancel="closeEditModal">
      <j-editor
        v-if="editRowModal"
        v-model="currentItemContent"></j-editor>
    </a-modal>
  </div>
</template>

<script>
import { tileEditPageMixin } from '@comp/template/tileStyle/tileEditPageMixin'
import { getAction } from '@api/manage'
import REGEXP from '@/utils/regexp'

import JEditor from '@comp/els/JEditor'
export default {
    mixins: [tileEditPageMixin],
    name: 'EditDeliveryOrderAddressModal',
    components: {
        JEditor
    },
    data () {
        return {
            defaultInsertFactory: null,
            editRowModal: false,
            editItemRow: {},
            currentItemContent: '',
            // 地址
            options: [],
            orgType: '',
            pageData: {
                title: '',
                form: {
                    businessType: '',
                    organizationType: '',
                    organizationName: '',
                    remark: '',
                    consignee: '',
                    address: '',
                    purchasePhone: '',
                    provinceId: '',
                    cityId: '',
                    countyId: '',
                    province: '',
                    city: '',
                    county: '',
                    consigneeEmail: '',
                    deliverCompany: '',
                    zip: '',
                    phone: '',
                    jobFunction: '',
                    selectAddress: []
                },
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            list: [
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_organizationType`, '组织类型'),
                                    fieldName: 'organizationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_organizationType`, '组织类型'),
                                    dictCode: 'orgCategoryCode',
                                    changeEvent: this.getTypeValue
                                },
                                {
                                    type: 'serach',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'),
                                    fieldName: 'organizationName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectOrganizationTips`, '请选择组织'),
                                    clickFn: this.changeTacticsObject
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_consignee`, '收货人'),
                                    fieldName: 'consignee',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterConsignee`, '请输入收货人')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receivedPhone`, '收货人联系电话'),
                                    fieldName: 'purchasePhone',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPurchasePhone`, '请输入收货人联系电话')
                                },
                                {
                                    type: 'addressCascader',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProvince`, '选择省份'),
                                    fieldName: 'selectAddress',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProvince`, '选择省份'),
                                    state: 'provinceId',
                                    city: 'cityId',
                                    area: 'countyId',
                                    stateName: 'province',
                                    cityName: 'city',
                                    areaName: 'county'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_adress`, '地址'),
                                    fieldName: 'address',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterAddress`, '请输入地址')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_consigneeEmail`, '收货人的邮箱'),
                                    fieldName: 'consigneeEmail',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterConsigneeEmail`, '请输入收货人的邮箱')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliverCompany`, '收货公司'),
                                    fieldName: 'deliverCompany',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDeliverCompany`, '请输入收货公司')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_postCode`, '邮政编码'),
                                    fieldName: 'zip',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPostalCode`, '请输入邮政编码')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_consigneePlan`, '收货人座机'),
                                    fieldName: 'phone',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterConsigneePlan`, '请输入收货人座机')
                                }
                            ],
                            button: []
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RHzLLD_f74c9a47`, '工厂仓位维护'),
                        content: {
                            type: 'table',
                            ref: 'purchaseFactoryLocationList',
                            height: 400,
                            columns: [
                                { type: 'checkbox', width: 40},
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                { field: 'factory', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RH_bb23d`, '工厂'), editRender: {name: '$select', dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"'}, width: 120},
                                { field: 'storageLocation', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageCode`, '仓位编码'), editRender: {name: '$input'}, width: 120},
                                { field: 'storageLocationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageName`, '仓位名称'), editRender: {name: '$input'}, width: 120 },
                                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qMSWR_c6d6d7bd`, '可卸货数量'), editRender: {name: '$input', props: {type: 'number'}}, width: 120 }
                            ],
                            toolbarButton: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachmet`, '新增'), 
                                    type: 'primary', 
                                    clickFn: this.inSertCallBack, 
                                    showCondition: this.showAdd
                                },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatche`, '删除'), clickFn: this.deleteBatch, showCondition: this.showAdd }
                            ]
                        }
                    }
                ],
                validRules: {
                    consignee: [
                        { required: true, message: '必须填写收货人姓名', trigger: 'blur'}
                    ],
                    purchasePhone: [
                        { required: true, message: '必须填写收货人手机号码', trigger: 'blur'},
                        { pattern: REGEXP.mobile, message: '输入的手机格式不正确，请重新输入'}
                    ],
                    businessType: [
                        { required: false, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterBusinessType`, '请输入业务类型!') }, 
                        { max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}
                    ],
                    operateType: [
                        { required: false, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterOperateType`, '请输入操作类型!') }, 
                        { max: 50, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theContentContainsAMaximumOf50Characters`, '内容长度不能超过50个字符')}
                    ],
                    consigneeEmail: [
                        { required: false, message: '请输入收货人邮箱', trigger: 'blur'}, 
                        { pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/, message: '输入的邮箱格式不正确，请重新输入'}
                    ],
                    address: [
                        { required: false, message: '请输入收货人地址', trigger: 'blur' }
                    ],
                    selectAddress: [
                        { required: false, message: '请选择收货地区', trigger: 'change'}
                    ]
                }
            },
            url: {
                add: '/delivery/deliveryOrderAddress/add',
                edit: '/delivery/deliveryOrderAddress/edit',
                detail: '/delivery/deliveryOrderAddress/queryById'
            }
        }
    },
    methods: {
        deleteRow () {
            this.$refs.editPage.deleteRow()
        },
        addRow () {
            this.$refs.editPage.addRow()
        },
        editRow (row) {
            this.editItemRow = row
            this.currentItemContent = row.msgContent
            this.editRowModal = true
        },
        closeEditModal () {
            this.editRowModal = false
        },
        changeTacticsObject () {
            let item = {}
            let _this = this
            let type = _this.$refs.editPage.getParamsData().organizationType
            if(!type){
                _this.$message.error('请选择组织类型')
                return
            }
            item= {
                selectModel: 'multiple',
                sourceUrl: '/org/purchaseOrganizationInfo/list',
                params: {
                    status: '1',
                    orgCategoryCode: type
                },
                columns: [
                    {field: 'orgCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orgCode`, '组织编码'), with: 150},
                    {field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'), with: 150}
                ]
            }
            this.$refs.editPage.currentSelectModal.selectCallBack = this.selectCallBack
            this.$refs.editPage.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        selectCallBack (data) {
            console.log(this.$refs.editPage.$refs)
            this.$refs.editPage.$refs.baseForm[0].model.organizationName = data[0].orgCode + '_' + data[0].orgName
            this.$refs.editPage.$refs.baseForm[0].model.organizationNo = data[0].orgCode
            this.defaultInsertFactory = data[0].orgCode
        },
        confirmEdit () {
            this.editItemRow.msgContent = this.currentItemContent
            this.editRowModal = false
        },
        getTypeValue (val) {
            this.orgType = val
            this.pageData.form.selectAddress= []
            this.pageData.form.provinceId= ''
            this.pageData.form.cityId= ''
            this.pageData.form.countyId= ''
            this.pageData.form.province= ''
            this.pageData.form.city= ''
            this.pageData.form.county= ''
            if(this.orgType == 'thirdPartyStore') {
                this.pageData.validRules.address[0].required = true
                this.pageData.validRules.consigneeEmail[0].required = true
                this.pageData.validRules.selectAddress[0].required = true
                this.getTreeData()
            } else {
                this.pageData.validRules.address[0].required = false
                this.pageData.validRules.consigneeEmail[0].required = false
                this.pageData.validRules.selectAddress[0].required = false
            }
        },
        getOrgType (val) {
            this.orgType = val
            console.log('orgType', this.orgType)
            if(this.orgType == 'thirdPartyStore') {
                this.pageData.validRules.address[0].required = true
                this.pageData.validRules.consigneeEmail[0].required = true
                this.pageData.validRules.selectAddress[0].required = true
                this.getTreeData()
            } else {
                this.pageData.validRules.address[0].required = false
                this.pageData.validRules.consigneeEmail[0].required = false
                this.pageData.validRules.selectAddress[0].required = false
            }
        },
        getTreeData () {
            this.loading = true
            getAction('/pool/poolAddress/getTreeList', null).then((res) => {
                const { result } = res || {}
                const mode = this.mode || 'executive'
                this.options = result[mode] || []
                this.options = this.formatData(result)
            }).finally(() => {
                this.loading = false
            })
        },
        // 排除空children数组
        formatData (data) {
            for (let i = 0; i < data.length; i++) {
                if (data[i].children.length < 1) {
                    data[i].children = undefined
                } else {
                    this.formatData(data[i].children)
                }
            }
            return data
        },
        showAdd (){
            if(this.pageData.form.organizationType === 'factory' && this.pageData.form.organizationName){
                return true
            }else return false
        },
        inSertCallBack (){
            let insertRow = {factory: this.defaultInsertFactory, storageLocation: '', storageLocationName: '', quantity: ''}
            this.$refs.editPage.$refs.purchaseFactoryLocationList[0].insert(insertRow)
        },
        deleteBatch (){
            this.$refs.editPage.$refs.purchaseFactoryLocationList[0].removeCheckboxRow()
        }
    }
}
</script>