<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      refresh
      :url="url"/>
    <field-select-modal
      ref="fieldSelectModal"/>
    <a-modal
      v-drag
      :width="1000"
      :height="500"
      v-model="fileCompareVisible"
      title="对比结果"
      @ok="fileCompareVisible= false">
      <vxe-grid
        :height="300"
        v-bind="recordGridOptions">
      </vxe-grid>
    </a-modal>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <a-modal
      v-drag
      forceRender
      :visible="editRowModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑')"
      :width="800"
      @ok="confirmEdit"
      @cancel="closeEditModal">
      <j-editor
        v-if="editRowModal"
        v-model="currentItemContent"></j-editor>
    </a-modal>
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="checkEditContractTemplate(previewContent)"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
    <editSimpleContext-Modal
      ref="editSimpleContextModal"
    />
    <select-Data-Modal
      ref="selectDataModal"
      @ok="selectDataOk"/>
  </div>
</template>

<script lang="jsx">

import {EditMixin} from '@comp/template/edit/EditMixin'
import selectDataModal from './selectDataModal'
import Sortable from 'sortablejs'
import ViewItemDiffModal from './ViewItemDiffModal'
import HisContractItemModal from './HisContractItemModal'
import EditSimpleContextModal from './EditSimpleContextModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'
import JEditor from '@comp/els/JEditor'
import flowViewModal from '@comp/flowView/flowView'
import {checkEditContractTemplate} from '@/utils/util'
import {axios} from '@/utils/request'

export default {
    name: 'PurchaseContractHeadModal',
    mixins: [EditMixin],
    components: {
        flowViewModal,
        fieldSelectModal,
        selectDataModal,
        ViewItemDiffModal,
        HisContractItemModal,
        EditSimpleContextModal,
        JEditor
    },
    data () {
        return {
            recordGridOptions: {
                columns: [],
                data: []
            },
            fileCompareVisible: false,
            checkEditContractTemplate: checkEditContractTemplate,
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            notShowTableSeq: true,
            editItemRow: {},
            selectType: '',
            fileRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            pageData: {
                form: {
                    contractNumber: '',
                    contractName: '',
                    contractDesc: '',
                    contractSignAddress: '',
                    contractSignDate: '',
                    currency: '',
                    contractTemplateNumber: '',
                    contractTemplateName: '',
                    contractTemplateVersion: '',
                    auditStatus: '0',
                    contractStatus: '1',
                    contractVersion: '1',
                    contractType: '1',
                    companyCode: '',
                    companyName: '',
                    purchaseOrgCode: '',
                    purchaseGroupCode: '',
                    supplierName: '',
                    toElsAccount: '',
                    supplierCode: '',
                    purchasePrincipal: '',
                    templateName: '',
                    templateId: '',
                    templateNumber: '',
                    templateVersion: '',
                    totalTaxAmount: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseContractLineInfo`, '采购合同行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractItemList',
                            columns: [],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.insertGridItem
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteGridItem
                                }
                            ],
                            notShowTableSeq: true
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_neCc_27788ad8`, '合同内容'),
                        groupCode: 'itemContentInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseContractContentItemList',
                            columns: [
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'itemVersion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectNumber`, '项目版本'),
                                    width: 120
                                },
                                {
                                    field: 'itemType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_itemType`, '项目类型'),
                                    width: 120,
                                    dictCode: 'srmItemType'
                                },
                                {
                                    field: 'sourceType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    width: 120,
                                    dictCode: 'srmContractContentSourceType'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 150,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cr_a130b`, '修改')}
                                                onClick={() => this.editContentItemRow(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cr_a130b`, '修改')}  </a>)
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mA_d0bc6`, '查看')}
                                                onClick={() => this.viewDetail(row)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mA_d0bc6`, '查看')}</a>)
                                            return resultArray
                                        }
                                    }
                                }

                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'),
                                    type: 'primary',
                                    click: this.addContentItemRow
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    click: this.deleteContentGridItem
                                }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'purchaseAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 180
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'uploadSubAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'contractSimple',
                                    attr: this.attrHandle,
                                    callBack: this.uploadCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                                    click: this.deleteBatch
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    type: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteFilesEvent
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对'),
                                    clickFn: this.fileCompare,
                                    authorityCode: 'compare#elsFileCompareHead:edit'
                                },
                                {
                                    key: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#`, '比对结果'),
                                    clickFn: this.fileCompareResult,
                                    authorityCode: 'compare#elsFileCompareHead:list'
                                }
                            ]
                            // showOptColumn: true,
                            // optColumnList: [
                            //   {type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent},
                            //   { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                            // ],
                            // buttons: [
                            //     {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'), type: 'upload', businessType: 'contract', callBack: this.uploadCallBack},
                            //     {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.previewEvent},
                            //     {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            // ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                        authorityCode: 'contract#purchaseContractHead:edit',
                        type: 'primary',
                        click: this.saveEvent /*showCondition: this.showcEditConditionBtn*/
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_DJUz_2e91facc`, '提交审批'),
                        type: 'primary',
                        click: this.submitAudit,
                        showCondition: this.showAuditConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_previewContractText`, '预览合同文本'),
                        authorityCode: 'contract#purchaseContractHead:getPreviewData',
                        type: 'primary',
                        click: this.previewPdf
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionChange`, '版本变更'),
                        type: 'primary',
                        click: this.saveEvent,
                        showCondition: this.showUpgradeConditionBtn
                    },
                    //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_examineApprove`, '审批'), type: 'primary', click: this.submitAudit, showCondition: this.showcAuditConditionBtn},
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                        type: 'primary',
                        click: this.cancelAudit,
                        id: 'cancelAudit',
                        showCondition: this.showcCncelConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                        type: '',
                        click: this.showFlow,
                        id: 'showFlow',
                        showCondition: this.showFlowConditionBtn
                    },
                    //{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                add: '/contract/purchaseContractHead/add',
                edit: '/contract/purchaseContractHead/edit',
                isExistFrozen: 'supplier/supplierMaster/isExistFrozenStateData',
                audit: '/a1bpmn/audit/api/submit',
                detail: '/contract/purchaseContractHead/queryById',
                //public: '/contract/purchaseContractHead/publish',
                upload: '/attachment/purchaseAttachment/upload',
                submitAudit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_contractSimple_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    mounted () {
        this.pageData.groups.splice(0, 1)
    },
    created () {
        this.rowDrop()
    },
    beforeDestroy () {
        if (this.sortable) {
            this.sortable.destroy()
        }
    },
    methods: {
        formatPageData(item) {
            console.log("格式化数据", item)
            if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
                item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
            }
            return item;
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.contractNumber,
                actionRoutePath: '/srm/contract/purchase/PurchaseContractHeadListSimple'
            }
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        preview () {
            let contentGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            let voucherId = this.$refs.editPage.voucherId
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            if (!voucherId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }

            this.$refs.editPage.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: voucherId}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        fileCompareResult (row) {
            this.confirmLoading = true
            getAction('/compare/elsFileCompareHead/getResultById', {id: row.id}).then((res) => {
                if (res.success) {
                    if (res.result.elsFileCompareResultList && res.result.elsFileCompareResultList.length == 1) {
                        let filePath = res.result.elsFileCompareResultList[0].filePath
                        this.$previewFile.open({params: {}, path: filePath})
                    } else {
                        this.recordGridOptions.columns = [
                            {
                                title: '比对时间',
                                fieldLabelI18nKey: 'i18n_field_lIKI_326a4423',
                                field: 'createTime',
                                helpText: ''
                            },
                            {
                                title: '文件类型',
                                fieldLabelI18nKey: 'i18n_field_fileType',
                                field: 'fbk2_dictText',
                                helpText: ''
                            },
                            {
                                title: '文件名称',
                                fieldLabelI18nKey: 'i18n_title_fileName',
                                field: 'fileName',
                                helpText: ''
                            },
                            {
                                title: '文件路径',
                                fieldLabelI18nKey: 'i18n_field_filePath',
                                field: 'filePath',
                                helpText: ''
                            },
                            {
                                title: '操作',
                                fieldLabelI18nKey: 'i18n_title_operation',
                                field: 'sourceType_dictText',
                                headerAlign: 'center',
                                slots: {
                                    default: ({row}) => {
                                        let resultArray = []
                                        resultArray.push(<a
                                            title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}
                                            onClick={() => this.resultView(row)}> {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '预览')}</a>)
                                        resultArray.push(<a
                                            title={this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}
                                            style="margin-left:8px"
                                            onClick={() => this.resultDownload(row)}> {this.$srmI18n(`${this.$getLangAccount()}#`, '下载')}</a>)
                                        return resultArray
                                    }
                                }
                            }
                        ]
                        this.recordGridOptions.data = res.result.elsFileCompareResultList
                        this.fileCompareVisible = true
                    }
                }else {
                    this.$message.warning(res.message)
                }
            }
            ).finally(() => {
                this.confirmLoading = false
            })
            /*this.$router.push({
                path: '/srm/compare/ElsFileCompareHeadList',
                query: {sourceId: row.id}
            })*/
        },
        resultView (row) {
            this.$previewFile.open({params: {}, path: row.filePath})
        },
        resultDownload (row) {
            let link = document.createElement('a')
            link.style.display = 'none'
            link.href = row.filePath
            link.setAttribute('download', row.fileName)
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link) //下载完成移除元素
            window.URL.revokeObjectURL(row.filePath) //释放掉blob对象
        },
        compareFileTypes (file1, file2) {
            const getFileType = (filename) => {
                const fileExtension = filename.split('.').pop()
                return fileExtension
            }
            const type1 = getFileType(file1)
            const type2 = getFileType(file2)
            if (type1 == 'doc' && type2 == 'docx' || type1 == 'docx' && type2 == 'doc') return true
            if (type1 == 'xlsx' && type2 == 'xls' || type1 == 'xls' && type2 == 'xlsx') return true
            return type1 === type2
        },
        fileCompare (row) {
            this.fileRow = row
            this.selectType = 'file'
            let item = {
                selectModel: 'single',
                sourceUrl: '/attachment/purchaseAttachment/listFileCompare',
                params: {
                    businessType: 'contractSimple'
                },
                columns: [
                    {
                        field: 'businessType_dictText',
                        fieldLabelI18nKey: 'i18n_field_businessType',
                        title: '业务类型',
                        with: 150
                    },
                    {
                        field: 'fileType_dictText',
                        fieldLabelI18nKey: 'i18n_field_fileType',
                        title: '文件类型',
                        with: 150
                    },
                    {
                        field: 'uploadElsAccount',
                        fieldLabelI18nKey: 'i18n_field_uploadElsAccount',
                        title: '文件上传方账号',
                        with: 150
                    },
                    {
                        field: 'uploadSubAccount',
                        fieldLabelI18nKey: 'i18n_title_fileUploadPartySubAccount',
                        title: '文件上传方子账号',
                        with: 150
                    },
                    {
                        field: 'uploadTime',
                        fieldLabelI18nKey: 'i18n_title_uploadTime',
                        title: '上传时间',
                        with: 150
                    },
                    {
                        field: 'fileName',
                        fieldLabelI18nKey: 'i18n_title_fileName',
                        title: '文件名称',
                        with: 150
                    }, {
                        field: 'filePath',
                        fieldLabelI18nKey: 'i18n_field_filePath',
                        title: '文件路径',
                        with: 150
                    }, {
                        field: 'fileSize',
                        fieldLabelI18nKey: 'i18n_field_fileSize',
                        title: '文件大小',
                        with: 150
                    }
                ]
            }
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
        },
        previewPdf () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            if (!this.currentEditRow.id) {
                return this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_sMSniTUB_2fb499f5`, '保存后才允许预览'))
            }
            this.confirmLoading = true
            axios({
                url: '/contract/purchaseContractHead/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res.type === 'application/json') {
                    const fileReader = new FileReader()
                    fileReader.onloadend = () => {
                        const jsonData = JSON.parse(fileReader.result)
                        this.$message.error(jsonData.message)
                    }
                    fileReader.readAsText(res)
                    return
                }

                let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                window.open(url)
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        //新增行
        insertGridItem () {
            let pageData = this.$refs.editPage ? this.$refs.editPage.form : {}
            this.selectType = 'cocent'
            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectSupplierFirst`, '请先选择供应商！'))
                return
            }
            let params = {toElsAccount: pageData.toElsAccount}
            this.$refs.selectDataModal.open(params)
        },
        fieldSelectOk (data) {
            if (this.selectType == 'file') {
                let flag = this.compareFileTypes(this.fileRow.fileName, data[0].fileName)
                if (!flag) return this.$message.warning('不同类型文件不做对比')
                this.$refs.editPage.confirmLoading = true
                let fileCompareRow = {
                    fileASourceId: this.fileRow.id,
                    fileABusinessType: this.fileRow.businessType,
                    fileAType: this.fileRow.fileType,
                    fileAUploadElsAccount: this.fileRow.uploadElsAccount,
                    fileAUploadSubAccount: this.fileRow.uploadSubAccount,
                    fileAUploadTime: this.fileRow.uploadTime,
                    fileAName: this.fileRow.fileName,
                    fileAPath: this.fileRow.filePath,
                    fileASize: this.fileRow.fileSize,
                    fileBSourceId: data[0].id,
                    fileBBusinessType: data[0].businessType,
                    fileBType: data[0].fileType,
                    fileBUploadElsAccount: data[0].uploadElsAccount,
                    fileBUploadSubAccount: data[0].uploadSubAccount,
                    fileBUploadTime: data[0].uploadTime,
                    fileBName: data[0].fileName,
                    fileBPath: data[0].filePath,
                    fileBSize: data[0].fileSize
                }
                postAction('/compare/elsFileCompareHead/fileCompare', fileCompareRow).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    this.$refs.editPage.confirmLoading = false
                })
            } else if (this.selectType == 'cocent') {
                let detailGrid = this.$refs.editPage.$refs.purchaseContractItemList[0]
                let tableData = detailGrid.getTableData().fullData
                let addTableData = []
                data.forEach((item, i) => {
                    let lineNum = tableData.length + (i + 1)
                    addTableData.push({
                        itemNumber: lineNum,
                        sourceType: item.sourceType,
                        sourceType_dictText: item.sourceType_dictText,
                        sourceNumber: item.sourceNumber,
                        sourceItemNumber: item.sourceItemNumber,
                        taxAmount: item.taxAmount,
                        netAmount: item.netAmount || 0,
                        currency: item.currency,
                        price: item.price,
                        quantityUnit: item.quantityUnit,
                        quantity: item.quantity,
                        materialSpec: item.materialSpec,
                        materialDesc: item.materialDesc,
                        taxCode: item.taxCode,
                        taxRate: item.taxRate,
                        materialNumber: item.materialNumber
                    })
                })
                detailGrid.insertAt(addTableData, -1)
                this.calculateAmount()
            }

        },
        calculateAmount () {
            let detailGrid = this.$refs.editPage.$refs.purchaseContractItemList[0].getTableData().tableData
            let totalTaxAmount = 0
            let totalNetAmount = 0
            detailGrid.forEach(item => {
                if (item.taxAmount) {
                    totalTaxAmount += Number(item.taxAmount)
                }
                if (item.netAmount) {
                    totalNetAmount += Number(item.netAmount)
                }
            })
            if (totalTaxAmount === 0 || Math.abs(totalTaxAmount) > 0) {
                totalTaxAmount = Number(totalTaxAmount).toFixed(2)
            }
            this.$refs.editPage.form.totalTaxAmount = totalTaxAmount
            this.$refs.editPage.form.totalNetAmount = totalNetAmount
        },
        //删除复选框选定行
        deleteGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseContractItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
            this.calculateAmount()
        },
        addContentItemRow () {
            this.$refs.editSimpleContextModal.open()
        },
        editContentItemRow (row) {
            this.$refs.editSimpleContextModal.open(row)
        },
        fieldContentItemOk (data) {
            if (data.itemContent == '') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_CcxOLVW_60c5e69d`, '内容不能为空'))
                return false
            }
            let detailGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            let addTableData = []
            detailGrid.remove()
            addTableData.push({
                itemNumber: '1',
                itemName: data.itemName,
                itemVersion: data.itemVersion,
                itemType: data.itemType,
                itemType_dictText: data.itemType_dictText,
                itemContent: data.itemContent,
                originalContent: data.itemContent,
                itemId: data.id,
                changeFlag: '0',
                sourceType: '3',
                sourceType_dictText: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_lOcI_2def0ebc`, '手动创建')
            })
            let conent = ''
            conent = addTableData.map(item => {
                return item.itemContent
            })
            this.businessTemplate = conent.join('')
            detailGrid.insertAt(addTableData, -1)
            return true
        },
        addSelectModel (id) {
            getAction('/contract/purchaseContractTemplateHead/queryById', {id: id}).then(res => {
                if (res.success) {
                    res.result = this.formatPageData(res.result);
                    let detailGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
                    detailGrid.remove()
                    let tableData = detailGrid.getTableData().fullData

                    let addTableData = []
                    res.result.purchaseContractTemplateItemList.forEach((item, i) => {
                        let lineNum = tableData.length + (i + 1)
                        addTableData.push({
                            itemNumber: lineNum,
                            itemName: item.itemName,
                            itemVersion: item.itemVersion,
                            itemType: item.itemType,
                            itemType_dictText: item.itemType_dictText,
                            itemContent: item.itemContent,
                            originalContent: item.itemContent,
                            itemId: item.id,
                            changeFlag: '0',
                            sourceType: '1',
                            sourceType_dictText: '合同模板'
                        })
                    })
                    let conent = ''
                    conent = res.result.purchaseContractTemplateItemList.map(item => {
                        return item.itemContent
                    })
                    this.businessTemplate = conent.join('')
                    detailGrid.insertAt(addTableData, -1)
                }
            })
        },
        //删除复选框选定行
        deleteContentGridItem () {
            let itemGrid = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        editRow (row) {
            this.editItemRow = row
            this.currentItemContent = row.itemContent
            this.editRowModal = true
        },
        closeEditModal () {
            this.editRowModal = false
        },
        confirmEdit () {
            this.editItemRow.itemContent = this.currentItemContent
            let changeFlag = '0'
            if (this.editItemRow.itemContent != this.editItemRow.originalContent) {
                changeFlag = '1'
            }
            this.editItemRow.changeFlag = changeFlag
            this.editRowModal = false
        },
        rowDrop () {
            this.$nextTick(() => {
                let contractBuyContentItemList = this.$refs.editPage.$refs.purchaseContractContentItemList[0]
                this.sortable = Sortable.create(contractBuyContentItemList.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
                    handle: '.drag-btn',
                    onEnd: ({newIndex, oldIndex}) => {
                        let {fullData} = contractBuyContentItemList.getTableData()
                        let tableData = [...fullData]
                        let currRow = tableData.splice(oldIndex, 1)[0]
                        tableData.splice(newIndex, 0, currRow)
                        tableData = tableData.map((item, index) => {
                            item.itemNumber = index + 1
                            return item
                        })
                        contractBuyContentItemList.loadData(tableData)
                        contractBuyContentItemList.syncData()
                    }
                })
            })
        },
        uploadCallBack (result) {
            debugger
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => n.id).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        goBack () {
            this.$emit('hide')
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        showcEditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let contractStatus = params.contractStatus
            if (contractStatus == '1' || contractStatus == '5') {
                return true
            } else {
                return false
            }
        },
        showcAuditConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let contractStatus = params.contractStatus
            if ((params.id)) {
                return true
            }
            if (contractStatus == '1' || contractStatus == '5') {
                return true
            } else {
                return false
            }
        },
        showUpgradeConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let contractStatus = params.contractStatus
            if (contractStatus != '4') {
                return false
            } else {
                return true
            }
        },
        /*submitAudit () {
            const _this = this
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                const cb = () => {
                    const param = {
                        businessId: data.id,
                        rootProcessInstanceId: data.flowId || '',
                        businessType: 'contract',
                        auditSubject: `合同编号：${data.contractNumber}`,
                        ...data
                    }
                    postAction('/a1bpmn/audit/api/submit', param ).then(res =>  {
                        const type = res.success ? 'success' : 'error'
                        _this.$message[type](res.message)
                        _this.goBack()
                        //this.$parent.submitCallBack(data)
                    })
                }
                console.log('_this :>> ', _this)
                _this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitApproval`, '提交审批'),
                    content: '提交审批后将不能修改，是否确认提交审批?',
                    onOk () {
                        cb && cb()
                    },
                    onCancel () {
                        console.log('onCancel')
                    }
                })
            }
            this.$refs.editPage.handleSend('', fn)
        },*/
        submitAudit () {
            const _this = this
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if ((!params.id)) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return
            }
            let thisData = this.$refs.editPage.$refs.purchaseContractContentItemList[0].getTableData().fullData
            if (!thisData || thisData.length === 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractTermLibraryInformationNeedsConfigured`, '需配置合同条款库信息!'))
                return
            }
            const fn = (data, vm) => {
                console.log('data :>> ', data)
                console.log('vm :>> ', vm) // 编辑模板组件实例
                // 提交审批时去除合同内容
                data.purchaseContractContentItemList = []
                const param = {
                    businessId: data.id,
                    rootProcessInstanceId: data.flowId || '',
                    businessType: 'contract',
                    auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractNoCode`, '合同编号')}：${data.contractNumber} ${data.contractName || ''}`,
                    params: JSON.stringify(data)
                }

                const frozen = {
                    toElsAccount: params.toElsAccount,
                    frozenFunction: '3',
                    orgType: '0',
                    orgCode: params.purchaseOrg
                }
                // 检测供应商对否被冻结
                if ((frozen.toElsAccount && frozen.toElsAccount.length > 0) && (frozen.orgCode && frozen.orgCode.length > 0)) {
                    postAction(_this.url.isExistFrozen, frozen).then(rest => {
                        if (rest.success) {
                            postAction('/a1bpmn/audit/api/submit', param).then(res => {
                                const type = res.success ? 'success' : 'error'
                                _this.$message[type](res.message)
                                //_this.goBack()
                                _this.$parent.submitCallBack(data)
                            })
                        } else {
                            _this.$message.warning(rest.message)
                        }
                    })
                } else {
                    postAction('/a1bpmn/audit/api/submit', param).then(res => {
                        const type = res.success ? 'success' : 'error'
                        _this.$message[type](res.message)
                        //_this.goBack()
                        _this.$parent.submitCallBack(data)
                    })
                }

            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_saveAndApprove`, '保存并审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterSavingCannotModifiedSureSaveSubmitForApproval`, '保存提交审批后将不能修改，是否确认保存并提交审批?'),
                onOk () {
                    _this.$refs.editPage.handleSend('', fn)
                },
                onCancel () {
                    console.log('onCancel')
                }
            })

        },
        auditPostData (invokeUrl) {
            this.$refs.editPage.confirmLoading = true
            let formData = this.$refs.editPage.form
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'contract'
            param['auditSubject'] = '合同编号：' + formData.contractNumber + ' ' + formData.contractName || ''
            param['params'] = JSON.stringify(formData)
            postAction(invokeUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        cancelAudit () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.init()
                    that.auditPostData(that.url.cancelAudit)
                }
            })
        },
        showcCncelConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus != '1') {
                return false
            } else {
                return true
            }
        },
        showFlowConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            let auditStatus = params.auditStatus
            if (auditStatus == '0' || auditStatus == '3') {
                return false
            } else {
                return true
            }
        },
        showFlow () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            this.flowId = params.flowId
            this.flowView = true
        },
        closeFlowView () {
            this.flowView = false
        },
        handleCancel () {
            this.visible = false
        }
    }
}
</script>