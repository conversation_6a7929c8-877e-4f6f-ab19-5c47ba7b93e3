<template>
  <div class="quotesItem">
    <div
      v-for="(item, i) in quotesList"
      :key="i"
      style="margin-top: 10px">
      <titleCrtl>
        <div>
          <span class="marging_right_10">{{ $srmI18n(`${$getLangAccount()}#i18n_field_essu_30ba66ba`, '最终报价') }} </span>
          <span class="marging_right_10">{{ $srmI18n(`${$getLangAccount()}#i18n_field_suyRKI_db7df728`, '报价截止时间') }}  {{ item.quotedPriceEndTime }}</span>
          <span>{{ item.status_dictText }}</span>
        </div>
        <template slot="right">
          <a-button
            v-if="item.status == '0' && isEdit"
            type="primary"
            :loading="loading"
            @click="handleCloseQuotes(item.id)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_yWsu_3b36f8de`, '结束报价') }}</a-button>
        </template>
      </titleCrtl>
      <listTable
        :ref="`listTable` + i"
        :setGridHeight="'auto'"
        :fromSourceData="item.tenderEvaQuotedPriceItemList"
        :showTablePage="false"
        :statictableColumns="statictableColumns"
      >
      </listTable>
    </div>
  </div>
</template>
<script lang="jsx">
import { getAction } from '@/api/manage'
import titleCrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
export default {
    props: {
        quotesList: {
            type: Array,
            default: () => []
        },
        pageStatus: {
            type: String,
            default: 'edit'
        },
        determineType: {
            type: String,
            default: 'total'
        }
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit' ? true : false
        }
    },
    data () {
        return {
            statictableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'width': 100,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suzE_2e0e107d`, '报价状态'),
                    'field': 'status_dictText'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suKI_2e0cbb30`, '报价时间'),
                    'field': 'quotedPriceTime'
                },
                {
                    'width': 200,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suVH_2e09d920`, '报价信息'),
                    slots: {
                        default: ({row}) => {
                            let list = row.quotedPrice ? row.quotedPrice.map(item => {
                                return <div class={'marging_bottom_4'}>
                                    <span class={'marging_right_10'}>{item.title}</span>
                                    <span class={'marging_right_10'}>{item.price}</span>
                                    <span class={'marging_right_10'}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名')}{item.sequence}</span>
                                </div>
                            }) : []
                            return [...list]
                        }
                    }
                },
                {
                    'width': 120,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    'field': 'ctrl',
                    slots: {
                        default: ({row}) => {
                            return row.headStatus == '1' ?[
                                <a-button type='link' onClick={() => {
                                    if (this.loading) return
                                    this.view(row)
                                }}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_view`, '查看')}</a-button>
                            ] : []
                        }
                    }
                }
            ],
            loading: false,
            url: {
                finish: '/tender/evaluation/tenderEvaQuotedPriceHead/finish',
                view: '/tender/evaluation/tenderEvaQuotedPriceHead/queryByItemId'
            }
        }
    },
    components: {
        titleCrtl,
        listTable
    },
    methods: {
        handleCloseQuotes (id) {
            this.loading = true
            getAction(this.url.finish, {id}).then(res => {
                let type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.$emit('closeQuotes')
                }
            }).finally(() => {
                this.loading = false
            })
        },
        view (row) {
            this.loading = true
            getAction(this.url.view, {itemId: row.id}).then(res => {
                if (res.success) {
                    this.$emit('showPriceList', res.result)
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        }
    },
    created () {
        if (this.determineType == 'sub') {
            this.statictableColumns.splice(4, 1)
        }
    }
}
</script>
<style lang="less" scoped>
.quotesItem{
    .marging_right_10{
        margin-right: 10px;
    }
    .marging_bottom_4 {
        padding: 2px 0;
    }
    :deep(.vxe-table--render-default .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
    }
    :deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis>.vxe-cell){
        max-height: none;
    }
}

</style>