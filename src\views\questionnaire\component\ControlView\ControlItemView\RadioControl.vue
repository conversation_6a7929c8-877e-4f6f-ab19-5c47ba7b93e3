<template>
  <!-- :label="`${index}.${attr.label}`" -->
  <a-form-item
    :label="attr.label"
    :label-col="{ span: attr.layout }"
    :wrapper-col="{ span: attr.layout === 24 ? 24 : 24 - attr.layout }"
    :required="attr.rules.length > 0"
  >
    <a-radio-group :default-value="attr.initialValue">
      <template v-for="(el, idx) of attr.options">
        <a-radio
          :style="radioStyle"
          :value="el.value"
          :key="idx">
          {{ String.fromCharCode((65 + idx)) }}.{{ el.label }}
        </a-radio>
      </template>
    </a-radio-group>
  </a-form-item>
</template>

<script>
import { mapState } from 'vuex'
export default {
    name: 'RadioControl',
    inject: ['index'],
    props: {
        data: {
            type: Object,
            required: true
        }
    },
    data () {
        return {
            radioStyle: {
                display: 'block',
                height: '30px',
                lineHeight: '30px'
            }
        }
    },
    computed: {
        attr () {
            return this.data.attr
        },
        ...mapState({
            formData: state => state.formDesigner.formData
        })
    }
}
</script>

<style lang="less" scoped>

</style>
