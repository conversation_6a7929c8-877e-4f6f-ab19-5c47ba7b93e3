<template>
  <div class="SignUpManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="showLayout"
        :ref="businessRefName"
        :currentEditRow="currentEditRowData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :fromSourceData="fromSourceData"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>
      <a-modal
        v-drag     
        v-model="visible" 
        :width="800"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_sRUJtH_20284311`, '报名审查记录')"
        :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')">
        <template slot="footer">
          <a-button @click="() => {this.visible=false}">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        </template>
        <vxe-grid
          v-bind="gridOptions"></vxe-grid>
      </a-modal>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT, USER_COMPANYSET } from '@/store/mutation-types'
import { baseMixins } from '../plugins/baseMixins.js'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    name: 'SignUpManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        fieldSelectModal
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            templateMsg: {},
            visible: false,
            gridOptions: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 300,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 禁用自定义工具
                },
                columns: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50},
                    {field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJL_164dad6`, '审查人'), width: 150},
                    {field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJKI_2b39e622`, '审查时间'), width: 150},
                    {field: 'status_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_reviewStatus`, '审查状态'), width: 140},
                    {field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJII_2b3941f6`, '审查意见')}
                ],
                data: []
            },
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UJtH_2b3e4109`, '审查记录'),
                    click: () => {this.visible=true},
                    key: 'record'
                }
            ],
            attachmentListData: {},
            fromSourceData: {},
            url: {
                detail: '/tender/sale/supplierTenderProjectSignUp/queryBySubpackageId'
            },
            showLayout: false,
            userInfo: {},
            projectObj: {},
            queryData: {},
            currentEditRowData: {},
            remoteJsFilePath: '',
            formatType: {}
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    created () {
        this.init()
        this.getQueryFileType()
    },
    methods: {
        // 获取文件类型数据字典
        getQueryFileType () {
            // 获取表格下拉字典
            let postData = {
                busAccount: this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'supplierSignUpFileType'
            }
            ajaxFindDictItems(postData).then(res => {
                if (res.success) {
                    const RESULTDATA = res.result || []
                    let fileType = {}
                    RESULTDATA.forEach(data => {
                        fileType[data.value] = data.title
                    })
                    this.formatType = fileType
                    console.log(this.formatType)
                }
            })
        },
        // 获取业务模板信息
        // async getBusinessTemplate () {
        //     this.templateMsg = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
        //         templateNumber: this.fromSourceData.templateNumber,
        //         templateName: this.fromSourceData.templateName,
        //         templateVersion: this.fromSourceData.templateVersion,
        //         templateAccount: this.fromSourceData.templateAccount
        //     } : await this.getBusinessTemplate('SupplierTenderProjectSignUp').then(rs=>{
        //         return rs
        //     })
        //     console.log('123', this.templateMsg)
        //     this.remoteJsFilePath = `${this.templateMsg['templateAccount']}/sale_SupplierTenderProjectSignUp_${this.templateMsg['templateNumber']}_${this.templateMsg['templateVersion']}`
        //     // let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectSignUp'}
        //     // return getAction('/template/templateHead/getListByType', params).then(res => {
        //     //     if(res.success) {
        //     //         if(res.result.length > 0) {
        //     //             let options = res.result.map(item => {
        //     //                 return {
        //     //                     templateNumber: item.templateNumber,
        //     //                     templateName: item.templateName,
        //     //                     templateVersion: item.templateVersion,
        //     //                     templateAccount: item.elsAccount
        //     //                 }
        //     //             })
        //     //             this.currentEditRowData = options[0]
        //     //             this.remoteJsFilePath = `${this.currentEditRowData['templateAccount']}/sale_SupplierTenderProjectSignUp_${this.currentEditRowData['templateNumber']}_${this.currentEditRowData['templateVersion']}`
        //     //         } else {
        //     //             this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
        //     //         }
        //     //     }else {
        //     //         this.$message.warning(res.message)
        //     //     }
        //     // })
        // },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        async handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)
            // -- start -- sb xiefa
            // pageConfig.groups.forEach(group => {
            //     if (group.groupCode == 'attachmentList') {
            //         group.loadData.forEach(load => {
            //             debugger
            //             load['fileType_dictText'] = this.formatType[load.fileType] || ''
            //             load['fileType'] = this.formatType[load.fileType] || ''
            //             console.log(this.formatType, load.fileType)
            //             console.log(load)
            //         })
            //     }
            // })
            // -- end ---
            this.fromSourceData.attachmentList.forEach(item=>{
                item.fileType_dictText = this.formatType[item.fileType] || ''
            })
            let {consortiumBidding} = resultData || this.subpackage
            // 非允许联合体情况
            if(consortiumBidding != 1 ){
                pageConfig.groups[0].formModel.combination = '0'
                pageConfig.groups[0].formModel.combination_dictText = '否'
            }
            console.log(pageConfig)
            // 不允许联合体，则是否联合体为否
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            // 不允许联合体情况
            let flag = (consortiumBidding != 1)
            setDisabledByProp('combination', flag)
            setDisabledByProp('combinationName', flag)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            // 不允许联合体情况
            let validateFlag = (consortiumBidding != 1)
            setValidateRuleByProp('combination', validateFlag)
            setValidateRuleByProp('combinationName', validateFlag)

            this.gridOptions.data = resultData.tenderProjectSignUpRejectList || []

            pageConfig.groups[1]['extend'] = {
                optColumnList: [
                    { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                    { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                ]
            }
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierName') {
                    formModel[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
                if (key == 'supplierAccount') {
                    formModel[key] = resultData[key] || this.userInfo.elsAccount
                }
            }
        },
        checkItemSelectOk (data) {
            console.log(data)
        },
        preViewEvent (Vue, row){
            row.subpackageId = this.subId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        async downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const fileName = row.fileName
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        getSubPageData () {
            return getAction(this.url.detail, {subpackageId: this.subId}).then(async (res) => {
                if(res.success) {
                    this.fromSourceData = res.result || {}
                    this.templateMsg = this.fromSourceData.templateNumber && this.fromSourceData.templateAccount ? {
                        templateNumber: this.fromSourceData.templateNumber,
                        templateName: this.fromSourceData.templateName,
                        templateVersion: this.fromSourceData.templateVersion,
                        templateAccount: this.fromSourceData.templateAccount
                    } : await this.getBusinessTemplate('SupplierTenderProjectSignUp').then(rs=>{
                        return rs
                    })
                    if (!this.templateMsg) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                        return
                    }
                    this.remoteJsFilePath = `${this.templateMsg['templateAccount']}/sale_SupplierTenderProjectSignUp_${this.templateMsg['templateNumber']}_${this.templateMsg['templateVersion']}`
                }
            })
        },
        init () {
            this.confirmLoading = true
            Promise.all([this.getSubPageData()]).then(() => {
                this.showLayout = true
                this.confirmLoading = false
            }).catch((error) => {
                this.confirmLoading = false
            })
        }
    }
}
</script>

<style lang="less" scoped>
.registration-title{
    background-color: #eee;
    padding: 5px 10px;
    margin-bottom: 10px;
}
</style>


