<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showOnlinePage && !detailOnlinePage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <EditFadadaSignTaskSale-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideEditPage" />
    <ViewFadadaSignTaskSale-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <EditFadadaSignTaskSaleOnlineModal
      v-if="showOnlinePage"
      @handleChidCallback="handleChidCallback"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <ViewFadadaSignTaskSaleOnlineModal
      v-if="detailOnlinePage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import EditFadadaSignTaskSaleModal from './modules/EditFadadaSignTaskSaleModal'
import EditFadadaSignTaskSaleOnlineModal from './modules/EditFadadaSignTaskSaleOnlineModal'
import ViewFadadaSignTaskSaleOnlineModal from './modules/ViewFadadaSignTaskSaleOnlineModal'
import ViewFadadaSignTaskSaleModal from './modules/ViewFadadaSignTaskSaleModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import { httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditFadadaSignTaskSaleModal,
        ViewFadadaSignTaskSaleModal,
        EditFadadaSignTaskSaleOnlineModal,
        ViewFadadaSignTaskSaleOnlineModal
    },
    data () {
        return {
            showEditPage: false,
            showOnlinePage: false,
            detailOnlinePage: false,
            pageData: {
                businessType: 'fadada',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWdD_3a0b6871`, '签署主题')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'fadada#fadadaSignTaskSale:view', clickFn: this.handleView},
                    {type: 'edit', title: '签署人（或文件）维护', authorityCode: 'fadada#fadadaSignTaskSale:edit', clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'download', title: '查看签署文档', authorityCode: 'fadada#fadadaSignTaskSale:signFileDownload', clickFn: this.handleDownload, allow: this.showDownloadCondition},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 300
            },
            url: {
                list: '/electronsign/fadada/fadadaSignTaskSale/list',
                add: '/electronsign/fadada/fadadaSignTaskSale/add',
                cancel: '/electronsign/fadada/fadadaSignTaskSale/cancel',
                signFileDownload: '/electronsign/fadada/fadadaSignTaskSale/signFileDownload',
                exportXlsUrl: 'fadada/fadadaSignTaskSale/exportXls',
                columns: 'fadadaSignTaskSaleList'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/electronsign/fadada/fadadaSignTaskSale/counts')
    },
    methods: {
        showDownloadCondition (row){
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.signTaskStatus==='task_finished'){
                return false
            }
            return true
        },
        // 返回按钮
        hideEditPage () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) {
                // 关闭超链接页面
                this.closeCurrent()
            }
            // this.showDetailOnePage= false
            this.showEditPage = false
            this.showDetailPage = false
            this.showOnlinePage = false
            this.detailOnlinePage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        handleView (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showOnlinePage = false
            if(row.onlineSealed==='1'){
                this.showDetailPage = false
                this.detailOnlinePage = true
            }else{
                this.detailOnlinePage = false
                this.showDetailPage = true
            }
        },
        handleChidCallback () {
            this.hideEditPage()
        },
        handleEdit (row){
            this.currentEditRow = row
            this.showDetailPage = false
            this.detailOnlinePage = false
            if(row.onlineSealed==='1'){
                this.showEditPage = false
                this.showOnlinePage = true
            }else{
                this.showOnlinePage = false
                this.showEditPage = true
            }
        },
        showEditCondition (row) {
            //已退回的单据直接置灰
            if(row.voucherSendBackFlag==='1'){
                return true
            }
            if(row.saleSignerSubmintStatus==='1'){
                return true
            }
            if(row.saleFileUploaded==='1'){
                return true
            }
            return false
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack (){
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        cancel (row){
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
                onOk: function () {
                    that.postUpdateData(that.url.cancel, row)
                }
            })
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('法大大签署任务（采购）')
        },
        handleDownload (row){            
            httpAction(this.url.signFileDownload, {id: row.relationId}, 'get').then((res) => {
                if (res.success) {
                    window.open(res.message, '_blank')
                } else {
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>