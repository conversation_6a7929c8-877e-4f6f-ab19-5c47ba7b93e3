<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <!-- 表单区域 -->
    <costHead-edit
      v-if="showEditPage"
      :currentEditRow="currentEditRow"
      @hide="hideEditPage" />
    <costHead-detail
      v-if="showDetailPage"
      :currentEditRow="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import {getAction} from '@/api/manage'
import {ListMixin} from '@comp/template/list/ListMixin' 
import CostHeadEdit from './modules/CostHeadEdit'
import CostHeadDetail from './modules/CostHeadDetail'

export default {
    mixins: [ListMixin],
    components: {
        CostHeadEdit,
        CostHeadDetail
    },
    data () {
        return {
            pageData: {
                businessType: 'cost',
                button: [
                    {allow: ()=> { return this.btnInvalidAuth('cost#CostHead:add') }, label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary'}
                ],
                table: {
                    tableData: [],
                    tableColumn: []
                },
                formField: [
                    { 
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterMaterialCode`, '请输入物料编码')
                    }
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition},
                    {type: 'invalid', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_invalid`, '失效'), clickFn: this.invalid, allow: this.allowInvalid},
                    {allow: ()=> { return this.btnInvalidAuth('cost#CostHead:delete') }, label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDelete}
                ],
                optColumnWidth: 250,
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/cost/purchaseCostHead/list',
                delete: '/cost/purchaseCostHead/delete',
                invalid: '/cost/purchaseCostHead/invalid',
                columns: 'PurchaseCostHeadList'
            }
        }
    },
    methods: {
        handleAdd () {
            this.currentEditRow = {}
            this.showEditPage = true
        },
        showEditCondition (row) {
            if(this.btnInvalidAuth('cost#CostHead:edit')){
                return  true
            }
        },
        allowInvalid (row){
            //0:正常;1:失效
            return '0' !== row.status
        },
        invalid (row) {
            const that = this
            this.$confirm({
                title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmFailure`, '确认失效'),
                content: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_confirmFailureTips`, '确认将该条模板失效吗?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.invalid, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.loading = false
                    })   
                }
            })
            
        }
    }
}
</script>