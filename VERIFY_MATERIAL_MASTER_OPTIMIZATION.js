// 🚀 验证物料主数据页签空间填充效果的脚本
// 请在浏览器控制台中执行此脚本

console.log('🔍 开始验证物料主数据页签空间填充效果...');
console.log('='.repeat(80));

// 1. 检查当前页面环境
const windowHeight = document.documentElement.clientHeight;
const windowWidth = document.documentElement.clientWidth;
console.log(`📐 窗口尺寸: ${windowWidth} × ${windowHeight}`);

// 2. 查找物料主数据相关表格元素
const materialGridSelectors = [
    // 物料行信息
    '[data-group-code="itemInfo"]',
    '[data-group-code="purchaseMaterialItemList"]',
    '[ref*="purchaseMaterialItemList"]',
    '.lineWrap[ref*="purchaseMaterialItemList"]',
    
    // 品牌信息
    '[data-group-code="brandInfo"]',
    '[data-group-code="materialBrandInfo"]',
    '[ref*="materialBrand"]',
    '.lineWrap[ref*="materialBrand"]',
    
    // 计量单位信息
    '[data-group-code="unitInfo"]',
    '[data-group-code="materialUnitInfo"]',
    '[ref*="materialUnit"]',
    '.lineWrap[ref*="materialUnit"]',
    
    // 附件信息
    '[data-group-code="attachmentList"]',
    '[data-group-code="purchaseAttachmentList"]',
    '[data-group-code="fileInfo"]',
    '[ref*="purchaseAttachment"]',
    '.lineWrap[ref*="purchaseAttachment"]'
];

const foundMaterialGrids = [];
materialGridSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    if (elements.length > 0) {
        elements.forEach((el, index) => {
            foundMaterialGrids.push({
                selector,
                index,
                element: el,
                size: `${el.offsetWidth} × ${el.offsetHeight}`,
                ref: el.getAttribute('ref'),
                dataGroupCode: el.getAttribute('data-group-code'),
                className: el.className
            });
        });
    }
});

console.log('🎯 物料主数据表格查找结果:');
if (foundMaterialGrids.length > 0) {
    console.log('✅ 找到物料主数据表格:');
    foundMaterialGrids.forEach(grid => {
        console.log(`- 选择器: ${grid.selector}`);
        console.log(`  尺寸: ${grid.size}`);
        console.log(`  ref: ${grid.ref || '无'}`);
        console.log(`  data-group-code: ${grid.dataGroupCode || '无'}`);
        console.log('---');
    });
} else {
    console.log('❌ 未找到物料主数据表格');
}

// 3. 检查当前活动页签
const activeTab = document.querySelector('.ant-tabs-tab-active');
if (activeTab) {
    const tabText = activeTab.textContent.trim();
    console.log(`📋 当前活动页签: "${tabText}"`);
    
    const isMaterialTab = tabText.includes('物料行') || tabText.includes('物料行信息') || tabText.includes('itemInfo');
    const isBrandTab = tabText.includes('品牌') || tabText.includes('品牌信息') || tabText.includes('brand');
    const isUnitTab = tabText.includes('计量单位') || tabText.includes('单位信息') || tabText.includes('unit');
    const isAttachmentTab = tabText.includes('附件') || tabText.includes('文件') || tabText.includes('attachment');
    
    console.log(`是否为物料行信息页签: ${isMaterialTab ? '✅ 是' : '❌ 否'}`);
    console.log(`是否为品牌信息页签: ${isBrandTab ? '✅ 是' : '❌ 否'}`);
    console.log(`是否为计量单位信息页签: ${isUnitTab ? '✅ 是' : '❌ 否'}`);
    console.log(`是否为附件信息页签: ${isAttachmentTab ? '✅ 是' : '❌ 否'}`);
} else {
    console.log('❌ 未找到活动页签');
}

// 4. 分析空间利用效果
const mainMaterialGrid = foundMaterialGrids[0]?.element;

if (mainMaterialGrid) {
    console.log('📊 空间利用效果分析:');
    
    const materialHeight = mainMaterialGrid.offsetHeight;
    const materialUtilization = (materialHeight / windowHeight * 100).toFixed(1);
    console.log(`物料主数据表格:`);
    console.log(`- 高度: ${materialHeight}px`);
    console.log(`- 空间利用率: ${materialUtilization}%`);
    
    // 检查样式
    const materialStyles = window.getComputedStyle(mainMaterialGrid);
    console.log(`- CSS height: ${materialStyles.height}`);
    console.log(`- CSS min-height: ${materialStyles.minHeight}`);
    
    // 期望的优化高度
    const expectedHeight = windowHeight - 120;
    const heightDiff = Math.abs(materialHeight - expectedHeight);
    console.log(`📈 优化效果评估:`);
    console.log(`- 期望高度: ${expectedHeight}px`);
    console.log(`- 实际高度: ${materialHeight}px`);
    console.log(`- 高度差异: ${heightDiff}px`);
    console.log(`- 优化效果: ${heightDiff < 100 ? '✅ 优秀' : heightDiff < 200 ? '⚠️ 良好' : '❌ 需要改进'}`);
}

// 5. 检查优化配置是否生效
console.log('🔍 检查优化配置:');
console.log('请查看控制台中是否有以下调试信息:');
console.log('- "🎯 物料行信息表格极致空间优化"');
console.log('- "🎯 品牌信息表格极致空间优化"');
console.log('- "🎯 计量单位信息表格极致空间优化"');
console.log('- "🎯 附件信息表格极致空间优化"');

// 6. 检查CSS样式应用
if (mainMaterialGrid) {
    const vxeTable = mainMaterialGrid.querySelector('.vxe-table');
    const bodyWrapper = mainMaterialGrid.querySelector('.vxe-table--body-wrapper');
    
    if (vxeTable) {
        const tableStyles = window.getComputedStyle(vxeTable);
        console.log('🎨 物料主数据表格样式检查:');
        console.log(`- VXE表格 height: ${tableStyles.height}`);
        console.log(`- VXE表格 min-height: ${tableStyles.minHeight}`);
        
        const hasOptimizedStyles = tableStyles.height.includes('calc') || 
                                  tableStyles.minHeight.includes('calc') ||
                                  tableStyles.height.includes('vh');
        console.log(`- 是否应用优化样式: ${hasOptimizedStyles ? '✅ 是' : '❌ 否'}`);
    }
    
    if (bodyWrapper) {
        const bodyStyles = window.getComputedStyle(bodyWrapper);
        console.log(`- 表格体 max-height: ${bodyStyles.maxHeight}`);
        console.log(`- 表格体 min-height: ${bodyStyles.minHeight}`);
    }
}

// 7. 无数据时的310px高度检查
console.log('📏 无数据时310px高度检查:');
if (mainMaterialGrid) {
    const tableData = mainMaterialGrid.querySelectorAll('.vxe-body--row');
    const hasData = tableData.length > 0;
    console.log(`- 是否有数据: ${hasData ? '✅ 有数据' : '❌ 无数据'}`);
    
    if (!hasData) {
        const currentHeight = mainMaterialGrid.offsetHeight;
        console.log(`- 无数据时当前高度: ${currentHeight}px`);
        console.log(`- 是否接近310px: ${Math.abs(currentHeight - 310) < 50 ? '✅ 是' : '❌ 否'}`);
    }
}

// 8. 提供切换页签的便捷方法
console.log('🔄 页签切换便捷方法:');
const allTabs = document.querySelectorAll('.ant-tabs-tab');
console.log(`页面共有 ${allTabs.length} 个页签:`);

allTabs.forEach((tab, index) => {
    const tabText = tab.textContent.trim();
    const isMaterialTab = tabText.includes('物料行') || tabText.includes('物料行信息');
    const isBrandTab = tabText.includes('品牌') || tabText.includes('品牌信息');
    const isUnitTab = tabText.includes('计量单位') || tabText.includes('单位信息');
    const isAttachmentTab = tabText.includes('附件') || tabText.includes('文件');
    
    if (isMaterialTab || isBrandTab || isUnitTab || isAttachmentTab) {
        let tabType = '';
        if (isMaterialTab) tabType = '(物料行信息)';
        else if (isBrandTab) tabType = '(品牌信息)';
        else if (isUnitTab) tabType = '(计量单位信息)';
        else if (isAttachmentTab) tabType = '(附件信息)';
        
        console.log(`- ${index + 1}. "${tabText}" ${tabType}`);
        
        // 为相关页签添加点击高亮
        if (isMaterialTab) {
            tab.style.border = '2px solid green';
            tab.style.backgroundColor = '#e6ffe6';
        } else if (isBrandTab) {
            tab.style.border = '2px solid orange';
            tab.style.backgroundColor = '#fff3e6';
        } else if (isUnitTab) {
            tab.style.border = '2px solid purple';
            tab.style.backgroundColor = '#f3e6ff';
        } else if (isAttachmentTab) {
            tab.style.border = '2px solid brown';
            tab.style.backgroundColor = '#f5f5dc';
        }
    }
});

// 9. 提供强制优化方案
if (mainMaterialGrid) {
    const idealHeight = windowHeight - 120;
    console.log('🔧 如果物料主数据表格高度不理想，请执行以下强制优化代码:');
    console.log(`
// 强制设置物料主数据表格高度
const materialGrids = document.querySelectorAll('[data-group-code="itemInfo"], [data-group-code="purchaseMaterialItemList"], [data-group-code="brandInfo"], [data-group-code="unitInfo"], [data-group-code="attachmentList"]');
materialGrids.forEach(grid => {
    if (grid) {
        grid.style.height = '${idealHeight}px';
        grid.style.minHeight = '${idealHeight}px';
        
        const table = grid.querySelector('.vxe-table');
        if (table) {
            table.style.height = '${idealHeight}px';
            table.style.minHeight = '${idealHeight}px';
        }
        
        const bodyWrapper = grid.querySelector('.vxe-table--body-wrapper');
        if (bodyWrapper) {
            bodyWrapper.style.maxHeight = '${idealHeight - 50}px';
            bodyWrapper.style.minHeight = '${idealHeight - 100}px';
        }
        
        console.log('✅ 已强制优化物料主数据表格高度');
    }
});
    `);
}

// 10. 对比分析
console.log('📊 与其他页签对比分析:');
const contactGrid = document.querySelector('[data-group-code="supplierContactsInfoList"], [data-group-code="contactsInfo"]');
const addressGrid = document.querySelector('[data-group-code="supplierAddressInfoList"], [data-group-code="addressInfo"]');

if (contactGrid && mainMaterialGrid) {
    const contactHeight = contactGrid.offsetHeight;
    const materialHeight = mainMaterialGrid.offsetHeight;
    const heightDiff = Math.abs(contactHeight - materialHeight);
    console.log(`联系人信息表格高度: ${contactHeight}px`);
    console.log(`物料主数据表格高度: ${materialHeight}px`);
    console.log(`高度差异: ${heightDiff}px`);
    console.log(`一致性: ${heightDiff < 50 ? '✅ 高度一致' : '⚠️ 存在差异'}`);
}

console.log('🎉 物料主数据页签验证完成！');
console.log('💡 请切换到不同的物料主数据页签查看优化效果。');
