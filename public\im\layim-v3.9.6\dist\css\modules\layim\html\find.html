<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <title>发现</title>

    <link rel="stylesheet" href="/im/layim-v3.9.6/dist/css/layui.css" />
    <style>
      .srm-find {
      	display: none;
      }
      .layui-input.search-input {
      	padding: 4px 12px;
      	border: 1px solid #d9d9d9;
      	border-radius: 4px;
      	width: 680px;
      	height: 32px;
      }
      .layui-btn.search-btn {
      	margin-left: 4px;
      	border: 1px solid #4e85ff;
      	border-radius: 4px;
      	width: 82px;
      	height: 32px;
      	background: #4e85ff;
      	box-shadow: 0 2px 0 rgb(0 0 0 / 4%);
      	line-height: 32px;
      	letter-spacing: 1px;
      }
      .panel-wrap {
      	position: relative;
      	box-sizing: border-box;
      	padding: 8px 35px 8px 64px;
      	border-radius: 8px;
      	height: 56px;
      	background: #fff;
      	cursor: pointer;
      	font-size: 0;
      }
      .panel-wrap:hover {
      	background: #4e85ff;
      }
      .panel-wrap span.username {
      	display: block;
      	overflow: hidden;
      	max-width: 134px;
      	text-overflow: ellipsis;
      	font-weight: 500;
      	font-size: 13px;
      	color: #454f59;
      	white-space: nowrap;
      }
      .panel-wrap img {
      	position: absolute;
      	left: 16px;
      	top: 8px;
      	border-radius: 100%;
      	width: 40px;
      	height: 40px;
      }
      .panel-wrap p.account {
      	display: block;
      	overflow: hidden;
      	margin: 0;
      	padding: 0;
      	max-width: 134px;
      	line-height: 2;
      	text-overflow: ellipsis;
      	font-size: 12px;
      	color: rgba(69, 79, 89, .4);
      	white-space: nowrap;
      }
      .panel-wrap span.status {
      	position: absolute;
      	right: 8px;
      	top: 50%;
      	font-size: 12px;
      	color: #454f5966;
      	/* 10号字体 */
      	/* transform: scale(.83); */
      	transform: scale(.83) translateY(-50%);
      }
      .panel-wrap .icon {
      	position: absolute;
      	right: 14px;
      	top: 50%;
      	color: rgba(69, 79, 89, .4);
      	transform: translateY(-50%);
      }
      .panel-wrap:hover span.username,
      .panel-wrap:hover p.account,
      .panel-wrap:hover .icon,
      .panel-wrap:hover span.status {
      	color: #fff;
      }
    </style>
  </head>
  <body>
    <div style="margin: 10px 16px 32px">
      <form class="layui-form" style="display: -webkit-inline-box; margin-bottom: 10px">
        <input
          type="text"
          name="username"
          placeholder="请输入用户名搜索"
          autocomplete="off"
          class="layui-input search-input"
          id = "finput"
        />
        <button class="layui-btn search-btn" lay-submit lay-filter="*" id = "fsearch">
          搜索
        </button>
      </form>
      <div>
        <div id="user_list" class="layui-row layui-col-space8 srm-find">
          <!-- <div class="layui-col-md6">
            <div class="layui-card">
              <div class="layui-card-header">
                <div style="float: right">
                  <button style="background: none; border: none">添加好友+</button>
                </div>
              </div>
              <div class="layui-card-body"></div>
            </div>
          </div> -->
        </div>
      </div>
      <!-- <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px">
        <legend>总页数大于页码总数</legend>
      </fieldset> -->
      <!-- <div id="demo1"></div> -->
    </div>

    <script src="/im/layim-v3.9.6/dist/layui.js"></script>
    <script>
      var token = localStorage.getItem('t_token');
      var BASE_URL = localStorage.getItem('IM_BASE_URL') || '//v5sit.51qqt.com/els/im'
      const mglobalSrmI18n = window.parent.globalSrmI18n ? window.parent.globalSrmI18n : function(){}
      const mglobalGetLangAccount = window.parent.globalGetLangAccount ? window.parent.globalGetLangAccount : function(){}

      var i18nTxt = mglobalSrmI18n(mglobalGetLangAccount() + '#i18n_alert_oOi_179d4fa', '待验证');

      var cache = parent.layui.layim.cache()
      console.log('cache :>> ', cache);
      var defaultAvatar = cache.base.defaultAvatar || ''
      var parentFriend = cache.friend || ''

      if (location.hostname.indexOf('localhost') != -1) {
        // BASE_URL = 'http://localhost:11888/els/im'
        BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
      }
      document.getElementById("finput").placeholder = mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_VWNjDRdd_feaf9c1b", "请输入用户名搜索")
      document.getElementById("fsearch").innerHTML = '<i class="layui-icon layui-icon-search"></i>' + mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_title_search", "搜索")
      var $query = null

      function addFriend(user, groupId) {
        console.log('add friend', user.id);
        debugger;
        const _this = $query;
        _this.ajax({
          headers: {
            'X-Access-Token': token
          },
          url: BASE_URL + '/user/addFriend?friendId=' + user.id + '&groupId=' + groupId,
          type: 'get',
          contentType: 'application/json',
          dataType: 'json',
          async: true,
          success: function (res) {
            console.log('ajax success', res);
            console.log('success', res);
            layer.msg(res.message);

            removeElem(user._idx)
          },
          error: function (res) {
            layer.msg('ajax error');
            console.log('ajax error');
          }
        });
      }

      function removeElem (idx) {
        var $ = $query;
        var elem = $('#user_list .layui-col-xs4').eq(idx)
        if (!elem) {
          return
        }
        var panel = elem.find('.layui-panel')
        var icon = panel.find('.icon')
        icon.remove()
        panel.find('.panel-wrap').append('<span class="status">' + i18nTxt + '</span>')
      }

      function setFriendGroup(user) {
        parent.layui.layim.setFriendGroup({
          type: 'friend'
          ,username: user.username
          ,avatar: user.avatar
          ,group: parentFriend //获取好友分组数据
          ,submit: function(group, index){
            // 弹窗后执行上面操作
            // 添加好友请求
            parent.layui.layer.close(index);
            addFriend(user, group)
          }
        });
      }
      // 添加好友操作
      function addFriendHandle (obj) {
        setFriendGroup(obj)
      }
      function getAllUsers(data) {
          var $ = $query
          var val = data ? data.field.username : ''
          if ($.trim(val) == '') {
            layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_RImxOLV_4348822", "关键词不能为空"));
            return false
          }
          $.ajax({
            headers: {
              'X-Access-Token': token
            },
            url: BASE_URL + '/user/getAllUsers?name=' + (data ? data.field.username : ''),
            type: 'get',
            contentType: 'application/json',
            dataType: 'json',
            async: true,
            success: function (res) {
              console.log('ajax success', res);
              // friendStatus 好友状态, 0: 可发送好友请求, 1: 已发送好友请求, 2: 已添加为好友
              let result = res.result || []
              result = result.filter(n => n.friendStatus !== '2')


              // 用户列表
              var _htmlUsersParent = $('#user_list');
              _htmlUsersParent.empty();
              if (res.code === 200) {
                var _html;
                if (result.length == 0) {
                  layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_BjYurjD_cbb5327a", "没有找到该用户"))
                  return
                }
                $('.srm-find').show()
                result.forEach((item) => {
                  let _iconHtml = item.friendStatus === '0'
                    ? '<i class="icon layui-icon layui-icon-add-circle" data-username=' + item.username + ' data-avatar=' + item.avatar + ' data-id=' + item.id + '></i>'
                    : '<span class="status">' + i18nTxt + '</span>'
                  var _html = [
                    '<div class="layui-col-xs4">',
                    '<div class="layui-panel">',
                    '<div class="panel-wrap">',
                    '<img src="' +  item.avatar + '" onerror="javascript:this.src=\'' + defaultAvatar + '\',this.onerror=null" />',
                    '<span class="username">' + item.username + '</span>',
                    '<p class="account">' + item.enterpriseName + '</p>',
                    _iconHtml,
                    '</div>',
                    '</div>',
                    '</div>',
                  ].join('')
                  
                  _htmlUsersParent.append(_html);
                });
                $('#user_list .layui-panel').on('click', 'i.layui-icon',function() {
                  var friends = [] 

                  var col = $(this).closest('.layui-col-xs4')
                  var _idx = col.index()
                  console.log('_idx :>> ', _idx);
                  
                  var id = $(this).data('id')
                  var username = $(this).data('username')
                  var avatar = $(this).data('avatar')
                  var params = {
                    id,
                    username,
                    avatar,
                    _idx
                  }
                  var friArr = parentFriend
                  for (var i = 0; i < friArr.length; i++) {
                    friends = friends.concat(friArr[i].list)
                  }
                  for (var j = 0; j < friends.length; j++) {
                      // 当前好友列表存在，抛出提示
                     if (friends[j].id == id) {
                      layer.msg(mglobalSrmI18n(mglobalGetLangAccount() + "#i18n_field_rjDIKyjABs_cf8cb984", "该用户已在好友列表中"));
                      return
                     }
                  }
                  // 添加好友
                  addFriendHandle(params)
                })
              }
              // 分页
              // laypage.render({
              //   elem: 'demo1',
              //   count: 70, //数据总数
              //   jump: function (obj) {
              //     _htmlUsersParent.empty();
              //     var _html;
              //     users.forEach((item) => {
              //       _htmlUsersParent.append(_html);
              //     });
              //   }
              // });
            },
            error: function () {
              console.log('ajax error');
            }
          });
        }
      layui.use(['layim', 'laypage', 'form'], function () {
        var layim = layui.layim,
          layer = layui.layer,
          laytpl = layui.laytpl,
          $ = layui.jquery,
          laypage = layui.laypage,
          form = layui.form;
          $query = layui.jquery
        
        // 用户列表
        var _htmlUsersParent = $('#user_list');
        _htmlUsersParent.empty();
        // 第一版默认不获取相关用户
        // getAllUsers()
        form.on('submit(*)', function (data) {
          // console.log(data.elem); //被执行事件的元素DOM对象，一般为button对象
          // console.log(data.form); //被执行提交的form对象，一般在存在form标签时才会返回
          // console.log(data.field); //当前容器的全部表单字段，名值对形式：{name: value}

          getAllUsers(data)
          return false; //阻止表单跳转。如果需要表单跳转，去掉这段即可。
        });
      });
    </script>
  </body>
</html>
