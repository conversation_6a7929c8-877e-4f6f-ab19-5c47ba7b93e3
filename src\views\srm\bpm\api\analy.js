import { getAction, postAction } from '@/api/manage'
export function updataModel (id) {
    return postAction(
        `/a1bpmn/api/repository/process-definitions/publish?modelId=${id}`
    )
}
export function publishModel (id, businessType) {
    return postAction(
        `/a1bpmn/audit/config/repository/process-definitions/publish?modelId=${id}&businessType=${businessType}`
    )
}

export function deleteProcessDefinition (id) {
    return getAction(
        `/a1bpmn/api/models/deleteProcessDefinition/${id}`
    )
}
export function queryByByModelKey (key) {
    return getAction(
        '/a1bpmn/audit/config/queryByByModelKey',
        { modelKey: key }
    )
}
export function deleteByModelKey (key) {
    return getAction(
        '/a1bpmn/audit/config/deleteByModelKey',
        { modelKey: key }
    )
}
export function copyByModelKey (key) {
    return getAction(
        '/a1bpmn/audit/config/copyByModelKey',
        { modelKey: key }
    )
}
export function auditConfigAdd (data) {
    return postAction(
        '/a1bpmn/audit/config/add',
        data
    )
}
export function auditConfigEdit (data) {
    return postAction(
        '/a1bpmn/audit/config/edit',
        data
    )
}
export function invalidByModelKey (key) {
    return getAction(
        '/a1bpmn/audit/config/invalidByModelKey',
        { modelKey: key }
    )
}
export function getNextNode (taskId) {
    return getAction(
        `/a1bpmn/api/cockpit/process-instance/getNextNode/${taskId}`
    )
}
export function getTaskBtn (taskId) {
    return getAction(
        `/a1bpmn/api/runtime/getTaskBtn/${taskId}`
    )
}
export function completeAdHoc (data) {
    return postAction(
        '/a1bpmn/api/runtime/task/completeAdHoc',
        data
    )
}
export function completeTask (data) {
    return postAction(
        '/a1bpmn/audit/api/runtime/task/v2/complete',
        data
    )
}
export function getBackNodeUser ({ taskId, nodeId }) {
    return getAction(
        `/a1bpmn/api/runtime/task/v1/getBackNodeUser/${taskId}/${nodeId}`
    )
}
export function getBackNode (taskId) {
    return getAction(
        `/a1bpmn/api/runtime/task/v1/getBackNode/${taskId}`
    )
}
/**
 * 特事特办节点
 * @param taskId
 */
export function getTransactionUrge (taskId) {
    return getAction(
        `/a1bpmn/api/runtime/task/v1/getTransactionUrge/${taskId}`
    )
}
/**
 * 作废
 * @param taskId
 */
export function invalidTask (taskId) {
    return getAction(
        `/a1bpmn/audit/api/history/task/v1/invalid/${taskId}`
    )
}
/**
 * 审批历史
 * @param bizKey 业务单据ID
 */
export function getHistoryTableData (bizKey) {
    return getAction(
        // `/a1bpmn/api/runtime/hisInstance/v2/nodeOpinion/${processInstanceId}`
        `/a1bpmn/api/runtime/hisInstance/all/nodeOpinion/${bizKey}`
    )
}

export function getOutgoingFlows (id) {
    return getAction(
        `/a1bpmn/api/cockpit/process-instance/outgoingFlows/${id}`
    )
}
export function completeActivitySignOnly (taskId) {
    return postAction(`/api/oa/office/completeActivitySignOnly/${taskId}`)
}
export function completeActivityOrNo (taskId) {
    return postAction(`/api/oa/office/completeActivityOrNo/${taskId}`)
}
export function completeActivity (taskId) {
    return postAction(`/api/oa/office/completeActivity/${taskId}`)
}
export function getNextNodeFaWen (taskId) {
    return postAction(`/api/oa/office/process-instance/getNextNode/${taskId}`)
}

export function informedTask (data) {
    return postAction('/a1bpmn/api/cockpit/process-instance/v2/informed', data)
}
export function transferTask (data) {
    return postAction('/a1bpmn/api/runtime/task/v2/transferTask', data)
}
export function changeActivity (data) {
    return postAction(`/a1bpmn/audit/api/task/v1/anyway/changeActivity/${data.taskId}`, data)
}
export function addNodeOperate (data) {
    return postAction(`/a1bpmn/api/cockpit/process-instance/addNode/${data.taskId}`, data)
}
export function addSign (data) {
    return postAction(`/a1bpmn/audit/api/process-instance/before-sign/${data.taskId}`, data)
}
export function addSignOperate (data) {
    return postAction(`/a1bpmn/api/cockpit/process-instance/v2/addSign/${data.taskId}`, data)
}
export function revokeStartActivity (data) {
    return postAction('/a1bpmn/audit/api/task/v1/changeActivityState', data)
}
// 新增BPM催办
export function addUrg (data) {
    return postAction('/urg/api', data)
}
//审批后撤回
export function withdrawalTask (data) {
    return postAction('/a1bpmn/audit/api/cancel', data)
}
export function withdrawalAddPreNode (data) {
    return postAction('/a1bpmn/audit/api/cancelPreNode', data)
}
// 批量转办
export function batchTransferTask (data) {
    return postAction('/a1bpmn/audit/api/task/v1/batchTransferTask', data)
}