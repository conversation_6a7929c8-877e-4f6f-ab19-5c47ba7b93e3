<template>
  <a-modal
    v-drag    
    v-model="regretVisible"
    :title="message"
    @ok="regret">
    <a-spin :spinning="loading">
      <div>
        <a-radio-group
          v-model="regretValue"> 
          <a-radio
            class="radio-wrap"
            :value="0">{{ $srmI18n(`${$getLangAccount()}#i18n_title_noMoreFulfillment`, '悔标，不再寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="1">{{ $srmI18n(`${$getLangAccount()}#i18n_title_replaceSourcing`, '悔标，重新寻源') }}</a-radio> 
          <a-radio
            class="radio-wrap"
            :value="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_priceComparison`, '重新比价/定标') }}</a-radio>
        </a-radio-group>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { postAction } from '@/api/manage'
export default {
    name: 'Regret',
    data () {
        return {
            loading: false,
            regretValue: 0,
            regretVisible: false,
            headId: undefined,
            message: '',
            regretWay: 'material',
            regretList: null
        }
    },
    methods: {
        openMaterial (headId, regretList, message) {
            this.headId = headId
            this.regretList = regretList
            this.message = message
            this.regretWay = 'material'
            this.regretVisible = true
        },
        openWhole (headId, message){
            this.headId = headId
            this.regretList = null
            this.message = message
            this.regretWay = 'whole'
            this.regretVisible = true
        },
        regret (){
            let param = {}
            param['id'] = this.headId
            param['regretFlag'] = this.regretValue
            param['regretWay'] = this.regretWay
            param['purchaseEnquiryItemList'] = this.regretList
            this.loading = true
            postAction('/enquiry/purchaseEnquiryHead/regret', param).then(res => {
                if(res.success) {
                    this.$message.success(res.message)
                    this.$parent.refresh()
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.regretVisible = false
                this.loading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
.radio-wrap {
    display: block;
    height: 30px;
}
</style>