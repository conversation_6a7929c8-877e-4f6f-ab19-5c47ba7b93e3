<template>
  <div>
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIVH_2f594f3d`, '文件信息') }}</span>
    </titleTrtl>
    <listTable
      ref="listTable"
      :pageData="pageData"
      :externalToolBar="externalToolBar"
      :pageStatus="pageStatus"
      :fromSourceData="fromSourceData.saleAttachmentDTOList"
      :statictableColumns="tableColumns"
      :showTablePage="false" />
    <div>
      <a-upload
        ref="file"
        name="file"
        style="display: none"
        :showUploadList="false"
        :action="uploadUrl"
        :headers="uploadHeader"
        multiple
        :accept="accept"
        :before-upload="beforeUpload"
        :data="uploadData"
        @change="handleUploadChange"
      >
      </a-upload>
    </div>
    <EncryptedFileModal
      ref="EncryptedFileModal" />
  </div>
</template>
<script>
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import EncryptedFileModal from './encryptedFileModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    props: {
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String
        }
    },
    mixins: [baseMixins],
    components: {
        titleTrtl,
        EncryptedFileModal,
        listTable
    },
    computed: {
        subpackage (){
            return this.currentSubPackage()
        },
        externalToolBar () {
            let btnList = []
            if (this.pageStatus == 'edit') {
                btnList = [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        click: this.upload
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
                if (this.subpackage.useCa == '1') btnList.unshift({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QIuw_2f599fb5`, '文件加密'),
                    click: this.encryptedFile
                })
            }
            return btnList
        },
        accept () {
            if (this.subpackage.useCa == '1') {
                return '.enc'
            } else {
                return '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf'
            }
        },
        uploadData () {
            if (this.subpackage.useCa == '1') {
                return {headId: `${this.fromSourceData.id}_${this.checkType}`, businessType: 'tender', caType: 'shanghai_test_ca', saveType: 'decrypt'}
            } else {
                return {headId: `${this.fromSourceData.id}_${this.checkType}`, businessType: 'tender'}
            }
        },
        tableColumns () {
            let dictCode = ''
            if (this.checkType == '0') {
                dictCode = 'preSaleDocumentSubmitFileType'
            } else {
                if (this.processType == '1') {
                    dictCode = 'resultSaleDocumentSubmitFileType '
                } else {
                    dictCode = 'saleDocumentSubmitFileType'
                }
            }
            let columns = [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    groupCode: 'saleAttachmentDTOList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                    fieldLabelI18nKey: 'i18n_field_fileType',
                    field: 'fileType',
                    defaultValue: '',
                    width: '150',
                    dictCode: dictCode,
                    fieldType: 'select',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                    fieldLabelI18nKey: '',
                    field: 'fileName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    fieldLabelI18nKey: '',
                    field: 'uploadTime'
                },
                {
                    fieldLabelI18nKey: '',
                    field: 'uploadSubAccount_dictText',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    fieldLabelI18nKey: '',
                    field: 'grid_opration',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '150',
                    slots: { default: 'grid_opration' }
                }
            ]
            return columns
        }
    },
    data () {
        return {
            pageData: {
                optColumnList: [
                    { type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                    { type: 'reply', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '下载'), clickFn: this.downloadEvent }
                ]
                
            },
            showEncryptedFileModal: false,
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`
        }
    },
    methods: {
        preViewEvent (row) {
            row.subpackageId = this.subpackageId()
            this.$previewFile.open({params: row })
        },
        getValidate () {
            let P = new Promise((resolve, reject) => {
                let data = this.$refs['listTable'].getTableData().fullData
                if (data.length == 0) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSunJQI_87e2fedd`, '请添加递交文件'))
                    resolve(true)
                    return false
                }
                this.$refs['listTable'].getValidate().then(res => {
                    resolve(res)
                }, err => {
                    resolve(err)
                })
            }) 
            return P
        },
        getParamsData () {
            let p = this.$refs['listTable'].getTableData().fullData
            return p
        },
        // 下载
        async downloadEvent (row) {
            row.subpackageId = this.subpackageId()
            const fileName = row.fileName
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.listTable.loading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.listTable.loading = false
            })
        },
        upload () {
            this.$refs['file'].$children[0].$el.click()
        },
        beforeUpload (file) {
            var flieArr = file.name.split('.')
            let suffix = flieArr[flieArr.length - 1]
            if (this.subpackage.useCa == '1' && suffix != 'enc') {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CAuwIQIAcROLencyL_c858e90c`, 'CA加密下：文件类型只能为.enc结尾'))
                return false
            }
            return true
        },
        handleUploadChange ({file}) {
            if (file.status === 'done') {
                if (file.response.success) {
                    let {fileName, filePath, ...others} = file.response.result || {}
                    let fileObj = {
                        ...others,
                        name: fileName,
                        url: filePath,
                        fileType: null,
                        fileType_dictText: null,
                        fileName, filePath
                    }
                    this.$refs['listTable'].insertAt([fileObj], -1)
                } else {
                    this.$message.error(`${file.name} ${file.response.message}`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            }
        },
        businessGridDelete () {
            this.$refs['listTable'].businessGridDelete()
        },
        encryptedFile () {
            console.log(this.$refs['EncryptedFileModal'])
            this.$refs['EncryptedFileModal'].open()
        }
    },
    mounted (){
        console.log('fromSourceData', this.fromSourceData)
        this.fromSourceData.saleAttachmentDTOList.forEach(item=>{
            if(item.fileType=='5'){
                item.fileType = ''
            }
        })
        
    }
}
</script>
