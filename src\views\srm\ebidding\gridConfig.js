import { srmI18n, getLangAccount } from '@/utils/util'

export const materialGridOptions = {
    border: false,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    height: 300,
    loading: false,
    // editConfig: {
    //     trigger: 'manual',
    //     mode: 'row',
    //     showStatus: true,
    //     icon: 'fa fa-file-text-o'
    // },
    columns: [
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDescription`, '物料描述'), showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_title_quantity`, '数量'), showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_title_demandDate`, '需求日期'), showOverflow: true }
    ],
    data: []
}