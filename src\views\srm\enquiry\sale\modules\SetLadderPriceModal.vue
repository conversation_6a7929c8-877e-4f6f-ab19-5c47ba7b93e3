<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    :title="modelTitle"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :edit-config="gridCustomEditConfig"
              :radio-config="{highlight: true, reserve: true, trigger: 'row'}"
              :toolbar="{ slots: { buttons: 'toolbar_buttons' }}"
            >
              <template #toolbar_buttons>
                <div style="margin-top:-8px;">
                  <label style="">{{ $srmI18n(`${$getLangAccount()}#i18n_field_currency`, '币别') }}：{{ currency }}</label>
                  <label style="margin-left:60px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_enterTaxCode`, '税码') }}：{{ taxCode }}</label>
                  <label style="margin-left:60px">{{ $srmI18n(`${$getLangAccount()}#i18n_title_taxRate`, '税率') }}：{{ taxRate }}</label>
                </div>
              </template>
              
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'SetLadderPriceModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            modelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderReportPrice`, '阶梯报价'),
            currency: '',
            taxCode: '',
            taxRate: '',
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableHeaderData: [],
            tableHeaderColumn: [
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 150 },
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 100 },
                { field: 'price', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), width: 100 },
                { field: 'netPrice', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价'), width: 100 }
            ],
            gridCustomEditConfig: {
                trigger: 'click',
                mode: 'cell',
                showStatus: true
            }
        }
    },
    methods: {
        open (row, head) {
            this.tableHeaderColumn.forEach(item => {
                if(head['quoteType'] == '1'){
                    if(item.field == 'netPrice'){
                        item.editRender = {name: 'AInputNumber', events: {change: this.computPrice}}
                    }
                }else{
                    if(item.field == 'price'){
                        item.editRender = {name: 'AInputNumber', events: {change: this.computNetPrice}}
                    }
                }
            })
            this.modelTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_material`, '物料')+':' + row.materialDesc
            this.currency = row.currency_dictText
            this.taxCode = row.taxCode
            this.taxRate = row.taxRate
            let that = this
            this.madalVisible = true
            if(row.ladderPriceJson){
                let itemList = JSON.parse(row.ladderPriceJson)
                // 根据税率更新值
                let self = this
                itemList.forEach(rs => {
                    if(head['quoteType'] == '1'){
                        self.computPrice({row: rs})
                    } else {
                        self.computNetPrice({row: rs})
                    }
                })
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(itemList)
                })
            }
        },
        computNetPrice ({row}){
            let price = row.price
            if(price && this.taxRate){
                let netPrice = price/(1 + parseFloat(this.taxRate)/100)
                row.netPrice = netPrice.toFixed(6)
            }else{
                row.netPrice = ''
            }
        },
        computPrice ({row}){
            let netPrice = row.netPrice
            if(netPrice && this.taxRate){
                let price = netPrice*(1 + parseFloat(this.taxRate)/100)
                row.price = price.toFixed(6)
            }else{
                row.price = ''
            }
        },
        goBack () {
            this.$emit('hide')
        },
        setLadderOk (){
            let itemList = this.$refs.headerGrid.getTableData().fullData
            if(itemList.length == 0 ){
                this.madalVisible = false
            }
            for(let i in itemList){
                let item = itemList[i]
                if(item.pirce){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPriceNotEmpty`, '含税价不能为空！') )
                    return
                }
            }
            // this.$emit('setLadderCallBack',itemList)
            this.$parent.$refs.editPage.$parent.setLadderCallBack(itemList)
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>
