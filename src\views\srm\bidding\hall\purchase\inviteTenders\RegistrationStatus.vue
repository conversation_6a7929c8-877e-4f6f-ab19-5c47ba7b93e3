<template>
  <div class="registration-status">
    <content-header
      v-if="showHeader"
      class="posA"
      :btns="btns"
      @content-header-check="checkRisk"
    />

    <a-spin :spinning="confirmLoading">

      <div
        v-if="vuex_currentEditRow.id"
        class="container"
        :style="style">
        
        <component
          :is="curComp"
          :current-edit-row="vuex_currentEditRow" />
            
      </div>
    </a-spin>
    
    <a-modal
      v-drag    
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果')"
      v-model="riskShow"
      @ok="confirm"
      @cancel="confirm"
    >
      <div
        v-for="(data, index) in riskList"
        :key="data">
        <p v-if="data.type==='0'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KCbVeMKdeyR_3727867`, '在股权穿透存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='1'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KeslILMKdeyR_85d14c24`, '在最终受益人存在相同结果') }}：{{ data.result
        }}</p>
        <p v-if="data.type==='2'">{{ index + 1 }}、{{ data.sourceName }} {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KKVLMKdeyR_46ebba36`, '在实控人存在相同结果') }}：{{ data.result
        }}</p>
        <p
          :key="index"
          v-if="data.type==='4'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_KsuWWMKdeyR_17c9510e`, '在报价ip存在相同结果') }}：{{ data.result
          }}</p>
        <p 
          v-if="data.type === '5' || data.type === '6' || data.type === '7' || data.type === '8'">{{ index + 1 }}、{{ data.sourceName }} {{ $srmI18n(`${$getLangAccount()}#i18n_field_U_4e0e`, '与') }} {{ data.toName }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_MKW_162724a`, '存在：') }}{{ data.result }}{{ $srmI18n(`${$getLangAccount()}#i18n_field_RH_a59e8`, '关系') 
        }}</p>
      </div>
    </a-modal>
  </div>
</template>

<script>
import SupplierInfo from './components/SupplierInfo'

import ContentHeader from '@/views/srm/bidding/hall/components/content-header'
import { getAction } from '@/api/manage'

import { SET_VUEX_CURRENT_EDIT_ROW } from '@/store/mutation-types'
import { mapState, mapMutations } from 'vuex'

export default {
    name: 'RegistrationStatus',
    components: {
        'content-header': ContentHeader,
        'supplier-info': SupplierInfo
    },
    data () {
        return {
            confirmLoading: false,
            riskShow: false,
            riskList: [],
            isLocal: true,
            btns: [
                { 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LBJi_2919d4bc`, '围标探测'), 
                    type: 'primary', 
                    event: 'check', 
                    icon: 'question-circle-o', 
                    helpText: `1、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yVLIxDjWVOSmhyR_c7532315`, '接口为异步调用，请静候查询结果')}<br> 2、${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lnICdjWRWefRdXWRxOfUWWu_f2d40ee9`, '受第三方应用限制，最大供应商数量不能大于20家')}`,
                    authorityCode: 'bidding#purchaseBiddingHead:fenceDetection'
                }
            ],
            showHeader: true,
            height: 400
        }
    },
    // 在子组件中注入在父组件中创建的属性
    inject: [
        'routerRefresh',
        'updateVuexCurrentEditRow'
    ],
    computed: {
        ...mapState({
            vuex_currentEditRow: state => state.app.vuex_currentEditRow
        }),
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        curComp () {
            return 'supplier-info'
        }
    },
    methods: {
        ...mapMutations({
            setVuexCurrentEditRow: SET_VUEX_CURRENT_EDIT_ROW
        }),
        confirm () {
            this.riskShow = false
        },
        //查看风险
        handleRisk ({ toElsAccount }) {
            sessionStorage.setItem('cache_elsAccout', toElsAccount)
            this.$router.push({
                path: '/srm/base/SupplierVenture'
            })
        },
        checkRisk () {
            const url = '/bidding/purchaseBiddingHead/queryRisk?id=' + this.vuex_currentEditRow.id
            this.confirmLoading = true
            getAction(url)
                .then(res => {
                    if (res && res.success) {
                        if (res.result && res.result.length) {
                            this.riskList = res.result
                            this.riskShow = true
                        } else {
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_IiRCISReWF_3b256afc`, '检测公司间无共同数据'))
                        }
                    }else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.init()
                })
        }
    },
    created () {
        this.height = document.documentElement.clientHeight
    }
}
</script>

<style lang="less" scoped>
.registration-status {
  .container {
    background: #fff;
    padding: 12px;
    margin-top: 44px;
  }

  :deep(.page-container ){
    padding: 0;
  }
}
</style>
