<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal 
      ref="fieldSelectModal" />
    <a-modal
    v-drag    
      v-model="visible"
      title="驳回"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item label="驳回原因">
          <a-input
            v-model="form.reason"
            type="reason" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
export default {
    name: 'EsignFlowAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            visible: false,
            form: {
                reason: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'esignNumber'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_p9Gt6s92wep4uCmw`, '主体文件是否已上传e签宝'),
                                    fieldName: 'uploaded',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractRemind`, '文件到期前多少时间提醒(小时)'),
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isFile`, '是否已归档'),
                                    fieldName: 'archiving',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOpen`, '是否已开启'),
                                    fieldName: 'initiate',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gwarjw3QgSxzQUiQ`, '签署完成是否自动发送'),
                                    fieldName: 'autoSend',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseEsignSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 200 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'personType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Jw4cGjnuDCPCSekJ`, '签署人类型'), width: 120},
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ROSkyKENR2a4MA2u`, '签署/抄送人'), width: 120 },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120},
                            { field: 'esignStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esignStatus`, '签署状态'), width: 120},
                            { field: 'actorIndentityType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'), width: 120}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseEsignAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'esignType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120},
                            { field: 'uploaded', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 180, visible: false },
                            { field: 'uploaded_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 200, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BbOiR9iTwLOeSCIp`, '原文件下载'), clickFn: this.downloadEvent },
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), clickFn: this.flowFileDownload, allow: this.allowDownload },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_nKZbyZGNSutnhk7s`, '供应商回传文件'), groupCode: 'signRetrunAttachments', type: 'grid', custom: {
                        ref: 'signRetrunAttachments',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { ...new BatchDownloadBtn().btnConfig }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadReturnFileEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), type: 'primary', click: this.confirmEvent, showCondition: this.showBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'), type: 'primary', click: this.rejectEvent, showCondition: this.showBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/esign/purchaseEsign/queryById',
                //原文件下载链接
                download: '/attachment/purchaseAttachment/download',
                //签署文件下载链接
                esignFIledownload: '/esign/purchaseEsignAttachment/esignFiledownload',
                //回传文件下载链接
                returnFileDownload: '/esign/purchaseSignReturnAttachment/download',
                //确认
                confirm: '/esign/purchaseEsign/confirm',
                //驳回
                reject: '/esign/purchaseEsign/reject',
                getSignature: '/attachment/purchaseAttachment/getSignature'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        },
        showBtn (){
            let data = this.currentEditRow
            if(data.returnSignedFile==='1' && data.reject!=='1' && data.returnFileConfirm!=='1'){
                return true
            }
            return false
        },
        allowDownload (row){
            if(row.esignType==='1'){
                return false
            }
            return true
        },
        flowFileDownload (row){
            console.log(row)
            if(row.esignFilePath){
                this.downloadFile(row, this.url.esignFIledownload, row.uploadFileName)
            }else {
                getAction('/esign/purchaseEsign/signFileDownload', {id: row.headId}).then(res => {
                    if(res.success) {
                        res.result.forEach(key => {
                            if(key.fileId == row.fileId){
                                this.downloadFile(row, key.fileUrl, key.fileName)
                            }
                        })
                    }
                })
            }
        },
        preViewEvent (row){
            let preViewFile = row
            postAction(this.url.getSignature, {id: preViewFile.id}).then(res => {
                if(res.success){
                    this.$previewFile.open({params: preViewFile, path: res.message})
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        downloadReturnFileEvent (row){
            this.downloadFile(row, this.url.returnFileDownload, row.fileName)
        },
        downloadFile (data, url, fileName){
            let id = data.id
            const params = {
                id
            }
            getAction(url, params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        rejectEvent (){
            this.visible = true
        },
        handleOk (){
            const params = this.currentEditRow
            params.rejectReason = this.form.reason
            postAction(this.url.reject, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        },
        confirmEvent (){
            const params = this.currentEditRow
            postAction(this.url.confirm, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })
        }
    }
}
</script>