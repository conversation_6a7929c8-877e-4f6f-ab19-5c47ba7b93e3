<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :currentEditRow="currentEditRow"
      :page-data="pageData"
      :url="url" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
export default {
    name: 'ElsSealsAdd',
    mixins: [DetailMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            selectType: 'seals',
            confirmLoading: false,
            pageData: {
                form: {
                    elsAccount: '',
                    sealType: '',
                    companyName: '',
                    subAccount: '',
                    companyId: '',
                    accountId: '',
                    userIds: '',
                    userNames: '',
                    sealName: '',
                    filePath: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedV_27cc4b0f`, '印章详情'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [

                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_transferType`, '类型'),
                                    fieldName: 'sealType',
                                    required: '1',
                                    dictCode: 'contractLockSealsType'
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWRC_2e0e62aa`, '所属公司'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dWRC_2e0e62aa`, '所属公司')
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人'),
                                    fieldName: 'userNames',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WedWL_d12084ec`, '印章所属人')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_sMURgjgo`, '印章别名'),
                                    required: '1',
                                    fieldName: 'sealName'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeLm_27cc1068`, '印章规格'),
                                    fieldName: 'sealSpec',
                                    dictCode: 'contractLockSealsSpec'
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_qBGuFJsS`, '图片路径'),
                                    fieldName: 'filePath',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'image',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hLlbPO_3b0edf09`, '法人授权图片'),
                                    fieldName: 'lpLetterPath',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UnKmjW_4305e96c`, '审核失败原因'),
                                    fieldName: 'fbk1',
                                    disabled: true
                                }
                            ]
                        }

                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/contractLock/saleClSeals/queryById'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>