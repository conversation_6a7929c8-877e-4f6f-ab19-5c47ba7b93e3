<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"/>
    <!-- 表单区域 -->
    <saleAddCost-edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 详情页面 -->
    <saleAddCost-detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <tem-select-modal 
      ref="temSelectModal"
      :pageData="pageData"
      @success="handleEdit"></tem-select-modal>
  </div>
</template>
<script>
import SaleAddCostEdit from './modules/SaleAddCostEdit'
import SaleAddCostDetail from './modules/SaleAddCostDetail'
import TemSelectModal from './modules/TemSelectModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import { postAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        SaleAddCostEdit,
        SaleAddCostDetail,
        TemSelectModal
    },
    data () {
        return {
            pageData: {
                businessType: 'addCost',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNujty_c91c347a`, '请输入费用单号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'finance#saleAddCost:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnWidth: 220,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'finance#saleAddCost:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'finance#saleAddCost:edit'},
                    {type: 'invalid', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleInvalid, allow: this.showInvalidCondition, authorityCode: 'finance#saleAddCost:invalid'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.showDeleteCondition, authorityCode: 'finance#saleAddCost:delete'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/finance/saleAddCost/list',
                add: '/finance/saleAddCost/add',
                invalid: '/finance/saleAddCost/invalid',
                delete: '/finance/saleAddCost/delete',
                deleteBatch: '/finance/saleAddCost/deleteBatch',
                exportXlsUrl: 'finance/saleAddCost/exportXls',
                importExcelUrl: 'finance/saleAddCost/importExcel',
                columns: 'saleAddCostList'
            },
            tabsList: []
        }
    },
    mounted () {
        // this.serachTabs('srmAddCostConfirmStatus', 'confirmStatus')
        this.serachCountTabs('/finance/saleAddCost/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.costNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SaleAddCost', url: this.url || '', recordNumber})
        },
        allowChat (row) {
            if((row.confirmStatus == '0')) {
                return true
            }else {
                return false
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls('销售附加费用管理')
        },
        handleAdd () {
            let data = {
                url: '/enterprise/elsEnterpriseInfo/getPurchaseAccount',
                params: {
                    toElsAccount: this.$ls.get('Login_elsAccount')
                }
            }
            this.$refs.temSelectModal.open(data)
        },
        showEditCondition (row) {
            if ('0' == row.confirmStatus) {
                return false
            }else {
                return true
            }
        },
        showDeleteCondition (row) {
            let confirmStatus = row.confirmStatus
            if ('0' == confirmStatus) {
                return false
            }else {
                // 不可操作
                return true
            }
        },
        handleInvalid (row) {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: function () {
                    that.loading = true
                    postAction(that.url.invalid, row).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.searchEvent()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        showInvalidCondition (row) {
            let confirmStatus = row.confirmStatus
            if ('4' == confirmStatus || row.costStatus == '1') {
                // 不可操作
                return true
            } else {
                return false
            }
        }
    }
}
</script>