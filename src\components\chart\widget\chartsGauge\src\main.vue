<!--
 * @Author: fzb
 * @Date: 2022-02-21 16:28:52
 * @LastEditTime: 2022-03-01 17:15:50
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\chartsBar\src\main.vue
-->
<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
export default {
  name: 'ChartsGauge',
   mixins:[chartsMixins],
  computed: {
    option() {
      return {
        customColor:
          this.widget.option.series[0].axisLine.lineStyle.customColor,
        customOffsetCenter:
          this.widget.option.series[0].detail.customOffsetCenter,
      };
    }
  },
  watch: {
    option: {
      handler(val) {
        this.widget.option.series[0].axisLine.lineStyle.color = [
          [1, val.customColor],
        ];
        this.widget.option.series[0].detail.offsetCenter[0] =
          val.customOffsetCenter[0] + "%";
        this.widget.option.series[0].detail.offsetCenter[1] =
          val.customOffsetCenter[1] + "%";
      },
      deep: true,
    }
  },
  methods: {
    refreshWidgetData(data) {
      const option = this.widget.option;
      option.series[0].min = data.min;
      option.series[0].max = data.max;
      option.series[0].detail.formatter = "{value}" + data.unit;
      option.series[0].data[0].name = data.name;
      option.series[0].data[0].value = data.value;
    }
  }
};
</script>