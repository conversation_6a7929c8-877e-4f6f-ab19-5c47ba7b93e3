import { RouteView } from '@/components/layouts'

const inviteTendersRouter = {
    path: '/projectHall/inviteTenders',
    name: 'project_inviteTenders',
    meta: {
        type: 'purchase',
        title: '自主招标',
        titleI18nKey: 'i18n_dict_JdYB_3c4049dd',
        icon: 'icon-111-03',
        keepAlive: false
    },
    component: RouteView,
    children: [
        {
            path: '/projectHall/inviteTenders/announcement',
            name: 'project_announcement',
            meta: {
                title: '项目公告',
                titleI18nKey: 'i18n_menu_dIRx_47180cb3',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'announcement' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/Announcement.vue')
        },
        {
            path: '/projectHall/inviteTenders/registrationStatus',
            name: 'project_registrationStatus',
            meta: {
                title: '供应商列表',
                titleI18nKey: 'i18n_title_supplierList',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'registrationStatus' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/RegistrationStatus.vue')
        },
        // {
        //     path: '/projectHall/inviteTenders/qualificationExamination',
        //     name: 'project_QualificationExamination',
        //     meta: {
        //         title: '资格审查',
        //         titleI18nKey: 'i18n_field_qualificationReview',
        //         keepAlive: false
        //     },
        //     component: () => import(/* webpackChunkName: 'qualificationExamination' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/qualificationExamination.vue')
        // },
        {
            path: '/projectHall/inviteTenders/openingAuthority',
            name: 'project_openingAuthority',
            meta: {
                title: '开通权限',
                titleI18nKey: 'i18n_menu_vebW_2d594a07',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'openingAuthority' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/OpeningAuthority.vue')
        },
        {
            path: '/projectHall/inviteTenders/selectParticipants',
            name: 'project_selectParticipants',
            meta: {
                title: '设置参与人员',
                titleI18nKey: 'i18n_menu_GRsULj_a0d00fda',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'selectParticipants' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/SelectParticipants.vue')
        },
        {
            path: '/projectHall/inviteTenders/viewChangeTenders',
            name: 'project_viewChangeTenders',
            meta: {
                title: '招标单变更',
                titleI18nKey: 'i18n_menu_YBtAH_a19aa425',
                keepAlive: false
            },
            component: () => import(/* webpackChunkName: 'selectParticipants' */ '@/views/srm/bidding_project/hall/purchase/inviteTenders/ViewChangeTenders.vue')
        }
    ]
}

export default inviteTendersRouter