<template>
  <div :style="{ padding: '0 0 32px 32px' }">
    <h4 :style="{ marginBottom: '20px' }">
      {{ title }}
    </h4>
    <v-chart
      :force-fit="true"
      :height="height"
      :data="data"
      :scale="scale"
    >
      <v-tooltip />
      <v-axis />
      <v-legend />
      <v-line
        position="type*y"
        color="x"
      />
      <v-point
        position="type*y"
        color="x"
        :size="4"
        :v-style="style"
        :shape="'circle'"
      />
    </v-chart>
  </div>
</template>

<script>
// import { DataSet } from '@antv/data-set'

export default {
    name: 'LineChartMultid',
    props: {
        title: {
            type: String,
            default: ''
        },
        dataSource: {
            type: Array,
            default: () => [
                { type: 'Jan', els: 7.0, wyssss: 3.9 },
                { type: 'Feb', els: 6.9, wyssss: 4.2 },
                { type: 'Mar', els: 9.5, wyssss: 5.7 },
                { type: 'Apr', els: 14.5, wyssss: 8.5 },
                { type: 'May', els: 18.4, wyssss: 11.9 },
                { type: 'Jun', els: 21.5, wyssss: 15.2 },
                { type: 'Jul', els: 25.2, wyssss: 17.0 },
                { type: 'Aug', els: 26.5, wyssss: 16.6 },
                { type: 'Sep', els: 23.3, wyssss: 14.2 },
                { type: 'Oct', els: 18.3, wyssss: 10.3 },
                { type: 'Nov', els: 13.9, wyssss: 6.6 },
                { type: 'Dec', els: 9.6, wyssss: 4.8 }
            ]
        },
        fields: {
            type: Array,
            default: () => ['els', 'wyssss']
        },
        // 别名，需要的格式：[{field:'name',alias:'姓名'}, {field:'sex',alias:'性别'}]
        aliases: {
            type: Array,
            default: () => []
        },
        height: {
            type: Number,
            default: 254
        }
    },
    data () {
        return {
            scale: [{
                dataKey: 'x',
                min: 0,
                max: 1
            }],
            style: { stroke: '#fff', lineWidth: 1 }
        }
    },
    computed: {
        data () {
            const dv = new DataSet.View().source(this.dataSource)
            dv.transform({
                type: 'fold',
                fields: this.fields,
                key: 'x',
                value: 'y'
            })
            let rows =  dv.rows
            // 替换别名
            rows.forEach(row => {
                for (let item of this.aliases) {
                    if (item.field === row.x) {
                        row.x = item.alias
                        break
                    }
                }
            })
            return rows
        }
    }
}
</script>
