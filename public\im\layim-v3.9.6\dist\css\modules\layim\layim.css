/** layui-v2.5.7 MIT License */
html #layuicss-skinlayimcss {
    display: none;
    position: absolute;
    width: 1989px
}

body .layui-layim,
body .layui-layim-chat {
    box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.3), 0px 8px 52px rgba(84, 102, 139, 0.3);
    border: 1px solid #D9D9D9;
    border-color: rgba(0, 0, 0, .05);
    background-repeat: no-repeat;
    background-color: #F6F6F6;
    color: #333;
    font-family: \5FAE\8F6F\96C5\9ED1;
}

body .layui-layim-chat {
    background-size: cover
}

body .layui-layim-chat .layui-layer-setwin {
    right: 28px;
    top: 24px;
}

body .layui-layim .layui-layer-title {
    height: 126px;
    border-bottom: none;
    background: #4E85FF;
    border-radius: 12px 12px 0 0;
}

.layui-layim-main {
	position: relative;
	left: 0;;
	top: -98px;
	/* margin-top: -98px; */
}

body .layui-layim .layui-layer-content,
body .layui-layim-chat .layui-layer-content {
    overflow: visible
}

body .layui-find, body .layui-selectMember,
body .layui-selectMember, body .layui-selectMember {
    box-shadow: 0px 0px 40px rgba(0, 0, 0, 0.3);
    background-color: #F2F6FE;
    border-radius: 24px 24px 20px 20px;
}

body .layui-find .layui-layer-title,
body .layui-selectMember .layui-layer-title {
	border-bottom: none;
	border-radius: 20px 20px 0 0;
	height: 60px;
	background: #4e85ff;
	line-height: 60px;
	font-size: 14px;
	color: #fff;
    letter-spacing: 1px;
}

body .layui-find .layui-layer-setwin,
body .layui-selectMember .layui-layer-setwin {
    top: 25px;
}

body .layui-find .layui-layer-setwin .layui-layer-max,
body .layui-selectMember .layui-layer-setwin .layui-layer-max {
  background-position: -97px -40px;
}

body .layui-find .layui-layer-setwin .layui-layer-close1,
body .layui-selectMember .layui-layer-setwin .layui-layer-close1 {
  background-position: -80px -40px;
}

body .layui-layim .layui-layer-setwin .layui-layer-close1,
body .layui-selectMember .layui-layer-setwin .layui-layer-close1 {
  background-position: -80px -40px;
}

body .layui-find .layui-layer-setwin .layui-layer-min cite,
body .layui-selectMember .layui-layer-setwin .layui-layer-min cite {
  background-color: #fff;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.layui-layim cite,
.layui-layim em,
.layui-layim-chat cite,
.layui-layim-chat em {
    font-style: normal
}

.layui-layim-info {
    height: 54px;
    margin-bottom: 38px;
}

.layui-layim-info * {
    font-size: 14px
}

.layim-tab-content li h5 *,
.layui-layim-info div,
.layui-layim-skin li,
.layui-layim-tab li,
.layui-layim-tool li {
    display: inline-block;
    vertical-align: top;
    *zoom: 1;
    *display: inline
}

.layim-tab-content li h5 span,
.layui-layim-info .layui-layim-user,
.layui-layim-list li p,
.layui-layim-list li span,
.layui-layim-remark {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.layui-layim-info .layui-layim-user {
	box-sizing: border-box;
	padding: 5px 16px 0 80px;
	width: 100%;
	height: 100%;
}
.layui-layim-info .layui-layim-user > img {
	position: absolute;
	left: 16px;
	top: 0;
	border: 2px solid #fff;
	border-radius: 100%;
	width: 50px;
	height: 50px;
}
.layui-layim-info .layui-layim-user > span.enterpriseName {
	font-weight: 500;
	font-size: 13px;
	color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.layui-layim-info .layui-layim-user > span.username {
	display: block;
	font-weight: 400;
	font-size: 12px;
	color: rgba(255, 255, 255, .5);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.layui-layim-status {
	position: absolute;
	left: 16px;
	top: 6px;
	border-radius: 100%;
	width: 10px;
	height: 10px;
	background: #3bdbcf;
}

.layim-status-online {
    color: #3FDD86
}

.layim-status-hide {
    color: #DD691D
}

.layim-menu-box {
    display: none;
    position: absolute;
    z-index: 100;
    top: 24px;
    left: -31px;
    padding: 5px 0;
    width: 85px;
    border: 1px solid #E2E2E2;
    border-radius: 2px;
    background-color: #fff;
    box-shadow: 1px 1px 20px rgba(0, 0, 0, .1)
}

.layim-menu-box li {
    position: relative;
    line-height: 22px;
    padding-left: 30px;
    font-size: 12px
}

.layim-menu-box li cite {
    padding-right: 5px;
    font-size: 14px
}

.layim-menu-box li i {
    display: none;
    position: absolute;
    left: 8px;
    top: 0;
    font-weight: 700;
    color: #4290F7
}

.layim-menu-box .layim-this i {
    display: block
}

.layim-menu-box li:hover {
    background-color: #eee
}

.layui-layim-remark {
    position: relative;
    left: -6px;
    display: block;
    width: 100%;
    border: 1px solid transparent;
    margin-top: 8px;
    padding: 0 5px;
    height: 26px;
    line-height: 26px;
    background: 0 0;
    border-radius: 2px
}

.layui-layim-remark:focus,
.layui-layim-remark:hover {
    border: 1px solid #d2d2d2;
    border-color: rgba(0, 0, 0, .15)
}

.layui-layim-remark:focus {
    background-color: #fff
}

.layui-layim-tab {
	position: absolute;
	left: 6%;
	top: 72px;
	z-index: 99;
	border-radius: 12px;
	width: 88%;
	height: 40px;
	background: #fff;
	box-shadow: 0 0 12px rgb(0 0 0 / 16%);
	line-height: 40px;
}
.layui-layim-tab > li {
	position: relative;
	width: 33.33%;
	cursor: pointer;;
	text-align: center;
	font-size: 22px;
	color: #666;
	color: rgba(0, 0, 0, .6);
}
.layim-tab-two li {
	width: 50%;
}

.layui-layim-tab >li >i.layui-icon {
    font-size: 20px;
    color: rgba(78, 133, 255, 0.5);
}

.layui-layim-tab >li.layim-this >i {
    position: relative;
    color: #4E85FF;
}

.layui-layim-tab >li.layim-this >i:after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: -10px;
    width: 100%;
    height: 4px;
    background-color: #4E85FF;
    border-radius: 2px;
    transform: translateX(-50%);
}

.layui-layim-tab li.layim-hide {
    display: none
}

.layui-layim-tab li:hover {
    opacity: .8;
    filter: Alpha(opacity=80)
}

.layim-tab-content {
    display: none;
    padding: 20px 0 10px 0;
    height: 349px;
    overflow: hidden;
    background-color: #fff;
}

.layim-tab-content.layim-list-friend {
    padding: 36px 0 10px 0;
}

.layim-tab-content:hover {
    overflow-y: auto
}

.layim-tab-content li h5 {
    position: relative;
    margin-right: 15px;
    padding-left: 30px;
    height: 28px;
    line-height: 28px;
    cursor: pointer;
    font-size: 0;
    white-space: nowrap;
    overflow: hidden
}

.layim-tab-content li h5 * {
    font-size: 12px
}

.layim-tab-content li h5 span {
	max-width: 125px;
	font-weight: 400;
	font-size: 13px;
	color: #2a3141;
}

.layim-tab-content li h5 i {
    position: absolute;
    left: 12px;
    top: 0;
    color: rgba(0, 0, 0, 0.9);
}

.layim-tab-content li h5 em {
    padding-left: 5px;
    color: #2a3141
}

.layim-tab-content li h5[lay-type=true] i {
    top: 2px
}

.layim-tab-content li ul {
    display: none;
    margin: 16px 0 10px;
}

.layui-layim-list li {
    position: relative;
    height: 42px;
    padding: 5px 36px 5px 64px;
    font-size: 0;
    cursor: pointer;
}

.layui-layim-list li:not(:last-child) {
    border-bottom: 1px solid #F0F1F2;
}

.layui-layim-list li:hover {
    background-color: rgba(21, 112, 255, 0.04)
}

.layui-layim-list li.layim-null {
	padding: 0;
	border: 1px solid #f0f1f2;
	height: 56px;
	background: #f6f9ff;
	cursor: default;
	line-height: 56px;
	text-align: center;
	font-weight: 400;
	font-size: 12px;
	color: #454f5966;
}

.layui-layim-list li * {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 14px
}

.layui-layim-list li span.username {
	max-width: 155px;
	font-weight: 500;
	font-size: 13px;
	color: #454f59;
}
.layui-layim-list li img {
	position: absolute;
	left: 15px;
	top: 8px;
	border-radius: 100%;
	width: 40px;
	height: 40px;
}
.layui-layim-list li p {
	display: block;
	margin: 0;
	padding: 0;
	line-height: 2;
	font-size: 12px;
	color: rgba(69, 79, 89, .4);
}

.layui-layim-list li span.status {
    position: absolute;
    right: 8px;
    top: 5px;
    font-size: 12px;
    /* 10号字体 */
    transform: scale(0.83);
    color: #454F5966;
}

.layui-layim-list li span.status.online {
    color: #3BDBCF;
}

.layui-layim-list li span.badge {
	position: absolute;
	right: 6px;
	top: 50%;
	border-radius: 10px;
	font-size: 12px;
	transform: translateY(-50%) scale(.83);
}
.layui-layim-list li span.badge.multipleWords {
	padding: 0 8px;
}

.layui-layim-list li .layim-msg-status {
    display: none;
    position: absolute;
    right: 10px;
    bottom: 7px;
    padding: 0 5px;
    height: 16px;
    line-height: 16px;
    border-radius: 16px;
    text-align: center;
    font-size: 10px;
    background-color: #F74C31;
    color: #fff
}

.layim-list-gray {
    -webkit-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    filter: grayscale(100%);
    filter: gray
}

.layui-layim-tool {
    font-size: 0;
    background-color: #F6F6F6;
    padding: 0;
    border-radius: 0 0 12px 12px;
    height: 40px;
    background-color: #f6f9ff;
    line-height: 40px;
}

.layui-layim-tool li {
    position: relative;
    width: 25%;
    height: 37px;
    line-height: 40px;
    text-align: center;
    font-size: 22px;
    cursor: pointer
}

.layui-layim-tool li:active {
    background-color: #e2e2e2
}

.layui-layim-tool .layim-tool-msgbox {
    line-height: 37px
}

.layui-layim-tool .layim-tool-find {
    line-height: 38px
}

.layui-layim-tool .layim-tool-skin {
    font-size: 26px
}

.layim-tool-msgbox span {
    display: none;
    position: absolute;
    left: 23px;
    top: -16px;
    height: 20px;
    line-height: 20px;
    padding: 0 10px;
    border-radius: 2px;
    background-color: #4e85ff;
    color: #fff;
    font-size: 12px;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.layim-tool-msgbox .layer-anim-05 {
    display: block
}

.layui-layim-search {
    display: none;
    position: absolute;
    bottom: 5px;
    left: 5px;
    height: 28px;
    line-height: 28px
}

.layui-layim-search input {
    width: 210px;
    padding: 0 30px 0 10px;
    height: 30px;
    line-height: 30px;
    border: none;
    border-radius: 3px;
    background-color: #ddd
}

.layui-layim-search label {
    position: absolute;
    right: 6px;
    top: 4px;
    font-size: 20px;
    cursor: pointer;
    color: #333;
    font-weight: 400
}

.layui-layim-skin {
    margin: 10px 0 0 10px;
    font-size: 0
}

.layui-layim-skin li {
    margin: 0 10px 10px 0;
    line-height: 60px;
    text-align: center;
    background-color: #f6f6f6
}

.layui-layim-skin li,
.layui-layim-skin li img {
    width: 86px;
    height: 60px;
    cursor: pointer
}

.layui-layim-skin li img:hover {
    opacity: .8;
    filter: Alpha(opacity=80)
}

.layui-layim-skin li cite {
    font-size: 14px;
    font-style: normal
}

body .layui-layim-chat {
    background-color: #fff
}

body .layui-layim-chat-list {
    width: 760px
}

body .layui-layim-chat .layui-layer-title {
    height: 60px;
    border-bottom: none;
    background-color: #FFFFFF;
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
}

body .layui-layim-chat .layui-layer-content {
    background: 0 0
}

.layui-layim-min .layui-layer-content * {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 14px
}

.qqtLogo {
	position: absolute;
	top: -60px;
	z-index: 1000;
	width: 210px;
	height: 72px;
}
.qqtLogo::before {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 152px;
	height: 38px;
	background: url('./skin/qqtSrm.jpg') no-repeat center top/152px 38px;
    content: "";
	transform: translate(-50%, -50%);
}

.layim-chat-list {
    display: none;
    position: absolute;
    z-index: 1000;
    top: 12px;
    width: 210px;
    height: 100%;
    font-size: 0;
    overflow-x: hidden;
    overflow-y: auto;
}

.layim-chat-list:hover {
    overflow-y: auto
}

.layim-chat-list >li * {
	display: inline-block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.layim-chat-list >li {
	position: relative;
	box-sizing: border-box;
	padding: 14px 36px 0 70px;
	height: 72px;
}

.layim-chat-list >li::after {
	display: none;
	position: absolute;
	left: -1px;
	top: 0px;
	width: 4px;
	height: 72px;
	background: rgba(255, 255, 255, .5);
	content: "";
}

.layim-chat-list >li:not(:last-child) {
	border-bottom: 1px solid rgba(255, 255, 255, .16);
}

.layui-layim-min .layui-layer-content {
    position: relative;
    margin: 5px;
    padding: 5px 30px 5px 5px;
    line-height: 40px;
    cursor: pointer;
    border-radius: 3px
}

.layim-chat-list >li img {
    width: 40px;
    height: 40px;
    border-radius: 100%;
    border: 2px solid #fff;
    position: absolute;
	left: 16px;
	top: 14px;
}

.layui-layim-min .layui-layer-content .wrapper {
	position: relative;
	padding: 0 32px;
	border-top-right-radius: 10px;
	border-bottom-right-radius: 10px;
	height: 36px;
	background: #4e85ff;
	line-height: 36px;
}
.layui-layim-min .layui-layer-content img {
	position: absolute;
	left: -25px;
	top: -5px;
	border: 2px solid #fff;
	border-radius: 100%;
	width: 42px;
	height: 42px;
	box-shadow: 0 0 0 4px #4e85ff;
}
.layui-layim-photos {
	cursor: crosshair;
}
.layim-chat-list > li span.username {
	max-width: 106px;
	font-size: 13px;
	color: #fff;
}
.layui-layim-min .layui-layer-content span.ellipsis {
	max-width: 86px;
	font-size: 13px;
	color: #fff;
}
.layui-layim-min .layui-layer-content .wrapper i.layui-icon {
	position: absolute;
	right: 12px;
	top: 0;
	color: #3bdbcf;
	transform: rotate(-90deg);
}

.layim-chat-list >li p {
	display: block;
	margin: 0;
	padding: 0;
	line-height: 2;
	font-size: 12px;
	color: rgba(29, 19, 19, 0.5);
    max-width: 106px;
}

.layim-chat-list >li span.status {
    position: absolute;
    right: 8px;
    top: 14px;
    font-size: 12px;
    /* 10号字体 */
    transform: scale(0.83);
    color: #454F5966;
}

.layim-chat-list >li span.status.online {
    color: #3BDBCF;
}

.layim-chat-list >li span.badge {
	position: absolute;
	right: 6px;
	top: 50%;
	border-radius: 10px;
	font-size: 12px;
	transform: translateY(-50%) scale(.83);
}
.layim-chat-list >li span.badge.multipleWords {
	padding: 0 8px;
}

.layim-chat-list >li .layim-msg-status {
    display: none;
    position: absolute;
    right: 10px;
    bottom: 7px;
    padding: 0 5px;
    height: 16px;
    line-height: 16px;
    border-radius: 16px;
    text-align: center;
    font-size: 10px;
    background-color: #F74C31;
    color: #fff
}

.layim-chat-list > li.layui-show:hover span.status,
.layim-chat-list > li.layui-show:hover span.badge,
.layim-chat-list > li.layui-show:hover .layim-msg-status {
	display: none;
}

.layim-chat-list >li span cite {
    color: #999;
    padding-left: 10px
}

.layim-chat-list >li.layim-this, .layim-chat-list >li:hover {
    background: rgba(255, 255, 255, 0.16);
}

.layim-chat-list >li.layim-this::after, .layim-chat-list >li:hover::after {
    display: block;
}

.layim-chat-list >li .layui-icon {
    display: none;
    position: absolute;
    right: 6px;
    top: 20px;
    color: #FFFFFF;
    font-size: 22px
}

.layim-chat-list >li.layui-show:hover .layui-icon {
    display: inline-block;
}

.layim-chat-system {
    margin: 10px 0;
    text-align: center
}

.layim-chat-system span {
    display: inline-block;
    line-height: 30px;
    padding: 0 15px;
    border-radius: 3px;
    background-color: #e2e2e2;
    cursor: default;
    font-size: 14px
}

.layim-chat {
    display: none;
    position: relative;
    background-color: #fff;
    background-color: #F2F6FE;
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;
}

.layim-chat-title {
    position: absolute;
    top: -60px;
    height: 60px
}

.layim-chat-other {
    /* position: relative; */
    /* top: 0; */
    /* left: 0; */
    height: 60px;
    padding-left: 66px;
    padding-top: 12px;
    cursor: default;
    box-sizing: border-box;
}

.layim-chat-other img {
    position: absolute;
    left: 18px;
    top: 12px;
    width: 40px;
    height: 40px;
    border-radius: 100%;
}

.layim-chat-username {
    display: inline-block;
    /* position: relative; */
    font-size: 13px;
    font-weight: 500;
}

.layim-chat-group .layim-chat-other .layim-chat-username {
    cursor: pointer
}

.layim-chat-other .jumpDetail {
    color: #454F59;
    letter-spacing: 1px;
}

.layim-chat-other .jumpDetail,
.layim-chat-other .layim-chat-status > span {
	display: inline-block;
	overflow: hidden;
	max-width: 400px;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.layim-chat-other .layim-chat-status {
    margin: 0px;
}

.layim-chat-other .layim-chat-status >span {
    font-size: 12px;
    color: rgba(69, 79, 89, 0.4);
}

.layim-chat-group .layim-chat-other .layim-chat-username em {
    padding-left: 4px;
    color: #999;
    vertical-align: top;
}

.layim-chat-main {
    height: 262px;
    padding: 15px 15px 5px;
    overflow-x: hidden;
    overflow-y: auto
}

.layim-chat-main >ul >li {
    position: relative;
    font-size: 0;
    margin-bottom: 10px;
    padding-left: 60px;
    min-height: 68px;
}

/* 撤回消息class */
.layim-chat-main >ul >li.layim-chat-undo {
    line-height: 68px;
    text-align: center;
    font-size: 12px;
    color: #393D49;
    padding: 0;
    margin: 0;
}

.layim-chat-main >ul >li.layim-chat-undo span {
    padding: 8px 12px;
    background: #EAEEF2;
    color: rgba(69, 79, 89, 0.6);
    border-radius: 8px;
}

.toggle {
    position: absolute;
	right: 18px;
	top: -34px;
}

.toggle >.layui-icon {
    font-size: 24px;
    color: rgba(69, 79, 89, 0.6);
    cursor: pointer;
}

.toggle >.layui-icon:hover {
    color: #4290F7;
}

.member-setting {
    display: none;
	position: absolute;
	right: 0;
	top: 0;
	z-index: 999;
	border-left: 1px solid #e4e9f1;
	border-top: 1px solid #e4e9f1;
	border-bottom-right-radius: 24px;
	width: 250px;
	height: 100%;
	background: #fff;
    padding: 16px;
    box-sizing: border-box;
}

.member-setting >ul {
    *zoom: 1;
    margin: 0;
    height: 220px;
    overflow-x: hidden;
    overflow-y: auto;
    border-bottom: 1px solid rgba(69, 79, 89, 0.08);
}

.member-setting >ul::after {
    visibility: hidden;
    clear: both;
    display: block;
    content: '.';
    height: 0;
}

.member-setting >ul >li {
    width: 33.33%;
    float: left;
    text-align: center;
    margin-bottom: 16px;
    height: 60px;
    overflow: hidden;
}

.member-setting >ul >li >a {
    display: block;
    padding: 0 6px;
    color: rgba(69, 79, 89, 0.6);
}

.member-setting >ul >li img {
	border-radius: 100%;
	width: 30px;
	height: 30px;
}

.member-setting >ul >li i.layui-icon {
    border-radius: 100%;
    width: 30px;
    height: 30px;
    border: 1px dashed rgba(69, 79, 89, 0.4);
    display: inline-block;
    line-height: 30px;
}

.member-setting >ul >li cite {
	display: inline-block;
	overflow: hidden;
	margin-top: 6px;
	width: 100%;
	text-overflow: ellipsis;
	font-size: 12px;
	color: rgba(69, 79, 89, .8);
	white-space: nowrap;
}

.member-setting .menu-wrapper >ul >li {
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    color: #F93C00;
    border-bottom: 1px solid rgba(69, 79, 89, 0.08);
}

.layim-chat-text,
.layim-chat-user {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 14px
}

.layim-chat-user {
    position: absolute;
    left: 3px
}

.layim-chat-user img {
    width: 40px;
    height: 40px;
    border-radius: 100%
}

.layim-chat-user cite {
	overflow: hidden;
	position: absolute;
	left: 60px;
	top: -2px;
	width: 500px;
	line-height: 24px;
	text-overflow: ellipsis;
	text-align: left;
	font-style: normal;
	font-size: 12px;
	color: rgba(69, 79, 89, .4);
	white-space: nowrap;
}

.layim-chat-user cite i {
    padding-left: 15px;
    font-style: normal
}

.layim-chat-text {
    position: relative;
    line-height: 22px;
    margin-top: 25px;
    padding: 8px 15px;
    background-color: #fff;
    border-radius: 3px;
    color: #454F59;
    word-break: break-all;
    max-width: 462px\9;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 16px;
}

.layim-chat-text.chatlog {
    background-color: #F2F6FE;
}

/* .layim-chat-text:after {
    content: '';
    position: absolute;
    left: -10px;
    top: 13px;
    width: 0;
    height: 0;
    border-style: solid dashed dashed;
    border-color: #e2e2e2 transparent transparent;
    overflow: hidden;
    border-width: 10px
} */

.layim-chat-text a {
    color: #33DF83
}

.layim-chat-text img {
    max-width: 100%;
    vertical-align: middle
}

.layim-chat-text .layui-layim-file,
.layui-layim-file {
    display: block;
    text-align: center
}

.layim-chat-text .layui-layim-file {
    color: #333
}

.layui-layim-file:hover {
    opacity: .9
}

.layui-layim-file i {
    font-size: 80px;
    line-height: 80px
}

.layui-layim-file cite {
    display: block;
    line-height: 20px;
    font-size: 14px
}

.layui-layim-audio {
    text-align: center;
    cursor: pointer
}

.layui-layim-audio .layui-icon {
    position: relative;
    top: 5px;
    font-size: 24px
}

.layui-layim-audio p {
    margin-top: 3px
}

.layui-layim-video {
    width: 120px;
    height: 80px;
    line-height: 80px;
    background-color: #333;
    text-align: center;
    border-radius: 3px
}

.layui-layim-video .layui-icon {
    font-size: 36px;
    cursor: pointer;
    color: #fff
}

.layim-chat-main ul .layim-chat-system {
    min-height: 0;
    padding: 0
}

.layim-chat-main ul .layim-chat-mine {
    text-align: right;
    padding-left: 0;
    padding-right: 60px
}

.layim-chat-mine .layim-chat-user {
    left: auto;
    right: 3px
}

.layim-chat-mine .layim-chat-user cite {
    left: auto;
    right: 60px;
    text-align: right
}

.layim-chat-mine .layim-chat-user cite i {
    padding-left: 0;
    padding-right: 15px
}

.layim-chat-mine .layim-chat-text {
    margin-left: 0;
    text-align: left;
    background-color: #4290F7;
    color: #fff;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 0;
}

/* .layim-chat-mine .layim-chat-text:after {
    left: auto;
    right: -10px;
    border-top-color: #5FB878
} */

.layim-chat-mine .layim-chat-text a {
    color: #fff;
}

.layim-chat-footer {
	border-top: 1px solid #f1f1f1;
	border-bottom-left-radius: 24px;
	border-bottom-right-radius: 24px;
	background: #fff;
}

.layim-chat-tool {
    position: relative;
    padding: 0 8px;
    height: 38px;
    line-height: 38px;
    font-size: 0;
}

.layim-chat-tool span {
    position: relative;
    margin: 0 10px;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 24px;
    cursor: pointer
}

.layim-chat-tool .layim-tool-log {
    position: absolute;
    right: 5px;
    font-size: 14px
}

.layim-tool-log i {
    position: relative;
    top: 2px;
    margin-right: 5px;
    font-size: 20px;
    color: #999
}

.layim-tool-image input {
    position: absolute;
    font-size: 0;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: .01;
    filter: Alpha(opacity=1);
    cursor: pointer
}

body .layui-layim-face {
    margin: 10px 0 0 -18px;
    border: none;
    background: 0 0
}

body .layui-layim-face .layui-layer-content {
    padding: 0;
    background-color: #fff;
    color: #666;
    box-shadow: none
}

.layui-layim-face .layui-layer-TipsG {
    display: none
}

.layui-layim-face ul {
    position: relative;
    width: 372px;
    padding: 10px;
    border: 1px solid #D9D9D9;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, .2)
}

.layui-layim-face ul li {
    cursor: pointer;
    float: left;
    border: 1px solid #e8e8e8;
    height: 22px;
    width: 26px;
    overflow: hidden;
    margin: -1px 0 0 -1px;
    padding: 4px 2px;
    text-align: center
}

.layui-layim-face ul li:hover {
    position: relative;
    z-index: 2;
    border: 1px solid #eb7350;
    background: #fff9ec
}

.layim-chat-textarea {
    margin-left: 10px
}

.layim-chat-textarea textarea {
    display: block;
    width: 100%;
    padding: 5px 0 0;
    height: 68px;
    line-height: 20px;
    border: none;
    overflow: auto;
    resize: none;
    background: 0 0
}

.layim-chat-textarea textarea:focus {
    outline: 0
}

.layim-chat-bottom {
    position: relative;
    height: 46px
}

.layim-chat-send {
    position: absolute;
    right: 15px;
    top: 3px;
    height: 32px;
    line-height: 32px;
    font-size: 0;
    cursor: pointer
}

.layim-chat-send span {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 14px;
    line-height: 32px;
    margin-left: 5px;
    padding: 0 20px;
    background-color: #4290F7;
    color: #fff;
    border-radius: 4px;
}

.layim-chat-send span:hover {
    background-color: #5DA4F8
}

.layim-chat-send span:active {
    background-color: #4290F7
}

.layim-chat-send .layim-send-btn {
    border-radius: 4px 0 0 4px
}

.layim-chat-send .layim-send-set {
    position: relative;
    width: 30px;
    height: 32px;
    margin-left: 0;
    padding: 0;
    border-left: 1px solid #5DA4F8;
    border-radius: 0 4px 4px 0
}

.layim-send-set .layui-edge {
    position: absolute;
    top: 14px;
    left: 9px;
    border-width: 6px;
    border-top-style: solid;
    border-top-color: #fff
}

.layim-chat-send .layim-menu-box {
    left: auto;
    right: 0;
    top: 33px;
    width: 180px;
    padding: 10px 0
}

.layim-chat-send .layim-menu-box li {
    padding-right: 15px;
    line-height: 28px
}

body .layui-layim-min {
    background: transparent;
    box-shadow: none;
}

.layui-layim-min .layui-layer-content {
    margin: 0 5px;
    padding: 5px 10px;
    white-space: nowrap;
}

.layui-layim-min.layui-layer-page .layui-layer-content {
    overflow: unset;
}

.layui-layim-close .layui-layer-content span {
    width: auto;
    max-width: 120px
}

body .layui-layim-members {
    margin: 25px 0 0 -75px;
    border: none;
    background: 0 0
}

body .layui-layim-members .layui-layer-content {
    padding: 0;
    background: 0 0;
    color: #666;
    box-shadow: none
}

.layui-layim-members .layui-layer-TipsG {
    display: none
}

.layui-layim-members ul {
    position: relative;
    width: 578px;
    height: 200px;
    padding: 10px 10px 0;
    border: 1px solid #D9D9D9;
    background-color: #fff;
    background-color: rgba(255, 255, 255, .9);
    box-shadow: none;
    overflow: hidden;
    font-size: 0
}

.layui-layim-members ul:hover {
    overflow: auto
}

.layim-add-img,
.layim-add-remark,
.layui-layim-members li {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 14px
}

.layui-layim-members li {
    width: 112px;
    margin: 10px 0;
    text-align: center
}

.layui-layim-members li a {
    position: relative;
    display: inline-block;
    max-width: 100%
}

.layui-layim-members li a:after {
    content: '';
    position: absolute;
    width: 46px;
    height: 46px;
    left: 50%;
    margin-left: -23px;
    top: 0;
    border: 1px solid #eee;
    border-color: rgba(0, 0, 0, .1);
    border-radius: 100%
}

.layui-layim-members li img {
    width: 48px;
    height: 48px;
    border-radius: 100%
}

.layui-layim-members li:hover {
    opacity: .9
}

.layui-layim-members li a cite {
    display: block;
    padding: 0 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

body .layui-layim-contextmenu {
    margin: 70px 0 0 30px;
    width: 200px;
    padding: 5px 0;
    border: 1px solid #ccc;
    background: #fff;
    border-radius: 0;
    box-shadow: 0 0 5px rgba(0, 0, 0, .2)
}

body .layui-layim-contextmenu .layui-layer-content {
    padding: 0;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    box-shadow: none
}

.layui-layim-contextmenu .layui-layer-TipsG {
    display: none
}

.layui-layim-contextmenu li {
    padding: 0 15px 0 35px;
    cursor: pointer;
    line-height: 30px
}

.layui-layim-contextmenu li:hover {
    background-color: #F2F2F2
}

body .layui-layim-qqtmenu .layui-layer-content {
	padding: 8px;
	border: 1px solid #fff;
	border-radius: 8px;
	background: #e2e2e3;
	box-shadow: 0 0 16px rgba(0, 0, 0, .3);
}
body .layui-layim-qqtmenu .layui-layer-TipsG {
	display: none;
}
body .layui-layim-qqtmenu .layui-layer-content ul {
	margin: 0;
}
body .layui-layim-qqtmenu .layui-layer-content li {
	padding: 0 8px;
	border-radius: 8px;
	height: 26px;
	line-height: 26px;
	font-size: 13px;
	color: #2a3141;
}
body .layui-layim-qqtmenu .layui-layer-content li:hover {
	background: #639cf8;
	color: #fff;
}

body .layui-layim-qqtmenu .layui-layer-content li select {
    margin-left: 6px;
}

body .layui-layim-qqtmenu .layui-layer-content li:hover select {
	color: #2a3141;
}

.layim-add-box {
    margin: 15px;
    font-size: 0
}

.layim-add-img img,
.layim-add-remark p {
    margin-bottom: 10px
}

.layim-add-img {
    width: 100px;
    margin-right: 20px;
    text-align: center
}

.layim-add-img img {
    width: 100px;
    height: 100px
}

.layim-add-remark {
    width: 280px
}

.layim-add-remark .layui-select {
    width: 100%;
    margin-bottom: 10px
}

.layim-add-remark .layui-textarea {
    height: 80px;
    min-height: 80px;
    resize: none
}

.layim-tab-content,
.layui-layim-face ul,
.layui-layim-tab {
    margin-bottom: 0
}

.layim-tab-content li h5 {
    margin-top: 0;
    margin-bottom: 0
}

.layui-layim-face img {
    vertical-align: bottom
}

.layim-chat-other span {
    color: #444
}

.layim-chat-other span cite {
    padding: 0 15px;
    color: #999
}

.layim-chat-other:hover {
    text-decoration: none
}