import tileEditPage from './tileEditPage'
import { ButtonComponent } from '@comp/template/business/class/ComponentFactory'
export const tileEditPageMixin = {
    components: {
        tileEditPage
    },
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            pageData: {
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', clickFn: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack }
                ]
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.requestTabsData(this.currentEditRow.id)
            }
        },
        // 表格向下填充
        fillDownGridItem (info, otherParams) {
            new ButtonComponent().gridFillDown(info, otherParams)
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (row) {
            this.$refs.editPage.handleDownload(row)
        },
        refreshList () {
            this.$emit('ok')
        },
        saveEvent () {
            this.$refs.editPage.saveEvent()
        },
        requestAfter (data, formModel) {
            let list = this.pageData.panels[0].content.list || []
            let targetRelationList = list.filter((item)=> item.targetRelationData)
            targetRelationList.forEach((relation)=>{
                if (relation.targetRelationData && relation.targetRelationData.fieldName) {
                    relation.dictCode = 'message_'+ formModel[relation.targetRelationData.fieldName]
                } else {
                    console.warn('没有配置关联的fieldName字段')
                }
            })
        }
    }
}