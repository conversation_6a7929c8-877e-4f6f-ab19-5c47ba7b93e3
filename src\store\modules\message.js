import { getAction } from '@/api/manage'
const app = {
    state: {
        msgTotal: 0
    },
    mutations: {
        msgTotalNumber (state, newData) {
            state.msgTotal = newData
        }
    },
    actions: {
        updateMatTotal: ({ commit }) => {
            const url = 'message/elsMsgRecord/list'
            const params = {
                pageNo: 1,
                pageSize: 5,
                handleFlag: '0',
                column: 'id',
                order: 'desc'
            }
            getAction(url, params).then(res => {
                commit('msgTotalNumber', res.result.total || 0)
            })
            
        }
    }
}

export default app