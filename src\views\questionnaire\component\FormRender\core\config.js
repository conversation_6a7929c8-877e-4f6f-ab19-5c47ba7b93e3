import { srmI18n, getLangAccount } from '@/utils/util'


const defaultConfig = {
    input: {
        value: '',
        type: 'string',
        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseFill`, '请填入')
    },
    inputNumber: {
        value: 0,
        type: 'number',
        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseFill`, '请填入')
    },
    score: {
        value: 1,
        type: 'number'
    },
    radio: {
        value: undefined,
        type: 'string'
    },
    checkbox: { // 注意：凡是数组类型的最少验证需要添加 required:true 配置项
        value: [],
        type: 'array'
    },
    select: {
        value: undefined,
        type: 'string',
        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseChoose`, '请选择')
    },
    file: {
        value: [],
        type: 'array'
    }
}

export const defaultMsg = {
    required: srmI18n(`${getLangAccount()}#i18n_title_isRequired`, '此项为必选项！'),
    range: '此项需大于{min}小于{max}',
    len: '此项长度为{len}',
    whitespace: srmI18n(`${getLangAccount()}#i18n_title_cannotSpace`, '不能是空格')
}

export const patterns = {
    lowercase: { // todo 添加模式邮箱，url...
        regexp: /^[a-z]+$/,
        message: srmI18n(`${getLangAccount()}#i18n_title_fillInNonLowercaseLetters`, '填入内容非小写字母！')
    }
}

export const validateUI = {
    icon: true
}

export default defaultConfig
