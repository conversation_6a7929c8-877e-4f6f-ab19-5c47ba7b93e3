import ListLayout from './ListLayout.vue'
import { getAction } from '@/api/manage'
import { ajaxFindDictItems, ajaxFindCount, getSysTime } from '@/api/api'
import {cloneDeep} from 'lodash'
export const ListMixin = {
    components: {
        ListLayout
    },
    inject: ['closeCurrent'],
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                publicBtn: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Query`, '查询'), type: 'primary', icon: 'search', clickFn: this.searchEvent },
                    //{label: '高级查询', type: 'primary', icon: 'search', clickFn: this.superSearchEvent},
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reset`, '重置'), icon: 'reload', clickFn: this.resetEvent }
                ],
                button: [
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary' },
                    // {label: '删除', icon: 'delete', clickFn: this.handleDelete},
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), icon: 'import', folded: true, type: 'upload' },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
                    { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '说明附件'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
                ],
                defaultButton: [{ label: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KNlR_40eee965`, '视频说明'), icon: 'video-camera', folded: true, isDocument: true, clickFn: this.showHelpVideo }],
                showOptColumn: true,
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView },
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit },
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord }
                ],
                showGridLayoutBtn: true, // 列表格布局切换按钮
                optColumnWidth: 150,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            tabsList: [],
            //当前点击的行数据
            currentEditRow: {},
            _countTabsUrl: '', // 缓存tab数量接口，返回后更新count使用
            chatType: '',

            viewRow: null,
        }
    },
    watchRouterContol: false,
    created () {
        this.handleRemoveWatchRouter = this.$watch('$route.query', (newVal) => {
            let result = null
            if (newVal && Object.keys(newVal).length) {
                result = this.handleRouterChange(newVal)
            } 
            return result
        }, {
            deep: true,
            immediate: true
        })

        this.$options.watchRouterContol = true
    },
    activated () {
        console.log('activated')
        let { query } = this.$route
        if (!this.$options.watchRouterContol) {
            this.handleRemoveWatchRouter = this.$watch('$route.query', (newVal) => {
                let result = null
                if (newVal && Object.keys(newVal).length) {
                    result = this.handleRouterChange(newVal)
                } 
                return result
            }, {
                deep: true,
                immediate: true
            })
        }
        if(query.toList&&query.myToDo){
            let that=this   
            this.showEditPage = false
            let listPage=this.$refs.listPage
            if (that.tabsList.length > 0) {
                for (let [i, v] of that.tabsList.entries()) {
                    if (v[v.proName]==query[that.tabsList[0].proName]) {
                        listPage.activeKey=i
                        listPage.$nextTick(()=>{
                            listPage.$forceUpdate()
                        })
                    }
                }
            }
            setTimeout(() => {
                this.$refs.listPage.loadData('', query)
            }, 2000)
              
        }
        this.imJumpDetailFun()
    },
    deactivated () {
        console.log('deactivated')
        // if (this.$options.watchRouterContol) {
        this.$options.watchRouterContol = false
        this.handleRemoveWatchRouter()
        if (this.$store.state.app.closeTab) { //是否关闭tab
            this.changeTabCloseHandle()
        }
    },
    // 使用 beforeRouteUpdate 导航守卫监听路由变化
    beforeRouteUpdate (to, from, next) {
        if (to.query.linkRouteType === 'im' && to.path === from.path) {
            this.imJumpDetailFun()
        }
        next()
    },
    methods: {
        // 默认导入excelg 导入操作前校验操作
        // 需要返回promise
        handleBeforeImportExcel (data) {
            console.log('data :>> ', data)
            return Promise.resolve({
                flag: true,
                params: {}
            })
        },
        changeTabCloseHandle () {
            this.showDetailPage = false
            this.showEditPage = false
            this.$store.dispatch('SetTabCloseStatus', false) // 重写必须重置状态
        },
        handleRemoveWatchRouter: () => { },
        handleRouterChange (query) {
            let row = {
                id: query.id,
                templateName: query.templateName,
                templateNumber: query.templateNumber,
                templateAccount: query.templateAccount,
                templateVersion: query.templateVersion
            }
            if (query.open && !query.pageShow) {
                if (row.id) {
                    this.showEditPage = false
                    this.handleView(row)
                }
            } else if (query.pageShow) {
                if (row.id) {
                    this.showEditPage = false
                    this.handlePageShow(row)
                }
            } else if (query.linkFilter || query.toList) {
                this.$nextTick(() => {
                    this.showEditPage = false
                    this.$refs.listPage.loadData('', query)
                })
            } else {
                this.showDetailPage = false
                this.$nextTick(() => {
                    this.$refs.listPage && this.$refs.listPage.loadData()
                })
            }
        },
        // 废弃ing
        handleCacheUpdate () {
            if (this.$options.watchRouterContol) return
            this.$options.watchRouterContol = true
            let { query } = this.$route
            if (query.open || query.pageShow) {
                let row = {
                    id: query.id,
                    templateName: query.templateName,
                    templateNumber: query.templateNumber,
                    templateVersion: query.templateVersion
                }
                if (row.id) {
                    this.handleView(row)
                }
            } else {
                this.showDetailPage = false
            }
        },
        // tab页签改变前
        handleAfterChangeTab ({ _this, activeTabData, pageData, listGrid, tablePage }) {
            if (tablePage) {
                tablePage.currentPage = 1
            }
            if (listGrid) {
                listGrid.clearCheckboxReserve()
                listGrid.clearCheckboxRow()
                _this.loadData(activeTabData)
            }
        },
        // 要隐藏的列名，表格和要隐藏的列名
        autoHideColumns (grid, hideColumn) {
            let allColumns = grid.getTableColumn()
            if (allColumns.fullColumn) {
                allColumns.fullColumn.forEach((col) => {
                    col.visible = true
                    if (hideColumn && hideColumn.length) {
                        hideColumn.forEach((hideCol) => {
                            if (hideCol === col.property) {
                                col.visible = false
                            }
                        })
                    }
                })
            }
            grid.refreshColumn()
        },
        btnInvalidAuth (code) {
            return !this.$hasOptAuth(code)
        },
        // 按业务配置规则隐藏按钮
        hideBtnByBusinessRule (code) {
            return this.$hideBtnByBusinessRule(code)
        },
        // 改变tab页签时，按钮和列的动态显示和隐藏
        handleAfterChangeTabOpt ({ _this, activeTabData, pageData, listGrid, tablePage }) {
            this.handleAfterChangeTab({ _this, activeTabData, pageData, listGrid, tablePage })
            // 传入按不同的逻辑要隐藏的数组列
            let hideCols = [] // 例如：activeTabData.title === '议价中'? ['templateName', 'enquiryNumber']: [ 'enquiryNumber']
            if (activeTabData.title === '合格' || activeTabData.title === '全部') {
                hideCols = ['appealReason', 'replayFinishTime', 'replyResult_dictText', 'replyOption', 'autoReply', 'appealStatus_dictText', 'appealWindow', 'appealDeadTime', 'replyWindow', 'replyDeadTime', 'appealTime', 'appealUser', 'replyUser']
            }
            this.autoHideColumns(listGrid, hideCols)
        },
        showHelpText () {
            this.$refs.listPage.showHelpText()
        },
        showHelpPDF () {
            this.$refs.listPage.showHelpPDF()
        },
        showHelpVideo () {
            this.$refs.listPage.showHelpVideo()
        },
        modalFormOk () {
            // 新增/修改 成功时，重载列表
            this.$refs.listPage.loadData()
        },
        serachTabs (dictCode, field) {
            let that = this
            if (dictCode) {
                let postData = {
                    busAccount: this.$ls.get('Login_elsAccount'),
                    dictCode: dictCode
                }
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        let tabAll = { title: that.$srmI18n(`${that.$getLangAccount()}#i18n_title_all`, '全部') }
                        tabAll[field] = null
                        let options = []
                        options.push(tabAll)
                        res.result.map((item) => {
                            let tab = { title: that.$srmI18n(`${that.$getLangAccount()}#${item.textI18nKey}`, item.title) }
                            tab[field] = item.value
                            options.push(tab)
                        })
                        that.tabsList = options
                    }
                })
            }
        },
        serachCountTabs (url) {
            let that = this
            ajaxFindCount(url).then((res) => {
                if (res.success) {
                    let options = []
                    let array = res.result || []
                    array.forEach((data) => {
                        let tab = {}
                        tab.title = data.title
                        tab[data.fileName] = data.value
                        tab.total = data.total
                        tab.rejectReason = data.rejectReason
                        tab.proName = data.fileName
                        options.push(tab)
                    })
                    that.tabsList = options
                    this._countTabsUrl = url
                }
            })
        },
        selectedTemplate (data) {
            if (!data) {
                return
            }
            this.currentEditRow = data
            getSysTime().then(res => {
                let _timestamp = res.timestamp || ''
                this.currentEditRow = Object.assign({}, this.currentEditRow, {
                    _timestamp
                })
            }).finally(() => {
                this.$refs.listPage.closeTemplateModal()
                this.showEditPage = true
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        // 组装带有配置色的行数据
        initDefinedColumnSettingRow (ref, row) {
            let cols = []
            if (ref && this.$refs[ref]) {
                let list = this.$refs[ref]
                if (list.isAggregate) {
                    cols = list.groupColumns
                } else {
                    cols = list.tableColumns
                }
            }
            if (cols && cols.length) {
                cols.forEach((col) => {
                    if (col.fieldColors && col.fieldColors.length) {
                        let fieldName = col.field
                        row[`${fieldName + '_fieldColors'}`] = col.fieldColors
                    }
                })
            }
            return row
        }, 
        // 查看按钮打开编辑页面
        handleSpecialByView (row) {
            // 打开离开提示
            this.$store.dispatch('SetTabConfirm', true)
            // 走正常流程
            this.handleView(row)
        },
        // 查看
        handleView (row) {
            let viewRow = cloneDeep(row);
            const viewAuth = this.getViewAuth()
            if (!viewAuth) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BjbWWVKHRvjlb_beee0887`, '没有权限，请联系管理员授权'))
                return
            }
            let customRow = this.initDefinedColumnSettingRow('listPage', row)
            //打开详情页
            this.showDetailPage = false
            this.$nextTick(() => {
                this.viewRow = viewRow;
                this.currentEditRow = customRow
                this.showDetailPage = true
            })
        },
        getViewAuth () {
            // 处理列表 超链接回调直接执行 handleView 方法
            let result = true
            const $list = this.$refs.listPage
            if ($list) {
                const viewAuthBtn = $list.getAuthCodeBtns($list.getViewBtn())
                if (!viewAuthBtn.length) {
                    result = false
                }
            }
            return result
        },
        handlePageShow (row) {
            // 默认为查看英页面具体业务页面去调整
            this.showDetailPage = false
            this.$nextTick(() => {
                this.currentEditRow = row
                this.showDetailPage = true
                this.$store.dispatch('SetTabConfirm', true)
            })
        },
        handleRecord (row) {
            this.currentEditRow = row
            this.$refs.listPage.showRecordModal()
        },
        handleAdd () {
            this.currentEditRow = {}
            this.$refs.listPage.openTemplateModal()
        },
        // 编辑
        handleEdit (row) {
            console.log(JSON.stringify(row));
            this.currentEditRow = row
            this.showEditPage = true
            // 编辑页面离开tab提示
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEditFromViewPage(handleKey) {
            let viewRow = cloneDeep(this.viewRow);
            this.viewRow = null;
            this.hideEditPage();
            this.$nextTick(() => {
                if(!!handleKey) this[handleKey](viewRow);
                else this.handleEdit(viewRow);
            })
        },
        handleDelete (row) {
            this.$refs.listPage.deleteRows(row)
        },
        handleUpgrade (row) {
            this.$refs.listPage.upgradeVersion(row)
        },
        // 返回按钮
        hideEditPage () {
            let query = this.$route.query || {}
            // 普通页面超链接, 需求池转业务页面
            if (query.linkFilter || query.open || query.source == 'demand-pool') {
                // 关闭超链接页面
                this.closeCurrent()
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        // 更新list页面tab count
        refreshCountTabs () {
            if (this._countTabsUrl) {
                this.serachCountTabs(this._countTabsUrl)
            }
        },
        confirmHideEditPage () {
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToReturn`, '是否确认返回？'),
                onOk: () => {
                    that.hideEditPage()
                }
            })
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_files`, '文件'))
        },
        settingColumns () {
            this.$refs.listPage.settingColumnConfig()
        },
        searchEvent (flag) {
            this.$refs.listPage.handleQuery(flag)
        },
        resetEvent () {
            this.$refs.listPage.searchReset()
        },
        importExcel () {
            getAction('/base/excelHeader/getConfig/' + this.url.excelCode, null).then((res) => {
                if (res.success) {
                    let isPreview = res.result.previewData
                    let previewColumns = res.result.excelDetailList.map((item) => {
                        return {
                            title: item.columnName,
                            field: item.columnCode,
                            width: 120,
                            dataType: item.dataType,
                            dataFormat: item.dataFormat
                        }
                    })
                    this.$refs.listPage.$refs.importExcel.open(isPreview, previewColumns, res.result.excelName, null, this.handleBeforeImportExcel)
                } else {
                    this.$message.warning(res.message)
                }
            })
        },
        getDataByErp () {
            this.$refs.listPage.loading = true
            getAction(this.url.getDataByErpUrl)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
                        this.searchEvent()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.listPage.loading = false
                })
        },
        pushDataToERP () {
            let grid = this.$refs.listPage.$refs.listGrid
            let checkRow = grid.getCheckboxRecords() || []
            if (!checkRow.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRRiFIcWF_e9c4fe3f`, '请至少选择一行'))
                return
            }
            let ids = checkRow.map((row) => row.id).join(',')
            this.$refs.listPage.loading = true
            getAction(this.url.pushDataToERPUrl, { ids: ids })
                .then((res) => {
                    if (res.success) {
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功'))
                        this.searchEvent()
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.listPage.loading = false
                })
        },
        imJumpDetailFun () {
            // 从聊天窗口跳转而来的，查询row数据跳转到对应详情页
            const _this = this
            this.$nextTick(() => {
                if (this.$route.query.linkRouteType === 'im') {
                    const searchNumber = this.$route.query.searchNumber
                    const listUrl = this.url.list || ''
                    if (listUrl === '') return
                    const params = {
                        keyWord: searchNumber
                    }
                    getAction(listUrl, params).then((res) => {
                        if (res.success) {
                            _this.handleView(res.result.records[0])
                        }
                    })
                }
            })
        }
    }
}
