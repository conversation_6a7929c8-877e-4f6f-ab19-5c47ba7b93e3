<template>
  <div>
    <listTable
      ref="listTable"
      :fromSourceData="fromSourceData"
      :statictableColumns="tableColumns"
      :showTablePage="false"
      :pageData="pageData"
    />
  </div>
</template>
<script>
import listTable from '../../components/listTable'

export default {
    inject: ['currentSubPackage'],
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        }
    },
    components: {
        listTable
    },
    data () {
        return {
            pageData: {
                showOptColumn: true,
                optColumnList: [
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                }
            },
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    field: 'supplierName',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    width: 120
                },
                {
                    field: 'probeResult',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJiyR_4a0758e5`, '围标探测结果'),
                    width: 500
                },
                {
                    field: 'createTime',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'),
                    width: 150
                },
                {
                    field: 'createBy',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'),
                    width: 120
                }
            ]
        }
    },
    methods: {
    }
}
</script>

