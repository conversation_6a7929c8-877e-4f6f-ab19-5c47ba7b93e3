<template>
  <!-- 其他方式缴纳 -->
  <div class="insurance-guarantee-view">
    <div>
      <a-row style="padding: 10px 15px;">
        <a-col :span="12">
          <label>{{ $srmI18n(`${$getLangAccount()}#i18n_field_RIJW_274265fe`, '关键字：') }}</label>
          <a-input
            style="width: 220px;"
            v-model="tenderName"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNeBLRL_fb7f81a2`, '请输入投标人名称')"></a-input>
        </a-col>
        <a-col
          :span="12"
          style="text-align: right;">
          <a-button
            type="primary"
            icon="search"
            @click="searchFun"
            style="margin-right: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_search`, '搜索') }}</a-button>
          <a-button
            icon="reload"
            @click="searchFun('0')">{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}</a-button>
        </a-col>
      </a-row>
      <!-- <div class="add">
        <a-button
          type="primary"
          @click="exportExcel"
          :loading="loading">{{$srmI18n(`${$getLangAccount()}#i18n_title_eportExcel`, '导出Excel')}}</a-button>
      </div> -->
      <div class="grid">
        <ListTable 
          ref="listTable"
          :url="url"
          :defaultParams="{subpackageId:this.subId, refundType: '1', keyWord: tenderName}"
          :statictableColumns="tableColumns"
          :showTablePage="false">
        </ListTable>
      </div>
    </div>
  </div>
</template>

<script>
import ListTable from '../../components/listTable'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'

export default {
    components: {
        ListTable
    },
    inject: ['tenderCurrentRow', 'subpackageId'],
    data () {
        return {
            loading: false,
            tenderName: '',
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVey_43196f4f`, '退款账号'),
                    'field': 'refundAccount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyDR_e2871045`, '退款账号户名 '),
                    'field': 'refundName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyvDc_6e512646`, '退款账号开户行'),
                    'field': 'bankName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVKEy_1feb0481`, '退款联行号'),
                    'field': 'interBankNo'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVHfWjW_d1a4b644`, '退款金额（元）'),
                    'field': 'refundAmount'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVKI_4314f27c`, '退款时间'),
                    'field': 'updateTime'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVzE_431647c9`, '退款状态'),
                    'field': 'status_dictText'
                }
            ],
            url: {
                list: '/tender/supplier/purchaseTenderProjectMarginHead/queryRefund',
                expt: '/tender/supplier/purchaseTenderProjectMarginHead/exportRefundXls'
            }
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
            // return '1532190061912612866'
        }
    },
    methods: {
        exportExcel () { // 导出 excel
            this.loading = true
            getAction(this.url.expt, 
                {
                    defineColumnCode: 'tenderProjectRefundList', 
                    subpackageId: this.subId,
                    fileName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHYVtH_7bea69b0`, '保证金退款记录')
                }, {
                    responseType: 'blob'
                }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', '保证金退款记录.xls')
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(()=>{
                this.loading = false
            })
        },
        searchFun (type) {
            if (type == '0') {
                this.tenderName = ''
            }
            this.$refs.listTable.loadData && this.$refs.listTable.loadData()
        }
    }
}
</script>

<style lang="less" scoped>
.add {
  padding: 10px 15px;
}
</style>

