<template>
  <div style="height:100%">
    <list-layout
      v-show="!modelerModal"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" />
    <bpmModelerModal
      @cancel="cancelCallBack"
      :not100000="not100000"
      v-if="modelerModal"
      :current-edit-row="currentEditRow"
      :pageData="pageData"
    />

    <a-modal
      v-drag    
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWNDJUzsW_2e47ed63`, '请输入提交审批参数')"
      width="1000px"
      :visible="prompt.visible"
      :confirm-loading="ifLoading"
      @cancel="prompt.visible=false"
      @ok="handlePromptOk"
    >
      <a-spin :spinning="confirmLoading">
        <a-textarea
          :style="{width:'100%',height: '600px'}"
          placeholder="请在此输入请求参数"
          v-model="prompt.value"/>
      </a-spin>
    </a-modal>
    <a-modal
      v-drag    
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_mAQL_3106e730`, '查看流程')"
      width="1000px"
      :visible="LctVisible"
      @cancel="LctVisible=false"
      @ok="handleLctOk"
    >
      <iframe
        id="flowImageframe"
        width="100%"
        style="min-height: 600px"
        height="100%"
        frameborder="0"
        align="center"
        allowfullscreen="true"
        allow="autoplay"
        :src="targetUrl"
      />
    </a-modal>

    <flowViewModal
      v-model="flowView"
      isDesign
      :currentEditRow="currentEditRow"
    />

  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import {publishModel, deleteByModelKey, copyByModelKey, invalidByModelKey, updataModel} from '../api/analy.js'
import bpmModelerModal from './modules/bpmModelerModal'
import { postAction } from '@/api/manage'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { mapGetters } from 'vuex'

import flowViewModal from '@comp/flowView/flowView'

export default {
    mixins: [ListMixin],
    components: {
        bpmModelerModal,
        flowViewModal
    },
    computed: {
        ...mapGetters([
            'userInfo',
            'taskInfo'
        ]),
        not100000 () {
            return this.userInfo.elsAccount !== '100000'
        }
    },
    data () {
        return {
            flowView: false,
            ifLoading: false,
            confirmLoading: false,
            LctVisible: false,
            showIframe: false,
            targetUrl: '',
            prompt: {
                visible: false,
                value: ''
            },
            showEditPage: false,
            pageData: {
                // businessType: 'ecn',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_processName`, '流程名称'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_processName`, '流程名称')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型'),
                        fieldName: 'businessType',
                        dictCode: 'srmAuditBussinessType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UzESAc_ec2b9b6f`, '审批业务类型')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'bpmn#auditConfig:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                showOptColumn: true,
                superQueryShow: false,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleFlow},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, authorityCode: 'bpmn#auditConfig:edit'},
                    {type: 'copy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), clickFn: this.handleCopy, authorityCode: 'bpmn#auditConfig:copyData'},
                    {type: 'autotest', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOEiK_4d91f6c2`, '自动化测试'), clickFn: this.handleTest, authorityCode: 'bpmn#auditAPI:checkProcess'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', clickFn: this.publish, allow: this.allowPublish, authorityCode: 'bpmn#auditConfig:publish' },
                    {type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleCancel, allow: this.allowCancel, authorityCode: 'bpmn#auditConfig:invalid'},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionUpdating `, '版本更新'), clickFn: this.updata, allow: this.allowPublish2, authorityCode: 'bpmn#repository:publish'},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowPublish, authorityCode: 'bpmn#auditConfig:delete'}
                ],
                publicBtn: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.cancelCallBack}
                ]
            },
            modelerModal: false,
            width: '98%',
            xml: null,
            id: null,
            type: null,
            modelKey: null,
            card: true,
            url: {
                list: '/a1bpmn/audit/api/models/list',
                columns: 'bpmnProccessModelList'
            }
        }
    },
    methods: {
        handleFlow (row = {}) {
            console.log('row :>> ', row)
            this.currentEditRow = row
            this.flowView = true
        },
        handleAdd () {
            this.currentEditRow = {newAdd: true}
            this.modelerModal = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        handleEdit (row) {
            this.currentEditRow = row
            this.modelerModal = true
            this.$store.dispatch('SetTabConfirm', true)
        },
        cancelCallBack (){
            this.$store.dispatch('SetTabConfirm', false)
            this.modelerModal = false
            this.$refs.listPage.loadData()
        },
        allowCancel (row){
            if(row.status !== 2){
                return false
            }
            return true
        },
        allowPublish (row) {
            if([0, 2].includes( row.status )){
                return false
            }
            return true
        },
        allowPublish2 (row) {
            if(row.status == 3){
                return false
            }
            return true
        },
        updata (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionUpdating `, '版本更新'),
                onOk: () => {
                    updataModel(row.id, row.businessType).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        publish (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRelease`, '确认发布'),
                onOk: () => {
                    publishModel(row.id, row.businessType).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleCopy (row){
            let that = this
            this.$confirm({
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmCopyTemplate`, '确认复制模板？'),
                onOk: () => {
                    copyByModelKey(row.modelKey).then(res => {
                        if(res.success) {
                            that.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
                            that.$refs.listPage.loadData()
                        }
                    })
                }
            })
        },
        handleDelete (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmDelete`, '确认删除'),
                onOk: () => {
                    deleteByModelKey(row.modelKey).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleCancel (row) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLDK_38d74ae0`, '确认提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmVoidTemplate`, '确认作废模板？'),
                onOk: () => {
                    invalidByModelKey(row.modelKey).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                            that.$refs.listPage.loadData()
                        }else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        handleTest (row){
            this.currentEditRow = row
            this.prompt.visible = true
            this.prompt.value = ''
            console.log(row)
        },
        isJson (str) {
            if (typeof str == 'string') {
                try{
                    let obj = JSON.parse(str)
                    if (typeof obj == 'object' && obj) {
                        return true
                    }
                    return false
                }catch{
                    return false
                }
            }
        },
        handlePromptOk () {
            let { value } = this.prompt
            // if(!value){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sMCcxOLV_7de2bbbf`, '保存内容不能为空'))
            //     return
            // }
            if (!this.isJson(value)) return this.$message.warning('输入内容需为json格式')
            let invokeUrl = `/a1bpmn/audit/api/checkProcess/${this.currentEditRow.id}`
            let params = JSON.parse(value)
            this.ifLoading=true
            this.confirmLoading=true
            postAction(invokeUrl, params).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.procInstId = res.result.procInstId
                    this.targetUrl=`/els/bpmns/view/tab.html?fireHoverAction=true&table=false&instId=${this.procInstId}&messageId=${this.$ls.get(ACCESS_TOKEN)}`
                    this.LctVisible = true
                    this.prompt.visible=false

                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading=false
                this.ifLoading=false
            })
        },
        handleLctOk (){
            this.LctVisible = false
        }
    },
    created () {
        this.pageData.button[0]['hide'] = !this.not100000
    }
}
</script>