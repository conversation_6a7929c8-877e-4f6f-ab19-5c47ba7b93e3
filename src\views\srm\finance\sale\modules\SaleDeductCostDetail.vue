<template>
  <div class="page-container">
    <business-layout
      v-if="showRemote"
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :externalToolBar="externalToolBar"
      :remoteJsFilePath="remoteJsFilePath"
      :pageHeaderButtons="pageHeaderButtons"
      :handleAfterDealSource="handleAfterDealSource"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      modelLayout="tab"
      pageStatus="edit"
      v-on="businessHandler"> </business-layout>
    <!-- 加载配置文件 -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
      <!-- 驳回弹出框 -->
      <a-modal
          v-model="rejectVisible"
          :okText="okText"
          @ok="handleOk">
          <a-form-model
              ref="rejectForm"
              :rules="rejectRules"
              :model="rejectForm">
              <a-form-model-item
                  :label="this.$srmI18n(`${$getLangAccount()}#i18n_field_rejectReason`, '拒绝原因')"
                  rop="rejectReason" >
                  <a-textarea
                      v-model="rejectForm.rejectReason"
                      :placeholder="this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因')"
                      :auto-size="{ minRows: 3, maxRows: 5 }"
                  />
              </a-form-model-item>
          </a-form-model>
      </a-modal>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_SAVE} from '@/utils/constant.js'
import { postAction, getAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'SaleDeductCostDetail',
    mixins: [businessUtilMixin],
    components: {
        flowViewModal,
        BusinessLayout
    },
    data () {
        return {
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_define`, '确定'),
            rejectVisible: false,
            rejectForm: {rejectReason: ''},
            rejectRules: {
                rejectReason: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因'), trigger: 'blur' } ]
            },
            showRemote: false,
            businessRefName: 'businessRef',
            requestData: {
                detail: {
                    url: '/finance/saleDeductCost/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.confirmEvent,
                    show: this.showcConfirmConditionBtn,
                    authorityCode: 'deductCost#saleDeductCost:confirmById'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                    attrs: {
                        type: 'danger'
                    },
                    click: this.rejectEvent,
                    show: this.showcRejectConditionBtn,
                    authorityCode: 'deductCost#saleDeductCost:rejectById'
                },
                {
                    ...BUTTON_SAVE,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_exBIvnR_b4770134`, '同步附件给采购'),
                    args: {
                        url: '/finance/saleDeductCost/asyPurchaseAttachment'
                    },
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    click: this.showFlow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            externalToolBar: {
                saleAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'deductCost', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                ],

            },
            flowView: false,
            flowId: '',
            url: {
                detail: '/finance/saleDeductCost/queryById',
                confirm: '/finance/saleDeductCost/confirmById',
                reject: '/finance/saleDeductCost/rejectById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {  
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${busAccount}/sale_deductCost_${templateNumber}_${templateVersion}`
        }
    },
    created () {
        if (this.$route.query && this.$route.query.open && this.$route.query.id) {
            getAction(this.url.detail, { id: this.$route.query.id }).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.showRemote = true
                }
            })
        } else {
            this.showRemote = true
        }
       
    },
    methods: {
        deleteFilesEvent (Vue, row) {
            const fileGrid =this.getItemGridRef('saleAttachmentList')
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            const fileGrid = this.getItemGridRef('saleAttachmentList')
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '6',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            ]
                        }
                    }
                ],
                formFields: [],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        field: 'fileType_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                        fieldLabelI18nKey: '',
                        width: 200
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        width: 180
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        width: 120
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        width: 120
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        width: 120
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        width: '120',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        showFlow () {
            let params = this.getAllData() || {}
            this.flowId = params.flowId
            if (!this.flowId) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processNotViewCurrentStatus`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        preViewEvent (Vue, row) {
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        beforeHandleData (data) {
            data.formFields.forEach((item) => {
                if (item.fieldName == 'toElsAccount') {
                    item.fieldLabel = '采购方els账号'
                }
            })
        },
        handleAfterDealSource (data) {
            console.log(data)
        },
        goBack () {
            this.$emit('hide')
        },
        downloadEvent (Vue, row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        confirmEvent () {
            let params = this.getAllData() || {}
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLVVt_e20b58c4`, '确认扣款单'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLrVVt_a1f64002`, '是否确认该扣款单'),
                onOk: function () {
                    postAction(that.url.confirm, {
                        deductCostItemList: params.deductCostItemList,
                        id: params.id,
                        relationId: params.relationId,
                        toElsAccount: params.toElsAccount
                    }).then((res) => {
                        if (res.success) {
                            that.form = res.result
                            that.$message.success(res.message)
                            that.goBack()
                        } else {
                            that.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        // rejectEvent () {
        //     let params = this.getAllData() || {}
        //     let that = this
        //     this.$confirm({
        //         title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FKVVt_abb8874f`, '拒绝扣款单'),
        //         content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQFKrVVt_deee2d7`, '是否拒绝该扣款单'),
        //         onOk: function () {
        //             that.confirmLoading = true
        //             postAction(that.url.reject, { id: params.id, relationId: params.relationId, deductCostItemList: params.deductCostItemList })
        //                 .then((res) => {
        //                     if (res.success) {
        //                         that.form = res.result
        //                         that.$message.success(res.message)
        //                         that.goBack()
        //                     } else {
        //                         that.$message.warning(res.message)
        //                     }
        //                 })
        //                 .finally(() => {
        //                     that.confirmLoading = false
        //                     that.close()
        //                 })
        //         }
        //     })
        // },

        handleOk (){
            let data = this.getAllData() || {}
            let that = this
            if(this.opreationType == 'rejectOption'){
                let rejectReason = this.rejectForm.rejectReason
                if(!rejectReason || rejectReason == ''){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWNFKjW_b5fa4895`, '请输入拒绝原因'))
                    return
                }
                const params = {id: data.id, relationId: data.relationId, rejectReason: rejectReason}
                this.confirmLoading = true
                postAction(this.url.reject, params).then((res) => {
                    that.confirmLoading = true
                    if (res && res.success) {
                        this.goBack()
                        this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n__FKLRW_abb50027`, '拒绝成功！'))
                    } else {
                        this.$message.warning(res.message)
                    }
                }).finally(() => {
                    that.confirmLoading = false
                })
                this.rejectVisible = false
            }
        },
        rejectEvent () {
            this.rejectForm.rejectReason = ''
            this.rejectVisible = true
            this.opreationType = 'rejectOption'
        },

        showcConfirmConditionBtn () {
            let params = this.getAllData() || {}
            let confirmStatus = params.confirmStatus
            if ('1' == confirmStatus) {
                return true
            } else {
                // 不可操作
                return false
            }
        },
        showcRejectConditionBtn () {
            let params = this.getAllData() || {}
            let confirmStatus = params.confirmStatus
            if ('1' == confirmStatus) {
                return true
            } else {
                // 不可操作
                return false
            }
        }
    }
}
</script>
