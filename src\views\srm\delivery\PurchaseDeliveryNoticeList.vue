<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      :tableCode="url.columns"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url" 
      :row-style="rowStyle"
      :activeMethod="activeMethod"/>
    <!-- 编辑界面 -->
    <purchase-delivery-notice-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <!-- 查看界面 -->
    <PurchaseDeliveryNoticeDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
    <RelationGraphModal
      v-if="modalVisibleDocket"
      :modalVisibleDocket="modalVisibleDocket"
      :id="documentId"
      :rootId="rootId"
      @closeModalDocket="closeModalDocket"
      @onNodeClick="clickNode"
    />
    <Mmodel 
      ref="childMmodel" 
      @ok="confirmChanges"
      :tableHeaderData="tableHeaderData"/>
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import { postAction, getAction } from '@/api/manage'
import PurchaseDeliveryNoticeModal from './modules/PurchaseDeliveryNoticeModal'
import PurchaseDeliveryNoticeDetail from './modules/PurchaseDeliveryNoticeDetail'
import layIM from '@/utils/im/layIM.js'
import RelationGraphModal from '@comp/RelationGraphModal'
import Mmodel from './modules/Mmodel'
export default {
    mixins: [ListMixin],
    components: {
        PurchaseDeliveryNoticeModal,
        RelationGraphModal,
        PurchaseDeliveryNoticeDetail,
        Mmodel
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            modalVisibleDocket: false,
            rootId: '',
            documentId: '',
            pageData: {
                businessType: 'deliveryNotice',
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, type: 'primary', authorityCode: 'order#purchaseDeliveryNotice:list'},
                    // {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'), icon: 'plus', clickFn: this.publishDeliveryNotice, type: 'primary', authorityCode: 'deliveryNotice#purchaseDeliveryNotice:send'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FK_c764b`, '拒绝'), icon: 'plus', clickFn: this.publishDeliveryNotice, type: 'primary', authorityCode: 'order#purchaseDeliveryNotice:send'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), icon: 'plus', clickFn: this.publishDeliveryNoticeConfirm, type: 'primary', authorityCode: 'order#purchaseDeliveryNotice:send'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_AH_a8e1c`, '变更'), icon: 'plus', clickFn: this.publishDeliveryNoticeChange, type: 'primary', authorityCode: 'order#purchaseDeliveryNotice:send'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIWyd_5d71ea94`, '锁定/解锁'), icon: 'plus', clickFn: this.lockhDeliveryNotice, type: 'primary', authorityCode: 'order#purchaseDeliveryNotice:lockDeliveryNotice'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), icon: 'deleteBatch', clickFn: this.handleDeleteSingles, type: 'primary', authorityCode: 'deliveryNoticeByOrder#purchaseDeliveryNotice:deleteBatches'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: true, clickFn: this.handleExportXls, authorityCode: 'order#purchaseDeliveryNotice:exportXls'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_improt`, '导入'), icon: 'import', clickFn: this.importExcel, authorityCode: 'deliveryNotice#purchaseDeliveryNotice:import'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOrderNoOrAcctonORName`, '请输入通知单号/供应商ELS账号/供应商名称')
                    }

                ],
                form: {
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail, authorityCode: 'order#purchaseDeliveryNotice:queryById'},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditSingle, allow: this.allowEdit, authorityCode: 'order#purchaseDeliveryNotice:edit'},
                    { type: 'publish', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_ithx_2f618c73`, '整单发布'), clickFn: this.handlePublishSingle, allow: this.allowPuh, authorityCode: 'order#purchaseDeliveryNoticeHead:publish'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDeleteSingle, allow: this.allowDel, authorityCode: 'order#purchaseDeliveryNotice:delete'},
                    { type: 'close', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Rl_a72da`, '关闭'), clickFn: this.handleCloseSingle, allow: this.allowClose, authorityCode: 'order#purchaseDeliveryNotice:close'},
                    { type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleCancelSingle, allow: this.allowCancel, authorityCode: 'order#purchaseDeliveryNotice:cancel'},
                    { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat},
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tFKm_2766a28a`, '单据联查'), allow: this.showDocket, clickFn: this.viewDocket },
                    { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 200
            },
            url: {
                add: '/delivery/purchaseDeliveryNotice/add',
                list: '/delivery/purchaseDeliveryNotice/list',
                delete: '/delivery/purchaseDeliveryNotice/delete',
                close: '/delivery/purchaseDeliveryNotice/close',
                cancel: '/delivery/purchaseDeliveryNotice/cancel',
                confirm: '/delivery/purchaseDeliveryNotice/confirm',
                lockDeliveryNotice: '/delivery/purchaseDeliveryNotice/lockDeliveryNotice',
                deleteBatch: '/delivery/purchaseDeliveryNotice/deleteBatch',
                sendToSale: '/delivery/purchaseDeliveryNotice/send',
                importExcelUrl: 'delivery/purchaseDeliveryNotice/importExcel',
                columns: 'purchaseDeliveryNoticeList',
                excelCode: 'purchaseDeliveryNoticesExcelImport',
                exportXlsUrl: '/delivery/purchaseDeliveryNotice/exportXls'
            },
            // 弹窗数据
            tableHeaderData: [],
            tabsList: []
        }
    },
    mounted () {
        this.serachCountTabs('/delivery/purchaseDeliveryNotice/counts')
    },
    methods: {
        // 拒绝
        publishDeliveryNotice () {
            // 行状态=待采购确认&&是否锁定=否 拒绝
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(item.noticeStatus=='4'||item.noticeStatus=='5') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationCannotPerformedClosedVoidedOrders`, '已经关闭或者作废的订单，不能执行该操作!'))
                    return
                }
                //被锁定的行不能执行任何操作
                if(item.locked=='1') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,已经被锁定，暂时不能执行任何操作!'))
                    return
                }
                if(item.noticeStatus!='2') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EAPcjzExKRdXFKPKxORcFKtk_62e132ab`, '行,当前行的状态不是供应商拒绝，暂时不能执行拒绝操作!'))
                    return
                }
            }
            // 在这里加二次确认前的校验
            const callback = () => {
                const url = ''
                const params = rows.map(item => {
                    item.fbk1 = '0'
                    if (item._id) {
                        delete item.id
                    }
                    delete item._id
                    return item
                })
                // 在这里加所需的校验

                postAction(this.url.sendToSale, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(type=='success'){
                        this.$refs.listPage.loadData() // 刷新页面
                    }
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FK_c764b`, '拒绝'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLFKRic_66a10bc9`, '是否确认拒绝勾选行？'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        // 确定按钮
        publishDeliveryNoticeConfirm (){
            // 行状态=待采购确认&&是否锁定=否
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(item.noticeStatus=='4'||item.noticeStatus=='5') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationCannotPerformedClosedVoidedOrders`, '已经关闭或者作废的订单，不能执行该操作!'))
                    return
                }
                //被锁定的行不能执行任何操作
                if(item.locked=='1') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,已经被锁定，暂时不能执行任何操作!'))
                    return
                }
                if(item.noticeStatus!='2') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+ '行,当前行的状态不是供应商拒绝，暂时不能执行确认操作!')
                    return
                }
            }
            // 在这里加二次确认前的校验
            const callback = () => {
                const url = ''
                const params = rows.map(item => {
                    item.fbk1 = '1'
                    if (item._id) {
                        delete item.id
                    }
                    delete item._id
                    return item
                })
                // 在这里加所需的校验

                postAction(this.url.sendToSale, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(type=='success'){
                        this.$refs.listPage.loadData() // 刷新页面
                    }
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确定'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WKQRLRic_e7c8ee94`, '是否确认勾选行？'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        //变更按钮
        publishDeliveryNoticeChange (){
            //（行状态=待采购确认||采购已确定 ）&&是否锁定=否
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (!rows.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`, '请勾选需要发送数据'))
                return 
            }
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(item.noticeStatus=='4'||item.noticeStatus=='5') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationCannotPerformedClosedVoidedOrders`, '已经关闭或者作废的订单，不能执行该操作!'))
                    return 
                }
                //被锁定的行不能执行任何操作
                if(item.locked=='1') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,已经被锁定，暂时不能执行任何操作!'))
                    return 
                }
                //当前是变更操作,是否选择了责任方
                if(item.fbk1=='2'&&!item.responsibleParty) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineLabelHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,属于变更操作，责任方不能为空!'))
                    return 
                }
                if((item.noticeStatus!='2' && item.noticeStatus != '3') || item.noticeStatus == '2') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+(i+1)+ '行,当前行的状态不是已确认，暂时不能执行变更操作!')
                    return 
                }
            }
            this.tableHeaderData = rows
            // 调用弹窗组件方法
            this.$refs.childMmodel.open()
        },
        handleDeleteSingles () { // 批量删除
            const that = this
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (!rows.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#`, '请勾选需要删除的数据'))
                return
            }
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(item.noticeStatus!='0') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+'['+(i+1)+']行当前非新增状态，不能删除操作!')
                    return
                }
            }
            const ids = rows.map(item => {return item.id})
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                content: this.$srmI18n(`${this.$getLangAccount()}#`, '是否确定删除选中数据'),
                onOk () {
                    getAction('/delivery/purchaseDeliveryNotice/deleteBatch', {ids: ids.join(',')}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        that.$refs.listPage.loadData() // 刷新页面
                    })
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        handlePublishSingle (row){ // 整单发布
            const that = this
            that.loading = true
            const callBackFun = (resultData) => {
                // resultData.headId = resultData.id
                // resultData.id = row.id
                postAction('/delivery/purchaseDeliveryNoticeHead/publish', resultData).then(res => {
                    if(res.success) {
                        that.$message.success(res.message)
                        that.$refs.listPage.loadData() // 刷新页面
                    }else {
                        that.$message.warning(res.message)
                    }
                }).finally(() => {
                    that.loading = false
                })
            }
            
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#`, '整单发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeSelecteData`, '是否整单发布选中数据?'),
                onOk: function () {
                    that.loading = true
                    getAction('/delivery/purchaseDeliveryNoticeHead/queryById', {id: row.headId}).then(res => {
                        if(res.code == 200) {
                            callBackFun(res.result)
                        } else {
                            that.$message.error(res.message)
                            that.loading = false
                        }
                    })
                }
            })
        },
        confirmChanges (rows){
            // 在这里加二次确认前的校验
            const callback = () => {
                const url = ''
                const params = rows.map(item => {
                    item.fbk1 = '2'
                    if (item._id) {
                        delete item.id
                    }
                    delete item._id
                    return item
                })
                // 在这里加所需的校验

                postAction(this.url.sendToSale, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(type=='success'){
                        this.$refs.listPage.loadData() // 刷新页面
                    }
                })
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_send`, '发送'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isNeddSendData`, '是否确认发送单据'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.noticeNumber  || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseDeliveryNotice', url: this.url || '', recordNumber})
        },
        showDetail (row){
            ///row.id = row.headId
            this.currentEditRow = row
            this.currentEditRow.id = row.headId
            this.currentEditRow.busAccount = row.elsAccount
            this.showDetailPage = true
        },
        allowChat (row){
            if(row.sendStatus == '1'){
                return false
            }
            return true
        },
        allowDel (row){
            if(row.noticeStatus != '0'){
                return true
            }
            return false
        },
        allowPuh (row) {
            if(row.noticeStatus == '0'&& row.locked=='0'){
                return false
            }
            return true
        },
        allowClose (row){
            if(row.noticeStatus == '0'||row.noticeStatus=='4'||row.noticeStatus=='5'){
                return true
            }
            if(parseFloat(row.onWayQuantity)>0){
                return true
            }
            return false
        },
        allowCancel (row){
            if(row.noticeStatus == '0'||row.noticeStatus=='4'||row.noticeStatus=='5'){
                return true
            }
            if(parseFloat(row.onWayQuantity)>0){
                return true
            }
            if(parseFloat(row.receiveQuantity)>0){
                return true
            }
            return false
        },
        handleDeleteSingle (row) {
            if (row.noticeStatus != '0') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_onlyDocumentsNewStatusDeleted`, '只有新建状态的单据才能删除'))
            }else {
                this.handleDelete(row)
            }
        },
        handleCloseSingle (row){
            if (row.noticeStatus == '0') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newStatusCannotBeClosed`, '新建状态不能关闭!'))
            }else {
                const that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmClose`, '确认关闭'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_closeSelecteData`, '是否关闭选中数据?'),
                    onOk: function () {
                        that.loading = true
                        getAction(that.url.close, {id: row.id}).then(res => {
                            if(res.success) {
                                that.$message.success(res.message)
                                that.$refs.listPage.loadData() // 刷新页面
                            }else {
                                that.$message.warning(res.message)
                            }
                        }).finally(() => {
                            that.loading = false
                        })
                    }
                })
            }
        },
        handleCancelSingle (row){
            if (row.deliveryQuantity >0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dSthSWRfUWWxOkuW_70279f67`, '送货单发货数量大于0,不能作废!'))
                return true
            }


            if (row.noticeStatus == '0') {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_newStatusCannotBeClosed`, '新建状态不能关闭!'))
            }else {
                const that = this
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelecteData`, '是否作废选中数据?'),
                    onOk: function () {
                        that.loading = true
                        getAction(that.url.cancel, {id: row.id}).then(res => {
                            if(res.success) {
                                that.$message.success(res.message)
                                that.$refs.listPage.loadData() // 刷新页面
                            }else {
                                that.$message.warning(res.message)
                            }
                        }).finally(() => {
                            that.loading = false
                        })
                    }
                })
            }
        },
        lockhDeliveryNotice (){
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (!rows.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`, '请勾选需要发送数据'))
                return
            }
            const callback = () => {
                const params = rows.map(item => {
                    return item
                })
                postAction(this.url.lockDeliveryNotice, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(type=='success'){
                        this.$refs.listPage.loadData() // 刷新页面
                    }
                })
            }

            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIWyd_5d71ea94`, '锁定/解锁'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQdIWydisnc_3a7b0f29`, '是否锁定/解锁选中的行'),
                onOk () {
                    callback && callback()
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
       
        allowEdit (row){
            if ((row.noticeStatus == '0' )){
                return false
            }
            return true
        },

        confirmDeliveryNotice (){
            const that = this
            const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
            if (!rows.length) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`, '请勾选需要发送数据'))
                return
            }
            for(let i=0;i<rows.length;i++){
                let item = rows[i]
                if(item.noticeStatus!='2') {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+'['+(i+1)+']行当前不是待采购确认状态，暂时不能确认!')
                    return
                }
            }
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmTheSelectedRow`, '是否确认选择的行'),
                onOk () {
                    postAction(that.url.sendToSale, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        this.$refs.listPage.loadData() // 刷新页面
                    })
                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        cancelDeliveryNotice (){
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_invalid`, '失效'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoidTheSelectedRow`, '是否失效选择的行'),
                onOk () {

                },
                onCancel () {
                    console.log('Cancel')
                }
            })
        },
        activeMethod ({ row, rowIndex, column, columnIndex }) {
            return row.fbk1 && row.fbk1 === '2'
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dSeRt_2f9c7ea4`, '送货通知单'))
        },
        handleEditSingle (row) {
            this.currentEditRow = row
            this.currentEditRow.id = row.headId
            this.handleEdit(this.currentEditRow)
        },
        clickNode (){
            this.$store.dispatch('SetTabConfirm', false)
            this.modalVisibleDocket = false
        },
        closeModalDocket (){
            this.modalVisibleDocket = false
        },
        // 是否显示单据联查
        showDocket (row) {
            return !row.documentId
        },
        // 单据联查
        viewDocket (row) {
            this.documentId = row.documentId
            this.rootId = row.id
            this.modalVisibleDocket = true
        },
        rowStyle ({ row, column, rowIndex }) {
            if (row.soptRemark === '供应商已拒绝') {
                return {
                    backgroundColor: '#FFF6F6'
                }
            }
        }
    }
}
</script>
