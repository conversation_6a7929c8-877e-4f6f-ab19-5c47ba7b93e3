<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
    />
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_contractParamsCopy`, '复制合同参数')"
      :okText="$srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确认')"
      @ok="handleOk">
      <a-textarea
        v-model="elsAccount"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterTargetElsAccount`, '请输入目标ElsAccount')"
        :auto-size="{ minRows: 1, maxRows: 2 }"
      />
    </a-modal>
    <!-- 表单区域 -->
    <contract-edit
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import EditPurchaseContractParamModal from './modules/EditPurchaseContractParamModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        'contract-edit': EditPurchaseContractParamModal
        // ViewPurchaseContractParamModal
    },
    data () {
        return {
            showEditPage: false,
            auditVisible: false,
            elsAccount: '',
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramName`, '参数名称'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterParamsName`, '请输入参数名称')
                    },
                    {
                        type: 'select', 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_paramType`, '参数类型'),
                        fieldName: 'paramType', 
                        dictCode: 'srmParamType',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectParamsTypeTips`, '请选择参数类型')
                    }
                ],
                button: [
                    //{label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAddParam, type: 'primary'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'), icon: 'plus', clickFn: this.showTextInput, hide: 'true'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                form: {
                    keyWord: '',
                    paramType: ''
                },
                showOptColumn: true,
                optColumnList: [
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, showCondition: this.showEditNewCondition},
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, showCondition: this.showEditNewCondition}
                ]
            },
            url: {
                list: '/contract/purchaseContractParam/queryPageListSys',
                delete: '/contract/purchaseContractParam/delete',
                toElsAccount: '/contract/purchaseContractParam/toElsAccount',
                deleteBatch: '/contract/purchaseContractParam/deleteBatch',
                exportXlsUrl: 'contract/purchaseContractParam/exportXls',
                importExcelUrl: 'contract/purchaseContractParam/importExcel',
                columns: 'purchaseContractParamList'
            }
        }
    },
    created () {
        this.showCopy()
        if (this.$ls.get('Login_elsAccount') != '100000') {
            this.pageData.showOptColumn= false
        }
    },
    methods: {
        showCopy (){
            let subAccount = this.$ls.get('Login_elsAccount')
            if (subAccount && subAccount == '100000'){
                this.pageData.button[1]['hide'] = ''
            }
        },
        showEditNewCondition (){
            if (this.$ls.get('Login_elsAccount') == '100000') {
                return true
            } else {
                return false
            }
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractParams`, '合同参数'))
        },
        showTextInput (){
            this.auditVisible = true
        },
        handleOk (){
            this.copyToElsAccount(this.url.toElsAccount,  this.elsAccount)
        },
        copyToElsAccount (url, elsAccount){
            if(!elsAccount || elsAccount.trim()==''){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_elsaccountCannotBeEmpty`, 'ELSAccount不能为空'))
                return
            }
            url=url+'/'+elsAccount
            getAction(url, {}).then(res => {
                if(res.success) {
                    this.elsAccount = ''
                    this.$message.success(res.message)
                }else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.auditVisible = false
            })
        },
        handleAddParam () {
            this.currentEditRow = null
            this.showEditPage = true
            this.$store.dispatch('SetTabConfirm', true)
        }
    }
}
</script>