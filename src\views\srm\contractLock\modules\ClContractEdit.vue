<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <a-modal
      v-drag
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <a-modal
      v-drag
      centered
      :visible.sync="areaFlag"
      :width="500"
      :maskClosable="false"
      @ok="confirm"
      @cancel="close">
      <a-form-model
        ref="bizForm"
        :model="bizForm">
        <a
          href="https://esign.jcfintech.net.cn/#/admin/coordTools"
          target="_blank">打开定位工具</a>
        <a-form-model-item
          :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_Eo_12ee2c`, '页码')}`"
          prop="page">
          <a-input
            type="number"
            v-model="bizForm.page"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_VWNEo_f6e41655`, '请输入页码')}`"
          />
        </a-form-model-item>
        <a-form-model-item
          :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_WskB_24d7dd3`, 'X轴坐标')}`"
          prop="offsetX">
          <a-input
            type="number"
            v-model="bizForm.offsetX"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_VWNWskB_89753fbc`, '请输入X轴坐标')}`"
          />
        </a-form-model-item>

        <a-form-model-item
          :label="`${$srmI18n(`${$getLangAccount()}#i18n_field_WskB_24df232`, 'Y轴坐标')}`"
          prop="offsetY">
          <a-input
            type="number"
            v-model="bizForm.offsetY"
            :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_field_VWNWskB_8975b41b`, '请输入Y轴坐标')}`"
          />
        </a-form-model-item>
      </a-form-model>

      <field-select-modal></field-select-modal>
    </a-modal>

    <!-- 加载配置文件 -->
    <field-select-modal
      :modalTitle="modalTitle"
      ref="fieldSelectModal" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { REPORT_ADDRESS } from '@/utils/const.js'

export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        const that = this
        return {
            bizForm: {
                offsetX: '',
                offsetY: '',
                page: 1
            },
            modalTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据'),
            rowIndex: -1,
            createFlag: true,
            areaFlag: false,
            sealAeraIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            previewModal: false,
            previewContent: '',
            visible: false,
            signerBtn: false,
            templateNumber: undefined,
            templateOpts: [],
            printRow: {},
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            saleSignersCon: { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                ref: 'saleSigners',
                columns: [
                    { type: 'checkbox', width: 40 },
                    { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                    { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                    { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                    { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                    { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                    { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                    { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                    { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                        this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                    width: 120, editRender: {
                        name: '$select',
                        props: { multiple: true, clearable: true },
                        options: [
                            {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                            {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                            {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                        ]
                    }
                    }
                ]
            }},
            pageData: {
                form: {
                    busType: 'contract',
                    busNumber: null,
                    businessScene: null,
                    relationId: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: null,
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null,
                    firstSeal: null,
                    creatorId: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessNumber`, '业务单号'),
                                    fieldName: 'busNumber',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_WhoWillStampFirst`, '哪方先盖章'),
                                    fieldName: 'firstSeal',
                                    dictCode: 'srmSignatoryType',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'subject',
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'documentName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'documentId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            setDisabledByProp('busNumber', flag)
                                            setDisabledByProp('toElsAccount', flag)
                                        }else{
                                            setDisabledByProp('busNumber', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_zyH3twLIV67xJOvs`, '供方是否线上盖章'),
                                    fieldName: 'onlineSealed',
                                    dictCode: 'yn',
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        that.currentEditRow.onlineSealed = value
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                let validateRules = group.custom.validateRules
                                                if(flag){
                                                    validateRules.firstSeal = []
                                                }else {
                                                    validateRules.firstSeal = [{required: true, message: that.$srmI18n(`${that.$getLangAccount()}#i18n_field_pCWrexOLV_4ededc33`, '哪方先盖章不能为空')}]
                                                }
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '1')
                                            if(flag){
                                                setDisabledByProp('firstSeal', false)
                                            }else{
                                                setDisabledByProp('firstSeal', true)
                                            }
                                        }else{
                                            setDisabledByProp('firstSeal', true)
                                        }
                                        if(that.currentEditRow.sendStatus == '1'){
                                            setDisabledByProp('onlineSealed', true)
                                        }
                                    }
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn',
                                    required: '1'
                                },

                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjXKI_ef65ec11`, '签署有效时间'),
                                    fieldName: 'expireTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWsRKI_fa482f8c`, '签署终止时间'),
                                    fieldName: 'endTime'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: '是否需要选择发起人',
                                    fieldName: 'setCreater',
                                    dictCode: 'yn',
                                    placeholder: '是否需要选择发起人'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '发起人',
                                    fieldName: 'creator',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '发起人联系方式',
                                    fieldName: 'createContact',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: '发起人ID',
                                    fieldName: 'creatorId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                firstSeal: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pCWrexOLV_4ededc33`, '哪方先盖章不能为空')}],
                                onlineSealed: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pCWrexOLV_4ededc33`, '供方是否线上盖章不能为空')}],
                                busType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAcxOLV_8e2e4d07`, '业务类型不能为空')}],
                                busNumber: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessOrderNoCantEmpty`, '业务单号不能为空'), trigger: 'change'}],
                                subject: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_QIdDxOLV_a427525c`, '文件主题不能为空')}],
                                autoArchiving: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOLAxOLV_15ceb36`, '是否自动归档不能为空')}]
                            }
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSigners', type: 'grid', custom: {
                        ref: 'purchaseSigners',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                            width: 120, editRender: {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                                ]
                            }
                            },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            // { field: 'autoSign', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                            //     '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                            //     {label: '否', value: '0'},
                            //     {label: '是', value: '1'}]}},
                            // { field: 'page', type: 'number', title: '页码', width: 50 },
                            // { field: 'offsetX', type: 'number', title: 'x坐标', width: 80 },
                            // { field: 'offsetY', type: 'number', title: 'y坐标', width: 80 },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addPurchaseSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'), click: this.deleteSaleSignEvent, showCondition: this.showDeleteSaleSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            //{ type: 'add', title: '设置签署区域', clickFn: this.getSignArea },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal, allow: this.allowSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addPurchaseSignerEvent}
                        ],
                        rules: {
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            tenantTypeArr: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WPWCAcWxOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSigners',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'signatoryName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWLcR_75c3ca0`, '签署人姓名'), width: 120 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'tenantTypeArr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`,
                                this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型')),
                            width: 120, editRender: {
                                name: '$select',
                                props: { multiple: true, clearable: true },
                                options: [
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RC_a300c`, '公司'), value: 'COMPANY'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: 'PERSONAL'},
                                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPersonName`, '法人'), value: 'LP'}
                                ]
                            }
                            }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQI_39fcc8f1`, '签章文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_ESQIUB_224d09a`, '业务文件预览'), type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_PWQIUB_ee68be07`, '签署文件预览'), type: 'primary', click: this.downSignFile, showCondition: this.showSignFileDown },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hdRCSuPeL_2b11aaa5`, '发送供方添加签章人'), type: 'primary', click: this.sendEvent, showCondition: this.showSendBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hdRCre_19e38f18`, '发送供方盖章'), type: 'primary', click: this.sendLineEvent, showCondition: this.showSignSendBtns },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAnetx_f7de10e0`, '发起草稿'), type: 'primary', click: this.createDraft, showCondition: this.showCreateDraftBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileUpload`, '文件上传'), type: 'primary', click: this.fileUpload, showCondition: this.showUploadBtn },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                send: '/contractLock/elsClContract/send',
                edit: '/contractLock/elsClContract/edit',
                detail: '/contractLock/elsClContract/queryById',
                uploadFile: '/contractLock/elsClContract/uploadFileToContractLock',
                keyWordToAera: '/esign/elsEsign/keyWordToAera',
                viewSignFile: '/contractLock/elsClContract/browsePage',
                downSignFile: '/contractLock/elsClContract/downSignFile',
                createDraft: '/contractLock/elsClContract/createDraft'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        confirm () {
            const form = this.$refs.editPage.getPageData()
            form.purchaseSigners[this.rowIndex].offsetX =  this.bizForm.offsetX
            form.purchaseSigners[this.rowIndex].offsetY =  this.bizForm.offsetY
            form.purchaseSigners[this.rowIndex].page =  this.bizForm.page
            this.areaFlag = false
        },
        close () {
            this.areaFlag = false
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        getSignArea (row, column, $rowIndex){
            this.rowIndex = $rowIndex
            this.areaFlag = true
        },
        allowSeal (row){
            let arr = row.tenantTypeArr || []
            if(arr.includes('COMPANY')){
                return false
            }
            return true
        },
        initEdit ( ){
            if(this.currentEditRow && this.currentEditRow.id) {
                getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
                    if (res.success) {
                        this.currentEditRow = res.result
                    }
                })
            }
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        getSeal (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            let url = '/contractLock/purchaseClSeals/list'
            let columns = [
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            let params = {companyId: row.companyId, operateStatus: 'ENABLE', sealType: 'ENTERPRISE'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        showUploadBtn (){
            if(this.currentEditRow.contractStatus == 'DRAFT' && this.currentEditRow.uploaded !== '1'){
                return true
            }
            return false
        },
        showSignSendBtns (){
            if(this.currentEditRow.sendStatus == '1' || this.currentEditRow.onlineSealed =='1'){
                return false
            }
            return true
        },
        showSignFileDown (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }
            return false
        },
        showSendBtn (){
            if(this.currentEditRow.sendStatus == '1' || this.currentEditRow.onlineSealed !=='1'){
                return false
            }
            return true
        },
        showCreateDraftBtn (){
            const form = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            console.log('form:', form)
            console.log('currentEditRow:', this.currentEditRow)
            let purchaseFlag = false
            if(form.purchaseSigners){
                if(form.purchaseSigners.length>0){
                    purchaseFlag = true
                }
            }
            let saleFlag = false
            if(form.saleSigners){
                if(form.saleSigners.length>0){
                    saleFlag = true
                }
            }

            if(form.onlineSealed == '0' && this.currentEditRow.contractStatus =='RECALLED'){
                return true
            }
            if(form.onlineSealed == '0' && purchaseFlag && form.uploaded !== '1'){
                return true
            }
            if (!purchaseFlag ) {
                return false
            }
            if(form.onlineSealed == '1' && !saleFlag){
                return false
            }
            if(this.currentEditRow.sendBack == '1'){
                return false
            }
            if(this.currentEditRow.contractStatus =='RECALLED' || ( this.currentEditRow.saleSignStatus =='0' && this.currentEditRow.sendStatus =='1' && this.currentEditRow.uploaded !== '1')){
                return true
            }
            return false
        },
        showAddPurchaseSignEvent (){
            if(this.currentEditRow.onlineSealed == '0' && this.currentEditRow.saleSignStatus !== '1'){
                return false
            }
            return true
        },
        showDeleteSaleSignEvent (){
            if(this.currentEditRow.onlineSealed == '0' && this.currentEditRow.saleSignStatus !== '1'){
                return false
            }
            return true
        },
        addPurchaseSignEvent () {
            this.createFlag = false
            this.selectType = 'purchaseSign'
            const form = this.$refs.editPage.getPageData()
            let url = '/contractLock/purchaseCLCompanyInfo/getSignList'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 180 },
                { field: 'companyId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCAZdWW_99b0ddbb`, '公司id'), width: 120 },
                { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态')}
            ]
            let params = {}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignerEvent (row, column, $rowIndex){
            this.createFlag = false
            this.selectType = 'purchaseSigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()
            let url = '/contractLock/purchaseClPersonalInfo/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 100 },
                { field: 'applyContactType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'), width: 100 },
                { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'), width: 100 },
                { field: 'roleStr_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'), width: 100},
                { field: 'realName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKRLi_2b438163`, '是否实名认证'), width: 100, editRender: {name: '$select', options: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LLi_194b947`, '未认证'), value: '0'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILi_1721e0f`, '已认证'), value: '1'}
                ], disabled: true} }
            ]
            let params = {companyId: row.companyId, realName: '1'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()

            if(this.selectType == 'purchaseSign'){
                let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
                let insertData = []
                let row ={}
                row['companyId'] = data[0].companyId
                row['companyName'] = data[0].companyName
                row['elsAccount'] = data[0].elsAccount
                form.companyId = data[0].companyId
                form.companyName = data[0].companyName
                form.elsAccount = data[0].elsAccount
                insertData.push(row)
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                // const form = this.$refs.editPage.getPageData()single
                form.purchaseSigners[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'keyWord'){
                if(data && data.length>0){
                    const { pageNo = '', posx = '', posy = '' } = data[0] || {}
                    let result = `${pageNo}_${posx}_${posy}`
                    // const form = this.$refs.editPage.getPageData()
                    let param = form.purchaseSigners[this.sealAeraIndex]
                    param.signArea = result
                }
            }else if(this.selectType === 'purchaseSigner'){
                form.purchaseSigners[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseSigners[this.rowSignerIndex].accountId = data[0].accountId
                form.purchaseSigners[this.rowSignerIndex].signatoryName = data[0].applyUserName
                form.purchaseSigners[this.rowSignerIndex].signatoryContact = data[0].applyContact
                form.purchaseSigners[this.rowSignerIndex].signatoryContactType = data[0].applyContactType
                // form.purchaseSigners[this.rowSignerIndex].companyId = data[0].companyId
                // form.purchaseSigners[this.rowSignerIndex].companyName = data[0].companyName
                form.purchaseSigners[this.rowSignerIndex].roleType = '0'
            }
            else if(this.selectType === 'selectCreater'){
                this.createDraft(data[0].id)
            }
            this.modalTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据')

        },
        deleteSaleSignEvent (){
            this.createFlag = false
            let itemGrid = this.$refs.editPage.$refs.purchaseSigners[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        preview () {
            let params= this.$refs.editPage.getPageData()
            if(params.busType == 'order'){
                this.orderReview(params.relationId)
            }else {
                getAction('/contract/purchaseContractHead/getPreviewData', {id: params.relationId}).then((res) => {
                    if (res.success) {
                        this.previewModal = true
                        this.previewContent = res.result
                    }
                })
            }
        },
        selectedPrintTemplate () {
            if(this.templateNumber) {
                const that = this
                this.submitLoading = true
                let template = this.templateOpts.filter(item => {
                    return item.value == that.templateNumber
                })
                let params = {
                    templateNumber: this.templateNumber,
                    printId: template[0].printId,
                    printName: template[0].printName,
                    printType: template[0].printType,
                    param: template[0].param
                }
                that.demandVisible = false
                that.submitLoading = false
                let rowItem = this.printRow
                this.printRow = {}
                let urlParam = ''
                if (params.param) {
                    let json = JSON.parse(params.param)
                    console.log('json:', json)
                    Object.keys(json).forEach((key, i) => {
                        urlParam += '&'+key+'='+rowItem[json[key]]
                    })
                }
                if (params.printType=='ureport') {
                    const token = this.$ls.get('Access-Token')
                    //const url = REPORT_ADDRESS + '/els/report/jmreport/view/1411993811223187456?id='+row.id + '&token=' + token
                    const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.printName+'&token=' + token+urlParam
                    window.open(url, '_blank')
                }
                if (params.printType=='jimu') {
                    const token = this.$ls.get('Access-Token')
                    const url = REPORT_ADDRESS + '/els/report/jmreport/view/'+params.printId+'?token=' + token+urlParam
                    //const url = REPORT_ADDRESS + '/els/report/ureport/preview?_u=mysql:'+params.title+'&token=' + token+'&id='+rowItem.id
                    window.open(url, '_blank')
                }
            }
        },
        queryPrintTemList (elsAccount) {
            let params = {elsAccount: elsAccount, businessType: 'order'}
            return getAction('/system/elsPrintConfig/getPrintListByType', params)
        },
        orderReview (id){
            this.queryPrintTemList(this.$ls.get('Login_elsAccount')).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        getAction('/order/purchaseOrderHead/queryById', {id: id}).then(resOrder => {
                            this.printRow = resOrder.result
                            let options = res.result.map(item => {
                                return {
                                    value: item.id,
                                    printId: item.printId,
                                    printName: item.printName,
                                    title: item.templateName,
                                    printType: item.printType,
                                    param: item.param
                                }
                            })
                            this.templateNumber = ''
                            this.templateOpts = options
                            // 只有单个模板直接新建
                            if (this.templateOpts && this.templateOpts.length===1) {
                                this.templateNumber = this.templateOpts[0].value
                                this.selectedPrintTemplate()
                            } else {
                                // 有多个模板先选择在新建
                                this.printVisible = true
                            }
                        })
                    }else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VWERfWIr_5ae67c6d`, '请先配置打印模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
            })
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },

        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    let url = this.url.edit
                    if(params.onlineSealed == '0'){
                        params.firstSeal = 'sale'
                    }
                    //采购方操作
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.createFlag = true
                            this.$refs.editPage.confirmLoading = false
                            this.init()
                        }
                    })
                }
            }).catch(err => {
                that.$refs.editPage.confirmLoading = false
                console.log(err)
            })
        },
        createDraft (id){
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        this.$refs.editPage.confirmLoading = false
                        return
                    }
                }
                if (flag) {
                    if(!this.createFlag){
                        this.$message.warning('请先保存')
                        this.$refs.editPage.confirmLoading = false
                        return
                    }

                    const params = this.$refs.editPage.getPageData()

                    if(params.setCreater == '1' && !id){
                        this.selectType = 'selectCreater'
                        this.$refs.editPage.confirmLoading = false
                        let url = '/contractLock/purchaseClPersonalInfo/list'
                        let columns = [
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                            { field: 'applyUserName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), width: 100 },
                            { field: 'applyContactType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'), width: 100 },
                            { field: 'applyContact', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCK_3c3789dd`, '联系方式'), width: 100 },
                            { field: 'roleStr', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_role`, '角色'), width: 100, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Rvj_1de7873`, '管理员'), value: 'ADMIN'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LejR_30b760f9`, '普通员工'), value: null}
                            ], disabled: true} },
                            { field: 'realName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKRLi_2b438163`, '是否实名认证'), width: 100, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LLi_194b947`, '未认证'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ILi_1721e0f`, '已认证'), value: '1'}
                            ], disabled: true} }
                        ]
                        let crow = params.purchaseSigners[0]
                        let p = {companyId: crow.companyId, realName: '1'}
                        this.modalTitle = '选择合同发起人（当发起人和合同第一位签署人不同时，才可收到签署短信）'
                        this.$refs.fieldSelectModal.open(url, p, columns, 'single')
                        return
                    }

                    getAction(this.url.createDraft, {id: params.id, creatorId: id}).then(res=>{
                        if(res.success){
                            this.$message.success('合同创建成功')
                            this.$refs.editPage.confirmLoading = false
                            this.initEdit()
                        }else {
                            this.$refs.editPage.confirmLoading = false
                            if(res.message == '网络异常，请检查网络连接'){
                                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAQLAEsLHcAZdROlbWVWKAELiHcneROjlb_421c7ed2`, '发起流程企业还未进行契约锁功能授权，请先在企业认证进行合同功能的授权'))
                            }else {
                                this.$message.error(res.message)
                            }
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        sendLineEvent (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    if(params.onlineSealed == '0'){
                        params.firstSeal = 'sale'
                    }
                    //采购方操作
                    postAction('/contractLock/elsClContract/edit', params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.createFlag = true
                            //发送
                            let url = this.url.send
                            params.reportUrl = REPORT_ADDRESS
                            params.token = this.$ls.get('Access-Token')
                            postAction(url, params).then(res => {
                                this.$refs.editPage.confirmLoading = false
                                if(res.success){
                                    this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hdLR_284294df`, '发送成功'))
                                    this.goBack()
                                }else{
                                    this.$message.warning(res.message)
                                }
                            })
                        }
                    })

                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        sendEvent (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    //发送
                    let url = this.url.send
                    params.reportUrl = REPORT_ADDRESS
                    params.token = this.$ls.get('Access-Token')
                    postAction(url, params).then(res => {
                        this.$refs.editPage.confirmLoading = false
                        if(res.success){
                            this.$message.info(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hdLR_284294df`, '发送成功'))
                            this.goBack()
                        }else{
                            this.$message.warning(res.message)
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        fileUpload (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            const that = this
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    let url = this.url.uploadFile
                    params.reportUrl = REPORT_ADDRESS
                    params.token = this.$ls.get('Access-Token')
                    postAction(url, params).then(res => {
                        this.$refs.editPage.confirmLoading = false
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.signerBtn = true
                            this.initEdit()
                        }
                    })
                }
            }).catch(err => {
                console.log(err)
                that.$refs.editPage.confirmLoading = false
            })
        },
        signFileDown (){
            this.$refs.editPage.confirmLoading = true
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewSignFile, {id: params.id}).then(res => {
                this.$refs.editPage.confirmLoading = false
                if(res.success){
                    window.open(res.result)
                }else{
                    this.$message.warning(res.message)
                }
            })
        },
        downSignFile (){
            const params = this.$refs.editPage.getPageData()
            console.log('1')
            getAction(this.url.viewSignFile, {id: params.id}).then(res => {
                if(res.success){
                    window.open(res.result)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>