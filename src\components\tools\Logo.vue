<!--
 * @Author: your name
 * @Date: 2021-05-28 17:39:01
 * @LastEditTime: 2021-12-28 18:46:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\components\tools\Logo.vue
-->
<template>
  <div
    class="logo"
    :class="{'dark-color':setDarkThemeDefaulColor}">
    <router-link :to="{name:'dashboard'}">
     
      <img
        v-if="layoutMode === 'topmenu' && navTheme === 'light'"
        :src="companySet.companyLogo?companySet.companyLogo:logUrl"
        alt="logo"
      />
      <img
        v-else
        :src="companySet.companyLogo?companySet.companyLogo:logUrl"
        alt="logo"
      />
      <h1 v-if="opened">
        {{ title }}
      </h1>
    </router-link>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { mixin } from '@/utils/mixin.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
export default {
    name: 'Logo',
    mixins: [mixin],
    data () {
        return {
            localLogoUrl: require('@/assets/img/common/logo.png')
        }
    },
    props: {
        title: {
            type: String,
            default: 'SRM',
            required: false
        },
        showTitle: {
            type: Boolean,
            default: true,
            required: false
        }
    },
    computed: {
        ...mapState({
            opened: state => state.app.sidebar.opened
            
        }),
        ...mapGetters({
            companySet: 'getCompanySet'
        }),
        imageUrl () {
            const { protocol = '', hostname = '' } = window.location || {}
            return `${protocol}//${hostname}/opt/upFiles`
        },

        logUrl () {   
            let rs = this.localLogoUrl
            let userInfo = this.$ls.get(USER_INFO) || {} 
            let comSet = this.$ls.get(USER_COMPANYSET) || {}
            // 系统管理中启动 且有企业logo图片
            if (comSet.enterpriseLogo === '1' && userInfo.enterpriseLogo) {
                rs = userInfo.enterpriseLogo
            }else if(comSet.companyLogo){
                // 租户logo
                rs = comSet.companyLogo
            }
            return rs
        }
    }
}
</script>
<style lang="scss" scoped>
  /*缩小首页布 局顶部的高度*/
    $height: 48px;

    .sider {
      box-shadow: none !important;
      .logo {
        height: $height !important;
        box-shadow: none !important;
        /* line-height: $height !important; */
        transition: background 300ms;
        display: flex;
        justify-content: center;
        align-items: center;
        &.dark-color{
          background-color: #21314D!important;
        }
        a {
          display: flex;
          align-items: center;
          color: white;
          &:hover {
            color: rgba(255, 255, 255, 0.8);
          }
          >img{
              max-width: 120px;
          }
        }
      }

      &.light .logo {
        background-color: #1890ff;
      }
    }
</style>
