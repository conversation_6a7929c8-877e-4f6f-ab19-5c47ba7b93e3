<template>
  <a-tree-select
    v-bind="$attrs"
    :value="realValue"
    tree-data-simple-mode
    show-search
    :tree-default-expand-all="treeDefaultExpandAll"
    :treeNodeFilterProp="treeNodeFilterProp"
    style="width: 100%"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="treeData"
    :placeholder="placeholder"
    :replaceFields="replaceFields"
    :filterTreeNode="filterTreeNode"
    :allow-clear="$listeners.afterClearCallBack?false:true"
    @change="changeEvent"
  >
    <a-icon
      v-if="$listeners.afterClearCallBack?true:false"
      slot="suffixIcon"
      :style="{ fontSize: '15px',color:'rgba(0, 0, 0, 0.65)' }"
      type="close-circle"
      @click="clearInputValue"
    ></a-icon>
    <template
      v-if="showMaxTagPlaceholder"
      slot="maxTagPlaceholder">
      <span class="maxTagPlaceholder">+{{ length }}</span>
    </template>
  </a-tree-select>
</template>

<script>

import {TreeSelect} from 'ant-design-vue'
import {getAction} from '@api/manage'
export default {
    name: 'MTreeSelect',
    inheritAttrs: false,
    model: {
        prop: 'value',
        event: 'change'
    },
    components: {
        ATreeSelect: TreeSelect
    },
    props: {
        value: [String, Array],
        sourceUrl: [String],
        sourceMap: {
            type: Object,
            default: () => {}
        },
        sourceData: {
            type: Array,
            default: () => []
        },
        valueMap: {
            type: String,
            default: 'value'
        },
        titleMap: {
            type: String,
            default: 'title'
        },
        placeholder: {
            type: String,
            default: ''
        },
        showEmptyNode: {
            type: Boolean,
            default: false
        },
        parentNodeSelectable: {
            type: Boolean,
            default: true
        },
        treeDefaultExpandAll: {
            type: Boolean,
            default: true
        },
        treeNodeFilterProp: {
            type: String,
            default: 'value'
        }
    },
    data () {
        return {
            treeData: [],
            replaceFields: {children: 'children', title: this.titleMap, key: 'key', value: this.valueMap }
        }
    },
    computed: {
        realValue () {
            const { multiple = false } = this.$attrs || {}
            if (multiple) {
                return this.value ? this.value.split(',') : []
            }
            return this.value || ''
        },
        showMaxTagPlaceholder () {
            let { maxTagCount, multiple } = this.$attrs || {}
            if (!maxTagCount) return false
            let min = maxTagCount + 1
            return multiple && Array.isArray(this.realValue) && this.realValue.length >= min
        },
        length () {
            if (!this.showMaxTagPlaceholder) return ''
            let { maxTagCount } = this.$attrs || {}
            maxTagCount = maxTagCount || 1
            return this.realValue.length - maxTagCount
        }
    },
    watch: {
        sourceUrl: {
            immediate: true,
            handler (url) {
                if (url) {
                    this.loadRootNode()
                } else {
                    if (this.sourceData) {
                        if(!this.parentNodeSelectable) {//设置非叶子节点不可选属性
                            this.getDisabled(this.treeData)
                        }
                        this.treeData = this.sourceData
                    }
                }
            }
        }
        // sourceMap: {
        //     immediate: true,
        //     handler () {
        //         this.loadRootNode()
        //     }
        // }
    },
    methods: {
        filterTreeNode (inputValue, treeNode) {
            if (treeNode.data.props.title && treeNode.data.props.title.indexOf(inputValue) != -1 ) return true
            return false
        },
        // 加载根节点
        loadRootNode () {
            this.treeData = []
            let params = {parentId: '0'}
            Object.assign(params, this.sourceMap)
            getAction(this.sourceUrl, params).then(res => {
                if(res.success) {
                    let treeData = res.result
                    let emptyNode = []
                    if(this.showEmptyNode) {
                        emptyNode = [{
                            id: '11111',
                            pId: '0',
                            value: '0',
                            title: '无',
                            isLeaf: true
                        }]
                    }
                    this.treeData = emptyNode.concat(treeData)
                    if(!this.parentNodeSelectable) {//设置非叶子节点不可选属性
                        this.getDisabled(this.treeData)
                    }
                }
            })
        },
        getDisabled (data) {
            data.map(item => {
                if (item.children && item.children.length > 0) {
                    item.selectable = false
                    this.getDisabled(item.children)
                }
            })
        },
        changeEvent (value, label, extra) {
            const { multiple = false } = this.$attrs || {}
            let realValue = ''
            let realLabel = ''
            let oldVal = JSON.parse(JSON.stringify(this.realValue))
            if (multiple) {
                realValue = value.join(',')
                realLabel = label.join(',')
            } else {
                realValue = value
                realLabel = label
            }
            this.$emit('change', realValue, extra, oldVal, this.$attrs.configData, realLabel)
        },
        defaultClearCallBack (form){
            form[this.$attrs.configData.fieldName] =''
        },
        clearInputValue (event) {
            event.stopPropagation()
            let extend = (this.$attrs.configData && this.$attrs.configData.extend) || {}
            let afterClearCallBack = extend.afterClearCallBack || this.defaultClearCallBack
            this.$emit('afterClearCallBack', afterClearCallBack)
        }
    }
}
</script>