<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
    <!-- 编辑界面 -->
    <purchase-barcode-print-head-edit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <!-- 详情界面 -->
    <Purchase-barcode-print-head-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="handleList" />
    <div
      class="shadow-wrap"
      v-if="confirmLoading">
      <a-spin></a-spin>
      <span>printing</span>
    </div>
  </div>
</template>
<script>

import PurchaseBarcodePrintHeadEdit from './modules/PurchaseBarcodePrintHeadEdit'
import PurchaseBarcodePrintHeadDetail from './modules/PurchaseBarcodePrintHeadDetail'
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, postAction } from '@/api/manage'
import { getLodop } from '@/utils/LodopFuncs'

var LODOP, P_ID='', JobOk, JobExit, timeOutFunt, count=0 //声明为全局变量

export default {
    mixins: [ListMixin],
    components: {
        PurchaseBarcodePrintHeadEdit,
        PurchaseBarcodePrintHeadDetail
    },
    data () {
        return {
            showDetailPage: false,
            showEditPage: false,
            confirmLoading: false,
            currentResultRow: {},
            pageData: {
                businessType: 'barcodePrint',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNtFAy_ae8e2a03`, '请输入单据编号')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    { 
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        icon: 'plus', 
                        clickFn: this.handleAdd, 
                        type: 'primary',
                        authorityCode: 'barcode#print:add'
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
                        icon: 'file-text',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpText
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
                        icon: 'file-pdf',
                        folded: true,
                        isDocument: true,
                        clickFn: this.showHelpPDF
                    }
                ],
                optColumnWidth: 250,
                optColumnList: [
                    {
                        type: 'view',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
                        clickFn: this.handleView,
                        authorityCode: 'barcode#print:detail'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
                        clickFn: this.handleEdit,
                        allow: this.showEdit,
                        authorityCode: 'barcode#print:edit'
                    },
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'),
                        clickFn: this.preview,
                        authorityCode: 'barcode#print:print'
                    },
                    {
                        type: 'delete',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        clickFn: this.handleDelete,
                        allow: this.showDelete,
                        authorityCode: 'barcode#print:delete'
                    },
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ]
            },
            url: {
                list: '/base/barcode/purchaseBarcodePrintHead/list',
                add: '/base/barcode/purchaseBarcodePrintHead/add',
                delete: '/base/barcode/purchaseBarcodePrintHead/delete',
                deleteBatch: '/base/barcode/purchaseBarcodePrintHead/deleteBatch',
                enabled: '/base/barcode/purchaseBarcodePrintHead/enabled',
                disabled: '/base/barcode/purchaseBarcodePrintHead/disabled',
                templateCopy: '/base/barcode/purchaseBarcodePrintHead/templateCopy',
                templatePreview: '/base/barcode/purchaseBarcodePrintHead/templatePreview',
                saveBarcodeRecordByPrint: '/base/barcode/purchaseBarcodePoolHead/saveBarcodeRecordByPrint',
                columns: 'purchaseBarcodePrintHeadList'
            }
        }
    },
    methods: {
        handleDetail (row) {
            this.currentEditRow = row
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = true
        },
        handleList () {
            this.showResultPage = false
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent()
        },
        submitCallBack (row){
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        showEdit (row) {
            return row.status == '0' ? false : true
        },
        showDelete (row) {
            return row.status == '0' ? false : true
        },
        preview (row) {
            let that = this
            getAction(this.url.templatePreview+'?id='+row.id, {}).then((res) => {
                if (res.success) {
                    that.confirmLoading = true
                    LODOP = getLodop()//调用getLodop获取LODOP对象
                    eval(res.result.template)
                    LODOP.SET_PRINT_COPIES(res.result.printNumber) //打印份数
                    //TODO 循环查询打印队列状态
                    // LODOP.SET_PRINT_MODE('CATCH_PRINT_STATUS', true)
                    // if (LODOP.CVERSION) {
                    //     LODOP.On_Return=function (TaskID, Value){
                    //         P_ID=Value
                    //         if (P_ID!='') {	count=0; that.C_WaitFor(P_ID, row)}
                    //     }
                    //     LODOP.PREVIEW()
                    // } else {
                    //     P_ID=LODOP.PREVIEW()
                    //     if (P_ID!='') {	count=0; that.WaitFor(P_ID, that, row) }
                    // }
                    // TODO 只判断关闭插件时的打印情况
                    if (LODOP.CVERSION) {
                        CLODOP.On_Return=function (TaskID, Value){
                            that.confirmLoading = false
                            if (parseInt(Value)>0) {
                                that.writeBack(row, that)
                            }
                        }
                        LODOP.PREVIEW()
                    }else {
                        if (parseInt(LODOP.PREVIEW())>0) {
                            this.writeBack(row, that)
                        }
                    }

                    // document.getElementById('S1').value='打印预览返回结果：'+LODOP.PREVIEW()
                    // document.getElementById('id01').disabled = true

                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
            })
        },
        //TODO 循环查询打印队列状态，控制更精准
        C_WaitFor (P_ID, row){
            if (!this.confirmLoading){
                this.confirmLoading = true
            }
            let that = this
            count++
            console.log('正等待(JOB代码是'+P_ID+')打印结果：'+count+'秒')
            timeOutFunt=setTimeout(() => {this.C_WaitFor(P_ID, row)}, 1000)
            LODOP.On_Return_Remain=true
            LODOP.On_Return=function (TaskID, Value){
                console.log('进入onReturn: task:'+TaskID+'  JobExit:'+JobExit+'   Value:'+Value)
                // console.log('进入onReturn: task:'+TaskID+'   JobOk:'+JobOk+'  JobExit:'+JobExit+'   Value:'+Value)
                //TODO 判断打印状态是否成功，不同打印机状态码可能不一样，需要对应调整
                if (TaskID==JobExit){
                    if (Value==0){
                        clearTimeout(timeOutFunt)
                        console.log('打印成功！')
                        count=0
                        // 回写使用记录
                        this.writeBack(row, that)
                    }
                }
            }
            // JobOk=LODOP.GET_VALUE('PRINT_STATUS_OK', P_ID)
            JobExit=LODOP.GET_VALUE('PRINT_STATUS_EXIST', P_ID)
            // console.log('taskId1:'+JobOk)
            console.log('taskId2:'+JobExit)
            if (count>120){
                clearTimeout(timeOutFunt)
                console.log('打印超时(120秒)！')
                count=0
                alert('打印超过120秒没捕获到成功状态！')
            }
        },
        //TODO 循环查询打印队列状态，控制更精准
        WaitFor (P_ID, row){
            if (!this.confirmLoading){
                this.confirmLoading = true
            }
            count=count+1
            console.log('正等待(JOB代码是'+P_ID+')打印结果：'+count+'秒')
            timeOutFunt=setTimeout(() => {this.WaitFor(P_ID, row)}, 1000)
            if (LODOP.GET_VALUE('PRINT_STATUS_OK', P_ID)) {
                clearTimeout(timeOutFunt)
                console.log('打印成功！')
                count=0
                alert('打印成功！')
            }if ((!LODOP.GET_VALUE('PRINT_STATUS_EXIST', P_ID))&&(count>0)) {
                clearTimeout(timeOutFunt)
                console.log('打印任务被删除！')
                count=0
                alert('打印任务被删除！')
            } else if (count>120){
                clearTimeout(timeOutFunt)
                console.log('打印超时(120秒)！')
                count=0
                alert('打印超过120秒没捕获到成功状态！')
            }
        },
        // 回写打印记录
        writeBack (row, that){
            const params = {
                templateId: row.id,
                businessType: '打印任务'
            }
            postAction(that.url.saveBarcodeRecordByPrint, params).then((res) => {
                if (res.success) {
                    that.$message.success('打印成功！')
                    that.searchEvent()
                } else {
                    that.$message.warning(res.message)
                }
            }).finally(() => {
                that.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.shadow-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  z-index: 10000;
  background-color: rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
  color: #fff;
}
</style>