<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2022-08-19 16:45:00
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div class="business-container sale-eight-disciplines">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :url="url"
        modelLayout="masterSlave"
        pageStatus="edit"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
        <template
          #eightDisciplinesThreeList="{ slotProps }"
        >
          <edit-form-layout 
            class="eight-disciplines-three-list"
            v-if="threeListSlotData.groupType==='head'"
            :ref="threeListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :currentEditRow="currentEditRow"
            :group="threeListSlotData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners"
          ></edit-form-layout>
        </template>
        <template
          #eightDisciplinesSixList="{ slotProps }"
        >
          <edit-form-layout 
            v-if="sixListSlotData.groupType==='head'"
            :ref="sixListSlotData.groupCode+ 'form'"
            :busAccount="slotProps.busAccount"
            :currentEditRow="currentEditRow"
            :group="sixListSlotData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners"
          ></edit-form-layout>
        </template>
        <!-- "eightDisciplinesTwo" "eightDisciplinesFour" "eightDisciplinesSeven" "eightDisciplinesEight" -->
        <template
          #eightDisciplinesTwo="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="saleAttachmentD2ListSlotData.groupType==='item'"
            :ref="saleAttachmentD2ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="saleAttachmentD2ListSlotData"
            :loadData="saleAttachmentD2ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesSeven="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="saleAttachmentD7ListSlotData.groupType==='item'"
            :ref="saleAttachmentD7ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="saleAttachmentD7ListSlotData"
            :loadData="saleAttachmentD7ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesEight="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="saleAttachmentD8ListSlotData.groupType==='item'"
            :ref="saleAttachmentD8ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="saleAttachmentD8ListSlotData"
            :loadData="saleAttachmentD8ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
          >
          </edit-grid-layout>
        </template>
        <template #eightDisciplinesFour="{slotProps}">
          <!-- 上传 -->
          <edit-grid-layout 
            :style="{height: '200px' }"
            v-if="saleAttachmentD4ListSlotData.groupType==='item'"
            :ref="saleAttachmentD4ListSlotData.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="saleAttachmentD4ListSlotData"
            :loadData="saleAttachmentD4ListSlotData.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners">
          </edit-grid-layout>
        </template>
      </business-layout>
    </a-spin>

    <a-modal
    v-drag    
      v-model="rejectVisible"
      :title="rejectModelTitle"
      :okText="okText"
      @ok="handleOk">
      <a-form-model
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :model="rejectForm">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMyC_478bc282`, '驳回节点')">
          <a-select
            style="width: 100%"
            v-model="rejectForm.node">
            <a-select-option
              v-for="(item, i) in nodeList"
              :key="i"
              :value="item.val"
            >
              {{ item.key }}
            </a-select-option>
          </a-select>
        </a-form-model-item >
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_rMvj_478a05f6`, '驳回理由')"
        >
          <a-textarea
            v-model="rejectForm.reject"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由')"
            :auto-size="{ minRows: 3, maxRows: 5 }"
          />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
    

  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import DetailFormLayout from '@comp/template/business/DetailFormLayout.vue'
import DetailGridLayout from '@comp/template/business/DetailGridLayout.vue'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'

export default {
    name: 'SaleEightDisciplinesHeadEdit',
    components: {
        DetailFormLayout,
        DetailGridLayout,
        EditFormLayout,
        EditGridLayout,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNrMvj_ceb1c7df`, '请输入驳回理由'),
            rejectModelTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIyC_2e6c072a`, '指定节点'),
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reject`, '驳回'),
            rejectVisible: false,
            refresh: true,
            nodeList: [
                {key: 'D0:问题提出', val: 'D0'},
                {key: 'D1:小组成立', val: 'D1'},
                {key: 'D2:问题界定', val: 'D2'},
                {key: 'D3:围堵措施', val: 'D3'},
                {key: 'D4:原因分析', val: 'D4'},
                {key: 'D5:纠正措施', val: 'D5'},
                {key: 'D6:效果验证', val: 'D6'},
                {key: 'D7:预防再发生', val: 'D7'},
                {key: 'D8:结案评价', val: 'D8'}
            ],
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                export: { url: '/base/excelByConfig/downloadTemplate', args: () => {} },
                import: { url: '/base/excelByConfig/importExcel', args: () => {}  },
                detail: { url: '/eightReport/saleEightDisciplinesPoc/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                eightDisciplinesThreeList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                eightDisciplinesFiveList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                eightDisciplinesSixList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        key: 'gridAdd',
                        click: this.businessGridAdd,
                        attrs: {
                            type: 'primary'
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                saleAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete'
                    }
                ],
                saleAttachmentD2List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        attrCheck: 'itemNumber',
                        disabledHead: true,
                        itemNumberDefaultValue: 'D2',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.slotUploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessSlotGridDelete
                    }
                ],
                saleAttachmentD4List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        attrCheck: 'itemNumber',
                        disabledHead: true,
                        itemNumberDefaultValue: 'D4',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.slotUploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessSlotGridDelete
                    }
                ],
                saleAttachmentD7List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        attrCheck: 'itemNumber',
                        disabledHead: true,
                        itemNumberDefaultValue: 'D7',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.slotUploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessSlotGridDelete
                    }
                ],
                saleAttachmentD8List: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        attrCheck: 'itemNumber',
                        disabledHead: true,
                        itemNumberDefaultValue: 'D8',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'eightDisciplines', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        callBack: this.slotUploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessSlotGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/eightReport/saleEightDisciplinesPoc/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    authorityCode: 'eightReportPoc#SaleEightReportPocHead:edit'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/eightReport/saleEightDisciplinesPoc/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    authorityCode: 'eightReportPoc#SaleEightReportPocHead:publish'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            threeListSlotData: {},
            sixListSlotData: {},
            saleAttachmentD2ListSlotData: {},
            saleAttachmentD4ListSlotData: {},
            saleAttachmentD7ListSlotData: {},
            saleAttachmentD8ListSlotData: {},
            url: {
                reject: '/eightReport/saleEightDisciplinesPoc/reject',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.eightDisciplinesNumber,
                actionRoutePath: '/srm/eightReportPoc/purchase/PurchaseEightDisciplinesHeadList,/srm/eightReportPoc/sale/SaleEightDisciplinesHeadList'
            }
        },
        businessSlotGridDelete ({ pageConfig, groupCode }) {
            // if('D0'==pageConfig.groups[0].formModel.eightDisciplinesStatus){
            //     return true
            // }
            let gridRef = `${groupCode}grid`
            let itemGrid = this.$refs[gridRef].$refs[groupCode]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.srmI18n(`${this.getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
            let that = this
            const purchaseAttachmentLists = ['saleAttachmentList', 'saleAttachmentD2List', 'saleAttachmentD4List', 'saleAttachmentD7List', 'saleAttachmentD8List' ]
            const filterGroupsCode = ['eightDisciplinesThreeListSlot', 'eightDisciplinesSixListSlot', 'saleAttachmentD2List', 'saleAttachmentD4List', 'saleAttachmentD7List', 'saleAttachmentD8List']
            let itemInfo = pageConfig.groups.filter(rs => !filterGroupsCode.includes(rs.groupCode)).map(n => ({ label: n.groupName, value: n.groupCode }))
            purchaseAttachmentLists.forEach(rs => {
                if (that.externalToolBar[rs].length) {
                    that.externalToolBar[rs][0].args.headId = resultData.id || ''
                    that.externalToolBar[rs][0].args.itemInfo = itemInfo
                }
            })
            pageConfig.groups.forEach((rs, index) => {
                if (rs.groupCode == 'eightDisciplinesThreeListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.threeListSlotData = rs
                }
                if (rs.groupCode == 'eightDisciplinesSixListSlot') {
                    rs.show = false
                    rs.verify = true
                    this.sixListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD2List') {
                    rs.show = false
                    rs.verify = true
                    this.saleAttachmentD2ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD4List') {
                    rs.show = false
                    rs.verify = true
                    this.saleAttachmentD4ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD7List') {
                    rs.show = false
                    rs.verify = true
                    this.saleAttachmentD7ListSlotData = rs
                }
                if (rs.groupCode == 'saleAttachmentD8List') {
                    rs.show = false
                    rs.verify = true
                    this.saleAttachmentD8ListSlotData = rs
                }

            })
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory1`, '附件2'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD2List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD4List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD7List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentD8List',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd3slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesThreeListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_10010011`, 'd6slot'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'eightDisciplinesSixListSlot',
                        groupType: 'head',
                        show: false,
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk17',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk16',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesThreeListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk15',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    },
                    {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '拟制',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk14',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '审核',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk13',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }, {
                        groupCode: 'eightDisciplinesSixListSlot',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: '批准',
                        fieldLabelI18nKey: '',
                        fieldName: 'fbk12',
                        dictCode: '',
                        defaultValue: '',
                        dataFormat: '',
                        helpText: '',
                        alertMsg: '',
                        required: '0',
                        placeholder: ''
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        fieldType: 'select',
                        defaultValue: '',
                        dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD2List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD4List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD7List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    }, {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentD8List',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        slotUploadCallBack (result, ref) {
            debugger
            let gridRef = `${ref}grid`
            let fileGrid = this.$refs[gridRef].$refs[ref]
            fileGrid.insertAt(result, -1)
        },
        businessGridAdd ({ Vue, pageConfig, btn, groupCode }) {
            console.log('groupCode', groupCode)
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            itemGrid.insertAt([row], -1)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        reject (){
            let eightReportStatus = this.getAllData().eightDisciplinesStatus
            let currentStep = eightReportStatus.substring(1, 2)
            if(parseInt(currentStep)==0){
                return
            }
            this.rejectForm.node = 'D'+(parseInt(currentStep)-1)
            this.rejectVisible = true
        },
        handleOk () {
            const that = this
            let pageAllData = this.getAllData()
            //驳回理由
            pageAllData.rejectReason = this.rejectForm.reject
            //退回到的节点
            pageAllData.fbk1 = this.rejectForm.node
            postAction(this.url.reject, pageAllData).then(res => {
                if(res.success) {
                    that.$message.success(res.message)
                }else {
                    that.$message.warning(res.message)
                }
                that.stepBusinessRefresh()
            })
            this.rejectVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
.sale-eight-disciplines{
     :deep(.eightDisciplinesThreeList .vxe-grid){
         overflow: hidden;
     }
     :deep(.eightDisciplinesSixList .vxe-grid){
         overflow: hidden;
     }
}
.sale-eight-disciplines{
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col){
         width: 100%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-9){
         width: 21%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-15){
         width: 100%;
     }
     :deep(.eightDisciplinesTwo .ant-advanced-rule-form>.ant-row>.ant-col){
        margin-bottom: 95px;
     }


     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col){
         width: 100%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-9){
         width: 12.5%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col .ant-col-15){
         width: 100%;
     }
     :deep(.eightDisciplinesFour .ant-advanced-rule-form>.ant-row>.ant-col){
        margin-bottom: 95px;
     }

      :deep(.eightDisciplinesSeven>.ant-advanced-rule-form>.ant-form-item){
         min-height: 161px !important;
     }
      :deep(.eightDisciplinesEight>.ant-advanced-rule-form>.ant-form-item){
         min-height: 161px !important;
     }
}
</style>