<template>
  <div style="margin:0 10px 0 50px">
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n__IByC_37a6bb10`, '目标节点')"
          prop="nextId">
          <a-select
            style="width: 120px"
            v-model="form.nextId">
            <a-select-option
              :key="item.id + index"
              :value="item.id"
              v-for="(item, index) in form.nodeArray"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_uPAc_276e27ce`, '加签类型')"
          prop="parallel">
          <a-radio-group
            name="radioGroup"
            @change="handleChangeParallel"
            :defaultValue="false"
            v-model="form.parallel">
            <a-radio
              v-for="(item, index) in [
                { lable: $srmI18n(`${$getLangAccount()}#i18n_field_Vc_a005a`, '串行'), value: false },
                { lable: $srmI18n(`${$getLangAccount()}#i18n_field_Gc_bf896`, '并行'), value: true }
              ]"
              :key="index"
              :value="item.value"
            >{{ item.lable }}</a-radio
            >
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_proportion`, '比例')"
          prop="signScale">
          <a-select
            style="width: 120px"
            v-model="form.signScale">
            <a-select-option
              :key="item.lable + index"
              :value="item.lable"
              v-for="(item, index) in signType"
            >
              {{ item.value }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.parallel == true"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_passed`, '通过')"
          prop="pass">
          <a-select
            defaultValue="next"
            style="width: 120px"
            :allowClear="true"
            v-model="form.pass">
            <a-select-option value="next">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_QsSRyC_7c5dd062`, '流转后置节点') }}
            </a-select-option>
            <a-select-option value="pre">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_MYPRyC_5029dcda`, '回退前置节点') }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          v-if="form.parallel == true"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_refuse`, '拒绝')"
          prop="unPass">
          <a-select
            defaultValue="next"
            :allowClear="true"
            style="width: 120px"
            v-model="form.unPass">
            <a-select-option value="next">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_QsSRyC_7c5dd062`, '流转后置节点') }}
            </a-select-option>
            <a-select-option value="pre">
              {{ $srmI18n(`${$getLangAccount()}#i18n_field_MYPRyC_5029dcda`, '回退前置节点') }}
            </a-select-option>
            <a-select-option value="initiator">
              {{ $srmI18n(`${$getLangAccount()}#i18n_dict_MYuhAL_4f7a2766`, '回退到发起人') }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_massProdHeadd3b3_pubishAudit`, '审批策略')"
          prop="signType">
          <a-select
            defaultValue="scale"
            :allowClear="true"
            style="width: 120px"
            v-model="form.signType">
            <a-select-option value="scale">
              {{ $srmI18n(`${$getLangAccount()}#i18n__lveRR_15d58f52`, '比例通过制') }}
            </a-select-option>
            <!-- <a-select-option
              value="oneVeto"
              v-if="form.parallel == true">
              {{ $srmI18n(`${$getLangAccount()}#i18n__IPQK_254733b5`, '一票否决') }}
            </a-select-option>
            <a-select-option
              value="oneVetoPass"
              v-if="form.parallel == true">
              {{ $srmI18n(`${$getLangAccount()}#i18n__IPeR_254eb455`, '一票通过') }}
            </a-select-option> -->
          </a-select>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')"
          prop="usersInfo">
          <a-tag
            v-for="(users, userIndex) in form.usersInfo"
            :key="users.id"
            size="large"
            color="blue"
            closable
            @close="delCommonUsers(userIndex)"
          >{{ users.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectInformedUsers"></a-button>
          </div>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_LSRL_2527e109`, '任务名称')"
          prop="userTaskName">
          <a-input
            v-model="form.userTaskName"
            clearable />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from './modalMixins.js'
import { getOutgoingFlows, addSignOperate } from '../../api/analy'
export default {
    mixins: [modalMixins],
    data () {
        return {
            rules: {
                nextId: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFIByC_7efe05c7`, '请选择目标节点') } ],
                signScale: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFlv_f755eaae`, '请选择比例') } ],
                opinion: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMqdWII_13f544fb`, '请填写备注/意见'), trigger: 'change' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                userTaskName: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMLSRL_9060674e`, '请填写任务名称'), trigger: 'change' },
                    {max: 255, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow255`, '内容长度不能超过255个字符')}
                ],
                usersInfo: [{ required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFGvL_f32585e1`, '请选择处理人') } ]
            },
            labelCol: { span: 6 },
            singleUserData: {}
        }
    },
    methods: {
        handleChangeParallel (e) {
            if (e.target.value) {
                this.$set(this.form, 'signType', 'scale')
                this.$set(this.form, 'allStrage', false)
                this.$set(this.form, 'unPass', 'next')
                this.$set(this.form, 'pass', 'next')
            } else {
                this.$set(this.form, 'unPass', '')
                this.$set(this.form, 'pass', '')
            }

        },
        selectInformedUsers () {
            this.showUserSelectModal({ selectModel: 'multiple' })
        },
        fieldSelectOk (data) {
            this.$set(this.form, 'usersInfo', data)
        },
        delCommonUsers (index) {
            this.form.usersInfo.splice(index, 1)
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                this.loading = true
                let params = {
                    ...this.form,
                    all: false,
                    doUserId: ''
                }
                delete params.nodeArray
                addSignOperate(params).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.$message.success(res.message)
                        this.$emit('success', 'back')
                    } else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.loading = false
                    this.$emit('closeLoading')
                })
            })
        }
    },
    created () {
        this.loading = true
        getOutgoingFlows(this.taskId).then(res => {
            if (res.code == 0) {
                this.$set(this.form, 'nodeArray', res.data)
            }
            this.loading = false
        })
        this.$set(this.form, 'signType', 'scale')
        this.$set(this.form, 'parallel', false)
    }
}
</script>
