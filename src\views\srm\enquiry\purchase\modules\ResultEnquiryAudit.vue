<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LokNum
 * @LastEditTime: 2022-05-16 16:43:37
 * @Description: workFlow
-->
<template>
  <div class="page-container-default">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      isAudit
      :pageData="pageData"
      @loadSuccess="handleLoadSuccess"
      :singleGroupCoverConfig="singleGroupCoverConfig"
    >
      <template v-slot:customDetailContent>
        <a-tabs>
          <a-tab-pane forceRender tab="审批意见">
            <approvalHistoryModal ref="approvalHistoryModal" :visible="true"/>
          </a-tab-pane>
        </a-tabs>
      </template>

    </detail-layout>
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <!-- 查看流程 -->
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterapprovalComments`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <view-ladder-price-modal
      :current-edit-row="currentEditRow"
      ref="ladderPage"
    />
    <!-- 比价报表弹窗 -->
    <report-modal
      ref="reportModal"
      @exportCompare="exportCompare"/>
  </div>
</template>

<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction, downFile, postDownFile} from '@/api/manage'
import { httpAction } from '@/api/manage'
import ViewLadderPriceModal from '../../modules/ViewLadderPriceModal'
import flowViewModal from '@comp/flowView/flowView'
import reportModal from '../../component/reportModal'
import approvalHistoryModal from "@views/srm/bpm/components/modal/approvalHistoryModal";

export default {
    name: 'ResultEnquiryAudit',
    mixins: [DetailMixin],
    components: {
      approvalHistoryModal,
        flowViewModal,
        ViewLadderPriceModal,
        reportModal
    },
    provide () {
      return {
        currentEditRow: this.currentEditRow
      }
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvalOfInquiryResults`, '询价结果审批'),
            confirmLoading: false,
            flowId: 0,
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            currentRow: {},
            auditVisible: false,
            opinion: '',
            sourceId: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEnquiryItemList',
                        columns: [],
                        buttons: [
                            { type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表'), click: this.reportModalShow},
                            { type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_output`, '导出'), click: this.itemExportExcel}
                        ]
                    } },

                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'), groupCode: 'awardOpinion', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'purchaseAwardOpinionList',
                        innerHeight: 'auto',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'awardOpinion', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'), showOverflow: false, showFooterOverflow: false, align: 'left'},
                            //{ field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'), width: 150 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'), width: 150 }
                        ],
                        showOptColumn: false
                    } },

                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), 
                        groupCode: 'fileDemandInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), 
                        groupCode: 'fileInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), click: this.auditPass, showCondition: this.showAuditBtn},
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), click: this.auditReject, showCondition: this.showAuditBtn},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), click: this.showFlow},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/enquiry/purchaseEnquiryHead/resultAuditQuery'
            },
            costSlots: {
                default: ({ row }) => {
                    if (row && row.quotePriceWay == 2) {
                        let label = row.price ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        return [<a onClick={() => this.openCost(row)}>{label}</a>]
                    } else {
                        return ''
                    }
                }
            },
            ladderSlots: {
                default: ({row, column}) =>{
                    const detailTpl = (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                            <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                <template slot="title">
                                    <div>
                                        <vxe-table auto-resize border row-id="id" size="mini" data={this.initRowLadderJson(row[column.property])}>
                                            <vxe-table-column type="seq" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                            <vxe-table-column field="ladder" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                            <vxe-table-column field="price" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                            <vxe-table-column field="netPrice" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                        </vxe-table>
                                    </div>
                                </template>
                                {this.defaultRowLadderJson(row[column.property])}
                            </a-tooltip>
                        </div>)
                    if(row && row.quotePriceWay == 1){
                        // let label = row['ladderPriceJson'] ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看') : ''
                        // return [
                        //     <a onClick={() => this.setLadder(row)}>{label}</a>
                        // ]
                        return detailTpl
                    }else{
                        return ''
                    }
                }
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentRow.templateNumber
            let elsAccount = this.currentRow.templateAccount || this.$ls.get('Login_elsAccount')
            let templateVersion = this.currentRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_enquiry_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        formatTableData(data) {
            console.log('請求接口後格式化列表數據', data)
            data = data.map((item) => {
                if (item.price === 0 || Math.abs(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }

                
                if (item.quotaTaxAmount === 0 || Math.abs(item.quotaTaxAmount) > 0) {
                    item.quotaTaxAmount = Number(item.quotaTaxAmount).toFixed(2)
                }
                if (item.quotaNetAmount === 0 || Math.abs(item.quotaNetAmount) > 0) {
                    item.quotaNetAmount = Number(item.quotaNetAmount).toFixed(2)
                }
                
                return item
            })
            return data
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            console.log(jsonData)
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                if (Array.isArray(arr)) {
                    arr.forEach((item, index)=> {
                        let ladder = item.ladder
                        let price = item.price || ''
                        let str = `${ladder},${price}`
                        let separator = index===arr.length-1? '': ';'
                        arrString +=str+ separator
                    })
                }
            }
            return arrString
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        beforeHandleData (data) {
            data.itemColumns.forEach(item => {
                if(item.field == 'ladderPriceJson'){
                    item.slots = this.ladderSlots
                }
                if (item.field == 'costFormJson') {
                    item.slots = this.costSlots
                }
                //询价结果审批详情 - 询价行信息字段排序
                // let needSortKeyList = ["物料编码","含税单价","需求数量","主单位","辅数量","辅单位","未税单价","拆分数量","拆分含税金额","拆分不含税金额","含税金额","未税金额"];
                // let needSortKeyList = ["materialNumber","price","requireQuantity","quantityUnit","secondaryQuantity","purchaseUnit","netPrice","quotaQuantity","quotaTaxAmount","quotaNetAmount","taxAmount","netAmount"];
                // if (needSortKeyList.includes(item.field)) {
                //     item.sortable = true;
                // }
                if(item.field!==''){
                    item.sortable = true;
                }
            })
        },
        singleGroupCoverConfig (config, group, displayModel) {
            config.size = 'mini'
            config.rowClassName = this.rowClassName
            return config
        },
        rowClassName ({ row, rowIndex }) {
            let classNameRow = 'row-class-default'
            if (row.itemStatus) {
                if (row.itemStatus == '4') {
                classNameRow = 'row-class-blue' // 接受
                } else if(row.itemStatus == '5') {
                classNameRow = 'row-class-red' // 拒绝
                }
            }
            return classNameRow
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
            let opinionList = this.currentRow.purchaseAwardOpinionList
            let lastList = []
            lastList.push(opinionList[opinionList.length - 1])
            this.currentRow.purchaseAwardOpinionList = lastList
            console.log(lastList,'授标意见信息')
        },
        goBack () {
            this.$parent.hideController()
        },
        setLadder (row){
            this.$refs.ladderPage.open(row)
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText=this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText=this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        reportModalShow (){
            this.sourceId = this.$refs.detailPage.form.id
            this.getData()
        },
        itemExportExcel() {
            postDownFile("/enquiry/purchaseEnquiryHead/exportItemExcel/" + this.$refs.detailPage.form.id, null, null).then((data) => {
                // responseType为blob的请求，统一获取错误信息
                if (data.type === 'application/json') {
                    const fileReader = new FileReader()
                    fileReader.onloadend = () => {
                        const jsonData = JSON.parse(fileReader.result)
                        this.$message.error(jsonData.message)
                    }
                    fileReader.readAsText(data)
                    return
                }
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), '导出询价行.xls')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', '导出询价行.xls')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        exportCompare () {
            let params = {'headId': this.sourceId}
            downFile('/enquiry/purchaseEnquiryHead/exportBargain', params).then((data) => {
                if (!data) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败') )
                    return
                }
                if (typeof window.navigator.msSaveBlob !== 'undefined') {
                    window.navigator.msSaveBlob(new Blob([data]), this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                } else {
                    let url = window.URL.createObjectURL(new Blob([data]))
                    let link = document.createElement('a')
                    link.style.display = 'none'
                    link.href = url
                    link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')+'.xlsx')
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link) //下载完成移除元素
                    window.URL.revokeObjectURL(url) //释放掉blob对象
                }
            })
        },
        getData () {
            const params = {
                headId: this.sourceId
            }
            const apiQueryMaterialList = getAction('/enquiry/purchaseEnquiryHead/queryMaterialList', params)
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: 'success', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryMaterialList
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                const [infoRes] = res || []
                if (infoRes && infoRes.status === 'success') {
                    this.fixInfoData(infoRes.res)
                }
            })
        },
        fixInfoData ({ result }) {
            const { purchaseEnquiryItemList = [], ...others } = result || {}
            this.$refs.reportModal.open(this.sourceId, purchaseEnquiryItemList)
        }
    }
}
</script>

<style lang="scss" scoped>
.page-container-default{
  :deep(.row-class-default) {
    font-weight: bold;
  }

  :deep(.row-class-red) {
    font-weight: bold;
    // background-color: #facaca;
  }

  :deep(.row-class-blue) {
    font-weight: bold;
    background-color: #e8fbd3;
  }
} 
</style>
