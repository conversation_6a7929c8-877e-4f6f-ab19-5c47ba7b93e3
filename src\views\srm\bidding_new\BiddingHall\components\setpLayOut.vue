<template>
  <div >
    <div class="business-container">
      <div class="page-container">
        <div :class="isEdit ? 'edit-page':'detail-page'">
          <div class="page-header">
            <a-row>
              <a-col
                class="desc-col"
                :style="getDefaultColor"
                :span="6">
                <span v-if="isEdit">{{ $srmI18n(`${$getLangAccount()}#i18n_title_edit`, '编辑') }}{{ currentPageName }}</span>
                <span v-else>{{ currentPageName }}{{ $srmI18n(`${$getLangAccount()}#i18n_title_details`, '详情') }}</span>
              </a-col>
              <a-col
                v-if="pageHeaderButtons && pageHeaderButtons.length"
                class="btn-col"
                :span="18">
                <taskBtn
                  v-if="taskInfo.taskId"
                  :currentEditRow="currentEditRow"
                  :pageHeaderButtons="pageHeaderButtons"
                  v-on="$listeners"/>
                <business-button
                  v-else
                  :buttons="pageHeaderButtons"
                  :pageConfig="pageConfig"
                  :resultData="resultData"
                  v-on="$listeners">
                </business-button>
              </a-col>
            </a-row>
          </div>
          <div
            class="page-header">
            <a-steps
              :current="currentStep"
              @change="currentStepChange"
              size="small">
              <template
                v-for="group in pageConfig.groups"
              >
                <a-step
                  v-if="group.show"
                  :key="group.groupCode"
                  size="small"
                >
                  <template #title>
                    <a-tooltip :title="$srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName)">
                      {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }}
                    </a-tooltip>
                  </template>
                </a-step>
              </template>
            </a-steps>
          </div>
          <div
            id="page-content"
            class="page-content"
            :style="{height: pageContentHeight + 'px' }">
            <div
              v-for="(group, index) in pageConfig.groups"
              :key="group.groupCode"
              v-show="currentStep === index">
              <div v-if="group.show">
                <slot
                  :name="group.groupCode"
                  :slotProps="{groups: pageConfig.groups, group, busAccount, currentStep, pageConfig, resultData}"></slot>
              </div>
            </div>
          </div>
          <div
            class="page-footer"
            v-if="isEdit && pageConfig.pageFooterButtons && pageConfig.pageFooterButtons.length">
            <a-button
              :type="customPageFooterPreBtn.type"
              v-if="currentStep"
              @click="customPageFooterPreBtn.click">{{ customPageFooterPreBtn.title }}</a-button>
            <a-button
              :type="customPageFooterNextBtn.type"
              v-if="currentStep<(pageConfig.groups.filter(item=>item.show).length-1)"
              @click="customPageFooterNextBtn.click">{{ customPageFooterNextBtn.title }}</a-button>
            <business-button
              :buttons="pageFooterButtons"
              :pageConfig="pageConfig"
              :resultData="resultData"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { postAction, getAction } from '@/api/manage'
import {DEFAULT_COLOR, USER_ELS_ACCOUNT} from '@/store/mutation-types'
import BusinessButton from '@comp/template/business/components/BusinessButton'
import { mapGetters } from 'vuex'

export default {
    name: 'SetpLayOut',
    props: {
    // 当前编辑行的数据
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {}
        },
        sourceGroups: {
            required: true,
            type: Array,
            default: () => {
                return []
            }
        },
        // 底部按钮
        pageFooterButtons: {
            type: Array
        },
        // 详情头部按钮
        pageHeaderButtons: {
            type: Array
        },
        // 获取默认主题色
        getDefaultColor () {
            return {
                color: this.$ls.get(DEFAULT_COLOR)
            }
        },
        // 页面的状态
        pageStatus: {
            type: String,
            default: 'edit'
        },
        canchangeStep: {
            type: Boolean,
            default: true
        }
    },
    components: {
        BusinessButton
    },
    provide () {
        return {
            tplRootRef: this
        }
    },
    data () {
        return {
            currentStep: 0,
            currentPageName: '',
            //默认表格配置
            pageConfig: {
                groups: [
                    {
                        groupCode: null, // 分组编码,可自定义,要求对应后端的字段ref
                        groupName: null, // 分组显示的名称
                        groupType: null // head头分组-表单, item行分组-表格
                    }
                ],
                pageFooterButtons: [] // 底部按钮
            },
            editFormData: {},
            // 上一步配置
            customPageFooterPreBtn: {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_prevStep`, '上一步'),  type: 'primary', belong: 'preStep', click: this.prevStep },
            // 下一步配置
            customPageFooterNextBtn: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nextStep`, '下一步'), type: 'primary', belong: 'nextStep', click: this.nextStep }
        }
    },
    computed: {
        ...mapGetters([
            'taskInfo'
        ]),
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow) {
                if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
                    account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
                }
            }
            return account
        },
        pageContentHeight () {
            let contentHeight = '600'
            const clientHeight = document.documentElement.clientHeight
            contentHeight = clientHeight - 210
            return contentHeight
        },
        // 判断页面是编辑态还是详情态
        isEdit () {
            return this.pageStatus === 'edit'
        }
    },
    methods: {
        prevStep () {
            this.currentStep > 0 && this.currentStep--
            this.preOrNextStepHandle('pre')
        },
        nextStep () {
            if(this.currentStep < this.pageConfig.groups.filter(item=> item.show).length - 1) {
                this.currentStep++
            }
            this.preOrNextStepHandle('next')
        },
        // 上一步和下一步的处理逻辑外部使用
        preOrNextStepHandle (type) {
            let formCompnent = null
            let gridCompnent = null
            let groupData = null
            if (this.pageConfig.groups[this.currentStep]) {
                let formRefName = this.pageConfig.groups[this.currentStep].groupCode
                let gridRefName = this.pageConfig.groups[this.currentStep].custom?this.pageConfig.groups[this.currentStep].custom.ref: ''
                formCompnent = formRefName? this.$refs[formRefName] : ''
                gridCompnent = gridRefName? this.$refs[gridRefName][0] : ''
                groupData = this.pageConfig.groups[this.currentStep]
            }
            let emitData ={ formCompnent: formCompnent, gridCompnent: gridCompnent, pageConfig: this.pageConfig, groupData: groupData, form: this.form}
            if (type === 'next') {
                this.$emit('nextStepHandle', emitData)
            } else {
                this.$emit('preStepHandle', emitData)
            }
        },
        currentStepChange (step) {
            if (!this.canchangeStep) return
            this.currentStep= step
            this.$emit('handleStepChange', this.pageConfig.groups[step])
        }
    },
    mounted () {
    // 当前页面的名称
        this.currentPageName = this.$route.meta? this.$route.meta.title: ''
    },
    created () {
        // 分组模板
        this.$set(this.pageConfig, 'groups', this.sourceGroups)
        // 底部按钮组
        if (this.pageFooterButtons && this.pageFooterButtons.length) {
            this.$set(this.pageConfig, 'pageFooterButtons', this.pageFooterButtons)
        }
        this.resultData = this.fromSourceData
    }
}
</script>
