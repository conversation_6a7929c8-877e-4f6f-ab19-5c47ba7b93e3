<!--
 * @Author: fzb
 * @Date: 2021-05-31 14:15:16
 * @LastEditTime: 2021-08-02 19:34:43
 * @FilePath: \srm-frontend_v4.5\src\views\srm\enquiry\purchase\modules\PurchaseEditCost.vue
-->
<template>
  <div>
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="containerRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :url="url"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>
<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {getAction, postAction} from '@/api/manage'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {ajaxFindDictItems} from '@/api/api'
import moment from 'moment'

export default {
    name: 'SaleEightDisciplinesHeadDetail',
    components: {
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            requestData: {
                export: { url: '/base/excelByConfig/downloadTemplate', args: () => {} },
                import: { url: '/base/excelByConfig/importExcel', args: () => {}  },
                detail: { url: '/eightReport/saleEightDisciplines/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                purchaseEightDisciplinesTeamList: {add: true, delete: true}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/eightReport/saleEightDisciplines/edit',
                publish: '/eightReport/saleEightDisciplines/publish',
                reject: '/eightReport/saleEightDisciplines/reject',
                download: '/attachment/saleAttachment/download'
            },
            materialNumberOptions: {}
        }
    },
    created () {
        this.queryDictData()
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_eightDisciplines_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleAfterDealSource (pageConfig, resultData){
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
            }
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '10',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { key: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent }
                            ]
                        }
                    }
                ],
                itemColumns: [
                    {
                        groupCode: 'saleAttachmentList',
                        title: '关联Tab',
                        fieldLabelI18nKey: 'i18n_field_RKWWW_b61bceb4',
                        field: 'materialNumber',
                        align: 'center',
                        headerAlign: 'center',
                        // fieldType: 'select',
                        defaultValue: '',
                        // dictCode: 'SRMEightAttachmentRelationTab',
                        width: '150',
                        disabled: true,
                        slots: { default: ({row}) => {
                            return <span>{this.materialNumberOptions[row.materialNumber]}</span>
                        } }
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: {
                            default: ({row}) => {
                                return [
                                    <span>{moment(row.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</span>
                                ]
                            }
                        }
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150'
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        align: 'center',
                        headerAlign: 'center',
                        defaultValue: '',
                        width: '150',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const id = row.id
            const fileName = row.fileName
            const params = {
                id
            }
            getAction('/attachment/saleAttachment/download', params, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        queryDictData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'SRMEightAttachmentRelationTab'
            }
            ajaxFindDictItems(postData).then(res => {
                if(res.success) {
                    res.result.forEach(element => {
                        this.materialNumberOptions[element.value] = element.text
                    })
                }
            })
        }
    }
}
</script>