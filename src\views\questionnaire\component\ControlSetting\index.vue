<template>
  <div>
    <a-tabs
      defaultActiveKey="1"
      @change="callback">
      <a-tab-pane
        :tab="$srmI18n(`${$getLangAccount()}#i18n_title_topicConfiguration`, '题目配置')"
        key="1">
        <control-attr 
          :attachmentInfo="attachmentInfo"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import ControlAttr from './ControlAttr'
export default {
    name: 'ControlSetting',
    components: {
        ControlAttr
    },
    props: {
        attachmentInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    methods: {
        callback (key) {
            console.log(key)
        }
    }
}
</script>

<style lang="less" scoped>

</style>
