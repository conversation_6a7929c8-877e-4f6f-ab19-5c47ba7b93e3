<template>
  <div>
    <a-modal
      v-drag
      :visible="LeaderOpinionModalVisible"
      @cancel="handleCancel"
      centered>
      <template slot="title">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_reviewConclusion`, '评审结论') }}</span>
        <span class="desc">
          ({{ typeNum == 'leader' ? $srmI18n(`${$getLangAccount()}#i18n_field_UUSy_40ef29d0`, '评审环节') : $srmI18n(`${$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例') }}：{{ title }})
        </span>
      </template>
      <template slot="footer">
        <a-button
          key="submit"
          type="primary"
          :loading="confirmLoading"
          v-if="pageStatus=='edit' && leaderStatus && typeNum=='leader'"
          @click="handleOk">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          v-if="pageStatus=='edit' && typeNum !=='leader'"
          @click="handleOk">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_confirm`, '确认') }}
        </a-button>
        <a-button
          key="back"
          @click="handleCancel">
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
        </a-button>
      </template>
      <div>
        <a-textarea
          :disabled="pageStatus=='detail' || (typeNum == 'leader' && !leaderStatus)"
          v-model="formData[opinionCode]"
          :rows="4" />
      </div>
    </a-modal>
  </div>
</template>

<script>
import { postAction } from '@views/srm/bidding_new/plugins/manage'

export default {
    name: 'LeaderOpinionModal',
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        typeNum: {
            type: String,
            default: 'noLeader'
        }
    },
    data () {
        return {
            LeaderOpinionModalVisible: false,
            confirmLoading: false,
            formData: {},
            title: '',
            leaderStatus: false,
            opinionCode: 'opinion'
        }
    },
    methods: {
        handleOk (){
            if (this.typeNum == 'leader') {
                this.confirmLoading = true
                // 先执行一次保存，拿到行id再拿id去生成
                postAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/saveLeaderOpinion', this.formData).then(res=>{
                    if(res.success){
                        this.LeaderOpinionModalVisible = false
                    }else {
                        this.$message.error(res.message)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            } else {
                this.$emit('success', this.formData)
                this.LeaderOpinionModalVisible = false
            }
        },
        handleCancel (){
            this.LeaderOpinionModalVisible = false
        },
        open ({data = {}, title, leaderStatus = false, opinionCode = 'opinion'}) {
            this.title = title
            this.leaderStatus = leaderStatus
            this.formData = Object.assign({}, data)
            this.opinionCode = opinionCode
            this.LeaderOpinionModalVisible = true
        }
    }
}
</script>
<style lang="less" scoped>
.desc{
    font-size: 13px;
    margin-left: 10px;
    color: rgba(218, 166, 11, 0.795);
}
</style>
