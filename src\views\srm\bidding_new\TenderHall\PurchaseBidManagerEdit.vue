<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="layoutShow && !detailShow"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :fromSourceData="fromSourceData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>

      <PurchaseBidManagerDetail
        v-if="detailShow"
        :currentEditRow="currentEditRow"
      ></PurchaseBidManagerDetail>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../plugins/baseMixins.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import PurchaseBidManagerDetail from './PurchaseBidManagerDetail.vue'
import { add } from '@/utils/mathFloat.js'

export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        EditFormLayout,
        PurchaseBidManagerDetail
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage', 'resetCurrentSubPackage'],
    mixins: [businessUtilMixin, baseMixins],
    computed: {
        subpackage (){
            return this.currentSubPackage()
        },
        externalToolBar () {
            return {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectPurchaseBid', // 必传,
                            itemNumberKey: 'materialNumber',
                            disabledItemNumber: true,
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    data () {
        return {
            templateMsg: {},
            businessRefName: 'businessRef',
            pageStatus: 'edit',
            confirmLoading: false,
            // externalToolBar: {},
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/edit'
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save2',
                    handleBefore: this.handleSubmitBefore,
                    click: this.handleSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish2',
                    // showMessage: true,
                    handleBefore: this.handleSubmitBefore,
                    click: this.handleSubmit
                }
            ],
            url: {
                queryById: '/tender/sale/supplierTenderProjectPurchaseBid/queryById',
                queryProjectInfo: '/tender/sale/supplierTenderProjectPurchaseBid/queryPurchaseBidInfo',
                add: '/tender/sale/supplierTenderProjectPurchaseBid/add',
                edit: '/tender/sale/supplierTenderProjectPurchaseBid/edit',
                submit: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
            },
            projectObj: {},
            fromSourceData: {},
            remoteJsFilePath: '',
            layoutShow: false,
            detailShow: false,
            // currentEditRow: {},
            queryData: {}

        }
    },
    created () {
        this.queryData = this.$ls.get('SET_TENDERCURRENTROW') || {}
    },
    async mounted () {
        // this.currentEditRow = await this.getBusinessTemplate('SupplierTenderProjectPurchaseBid')
        // this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/sale_SupplierTenderProjectPurchaseBid_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
        await this.getData()
    },
    methods: {
        attrHandle (){
            return {
                sourceNumber: this.fromSourceData.tenderProjectNumber|| this.fromSourceData.id || '',
                actionRoutePath: '采购商与供应商的路径逗号隔开,/bidder/BiddingManagerList'
            }
        },
        // 获取业务模板信息
        // getBusinessTemplate () {
        //     let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectPurchaseBid'}
        //     this.confirmLoading = true
        //     getAction('/template/templateHead/getListByType', params).then(res => {
        //         if(res.success) {
        //             if(res.result.length > 0) {
        //                 let options = res.result.map(item => {
        //                     return {
        //                         templateNumber: item.templateNumber,
        //                         templateName: item.templateName,
        //                         templateVersion: item.templateVersion,
        //                         templateAccount: item.elsAccount
        //                     }
        //                 })
        //                 this.currentEditRow = options[0]
        //                 this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/sale_SupplierTenderProjectPurchaseBid_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
        //                 this.getData()
        //             } else {
        //                 this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
        //             }
        //         }else {
        //             this.$message.warning(res.message)
        //         }
        //         this.confirmLoading = false
        //     })
        // },
        handleSubmitBefore (args) {
            console.log('args', args)
            return new Promise((resolve) => {
                let allData = args.allData
                allData['saleTenderInvoiceInfoList'] = [Object.assign(allData['invoiceInfo'], allData['payType'])]
                allData['noticeId'] = this.queryData && this.queryData.noticeId
                allData = Object.assign(allData, this.currentEditRow)
                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')

                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log('this.subpackage', this.subpackage)
            // 预审取preConsortiumBidding
            let consortiumBidding
            if(this.subpackage.checkType == '0'){
                consortiumBidding = this.subpackage.preConsortiumBidding
            }else if(this.subpackage.checkType == '1'){
                // 后审取consortiumBidding
                consortiumBidding = this.subpackage.consortiumBidding
            }
            // 非允许联合体情况
            if(consortiumBidding != 1 ){
                pageConfig.groups[0].formModel.combination = '0'
                pageConfig.groups[0].formModel.combination_dictText = '否'
            }
            console.log(pageConfig)
            // 不允许联合体，则是否联合体为否
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            // 不允许联合体情况
            let flag = (consortiumBidding != 1)
            setDisabledByProp('combination', flag)
            setDisabledByProp('combinationName', flag)

            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            // 不允许联合体情况
            let validateFlag = (consortiumBidding != 1)
            setValidateRuleByProp('combination', validateFlag)
            setValidateRuleByProp('combinationName', validateFlag)

            // 编辑赋值
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            const tenderProjectPurchaseBidOrderVOList = resultData['tenderProjectPurchaseBidOrderVOList']
            // 累加所有文件的标价总和
            let payMoney = 0
            if (tenderProjectPurchaseBidOrderVOList && tenderProjectPurchaseBidOrderVOList.length > 0) {
                tenderProjectPurchaseBidOrderVOList.forEach(item => {
                    const saleAmount = item.saleAmount ? parseFloat(item.saleAmount) : 0
                    payMoney = add(payMoney, saleAmount)
                })
            }
            if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        let {invoice, 
                            invoiceType,
                            enterpriseName, 
                            dutyParagraph, 
                            enterpriseAddress, 
                            enterprisePhone,
                            depositBank,
                            bankNumber } = saleTenderInvoiceInfoList[0]
                        group['formModel'] = {
                            invoice: invoice, 
                            invoiceType: invoiceType,
                            enterpriseName: enterpriseName, 
                            dutyParagraph: dutyParagraph, 
                            enterpriseAddress: enterpriseAddress, 
                            enterprisePhone: enterprisePhone,
                            depositBank: depositBank,
                            bankNumber: bankNumber }
                    }
                    if (group.groupCode == 'payType') {
                        let {remark, payType, payType_dictText} = saleTenderInvoiceInfoList[0]
                        group['formModel'] = {payMoney, remark, payType, payType_dictText}
                    }
                })
            } else {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'payType') {
                        group['formModel']['payMoney'] = payMoney
                    }
                })
            }

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        },
        handleSave () {
            let {nodeId, extend: {checkType, processType}} = this.currentNode()
            // let params = Object.assign(this.currentEditRow, this.$refs[this.businessRefName].extendAllData().allData)
            let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, this.currentEditRow)
            params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
            params['noticeId'] = params['noticeId'] || this.queryData.noticeId || ''
            params['checkType'] = checkType
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            
            params = {
                ...params,
                ...this.templateMsg
            }
            postAction(url, params, {headers: {xNodeId: nodeId}}).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.currentEditRow.id = res.result.id
                    this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmit () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then(res => {
                let {nodeId, extend: {checkType, processType}} = this.currentNode()
                let params = {
                    ...this.$refs[this.businessRefName].extendAllData().allData,
                    ...this.currentEditRow
                }
                params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
                params['noticeId'] = params['noticeId'] || this.queryData.noticeId || ''
                params['checkType'] = checkType
                this.confirmLoading = true
                
                params = {
                    ...params,
                    ...this.templateMsg
                }
                postAction(this.url.submit, params, {headers: {xNodeId: nodeId}}).then(res => {
                    let type = res.success ? 'success': 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.currentEditRow['checkType'] = checkType
                        // this.$emit('resetCurrentSubPackage') || ''
                        this.resetCurrentSubPackage()
                        this.getData()
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            })         
        },
        async getData () {
            let url = ''
            let params = {}
            let cb = null
            let {nodeId, extend: {checkType, processType}} = this.currentNode()

            if (this.currentEditRow.id) {
                url = this.url.queryById
                params = {id: this.currentEditRow.id}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    let {status} = data
                    if (status == '1' || status == '2') {
                        this.currentEditRow = data
                        this.layoutShow = false
                        this.detailShow = true
                    } else {
                        this.layoutShow = true
                    }
                }
            } else {
                url = this.url.queryProjectInfo
                params = {subpackageId: this.queryData.subpackageId}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    let {status} = data
                    if (status == '1' || status == '2') {
                        this.currentEditRow = data
                        this.layoutShow = false
                        this.detailShow = true
                    } else {
                        this.layoutShow = true
                    }
                    let {realname, elsAccount} = this.$ls.get(USER_INFO)
                    this.fromSourceData['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                    this.fromSourceData['supplierAccount'] = elsAccount
                }
            }
            await getAction(url, params, {headers: {xNodeId: nodeId}}).then(async (res) => {
                if (res.success) {
                    this.templateMsg = res.result.templateNumber && res.result.templateAccount ? {
                        templateNumber: res.result.templateNumber,
                        templateName: res.result.templateName,
                        templateVersion: res.result.templateVersion,
                        templateAccount: res.result.templateAccount
                    } : await this.getBusinessTemplate('SupplierTenderProjectPurchaseBid').then(rs =>{
                        return rs
                    })
                    if (!this.templateMsg) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                        return
                    }
                    // this.getBusinessTemplate('SupplierTenderProjectPurchaseBid').then((result)=>{

                    //     console.log('result', result)

                    // })
                    console.log('this.templateMsg', this.templateMsg)
                    this.remoteJsFilePath = `${this.templateMsg['templateAccount']}/sale_SupplierTenderProjectPurchaseBid_${this.templateMsg['templateNumber']}_${this.templateMsg['templateVersion']}`
                    if (res.result) {
                        cb(res.result)
                    }
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>
