<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :pageFooterButtons="pageFooterButtons"
        :pageHeaderButtons="pageHeaderButtons"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        @nextStepHandle="nextStepHandle"
        @preStepHandle="preStepHandle"
        :fromSourceData="fromSourceData">
        <template #baseForm="{ slotProps }">
          <flieOfCa
            ref="flieOfCa"
            :fromSourceData="fromSourceData"
            :pageStatus="pageStatus"></flieOfCa>
        </template>
        <template #tenderBidLetterVoList="{ slotProps }">
          <tenderBidLetterVoList
            ref="tenderBidLetterVoList"
            :subpackage="subpackage"
            :fromSourceData="fromSourceData"
            :pageStatus="pageStatus"></tenderBidLetterVoList>
        </template>
      </setp-lay-out>
    </a-spin>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow" />
    <a-modal
      v-drag    
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_VWNCAVQ_8751a228`, '请输入CA口令')"
      :visible="showDecodeFileKey"
      :width="300"
      :confirmLoading="confirmLoading"
      @ok="checkPin"
      @cancel="() => {this.showDecodeFileKey = false}">
      <a-input v-model="decodeFileKey"></a-input>
    </a-modal>
    <a-modal
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_QIuw_2f599fb5`, '文件加密')"
      :visible="visiblePwd"
      :confirm-loading="confirmPwd"
      @ok="handleOk"
      @cancel="() => {this.visiblePwd = false}"
    >
      <a-form-model
        ref="ruleForm"
        :model="formPwd"
        :rules="rules"
        :label-col="{span: 6}"
        :wrapper-col="{span: 14}">
        <a-form-model-item 
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_uwwo_26f340a1`, '加密密码')" 
          prop="filePassword">
          <a-input-password 
            v-model="formPwd.filePassword" 
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNuwwo_ae1b028a`, '请输入加密密码')" />
        </a-form-model-item>
        <a-form-model-item 
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_RLuwwo_57fcf697`, '确认加密密码')" 
          prop="filePasswordConfirm">
          <a-input-password 
            v-model="formPwd.filePasswordConfirm" 
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_field_VWNRLwo_bffe125a`, '请输入确认密码')" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { postAction as ukAction } from './api/request'

import setpLayOut from '@views/srm/bidding_new/BiddingHall/components/setpLayOut'
import flowViewModal from '@comp/flowView/flowView'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import flieOfCa from './components/flieOfCa'
import tenderBidLetterVoList from './components/tenderBidLetterVoList'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'

export default {
    name: 'BiddingFile',
    components: {
        fieldSelectModal,
        setpLayOut,
        flowViewModal,
        flieOfCa,
        tenderBidLetterVoList
    },
    mixins: [baseMixins],
    computed: {
        subpackage (){
            return this.currentSubPackage()
        },
        subId () {
            return this.subpackageId()
        },
        pageStatus () {
            let { resultResponseStatus, preResponseStatus } = this.fromSourceData
            let status = 'detail'
            if ((resultResponseStatus != '1' && this.checkType == '1') || (preResponseStatus != '1' && this.checkType == '0')) {
                // 0 递交文件可以操作
                status = 'edit'
            }
            return status
        },
        pageFooterButtons () {
            let btn = [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        // 这里补充新增的保存接口url
                        url: ''
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleSave
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: ''
                    },
                    attrs: {
                        type: 'primary'
                    },
                    click: this.handleSubmit
                }
            ]
            return btn
        },
        pageHeaderButtons () {
            let btn = this.pageStatus == 'edit' ? [] : [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_qXDJ_2ffb62b0`, '撤销'),
                    attrs: {
                        type: 'primary'
                    },
                    key: 'revokeSave',
                    click: this.revokeSaveBefore
                }
            ]
            return btn
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            refresh: true,
            show: false,
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nJQI_42b16ea1`, '递交文件' ),
                    groupNameI18nKey: '',
                    groupCode: 'baseForm',
                    groupType: 'head',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMeBx_48ec2dd`, '填写投标函' ),
                    groupNameI18nKey: '',
                    groupCode: 'tenderBidLetterVoList',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                }
            ],
            businessRefName: 'businessRef',
            currentGroupCode: {
                groupCode: 'tenderBidLetterFormatGroupVo'
            },
            currentEditRow: {},
            status: '',
            confirmLoading: false,
            fromSourceData: {},
            userInfo: {},
            urlParams: {},
            showDecodeFileKey: false,
            decodeFileKey: false,
            uKeyInfo: null,
            currentRow: null,
            priceOpeningsList: null,
            resResult: null,
            formPwd: {
                filePassword: '',
                filePasswordConfirm: ''
            },
            rules: {
                filePassword: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNuwwo_ae1b028a`, '请输入加密密码' ), trigger: 'blur' }
                ],
                filePasswordConfirm: [
                    { required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNRLwo_bffe125a`, '请输入确认密码'), trigger: 'blur' }
                ]
            },
            confirmPwd: false,
            visiblePwd: false, // 文件密码弹窗状态
            url: {
                detail: '/tender/sale/supplierTenderDocumentSubmitInfo/queryDocumentSubmitInfo',
                save: '/tender/sale/supplierTenderDocumentSubmitInfo/addAll',
                publish: '/tender/sale/supplierTenderDocumentSubmitInfo/publish'
            }
        }
    },
    methods: {
        encodeFile () { // 加密
            this.$refs.tenderBidLetterVoList.$refs.auctionLetter.encodeFile()
        },
        toDecodeFile () { // 解密
            this.$refs.tenderBidLetterVoList.$refs.auctionLetter.toDecodeFile()
        },
        queryDetail (refreshChildren = false) {
            this.confirmLoading = true
            this.show = false
            let { subpackageId } = this.urlParams
            let params = {
                subpackageId: subpackageId,
                checkType: this.checkType
            }
            getAction(this.url.detail, params)
                .then((res) => {
                    if (res.success) {
                        this.resResult = res.result
                        this.fromSourceData = res.result
                        console.log(this.fromSourceData)
                        this.fromSourceData.tenderBidLetterVoList && this.fromSourceData.tenderBidLetterVoList.map(row => {
                            // 已加密数据不需要转换
                            if (row.priceOpeningsList[0].encryption !== '1') {
                                row.priceOpeningsList[0].customizeFieldData = row.priceOpeningsList[0].customizeFieldData ? JSON.parse(row.priceOpeningsList[0].customizeFieldData) : []
                                // 初始默认赋值投标单位名称
                                row.priceOpeningsList[0].customizeFieldData.map(item => {
                                    if (!item['supplierName']) {
                                        item['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                                        item['supplierAccount'] = this.$ls.get(USER_INFO).elsAccount
                                    }
                                })
                                row.priceOpeningsList[0].customizeFieldModel = row.priceOpeningsList[0].customizeFieldModel ? JSON.parse(row.priceOpeningsList[0].customizeFieldModel) : []
                            }
                        })
                        let { resultResponseStatus, preResponseStatus } = res.result
                        if ((resultResponseStatus != '1' && this.checkType == '1') || (preResponseStatus != '1' && this.checkType == '0')) {
                            // 0 递交文件可以操作
                            let { elsAccount } = this.$ls.get(USER_INFO)
                            this.fromSourceData['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                            this.fromSourceData['supplierAccount'] = elsAccount
                        }
                        this.show = true
                    } else {
                        this.$message.error(res.message)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                })
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        },
        // 获取数据和校验
        async getParamsName (save) {
            if(!save){
                let validate = await this.$refs.flieOfCa.getValidatePromise()
                if (validate) {
                    this.setCurrentStep(0)
                    return false
                }
            }
            
            let p = JSON.parse(JSON.stringify(this.fromSourceData))
            let {tenderBidLetterVoList = [], ...other} = p
            let saleTenderPriceOpeningsList = []
            let saleQuoteMaterialDataList = []
            // 编辑时数据处理
            if (this.pageStatus == 'edit') {
                let falg = false
                for (let i = 0; i < tenderBidLetterVoList.length; i++) {
                    // 需要CA加密
                    if (this.subpackage.useCa == '1') {
                        if (!save && tenderBidLetterVoList[i].priceOpeningsList[0].encryption !== '1') {
                            this.$message.warning(`投标函：${tenderBidLetterVoList[i].name}未加密`)
                            this.setCurrentStep(1)
                            return false
                        }
                        // 加密后清除物料数据
                        tenderBidLetterVoList[i].saleQuoteMaterialDataList = []
                    } else {
                        // 投标函其他类型不需要校验
                        if (tenderBidLetterVoList[i].formatType != '9') {
                            // 投标列输入quoteColumnSource 0 手动输入不需要校验物料行，但需要校验投标函报价
                            if (tenderBidLetterVoList[i].quoteColumnSource !== '0') {
                            // 物料报价列遍历
                                for (let item of tenderBidLetterVoList[i].saleQuoteMaterialDataList) {
                                    // 物料行遍历
                                    for (let material of item.materialDataList) {
                                        if (!save && !material.price) {
                                            falg = true
                                            this.$message.warning(`投标函[${tenderBidLetterVoList[i].name}]报价列[${item.title}]物料名称[${material.materialName}]未填写报价`)
                                            this.setCurrentStep(1)
                                            break
                                        }
                                    }
                                    if (falg) break
                                }
                            } else {
                                // quoteColumnSource 0 手动输入，需要校验投标函的报价列的数据
                                tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel.some(item => {
                                    if (!save && (item.must || item.must == '1') && !tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData[0][item.field]) {
                                        this.$message.warning(`投标函[${tenderBidLetterVoList[i].name}]报价列[${item.title}]未填写报价`)
                                        this.setCurrentStep(1)
                                        falg = true
                                        return true
                                    }
                                })
                            }
                            if (!save && falg) return false
                        }
                        tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData = JSON.stringify(tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldData)
                    }
                    // 数据格式处理
                    tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel.forEach(item=>{
                        item.must = item.must ? '1' : '0'
                    })
                    tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel = JSON.stringify(tenderBidLetterVoList[i].priceOpeningsList[0].customizeFieldModel)
                }
            }
            saleTenderPriceOpeningsList = tenderBidLetterVoList.map(item => {
                saleQuoteMaterialDataList.push(...item.saleQuoteMaterialDataList)
                return item.priceOpeningsList[0]
            })
            let EncryptDocumentsData = this.$refs.flieOfCa.getEncryptDocumentsData()
            let params = {
                ...other,
                // tenderBidLetterVoList,
                saleAttachmentDTOList: EncryptDocumentsData,
                saleTenderPriceOpeningsList,
                saleQuoteMaterialDataList
            }
            return params
        },
        handleOk () {
            this.$refs.ruleForm.validate(valid => {
                if (valid) {
                    if (this.formPwd.filePassword != this.formPwd.filePasswordConfirm) {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNjwosuIR_ed630e2e`, '请输入的密码保持一致'))
                        return
                    }
                }
                this.confirmPwd = true
                this.handlePublish()
            })
        },
        // 保存
        async handleSave () {
            let params = await this.getParamsName('save') 
            
            console.log(this.$refs.tenderBidLetterVoList.$refs.Information.$refs.materialList.$refs.listTable)
            console.log('params', params)
            this.confirmLoading = true
            postAction(this.url.save, params)
                .then((res) => {
                    let type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        // this.$emit('resetCurrentSubPackage')
                        this.resetCurrentSubPackage()
                        this.visiblePwd = false
                        this.showDecodeFileKey = false
                        // this.queryDetail(true)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.confirmPwd = false
                })
        },
        // 提交 
        async handleSubmit () {
            let params = await this.getParamsName()
            if (!params) return
            console.log(this, 'this=')
            this.$refs.tenderBidLetterVoList.$refs.Information.$refs.materialList.$refs.listTable.getValidate().then(res=>{
                this.$confirm({
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLDJ_392810a1`, '是否确认提交'),
                    onOk: () => {
                        // 是否需要加密
                        if (this.subpackage.openBidEncrypt == '1') {
                            // ca加密
                            if (this.subpackage.useCa == '1') {
                                this.showDecodeFileKey = true
                                this.decodeFileKey = ''
                            } else {
                                // 正常加密
                                this.visiblePwd = true
                            }
                        } else {
                            this.handlePublish()
                        }
                    },
                    onCancel () {
                    }
                })
            }).catch(error=>{
                console.log('err', error)
                this.$refs.tenderBidLetterVoList.$refs.Information.isTetterListTable = false
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VPSLcsdjlSdSMMi_d83b6e21`, '请将物料行中所有必填项填写完整！'))
            })
        },
        getUkeyInfo () { // 获取Uk
            return ukAction('/uk/action', {'type': 100}).then(res => {
                this.uKeyInfo = JSON.parse(res.data)[0]
            })
        },
        // 校验PIN码
        async checkPin () {
            if (!this.uKeyInfo) {
                await this.getUkeyInfo()
            }
            let p = {
                'type': 101,
                'pin': this.decodeFileKey,
                'asymid': 1,
                'keySN': this.uKeyInfo.keySN
            }
            ukAction('/uk/action', p).then(res => {
                if (res.code == '0') {
                    this.handlePublish()
                }
            })
        },
        async handlePublish () {
            let params = await this.getParamsName()
            if (this.subpackage.openBidEncrypt == '1') {
                params['filePassword'] = this.formPwd.filePassword
            }
            this.confirmLoading = true
            postAction(this.url.publish, params)
                .then((res) => {
                    let type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        // this.$emit('resetCurrentSubPackage')
                        this.resetCurrentSubPackage()
                        this.visiblePwd = false
                        this.showDecodeFileKey = false
                        this.queryDetail(true)
                    }
                })
                .finally(() => {
                    this.confirmLoading = false
                    this.confirmPwd = false
                })
        },
        fieldSelectOk (data) {
            let itemGrid = this.getItemGridRef(this.currentGroupCode)
            itemGrid.insertAt([...data], -1)
        },
        // 校验
        checkAllValidate (data) {

        },
        async nextStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        preStepHandle (data) {
            this.currentGroupCode = data.groupData
        },
        childrenInitData (data) {
            this.sourceGroups.map((ref) => {
                this.$refs[ref.groupCode].init(data)
            })
        },
        revokeSaveBefore (args) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_qMnJ_2f17810c`, '撤回递交'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQqMnJVH_f71bb151`, '是否撤回递交信息'),
                onOk: async () => {
                    let params = this.resResult
                    postAction('/tender/sale/supplierTenderDocumentSubmitInfo/revokeSave', params).then((res) => {
                        if (res.code == 200) {
                            // this.$emit('resetCurrentSubPackage')
                            this.resetCurrentSubPackage()
                            this.queryDetail(true)
                            this.$message.success(res.message)
                        } else {
                            this.$message.error(res.message)
                        }
                    })
                }
            })
        }
    },
    created () {
        this.urlParams = this.$ls.get('SET_TENDERCURRENTROW') || {}
        this.queryDetail()
    }
}
</script>
