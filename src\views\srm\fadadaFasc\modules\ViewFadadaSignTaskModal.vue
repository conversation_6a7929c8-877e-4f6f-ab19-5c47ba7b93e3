<template>
  <div class="FadadaSignTask business-container">
    <business-layout
        :ref="businessRefName"
        pageStatus="edit"
        modelLayout="masterSlave"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        v-on="businessHandler" />

    <field-select-modal
        isEmit
        ref="fieldSelectModal"
        @ok="fieldSelectOk" />

    <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow" />
  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import flowViewModal from '@comp/flowView/flowView'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction } from '@/api/manage'
import { BUTTON_BACK } from '@/utils/constant.js'

export default {
    name: 'editFadadaSignTaskModal',
    mixins: [businessUtilMixin],
    components: {
      flowViewModal,
      BusinessLayout,
      fieldSelectModal
    },
    props: {
      currentEditRow: {
        required: true,
        type: Object,
        default () {
          return {}
        }
      }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
              detail: { url: '/electronsign.fadada/fadadaSignTask/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
              {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                attrs: {
                  type: 'primary'
                },
                click: this.showFlow,
                show: this.showFlowBtn
              },
              {
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                args: {
                  url: '/a1bpmn/audit/api/cancel'
                },
                attrs: {
                  type: 'primary'
                },
                click: this.cancelAudit,
                show: this.showCncelConditionBtn
              },
              BUTTON_BACK
            ],
            url: {
                save: "/electronsign.fadada/fadadaSignTask/edit",
                audit: '/a1bpmn/audit/api/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                detail: '/contract/purchaseContractHead/queryById'
            }
        }
    },
    computed: {
      remoteJsFilePath () {
        let templateNumber = this.currentEditRow.templateNumber
        let templateVersion = this.currentEditRow.templateVersion
        let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
        return `${account}/purchase_electronsign.fadada_${templateNumber}_${templateVersion}`
      }
    },
    methods: {
        showCncelConditionBtn ({ pageData }) {
            if (pageData.auditStatus == '1') {
              return true
            } else {
              return false
            }
        },
        showFlowBtn ({ pageData }) {
            if (pageData.publishAudit == '1') {
              return true
            } else {
              return false
            }
        },
        showFlow () {
            let params = this.getAllData() || {}
            this.flowId = params.flowId
            if (!this.flowId) {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_processCannotViewed`, '当前不能查看流程！'))
              return
            }
            this.flowView = true
        },
        cancelAudit () {
            let that = this
            this.$confirm({
              title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
              content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
              onOk: function () {
                that.auditPostData(that.url.cancelAudit)
              }
            }) /*.finally(() => {
                    that.init()
                })*/
        },
        auditPostData (invokeUrl) {
            this.confirmLoading = true
            let formData = this.getAllData() || {}
            let formatData = {
              businessId: formData.id || '',
              rootProcessInstanceId: formData.flowId,
              businessType: 'electronsign.fadada',
              auditSubject: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YBthxUzAy_eee84974`, '招标单发布审批编号')}：${formData.projectNumber || ''}`,
              params: JSON.stringify(formData)
            }
            postAction(invokeUrl, formatData, 'post')
                .then((res) => {
                  if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.cancelAuditCallBack(formData)
                  } else {
                    this.$message.warning(res.message)
                  }
                })
                .finally(() => {
                  this.confirmLoading = false
                })
        },
    }
}
</script>
