<template>
  <div style="height: 100%">
    <a-spin :spinning="confirmLoading">
      <setp-lay-out
        v-if="show"
        :ref="businessRefName"
        :canchangeStep="canchangeStep"
        :currentEditRow="currentEditRow"
        :sourceGroups="sourceGroups"
        :pageStatus="pageStatus"
        :pageHeaderButtons="pageHeaderButtons">
        <template #winningAffirmMaterialList="{ slotProps }">
          <finalQuoteList 
            :pageStatus="'edit'"
            :canfinalQuoteList="pageStatus == 'edit'"
            determineType="sub"
            @back="back"
            :show="showfinalQuoteList"
            :currentEditRow="formData"
            v-if="showfinalQuoteList" />
          <div
            v-else
            :is="awardName"
            :pageStatus="pageStatus"
            ref="awardName"
            :resultData="formData">
          </div>
        </template>
        <template #reason="{ slotProps }">
          <div>
            <a-row class="margin-t-20">
              <a-col :span="3">
                <div class="label">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明:') }}
                </div>
              </a-col>
              <a-col :span="12">
                <a-textarea
                  :disabled="pageStatus == 'detail'"
                  v-model="formData.reason"
                  :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
              </a-col>
            </a-row>
            <a-row class="margin-t-20">
              <a-col :span="3">
                <div class="label">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_BIW_23da548`, '附件:') }}
                </div>
              </a-col>
              <a-col :span="3">
                <a-upload
                  name="file"
                  :multiple="false"
                  :action="uploadUrl"
                  :headers="uploadHeader"
                  :accept="accept"
                  :data="{headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                  :beforeUpload="beforeUpload"
                  @change="handleUploadChange"
                >
                  <a-button 
                    @click="fileUp"
                    v-if="showHeader && pageStatus == 'edit'"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                </a-upload>
              </a-col>
              <a-col 
                :span="20"
                :offset="3">
                <div
                  v-for="fileItem in formData.attachmentList"
                  :key="fileItem.id">
                  <span>{{ fileItem.fileName }}</span>
                  <a-button 
                    @click="preViewEvent(fileItem)"
                    type="link">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
                  <a-button 
                    v-if="showHeader && pageStatus == 'edit'"
                    @click="handleDeleteFile(fileItem)"
                    type="link" 
                    style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
                  <a-button 
                    v-if="pageStatus == 'detail'"
                    @click="downloadEvent(fileItem)"
                    type="link" 
                    style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
                </div>
              </a-col>
            </a-row>
          </div>
        </template>
      </setp-lay-out>
    </a-spin>
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"/>
  </div>
</template>
  
<script>
import setpLayOut from '@views/srm/bidding_new/BiddingHall/components/setpLayOut'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import flowViewModal from '@comp/flowView/flowView'
import normalAward from './modules/normalAward'
import materialAward from './modules/materialAward'
import supplierAward from './modules/supplierAward'
import finalQuoteList from './finalQuoteList'
import { add } from '@/utils/mathFloat.js'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'BiddingFile',
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage'
    ],
    components: {
        finalQuoteList,
        flowViewModal,
        setpLayOut,
        normalAward,
        supplierAward,
        materialAward,
        titleTrtl
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    if (!this.canEdit) return 'detail'
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        pageHeaderButtons () {
            let btn = []
            if (this.showfinalQuoteList) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.back }
                ] 
            } else {
                if ((this.formData.status == '0' || !this.formData.status) && this.pageStatus == 'edit') {
                    btn = this.awardNameBtn[this.awardName].map(item => {
                        return item
                    })
                    //评标方式 0-全部、1-线上、2-线下*/
                    let { evaluationType} = this.subPackageRow
                    if (evaluationType == '1') {
                        btn.splice(2, 0,
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAessu_119c780`, '发起最终报价'), attrs: {type: 'primary'}, click: this.finalQuote }
                        )
                    }
                }
                if (this.pageStatus == 'detail') {
                    btn = this.awardNameBtnView[this.awardName].map(item => {
                        return item
                    })
                }
                //点击发布后已发布状态status == 1以及审批中状态aduitStatus == 1，
                if(this.formData.status == '1' && this.formData.auditStatus == '1'){
                    btn = this.awardNameBtnView[this.awardName].map(item => {
                        return item
                    })
                    btn.splice(btn.length-1, 0,
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'), type: 'primary', click: this.withdraw }
                    )
                }
                if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
                    btn = this.awardNameBtnView[this.awardName].map(item => {
                        return item
                    })
                    btn.splice(btn.length-1, 0,
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow }
                    )
                }
                if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = [
                    btn = [
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.goBack }
                    ]
                ]
            }
            
            return btn
        }
    },
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        canEdit: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            refresh: true,
            showHeader: true,
            show: false,
            canchangeStep: true,
            flowView: false,
            flowId: false,
            awardNameBtn: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), click: () => {this.changeAwardName('supplierAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), click: () => {this.changeAwardName('materialAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新'), type: 'primary', click: this.reset },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.goBack }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            awardNameBtnView: {
                'normalAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pRdXlB_8d34dee3`, '按供应商授标'), type: 'primary', click: () => {this.changeAwardName('supplierAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_pSLRHlB_e8a46290`, '按物料明细授标'), type: 'primary', click: () => {this.changeAwardName('materialAward')}, attrs: {type: 'primary'} },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.goBack }
                ],
                'materialAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ],
                'supplierAward': [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: () => {this.changeAwardName('normalAward')} }
                ]
            },
            sourceGroups: [
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n__RIsBL_cbe8b614`, '确定中标人'),
                    groupNameI18nKey: '',
                    groupCode: 'winningAffirmMaterialList',
                    groupType: 'item',
                    show: true,
                    sortOrder: '1'
                },
                {
                    groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因'),
                    groupNameI18nKey: '',
                    groupCode: 'reason',
                    groupType: 'head',
                    show: true,
                    sortOrder: '2'
                }
            ],
            businessRefName: 'businessRef',
            awardName: 'normalAward',
            status: '',
            confirmLoading: false,
            showfinalQuoteList: false,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            formData: {},
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById',
                add: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/add',
                edit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/edit',
                submit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/submit',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryInfo: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInitInfoBySubpackage'
            }
        }
    },
    methods: {
        handleDeleteFile (index) {
            this.formData.attachmentList.splice(index, 1)
        },
        preViewEvent (row) {
            row.subpackageId = this.subPackageRow.id
            this.$previewFile.open({ params: row })
        },
        async downloadEvent (fileItem) {
            fileItem.subpackageId = this.subPackageRow.id
            let {message: url} = await getAttachmentUrl(fileItem)
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileItem.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
            })
        },
        goBack () {
            this.$emit('back')
        },
        reset () {
            this.getData()
        },
        fileUp (event) {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                event.stopPropagation() // 只执行button的click，
            }
        },
        showFlow (){
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        withdraw (){
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: () => {
                    this.postAuditData('/a1bpmn/audit/api/cancel', params)
                }
            })
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if(this.formData.id){
                if (file.status === 'done') {
                    if (file.response.success) {
                        console.log('fileList', fileList)
                        // 每次取最后一个值
                        const FileList = fileList[fileList.length - 1]
                        const {result=null} = FileList.response
                        result && (() => {
                            const rt = {
                                uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                                uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                                ...result
                            }
                            this.formData.attachmentList.splice(0, 0, rt)
                        })()
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                }
            }
        },
        changeAwardName (name) {
            this.confirmLoading = true
            this.showfinalQuoteList = false
            // 给个切换loading，假装一下加载
            setTimeout(() => {
                this.awardName = name
                this.confirmLoading = false
            }, 100)
        },
        async getData () {
            let res = null
            let params = {
                subpackageId: this.subId,
                affirmType: '1'
            }
            this.confirmLoading = true
            if (this.currentEditRow.id) {
                // 有ID
                params['id'] = this.currentEditRow.id
                res = await getAction(this.url.queryById, params)
            } else {
                // 无ID
                res = await getAction(this.url.queryInfo, params)
            }
            this.show = true
            this.confirmLoading = false
            if (res.success) {
                this.formData = res.result
            } else {
                this.$message.warning(res.message)
            }
            console.log(res)
        },
        getParams () {
            let params = Object.assign({}, this.formData)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['winningAffirmMaterialList'] = this.$refs.awardName.externalAllData()
            let materialMap = {}
            for (let item of params['winningAffirmMaterialList']) {
                if (item.award == '0') continue
                if (!materialMap[item.materialId]) {
                    materialMap[item.materialId] = item.quotaScale
                } else {
                    materialMap[item.materialId] = add(materialMap[item.materialId], item.quotaScale)
                }
                if (materialMap[item.materialId] > 100) {
                    this.$message.warning(`物料：【${item.materialName}】的拆分比例合计不能超过100%`)
                    return false
                }
            }
            return params
        },
        async save () {
            let params = this.getParams()
            if (!params) return
            // 拼接数据
            let url = params.id ? this.url.edit : this.url.add
            valiStringLength(params, [
                {field: 'reason', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.currentEditRow.id = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        publish () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    that.submit()
                }
            })
        },
        async submit () {
            let params = this.getParams()
            if (!params) return
            valiStringLength(params, [
                {field: 'reason', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(this.url.submit, params).then(res => {
                if (res.success) {
                    this.goBack()
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        back () {
            this.showfinalQuoteList = false
            this.canchangeStep = true
        },
        finalQuote () {
            this.setCurrentStep(0)
            this.canchangeStep = false
            this.showfinalQuoteList = true
        },
        setCurrentStep (i) {
            this.$refs[this.businessRefName].currentStep = i
        }
    },
    created () {
        this.getData()
    }
}
</script>
<style lang="less">
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
:deep(.ant-upload-list){
    display: none;
}
</style>
  