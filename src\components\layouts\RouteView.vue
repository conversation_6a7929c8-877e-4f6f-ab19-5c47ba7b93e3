<!--
 * @Author: your name
 * @Date: 2021-05-24 20:53:59
 * @LastEditTime: 2022-04-19 10:36:43
 * @LastEditors: LOK-E
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\components\layouts\RouteView.vue
-->
<template>
  <!-- 更新：增加contentHeight控制预览的区域高度，防止vxe表格grid渲染的时候找不到可渲染的高度 -->
  <div class="main">
    <keep-alive>
      <router-view v-if="keepAlive" />
    </keep-alive>
    <router-view v-if="!keepAlive" />
  </div>
</template>

<script>
export default {
    name: 'RouteView',
    computed: {
        keepAlive () {
            return this.$route.meta.keepAlive
        }
    },
    methods: {}
}
</script>
