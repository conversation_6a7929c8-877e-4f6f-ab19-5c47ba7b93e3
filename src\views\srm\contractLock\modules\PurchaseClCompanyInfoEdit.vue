<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url" />
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction } from '@/api/manage'
export default {
    name: 'ClCompanyInfoEdit',
    mixins: [EditMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            selectType: 'esignEnterpriseCertification',
            pageData: {
                form: {
                    loadingCompany: '0',
                    companyCode: '',
                    companyName: '',
                    subAccount: '',
                    accountId: '',
                    legalPerson: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    registerNo: '',
                    bankNo: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_64dEMQeAfsqvfsQ4`, '企业认证信息维护'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    fieldName: 'loadingCompany',
                                    dictCode: 'yn',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_loadingCompany`, '是否加载公司列表'),
                                    bindFunction: function (parentRef, pageData, groupData, value) {
                                        let setDisabledByProp = (prop, flag) => {
                                            for (let group of pageData.groups) {
                                                if (group.type) continue
                                                let formFields = group.custom.formFields
                                                for (let sub of formFields) {
                                                    if (sub.fieldName === prop) {
                                                        sub.disabled = flag
                                                        break
                                                    }
                                                }
                                            }
                                        }
                                        if(value){
                                            let flag = (value === '0')
                                            setDisabledByProp('companyCode', flag)
                                            setDisabledByProp('companyName', !flag)
                                        }else{
                                            setDisabledByProp('companyCode', true)
                                            setDisabledByProp('companyName', true)
                                        }
                                    },
                                    required: '1'

                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码'),
                                    fieldName: 'companyCode',
                                    dictCode: 'purchase_organization_info,org_name,org_code,org_category_code="companyCode"',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyCode`, '公司代码')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称'),
                                    fieldName: 'companyName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_companyName`, '公司名称')
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                    fieldName: 'applyContactType',
                                    dictCode: 'contractLockContactType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KHCKAc_c8bf10d`, '联系方式类型'),
                                     required: '1'
                                },
                                {
                                    fieldType: 'remoteSelect',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_cCcUoaXj`, '认证账号'),
                                    fieldName: 'subAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.form.subAccount = data[0].subAccount
                                        Vue.form.applyUserName = data[0].realname
                                        Vue.form.applyContact = data[0].phone
                                        if(Vue.form.applyContactType === 'EMAIL') {
                                            Vue.form.applyContact = data[0].email
                                        }
                                    }, extend: {
                                        modalColumns: [
                                            {field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账号'), with: 150},
                                            {field: 'realname', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qcItemName`, '名称'), with: 150},
                                            {field: 'phone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_phone`, '手机号'), with: 150},
                                            {field: 'email', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'), with: 150}
                                        ], modalUrl: '/account/elsSubAccount/list', modalParams: {status: 1}
                                    },
                                     required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcR_b51847bb`, '申请者姓名'),
                                    fieldName: 'applyUserName',
                                     required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                    fieldName: 'applyContact',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCK_2babc4f3`, '申请人联系方式'),
                                     required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCRXdiy_c6d93ea6`, '公司工商注册号'),
                                    fieldName: 'registerNo'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RChLcR_c2c6edeb`, '公司法人姓名'),
                                    fieldName: 'legalPerson'
                                }
                            ],
                            validateRules: {
                                loadingCompany: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BFQiKPrmHbGTqCS8`, '是否加载公司列表不能为空')}],
                                applyContact: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCKxOLV_5af31783`, '申请人联系方式不能为空')}],
                                subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LieyxOLV_542509fe`, '认证账号不能为空')}],
                                applyContactType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVLKHCKAcxOLV_5d30b933`, '申请人联系方式类型不能为空')}],
                                applyUserName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UVNcRxOLV_8150f64b`, '申请者姓名不能为空')}]
                            }
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitCertification`, '提交认证'), type: 'primary', click: this.submitCertificationEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/contractLock/purchaseCLCompanyInfo/add',
                auth: '/contractLock/purchaseCLCompanyInfo/submitCertification',
                detail: '/contractLock/purchaseCLCompanyInfo/queryById',
                edit: '/contractLock/purchaseCLCompanyInfo/edit'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        modifyInfoBtn (){
            if(this.currentEditRow.orgId){
                return true
            }
            return false
        },
        prevEvent () {
            this.$refs.editPage.prevStep()
        },
        nextEvent () {
            this.$refs.editPage.nextStep()
        },
        saveEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.currentEditRow.id? this.url.edit: this.url.add
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if (!this.currentEditRow.id) {
                            this.currentEditRow.id = res.result.id
                        }
                        this.goBack()
                    }).finally(() => {
                        this.$refs.editPage.confirmLoading = false
                    })
                }
            }).catch(err => {
                console.log(err)
            })
        },
        submitCertificationEvent () {
            this.$refs.editPage.confirmLoading = true
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.confirmLoading = false
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    const params = this.$refs.editPage.getPageData()
                    if(params.loadingCompany==='0' && !params.companyName){
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCRLxOLV_3f6f291f`, '公司名称不能为空'))
                        return
                    }
                    if(params.loadingCompany==='1' && !params.companyCode){
                        this.$refs.editPage.confirmLoading = false
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RCooxOLV_5449119a`, '公司代码不能为空'))
                        return
                    }
                    let url = this.url.auth

                    let longLink = params.longLink
                    if (longLink && longLink.length>0) {
                        const that = this
                        this.$refs.editPage.confirmLoading = false
                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificationSystems`, '确认认证'),
                            content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_rVHIDJRLiWKmDJMAbujWKQtTDJW_96191780`, '该信息已提交过认证，再次提交会产生费用，是否继续提交？'),
                            onOk: function () {
                                postAction(url, params).then(res => {
                                    const type = res.success ? 'success' : 'error'
                                    that.$message[type](res.message)
                                    window.open(res.result.certificationPageUrl)
                                    that.goBack()
                                }).finally(() => {
                                    that.confirmLoading = false
                                })
                            }
                        })
                    } else {
                        postAction(url, params).then(res => {
                            const type = res.success ? 'success' : 'error'
                            this.$message[type](res.message)
                            window.open(res.result.certificationPageUrl)
                            this.goBack()
                        }).finally(() => {
                            this.$refs.editPage.confirmLoading = false
                        })
                    }
                }
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>