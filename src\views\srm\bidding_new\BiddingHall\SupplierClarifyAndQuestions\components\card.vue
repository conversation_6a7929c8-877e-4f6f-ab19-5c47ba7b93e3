<template>
  <div class="container">
    <a-spin :spinning="confirmLoading">
      <div
        class="purchaserContext"
        v-if="ifPurchaser"
        style="text-align:left">
        <img
          src="~@/assets/img/supplier/ask.png"
          alt=""
          style="float:left;">
      
        <div
          class="replyContent-item">
          <span style="color:orange">{{ $srmI18n(`${$getLangAccount()}#i18n_field_DGC_387a9122`, '（提出方）') }}</span>
          <span style="font-size: 14px;color:rgb(143, 149, 155)">{{ getTitle(nodeData) }}</span>
          <p style="font-size: 14px;color:rgb(143, 149, 155);margin-left:44px">{{ getTime(nodeData) }}</p>
          <!-- 回复内容 -->
          <p
            class=""
            style="padding: 6px 15px; margin-bottom: 0px; margin-left: 25px;overflow-wrap: break-word">{{ nodeData.replyContent }}</p>
          <!-- 文件列表 -->
          <div
            class="fileList"
            style="padding-left:25px">
            <div
              class="fileItem"
              v-for="(file, index) in nodeData.purchaseAttachmentList"
              :key="index">
              <a-button
                type="link"
                @click="downloadEvent(file)">{{ file.fileName }}</a-button>
              <a
                style="margin-right: 8px"
                @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
              <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
            </div>
          </div>
        </div>
        <a-divider
          style="border-color: rgb(232, 232, 232)"
          dashed />
      </div>
      <div
        v-if="ifSupplier"
        class="supplierContext"
        style="text-align:right">
        <img
          src="~@/assets/img/supplier/answer.png"
          alt=""
          style="float:right;">
      
        <div
          class="replyContent-item">
          <span style="font-size: 14px;color:rgb(143, 149, 155)">{{ getTitle(nodeData) }}</span>
          <span style="color:orange">{{ $srmI18n(`${$getLangAccount()}#i18n_field_fBC_4345a971`, '（答复方）') }}</span>
          <p style="font-size: 14px;margin-right:44px;color:rgb(143, 149, 155)">{{ getTime(nodeData) }}</p>
          <!-- 回复内容 -->
          <span
            ref="pRight"
            class="pRight"
            style="display: inline-block; padding: 6px 15px; margin-bottom: 0px;margin-right: 29px; overflow-wrap: break-word;" >{{ nodeData.replyContent }}</span>
          <!-- 文件列表 -->
          <div
            class="fileList"
            style="padding-right:40px">
            <div
              class="fileItem"
              v-for="(file, index) in nodeData.saleAttachmentList"
              :key="index">
              <a-button
                type="link"
                @click="downloadEvent(file)">{{ file.fileName }}</a-button>
              <a
                style="margin-right: 8px"
                @click="preViewEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
              <a @click="downloadEvent(file)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>
            </div>
          </div>
        </div>
        <a-divider
          style="border-color: rgb(232, 232, 232)"
          dashed />
      </div>
    </a-spin>
  </div>
</template>

<script>
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    props: {
        nodeData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            ifSupplier: false,
            ifPurchaser: false,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/saleAttachment/upload`
        }
    },
    created () {
        
    },
    watch: {
        ifSupplier (val) {
            if (val) {
                this.$nextTick(()=>{
                    const PObj = this.$refs.pRight
                    PObj.offsetHeight > 35 && (()=>{
                        PObj.style.textIndent = 2+'em'
                        // PObj.style.textAlignLast = 'left'
                        PObj.style.textAlign = 'left'
                    })()

                })
                
            }
        }
    },
    mounted () {
        // 提出方
        this.ifPurchaser = (this.nodeData.responderType == 0)
        // 回复方
        this.ifSupplier = (this.nodeData.responderType == 1)
    },
    methods: {
        getTitle (item) {
            return `${item.responderName ?? ''}`
        },
        getTime (item) {
            return `${item.createTime}`
        },
        async downloadEvent (file) {
            console.log('lailelaodi')
            const fileName = file.fileName
            file.subpackageId = this.nodeData.subpackageId
            let {message: url} = await getAttachmentUrl(file)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.nodeData.subpackageId
            this.$previewFile.open({ params: row })
        }
    }
}
</script>

<style lang="less" scoped>
  .replyContent-item {
    // border: 1px solid #f2f3f5;
    // box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
}



</style>