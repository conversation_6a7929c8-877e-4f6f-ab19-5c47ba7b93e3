<template>
  <div class="els-page-comtainer">
    
    <a-page-header
      :class="[ fixPageHeader ? 'fixed-page-header' : '', ]"
      :title="title"
    >
      <template slot="extra">
        <a-button
          @click="handleOk"
          type="primary"
          key="2">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
        <a-button
          @click="goBack"
          key="3">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
      </template>
    </a-page-header>
    <div class="table-page-search-wrapper">
      <a-spin :spinning="confirmLoading">
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right">
          <a-collapse-panel
            key="1"
            :header="$srmI18n(`${$getLangAccount()}#i18n_field_100100`, '基本信息')"
          >
            <a-form :form="form">
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataCoding`, '数据编码')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_dataCodingMsg`, '请输入数据编码')"
                  v-decorator="['dataCode', validatorRules.dataCode]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataType`, '数据类型')">
                <j-dict-select-tag
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_dataTypeMsg`, '请选择数据类型')"
                  v-decorator="['dataType', validatorRules.dataType]"
                  :trigger-change="true"
                  dict-code="srmEchartDataType"
                />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataDescribe`, '数据描述')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_dataDescribeMsg`, '请输入数据描述')"
                  v-decorator="['dataDesc', validatorRules.dataDesc]" />
              </a-form-item>
              <a-form-item
                :labelCol="labelCol"
                :wrapperCol="wrapperCol"
                :label="$srmI18n(`${$getLangAccount()}#i18n_title_dataSQL`, '数据SQL')">
                <a-input
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_dataSQLMsg`, '请输入数据SQL')"
                  type="textarea"
                  rows="10"
                  v-decorator="['dataSql', validatorRules.dataSql]" />
              </a-form-item>
            </a-form>
          </a-collapse-panel>
        </a-collapse>
      </a-spin>
    </div>
  </div>
  <!-- </a-modal> -->
</template>

<script>
import { postAction } from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateReportCheck } from '@/api/api'

export default {
    name: 'ChartDataModal',
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    data () {
        return {
            activeKey: ['1'],
            fixPageHeader: false,
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chartData`, '图表数据'),
            visible: false,
            model: {},
            labelCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            wrapperCol: {
                xs: { span: 8 },
                sm: { span: 8 }
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            url: {
                add: '/report/dashboard/chartData/add',
                edit: '/report/dashboard/chartData/edit'
            }
        }
    },
    computed: {
        langAccount () {
            return this.$getLangAccount()
        },

        validatorRules (){
            let account = this.langAccount
            const {$srmI18n} = this
            return {
                dataCode: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataCodingMsg`, '请输入数据编码!')}, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')}, { validator: this.validateCode }]},
                dataType: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataTypeMsg`, '请选择数据类型!')}, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
                dataDesc: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataDescribeMsg`, '请输入数据描述!') }, {max: 50, message: $srmI18n(`${account}#i18n_title_overflow`, '内容长度不能超过50个字符!')}]},
                dataSql: {rules: [{ required: true, message: $srmI18n(`${account}#i18n_title_dataSQLMsg`, '请输入数据SQL!')}]}
            }
        }

    },
    mounted () {
        this.init()
        window.addEventListener('scroll', this.handleScroll)        
    },
    methods: {
        init () {
            if(this.currentEditRow) {
                this.edit(this.currentEditRow)
            }else {
                this.add()
            }
        },
        add () {
            this.edit({})
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_addChartData`, '新增图表数据')
        },
        edit (record) {
            this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_editChartData`, '编辑图表数据')
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.visible = true
            this.$nextTick(() => {
                this.form.setFieldsValue(pick(this.model, 'dataCode', 'dataType', 'dataDesc', 'dataSql'))
                //时间格式化
            })

        },
        validateCode (rule, value, callback) {
        // 重复校验
            var params = {
                tableName: 'els_chart_data',
                fieldName: 'data_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateReportCheck(params).then((res) => {
                if (res.success) {
                    callback()
                } else {
                    callback(res.message)
                }
            })
        },
        handleOk () {
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    if(!this.model.id){
                        httpurl+=this.url.add
                    }else{
                        httpurl+=this.url.edit
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化
            
                    console.log(formData)
                    postAction(httpurl, formData).then((res)=>{
                        if(res.success){
                            that.$message.success(res.message)
                            that.$emit('ok')
                        }else{
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                        that.goBack()
                    })
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleScroll () {
            var scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
            if (scrollTop > 130) {
                this.fixPageHeader = true
            }else {
                this.fixPageHeader = false
            }
        }
    }
}
</script>

<style lang="less" scoped>

</style>