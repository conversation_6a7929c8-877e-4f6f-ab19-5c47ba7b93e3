<template>
  <div>
    <div class="bassForm">
      <titleTrtl class="margin-b-10">
        <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_baseInfo`, '基本信息') }}</span>
      </titleTrtl>
      <a-form-model
        :label-col="labelCol"
        ref="form"
        :wrapper-col="wrapperCol"
        :model="fromSourceData">
        <a-row>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号')"
              prop="tenderProjectNumber"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.tenderProjectNumber"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称')"
              prop="tenderProjectName"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.tenderProjectName"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称')"
              prop="subpackageName"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.subpackageName"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tLRL_2715c81b`, '单位名称')"
              prop="supplierName"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.supplierName"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKnD_c8206068`, '是否联合体')"
              prop="combination_dictText"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.combination_dictText"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称')"
              prop="combinationName"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.combinationName"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人')"
              prop="contacts"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.contacts"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item
              :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系方式')"
              prop="contactsPhone"
            >
              <a-input
                :disabled="true"
                v-model="fromSourceData.contactsPhone"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <titleTrtl class="margin-b-10">
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIVH_2f594f3d`, '文件信息') }}</span>
    </titleTrtl>
    <list-table
      v-if="show"
      ref="attachmentInfoList"
      :pageData="pageData"
      :groupCode="groupCode"
      :statictableColumns="statictableColumns"
      setGridHeight="500"
      :fromSourceData="attachmentInfoList"
      :showTablePage="false"
    >
    </list-table>
    <h2
      v-else
      style="color:red;text-align:center">
      {{ $srmI18n(`${$getLangAccount()}#i18n_field_rtLLywWxiTmA_f674e12e`, '该单位未解密，不允许查看') }}</h2>
  </div>
</template>
<script lang="jsx">
import listTable from '../../components/listTable'
import titleTrtl from '../../components/title-crtl'
import {getAction} from '@/api/manage'
import {ajaxFindDictItems} from '@/api/api'
import {USER_ELS_ACCOUNT} from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    props: {
        groupCode: {
            default: '',
            type: String
        },
        fromSourceData: {
            default: () => {
            },
            type: Object
        },
        slotType: {
            default: '',
            type: String
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    computed: {
        subId () {
            return this.subpackageId()
        },
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    components: {
        listTable,
        titleTrtl
    },
    data () {
        return {
            show: false,
            labelCol: {
                span: 9
            },
            wrapperCol: {
                span: 15
            },
            attachmentInfoList: [],
            attachmentFileType: {},
            statictableColumns: [
                // { type: 'checkbox', width: 40, fixed: 'left' }, 
                {
                    type: 'seq',
                    width: 60,
                    fixed: 'left',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    groupCode: 'attachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                    fieldLabelI18nKey: '',
                    field: 'fileType_dictText',
                    // fieldType: 'select',
                    // dictCode: 'srmFileType',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250',
                    slots: {
                        default: ({row}) => {
                            return [
                                // <span>{this.attachmentFileType[row.fileType]}</span>
                                <span>{this.attachmentFileType[row.fileType]}</span>
                            ]
                        }
                    }
                },
                {
                    groupCode: 'attachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                    fieldLabelI18nKey: '',
                    field: 'fileName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                    fieldLabelI18nKey: '',
                    field: 'updateTime',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人'),
                    fieldLabelI18nKey: '',
                    // fieldType: 'number',
                    field: 'uploadSubAccount_dictText',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '250'
                },
                {
                    groupCode: 'attachmentInfoList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    fieldLabelI18nKey: '',
                    // fieldType: 'number',
                    // field: 'saleAmount',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: '183',
                    slots: {
                        default: () => {
                            let fileList = ''
                            //未解密情况时，展示文本，隐藏下载预览按钮
                            fileList = [
                                <div>
                                    <p style="color:red"> {this.$srmI18n(`${this.$getLangAccount()}#i18n_field_rtLLywxiTIKUB_d1e32e5e`, '该单位未解密,不允许下载预览')} </p>
                                </div>
                            ]
                            return [
                                fileList
                            ]
                        }
                    }
                }
            ],
            pageData: {
                optColumnList: [
                    {
                        key: 'download',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                        clickFn: this.downloadEvent
                    },
                    {
                        key: 'preView',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        clickFn: this.preViewEvent
                    }
                ]
            }

        }
    },
    methods: {
        // 预览
        preViewEvent (row) {
            row.subpackageId = this.subId
            this.$previewFile.open({params: row})
        },
        // 下载
        async downloadEvent (row) {
            if (!row.fileName) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            const {fileName} = row
            row.subpackageId = this.subId
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.attachmentInfoList.loading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.attachmentInfoList.loading = false
            })
        },
        externalAllData () {
            let {fullData} = this.$refs['attachmentInfoList'].getTableData()
            console.log('fullData', fullData)
            return fullData
        },
        // 获取表格下拉字典
        queryDictData (dictCode) {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: dictCode
            }
            ajaxFindDictItems(postData).then((res) => {
                if (res.success) {
                    res.result.map(({value, text, title}) => {
                        this.$set(this.attachmentFileType, value, text || title)
                    })
                }
            })
        }
    },
    created () {
        let attachmentFileType = ''
        if (this.propOfCheckType == '0') {
            attachmentFileType = 'preSaleDocumentSubmitFileType'
        } else {
            if (this.subPackageRow.processType == '1') {
                attachmentFileType = 'resultSaleDocumentSubmitFileType '
            } else {
                attachmentFileType = 'saleDocumentSubmitFileType'
            }
        }
        this.queryDictData(attachmentFileType)
        this.attachmentInfoList = this.fromSourceData.attachmentInfoList || []
        if (this.fromSourceData.attachmentInfoList.length > 0) {
            this.show = true
            this.statictableColumns[5].slots.default = 'grid_opration'
        }
    }
}
</script>
<style lang="less" scoped>

</style>