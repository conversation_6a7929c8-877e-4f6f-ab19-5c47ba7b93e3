<template>
  <div
    class="page-container"
    :style="{height: pageContentHeight}">
    <div class="edit-page">
      <content-header />
      <div
        class="page-content list" 
        v-if="!showClarifyEditPage && !showQuestionsReplyPage"
      >
        <Questions
          @handleReply="handleReply"
          @handleView="handleView"/>
        <Clarify
          @handClarifyEditPage="handClarifyEditPage"
          @handleClarifyViewPage="handleClarifyViewPage"/>
      </div>
      <ClarifyEditPage
        v-if="showClarifyEditPage"
        :current-edit-row="currentEditRow"
        :check="check"
        @hide="hideEditPage"
      ></ClarifyEditPage>
      <QuestionsEditPage
        v-if="showQuestionsReplyPage"
        :current-edit-row="currentEditRow"
        :check="check"
        @hide="hideEditPage"
      ></QuestionsEditPage>
    </div>
  </div>
</template>
<script>
import Clarify from './modules/Clarify.vue'
import Questions from './modules/Questions.vue'
import ClarifyEditPage from './modules/ClarifyEditPage.vue'
import QuestionsEditPage from './modules/QuestionsEditPage.vue'
import TrendVue from '../../../../../components/Trend/Trend.vue'
import ContentHeader from '../components/content-header'
export default {
    components: {
        Clarify,
        Questions,
        ClarifyEditPage,
        QuestionsEditPage,
        ContentHeader
    },
    computed: {
        pageContentHeight () {
            let height = document.body.clientHeight -70
            return height + 'px'
        }
    },
    data () {
        return {
            showClarifyEditPage: false,
            showQuestionsReplyPage: false,
            currentEditRow: {},
            check: false
        }
    },
    methods: {
        hideEditPage () {
            this.showClarifyEditPage = false
            this.showQuestionsReplyPage = false
            this.check=false
        },
        handleReply (row) {
            this.currentEditRow = row
            this.showQuestionsReplyPage = true
            this.check=false
        },
        handleView (row){
            this.currentEditRow = row
            this.showQuestionsReplyPage = true
            this.check=true
        },
        handClarifyEditPage (row) {
            this.currentEditRow = row
            this.showClarifyEditPage = true
            this.check=false
        },
        handleClarifyViewPage (row){
            this.currentEditRow = row
            this.showClarifyEditPage = true
            this.check=true
        }
    }
}
</script>
<style lang="less" scoped>
.ClarifyAndQuestions{
  height: 100%;
}
.page-content{
flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>


