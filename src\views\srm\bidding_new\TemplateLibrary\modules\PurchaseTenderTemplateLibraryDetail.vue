<template>
    <div class="els-page-comtainer">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :handleAfterDealSource="handleAfterDealSource"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        v-on="businessHandler"
      >
      </business-layout>

    </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'

export default {
    name: 'PurchaseTenderVariableLibraryDetail',
    components: {
        fieldSelectModal,
        BusinessLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data() {
        return {
            businessRefName: 'businessRefName',
            requestData: {
                detail: { url: '/tender/template/purchaseTenderVariableLibrary/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: "/tender/template/purchaseTenderVariableLibrary/add",
                edit: "/tender/template/purchaseTenderVariableLibrary/edit",
                detail: '/tender/template/purchaseTenderVariableLibrary/queryById'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderTemplateLibrary_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        goBack() {
            this.$emit('hide')
        },
        selectCallBack(item) {
            this.pageData.form.dictCode = item[0].dictCode
            this.$refs.editPage.$forceUpdate()
        }
    }
}
</script>

<style lang="less" scoped>
</style>