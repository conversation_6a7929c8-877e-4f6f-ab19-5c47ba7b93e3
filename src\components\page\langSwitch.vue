<template>
  <div class="lang-wrap">
    <a-select
      class="select-wrap"
      v-model="lang"
      size="small"
      :dropdownMatchSelectWidth="false"
      :dropdownStyle="{width: '85px'}"
      @change="changeLangHandle">
      <a-select-option
        v-for="el in langs"
        :key="el.value"
        :title="el.label"
      >
        {{ el.label }}
      </a-select-option> 
    </a-select>
  </div>
</template>

<script>
import {  DEFAULT_LANG, USER_INFO } from '@/store/mutation-types'
import {getAction} from '@/api/manage'
export default {
    name: 'Langswitch',

    props: {
        localLangs: {
            type: Array,
            default: ()=> {
                return null
            }
        }
    },
    data () {
        return {
            lang: this.$ls.get(DEFAULT_LANG) || 'zh',
            remoteLang: []
        }
    },

    mounted () {
        this.getRemoteLang()
    },
    computed: {
        langs () {
            return this.localLangs ? this.localLangs : this.remoteLang
        }
    },
    methods: {
        getRemoteLang () {
            const token = this.$ls.get('Access-Token')
            if (!token && this.localLangs.length > 0) {
                return
            }
            getAction('/account/permission/getLanguages', {token}).then(res => {
                if(res.success){
                    if (this.lang && !res.result[this.lang]) {
                        this.lang = 'zh'
                        this.changeLangHandle()
                    }
                    let languageMap = res.result || {}
                    this.remoteLang = Object.keys(languageMap).map(rs => {
                        return {
                            value: rs,
                            label: languageMap[rs]
                        }
                    })
                }
            })
        },
        // 改变语种处理
        changeLangHandle () {
            this.$store.commit('TOGGLE_LANG', this.lang)
            this.$emit('change-lang', this.lang)
            window.location.reload()
        }
    }
}
</script>

<style lang="scss" scoped>
.lang-wrap {
  min-width: 60px;
  .select-wrap {
    width: 100%;
  }
}
</style>