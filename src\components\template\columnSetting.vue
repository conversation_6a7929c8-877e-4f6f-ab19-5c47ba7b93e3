<!--
 * @Author: your name
 * @Date: 2021-03-31 10:21:40
 * @LastEditTime: 2022-03-08 11:26:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\components\template\columnSetting.vue
-->
<template>
  <a-modal
    v-drag
    centered
    :title="title"
    :width="1120"
    :visible="visible"
    :maskClosable="false"
    :confirmLoading="confirmLoading"
    @cancel="close"
  >
    <a-alert
      :message="$srmI18n(`${$getLangAccount()}#i18n_alert_DK_c8f6a`, '提示')"
      :description="$srmI18n(`${$getLangAccount()}#i18n_alert_GKEzEImLAJIIAXAEtGRWSAEtGRKMUEtGRWmLAJIIUAEtxeKMmLGRWsRmLGRqVVAXAEtGRSUEtGRW_6d17277`, '初始化状态下个人列自定义跟随企业级设置，无企业级设置时取平台级设置；个人列自定义与企业级不同时取个人设置。重置个人设置可重新跟随企业级设置或平台级设置。')"
      style="margin-bottom: 10px"
      type="info"
      closable
      show-icon
    />
    <div
      class="grid-box"
      style="overflow: hidden; position: relative"
    >
      <vxe-grid
        border
        auto-resize
        height="300"
        row-id="id"
        size="small"
        ref="columnGrid"
        :edit-config="{ trigger: 'click', mode: 'cell' }"
        :columns="tableColumn"
      >
        <template slot="empty">
          <a-empty />
        </template>
        <template #custom_field_colors_render="{ row, column }">
          <template v-if="row[column.property] && row[column.property].length == 1">
            <div
              v-for="(fieldColor, index) in row[column.property]"
              :key="index + 1"
            >
              <div v-if="row['link'] && row['link'] === '1'">
                <span style="color: #1890ff">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_BKy_225aeac`, '超链接') }}</span>
              </div>
              <a-row v-else>
                <a-col
                  class="color-value-box"
                  :span="16"
                  ><span :style="{ color: fieldColor.fieldColor }">{{ fieldColor.fieldColor }}</span></a-col
                >
                <a-col :span="8"
                  ><a-input
                    class="color-input"
                    type="color"
                    v-model="fieldColor.fieldColor"
                /></a-col>
              </a-row>
            </div>
          </template>
          <template v-else>
            <a-button
              type="dashed"
              size="small"
              class="btn-setting-box"
              @click="showDrawer(row)"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_title_dictionaryConfiguration`, '字典配置') }}</a-button
            >
          </template>
        </template>
      </vxe-grid>
      <a-drawer
        title="字典字段配置色"
        :width="400"
        placement="right"
        :closable="false"
        :visible="drawerVisible"
        :get-container="false"
        :wrap-style="{ position: 'absolute' }"
        @close="drawerVisible = false"
      >
        <div>
          <vxe-table
            border
            resizable
            show-overflow
            show-header-overflow
            size="mini"
            align="center"
            headerAlign="center"
            :data="selectedRow.fieldColors"
          >
            <vxe-column
              field="fieldLabel"
              title="字段名称"
            >
              <template #default="{ row }">
                <a-tooltip :title="row['fieldLabel']">
                  <span>{{ row['fieldLabel'] }}</span>
                </a-tooltip>
              </template>
            </vxe-column>
            <vxe-column
              field="fieldColor"
              title="颜色值"
            >
              <template #default="{ row }">
                <span :style="{ color: row['fieldColor'] }">{{ row['fieldColor'] }}</span>
              </template>
            </vxe-column>
            <vxe-column title="操作">
              <template #default="{ row }">
                <a-input
                  class="color-input"
                  type="color"
                  v-model="row['fieldColor']"
                />
              </template>
            </vxe-column>
          </vxe-table>
          <!-- <a-row style="padding-bottom: 14px;" v-for="(fieldColor, index) in selectedRow.fieldColors" :key="index+ 1">
                    <a-col style="line-height: 32px;" :span="14"><span :style="{color: fieldColor.fieldColor}"><span>{{fieldColor.fieldLabel}}</span>{{fieldColor.fieldColor}}</span></a-col>
                    <a-col :span="10"><a-input class="color-input" type="color" v-model="fieldColor.fieldColor" /></a-col>
                </a-row> -->
        </div>
      </a-drawer>
    </div>
    <template slot="footer">
      <a-button
        key="reset"
        @click="reset"
      >
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_reset`, '重置') }}
      </a-button>
      <a-button
        key="back"
        @click="close"
      >
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_cancle`, '取消') }}
      </a-button>
      <a-button
        key="submit"
        type="primary"
        @click="selectedOk"
      >
        {{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认') }}
      </a-button>
    </template>
  </a-modal>
</template>
<script lang="jsx">
import { Empty, Alert } from 'ant-design-vue'
import { ajaxGetAllColumns, ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { postAction } from '@/api/manage'
import Sortable from 'sortablejs'
export default {
  name: 'ColumnSetting',
  components: {
    AEmpty: Empty,
    AAlert: Alert
  },
  inject: ['routeReload'],
  props: {
    customReload: {
      type: Boolean,
      default: false
    },
    hiddenFieldColors: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      drawerVisible: false,
      selectedRow: { fieldColors: [] },
      visible: false,
      confirmLoading: false,
      columnsCode: '',
      title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
      tableColumn: [
        { type: 'checkbox', width: 40, fixed: 'left' },
        { type: 'seq', width: 50, align: 'center', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), fixed: 'left' },
        {
          width: 30,
          slots: {
            default: () => {
              return [
                <span
                  class='drag-btn'
                  style='cursor:move'
                >
                  <i class='vxe-icon--menu'></i>
                </span>
              ]
            },
            header: () => {
              return [
                <vxe-tooltip
                  content={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pressHoldDragUpDown`, '按住后可以上下拖动排序！')}
                  enterable
                >
                  <i class='vxe-icon--question'></i>
                </vxe-tooltip>
              ]
            }
          }
        },
        { field: 'columnCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnCoding`, '列编码'), sortable: true, align: 'center', width: 220 },
        { field: 'columnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title__columnName`, '列名称'), sortable: true, align: 'center', width: 120 },
        { field: 'columnGroup', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_AzV_13e95d5`, '列分组'), sortable: true, align: 'center', width: 120, editRender: { name: 'ASelect', options: [] } },
        { field: 'columnWidth', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_columnWidth`, '列宽'), align: 'center', width: 80, editRender: { name: 'AInput' } },
        {
          field: 'alignType',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_elsBarcodeRuleItemLista127_alignment`, '对齐方式'),
          align: 'center',
          editRender: {
            name: 'ASelect',
            options: [
              { value: 'left', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_alignLeft`, '左对齐') },
              { value: 'center', label: this.$srmI18n(`${this.$getLangAccount()}#i18n__sIIA_25c8121e`, '中间对齐') },
              { value: 'right', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_alignRight`, '右对齐') }
            ]
          }
        },
        {
          field: 'fixType',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OyCK_270fd6ee`, '冻结方式'),
          align: 'center',
          //  editRender: {
          //  name: 'ASelect',
          //  options: [
          //     {value: 'unfrozen', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LOy_18da922`, '未冻结')},
          //     {value: 'left', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kOy_16ae0de`, '左冻结')},
          //     {value: 'right', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jOy_14587ab`, '右冻结')}
          //     ],

          //     }
          slots: {
            // 使用 JSX 渲染
            default: ({ row, rowIndex }) => {
              return [
                <a-select
                  ref='select'
                  value={row.fixType}
                  onChange={this.handleFixTypeChange.bind(this, row, rowIndex)}
                >
                  <a-select-option value='unfrozen'>{this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_LOy_18da922`, '未冻结')}</a-select-option>
                  <a-select-option value='left'>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_kOy_16ae0de`, '左冻结')}</a-select-option>
                  <a-select-option value='right'>{this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jOy_14587ab`, '右冻结')}</a-select-option>
                </a-select>
              ]
            }
          }
        },
        { field: 'hidden', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hidden`, '是否隐藏'), align: 'center', editRender: { name: 'mSwitch', type: 'visible', props: { closeValue: '0', openValue: '1' } } },
        {
          field: 'fieldColors',
          title: '自定义颜色',
          align: 'center',
          slots: {
            default: 'custom_field_colors_render',
            header: () => {
              // const renderContentTip = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BmAQvCcOyGRWAJOAcLWJCWFWJCAoWMKKWqbIQvCcmszEEROyWJOAcLAvAcKWRbIAPARjQvEROyWCLOyLwyWWWWWWWWWWWWWWWWWWWWWWWW_78de294b`, '表格列文本内容颜色设置，当字段类型为“字典”且“字典编码”存在时，可针对文本内容各种状态配置颜色;字段类型为其他类型时，只针对当前列名的文本配置颜色。默认颜色为黑色#606222(R:96 G:98 B:102)')
              const renderContent = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOOy_2b2ca114`, '字段颜色')
              return [
                // (<vxe-tooltip class="field-tip" content={renderContentTip} enterable>
                //     <span><i class="vxe-icon--edit-outline"></i>{renderContent}<a-icon type="question-circle-o" /></span>
                // </vxe-tooltip>)
                <span>
                  <i class='vxe-icon--edit-outline'></i>
                  {renderContent}
                </span>
              ]
            }
          }
        }
      ],
      tableData: []
    }
  },
  created() {
    this.beforeSetColumn()
    this.getColumnGroupByCode()
  },
  methods: {
    beforeSetColumn() {
      this.tableColumn = this.tableColumn.filter((item) => {
        if (this.hiddenFieldColors && item.field == 'fieldColors') item.visible = false
        return item
      })
    },
    //fixtype值改变
    handleFixTypeChange(row, rowIndex, val) {
      row.fixType = val
      if (this.curLastFrozenIndex <= rowIndex) {
        this.curLastFrozenIndex = rowIndex
        if (this.curLastFrozenIndex > -1) {
          for (let [idx, v] of this.tableData.entries()) {
            if (idx <= rowIndex) {
              v.fixType = this.tableData[rowIndex].fixType
            }
          }
          this.$refs.columnGrid.loadData([])
          this.$nextTick(() => {
            this.$refs.columnGrid.loadData(this.tableData)
            this.$refs.columnGrid.$forceUpdate()
          })
        }
      }
    },
    //设置设置对应的列左右对应的列冻结
    setFrozenColumn(listArry) {
      let temRestArry = [...listArry]
      temRestArry.reverse()
      let resetIndex = temRestArry.findIndex((val) => val.fixType)
      let lastFrozenIndex = resetIndex > -1 ? listArry.length - resetIndex - 1 : resetIndex
      this.curLastFrozenIndex = lastFrozenIndex //打开时的最后一个设置fixtype的
      if (lastFrozenIndex > -1) {
        for (let [idx, v] of listArry.entries()) {
          if (idx <= lastFrozenIndex && !v.fixType) {
            v.fixType = listArry[lastFrozenIndex].fixType
          }
        }
      }
      return listArry
    },
    getColumnGroupByCode() {
      let postData = {
        busAccount: this.$ls.get(USER_ELS_ACCOUNT),
        dictCode: 'srmColumnGroup'
      }
      ajaxFindDictItems(postData).then((rs) => {
        if (rs.code == 200) {
          let column = rs.result.map((col) => {
            return {
              value: col.value,
              label: this.$srmI18n(`${this.$getLangAccount()}#${col.textI18nKey}`, col.title)
            }
          })
          this.tableColumn.forEach((col) => {
            if (col.field == 'columnGroup') {
              col.editRender.options = column
            }
          })
        }
      })
    },
    getColumnsList(column) {
      this.columnsCode = column
      this.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_title__tableCode`, '表格编码') + ':' + column
      ajaxGetAllColumns(column, null).then((res) => {
        let list = this.setFrozenColumn(res.result)
        list.forEach((item) => {
          if (item.fieldColors && item.fieldColors.length) {
            // 字符串转json
            item.fieldColors = JSON.parse(item.fieldColors)
          } else {
            // 存在配置数据字典初始化字段颜色数据,否则初始化当前字段颜色值
            if (item.fieldType === 'dict' && item.dictCode) {
              this.getFieldColorsData(item)
            } else {
              item.fieldColors = []
              let fieldColor = { fieldName: '', fieldLabel: '', fieldColor: '#606266' }
              item.fieldColors.push(fieldColor)
            }
          }
        })
        this.tableData = list
        this.$refs.columnGrid.loadData(list)

        const _this = this
        this.$nextTick(() => {
          let columnGrid = this.$refs.columnGrid
          this.groupSortable =
            columnGrid &&
            Sortable.create(columnGrid.$el.querySelector('.body--wrapper>.vxe-table--body tbody'), {
              handle: '.drag-btn',
              onEnd: ({ newIndex, oldIndex }) => {
                let { fullData } = columnGrid.getTableData()

                let tableData = [...fullData]
                let currRow = tableData.splice(oldIndex, 1)[0]
                tableData.splice(newIndex, 0, currRow)
                tableData = tableData.map((item, index) => {
                  item.sortOrder = index + 1
                  return item
                })
                _this.$refs.columnGrid.loadData([])
                this.$nextTick(() => {
                  _this.$refs.columnGrid.loadData(tableData)
                  _this.tableData = tableData
                  _this.$refs.columnGrid.$forceUpdate()
                })
              }
            })
        })
      })
    },
    // 初始化字段颜色数据
    getFieldColorsData(item) {
      item.fieldColors = []
      let postData = {
        busAccount: this.$ls.get(USER_ELS_ACCOUNT),
        dictCode: item.dictCode
      }
      ajaxFindDictItems(postData).then((res) => {
        if (res && res.success) {
          if (res.result && res.result.length) {
            res.result.forEach((dict) => {
              let fieldColor = { fieldName: dict.value, fieldLabel: dict.text, fieldColor: '#606266' }
              item.fieldColors.push(fieldColor)
            })
          }
        } else {
          this.formModel.fieldColors = []
          this.$message.warning(res.message)
        }
      })
    },
    showDrawer(row) {
      this.drawerVisible = true
      this.selectedRow = row
    },
    open(column) {
      this.getColumnsList(column)
      this.visible = true
    },
    close() {
      this.visible = false
    },
    // 重置
    reset() {
      let url = '/base/userColumnDefine/clearCurrentUserColumnDefine/' + this.columnsCode
      postAction(url).then(() => {
        this.routeReload()
      })
    },
    selectedOk() {
      let tableData = this.$refs.columnGrid.getTableData().fullData
      let columnList = []
      // let lastFixIndex=tableData.findLastIndex(val=> val.fixType!='unfrozen')

      tableData.forEach((item) => {
        // 判断字段颜色是否为字典类型，如是，正常保存，否则将当前列名和列编码存入
        if (item.fieldColors && item.fieldColors.length === 1) {
          item.fieldColors[0].fieldLabel = item.columnName
          item.fieldColors[0].fieldName = item.columnCode
        }

        let columnPro = {
          hidden: item.hidden,
          columnId: item.id,
          fixType: item.fixType,
          columnWidth: item.columnWidth,
          columnName: item.columnName,
          alignType: item.alignType,
          columnGroup: item.columnGroup,
          fieldColors: JSON.stringify(item.fieldColors)
          // sortOrder:item.sortOrder
        }

        // if(item.fixType){
        //     item.fixed=item.fixType=='unfrozen'?'':item.fixType
        // }
        if (item.sortOrder) {
          columnPro.sortOrder = parseInt(item.sortOrder)
        }
        columnList.push({
          ...columnPro
        })
      })
      let url = '/base/userColumnDefine/saveCurrentUserColumnDefine/' + this.columnsCode
      postAction(url, columnList).then(() => {
        if (!this.customReload) this.routeReload()
        else this.$emit('success')
      })
    }
  }
}
</script>
<style lang="less" scoped>
.color-value-box {
  line-height: 32px;
  font-weight: 700;
}
.color-input {
  padding: 0;
  width: 32px;
  border: 0;
}
.btn-setting-box {
  min-width: 84px;
  font-weight: 700;
  font-size: 13px;
}
</style>
