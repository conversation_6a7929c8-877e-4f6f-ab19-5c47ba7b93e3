<template>
  <div>
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n__tvVH_298b46a0`, '基本信息') }}</span>
    </titleTrtl>
    <Dataform
      ref="dataform"
      :formData="fromSourceData"
      :validateRules="validateRules"
      :pageStatus="pageStatus"
      :fields="fields"/>
  </div>
</template>

<script>
import Dataform from '@views/srm/bidding_new/BiddingHall/components/Dataform.vue'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import {USER_COMPANYSET, USER_INFO} from '@/store/mutation-types'
import {baseMixins} from '@views/srm/bidding_new/plugins/baseMixins'
import REGEXP from '@/utils/regexp'

export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    computed: {
        subpackage () {
            return this.currentSubPackage()
        }
    },
    data () {

        return {
            formData: {},
            validateRules: {},
            fields: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectNumber`, '项目编号'),
                    fieldLabelI18nKey: '',
                    field: 'tenderProjectNumber',
                    fieldType: 'input',
                    required: '1',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                    fieldLabelI18nKey: '',
                    field: 'tenderProjectName',
                    required: '1',
                    fieldType: 'input',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zsRL_268b7582`, '分包名称'),
                    fieldLabelI18nKey: '',
                    required: '1',
                    field: 'subpackageName',
                    fieldType: 'input',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    fieldLabelI18nKey: '',
                    required: '1',
                    field: 'supplierName',
                    fieldType: 'input',
                    disabled: true
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQKnD_c8206068`, '是否联合体'),
                    fieldLabelI18nKey: '',
                    field: 'combination',
                    dictCode: 'yn',
                    fieldType: 'select',
                    bindFunction: (v, item) => {
                        if (v == '0') {
                            this.$set(this.validateRules, 'combinationName', [])
                            this.fromSourceData.combinationName = ''
                            this.$set(this.fields[5], 'disabled', true)
                        } else {
                            this.$set(this.validateRules, 'combinationName', [
                                {
                                    required: true,
                                    message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRLlS_63c41428`, '联合体名称必填')
                                }
                            ])
                            this.$set(this.fields[5], 'disabled', false)
                        }
                    }
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称'),
                    fieldLabelI18nKey: '',
                    field: 'combinationName',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    fieldLabelI18nKey: '',
                    required: '1',
                    field: 'contacts',
                    fieldType: 'input'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系人电话'),
                    fieldLabelI18nKey: '',
                    required: '1',
                    field: 'contactsPhone',
                    fieldType: 'input'
                }
            ],
            userInfo: {}
        }
    },
    components: {
        titleTrtl,
        Dataform
    },
    methods: {
        handleAfterDealSource (resultData) {
            console.log('this.subpackage', this.subpackage)
            // 预审取preConsortiumBidding
            let consortiumBidding
            if (this.subpackage.checkType == '0') {
                consortiumBidding = this.subpackage.preConsortiumBidding
            } else if (this.subpackage.checkType == '1') {
                // 后审取consortiumBidding
                consortiumBidding = this.subpackage.consortiumBidding
            }
            // 不允许编辑联合体
            if (consortiumBidding != '1') {
                resultData.combination = '0'
                resultData.combinationName = ''
                this.$set(this.fields[5], 'disabled', true)
                this.$set(this.validateRules, 'combinationName', [])
            } else {
                // 允许编辑联合体
                resultData.combination = '1'
                this.$set(this.validateRules, 'combinationName', [
                    {
                        required: true,
                        message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRLlS_63c41428`, '联合体名称必填')
                    }
                ])
            }
            let setDisabledByProp = (prop, flag) => {
                for (let sub of this.fields) {
                    if (sub.field === prop) {
                        sub.disabled = flag
                        break
                    }
                }
            }
            let flag = consortiumBidding !== '1'
            setDisabledByProp('combination', flag)

            let validateFlag = consortiumBidding !== '1'
            for (let sub of this.fields) {
                if (sub.required == '1') {
                    this.validateRules[sub.field] = [
                        {
                            required: true,
                            message: `${sub.title}必填`
                        }
                    ]
                }
                if (sub.field == 'combination') {
                    this.validateRules[sub.field] = [
                        {
                            required: !validateFlag,
                            message: `${sub.title}必填`
                        }
                    ]
                }
                if (sub.field == 'contactsPhone') {
                    let checkContactsPhone = (item, cellValue) => {
                        if (cellValue === '') {
                            return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNyo_f6dbcb13`, '请输入号码'))
                        } else {
                            let reg = REGEXP.mobile
                            if (!reg.test(cellValue)) {
                                return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNiRyo_b9c72f7e`, '请输入正确号码'))
                            }
                        }
                    }
                    this.validateRules[sub.field] = [
                        {required: true, message: `${sub.title}必填`},
                        {validator: checkContactsPhone, trigger: 'change'}
                    ]
                }

            }
            for (let key in resultData) {
                if (key == 'supplierName') {
                    resultData[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
                if (key == 'toElsAccount') {
                    resultData[key] = resultData[key] || this.userInfo.elsAccount
                }
            }
        }
    },
    created () {
        this.userInfo = this.$ls.get(USER_INFO) || {}
        this.handleAfterDealSource(this.fromSourceData)
    }
}
</script>
