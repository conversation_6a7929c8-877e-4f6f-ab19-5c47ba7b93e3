// 使用腾讯地图
export default {
    init: ()=> {
        const key = 'RSJBZ-YFUHF-64UJW-JFR4T-65CJS-6FFEW'
        const tMapUrl = `https://map.qq.com/api/gljs?v=1.exp&libraries=tools,service&key=${key}&callback=onMapCallback`
        return new Promise((resolve, reject)=> {
            // 如果已加载，直接返回TMap
            if (typeof TMap !== 'undefined') {
                resolve(TMap)
                return true
            }
            // 地图异步加载回调处理
            window.onMapCallback = ()=> {
                resolve(TMap)
            }
            // 动态插入脚本
            let scriptNode = document.createElement('script')
            scriptNode.setAttribute('type', 'text/javascript')
            scriptNode.setAttribute('src', tMapUrl)
            document.body.appendChild(scriptNode)
        })
    }

}