<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      :tableCode="url.columns"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      v-show="!showEditPage && !showNoticeToDeliveryPage && !showDetailPage"
      :pageData="pageData"
      :url="url"
      :activeMethod="activeMethod"
    />
    <!-- <deliveryNoticeToDelivery-modal
      ref="modalForm"
      v-if="showNoticeToDeliveryPage"
      :current-edit-row="currentEditRow"
      @hide="hideNoticeToDeliveryPage"
      @ok="modalFormOk"
    /> -->
    <!-- 创建发货单 -->
    <SaleDeliveryNoticeEdit
      v-if="showNoticeToDeliveryPage"
      :current-edit-row="currentEditRow"
      :sourceData="sourceData"
      @hide="hideNoticeToDeliveryPage"
    />
    <SaleDeliveryNoticeByOrderDetail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetailPage"
    />
    <a-modal
      v-drag
      centered
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplate`, '选择模板')"
      :width="360"
      v-model="templateVisible"
      @ok="selectedTemplate"
    >
      <template slot="footer">
        <a-button
          key="back"
          @click="handleTemplateCancel"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_cancle`, '取消') }}
        </a-button>
        <a-button
          key="submit"
          type="primary"
          :loading="submitLoading"
          @click="selectedDeliveryTemplate"
        >
          {{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}
        </a-button>
      </template>
      <m-select
        v-model="templateNumber"
        :options="templateOpts"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseSelectTemplate`, '请选择模板')"
      />
    </a-modal>
    <Mmodel
      ref="childMmodel"
      @ok="confirmChanges"
      :tableHeaderData="tableHeaderData"
    />
  </div>
</template>
<script>
import DeliveryNoticeToDeliveryModal from './modules/DeliveryNoticeToDeliveryModal'
import SaleDeliveryNoticeByOrderDetail from './modules/SaleDeliveryNoticeByOrderDetail'
import SaleDeliveryNoticeEdit from './modules/SaleDeliveryNoticeEdit'
import { ListMixin } from '@comp/template/list/ListMixin'
import { httpAction, postAction } from '@/api/manage'
import { formatDate } from '@/utils/util.js'
import layIM from '@/utils/im/layIM.js'
import Mmodel from './modules/Mmodel'
import moment from 'moment'
export default {
  mixins: [ListMixin],
  components: {
    DeliveryNoticeToDeliveryModal,
    SaleDeliveryNoticeByOrderDetail,
    SaleDeliveryNoticeEdit,
    Mmodel
  },
  data() {
    return {
      sourceData: [],
      showEditPage: false,
      showDetailPage: false,
      showNoticeToDeliveryPage: false,
      currentEditRow: {},
      businessType: '',
      toElsAccount: '',
      submitLoading: false,
      templateVisible: false,
      nextOpt: true,
      currentRow: {},
      templateNumber: undefined,
      templateOpts: [],
      pageData: {
        button: [
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_createShipment`, '创建发货单'), icon: 'check-circle', clickFn: this.createDelivery, type: 'primary', authorityCode: 'order#saleDeliveryNoticeOrder:noticeToDelivery' },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FK_c764b`, '拒绝'), icon: 'plus', clickFn: this.publishDeliveryNotice, type: 'primary', authorityCode: 'order#saleDeliveryNoticeOrder:sendToPur' },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), icon: 'plus', clickFn: this.publishDeliveryNoticeConfirm, type: 'primary', authorityCode: 'order#saleDeliveryNoticeOrder:sendToPur' },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
            allow: () => {
              return this.btnInvalidAuth('order#saleDeliveryNoticeOrder:export')
            },
            icon: 'download',
            folded: false,
            clickFn: this.handleExportXls,
            authorityCode: 'order#saleDeliveryNoticeOrder:export'
          },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF }
        ],
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
            fieldName: 'keyWord',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterNoticeOrName`, '请输入通知单或采购方名称')
          },
          {
            type: 'checkbox',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qhS_145c7a5`, '可发货'),
            fieldName: 'avaliableDelivery',
            options: [{ description: '', key: null, text: '是', textI18nKey: 'i18n_title_yes', title: '是', value: '1' }]
          }
        ],
        form: {
          keyWord: ''
        },
        optColumnList: [
          { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail, authorityCode: 'order#saleDeliveryNoticeOrder:queryById' },
          { type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat }
        ],
        showOptColumn: true
      },
      url: {
        list: '/delivery/saleDeliveryNoticeOrder/list',
        sendToPurchase: '/delivery/saleDeliveryNoticeOrder/sendToPur',
        noticeToDelivery: '/delivery/saleDeliveryNoticeOrder/noticeToDeliveryWait',
        columns: 'saleDeliveryNoticeByOrderList',
        exportXlsUrl: '/delivery/saleDeliveryNoticeOrder/exportXls'
      },
      tableHeaderData: [],
      tabsList: []
    }
  },
  mounted() {
    this.serachCountTabs('/delivery/saleDeliveryNoticeOrder/counts')
  },
  methods: {
    showDetail(row) {
      ///row.id = row.headId
      this.currentEditRow = row
      this.currentEditRow.id = row.headId
      this.currentEditRow.busAccount = row.elsAccount
      this.showDetailPage = true
    },
    hideDetailPage() {
      this.showDetailPage = false
      this.$refs.listPage.loadData() // 刷新页面
    },
    handleChat(row) {
      let { id } = row
      let recordNumber = row.noticeNumber || id
      // 创建群聊
      layIM.creatGruopChat({ id, type: 'SaleDeliveryNoticeOrder', url: this.url || '', recordNumber })
    },
    hideNoticeToDeliveryPage() {
      this.showEditPage = false
      this.showNoticeToDeliveryPage = false
    },
    createDelivery() {
      this.serachTemplate('delivery')
    },
    serachTemplate(businessType) {
      this.currentEditRow = {}
      let selectedRows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (selectedRows.length <= 0) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSelectTheRowToConvert`, '请选择需要转换的行'))
        return
      } else {
        this.pageData.toElsAccount = selectedRows[0].toElsAccount
        for (let i = 0; i < selectedRows.length; i++) {
          let item = selectedRows[i]
          if (item.locked == '1') {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_dSeRt_2f9c7ea4`, '送货通知单') + '[' + item.noticeNumber + ']' + '当前已经锁定，暂时不能执行该操作')
            return
          }
          if (item.noticeStatus !== '3') {
            this.$message.warning('送货通知单号为' + '[' + item.noticeNumber + ']' + '单据的当前状态不是采购已确认，暂时不能转发货单')
            return
          }
          if (!item.remainQuantity || item.remainQuantity == 0) {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_dSeRt_2f9c7ea4`, '送货通知单') + '[' + item.noticeNumber + ']' + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityThatCanShippedMustGreaterThanZero`, '的可发货数量必须大于0'))
            return
          }
        }
        if (this.nextOpt) {
          this.pageData.businessType = businessType
          this.pageData.toElsAccount = selectedRows[0].toElsAccount
          this.openModal(selectedRows[0].toElsAccount)
        }
      }
    },
    openModal(elsAccount) {
      this.$refs.listPage.queryTemplateList(elsAccount).then((res) => {
        if (res.success) {
          if (res.result.length > 0) {
            let options = res.result.map((item) => {
              return {
                value: item.templateNumber,
                title: item.templateName,
                version: item.templateVersion,
                account: item.elsAccount
              }
            })
            this.templateOpts = options
            // 只有单个模板直接新建
            if (this.templateOpts && this.templateOpts.length === 1) {
              this.templateNumber = this.templateOpts[0].value
              this.selectedDeliveryTemplate()
            } else {
              // 有多个模板先选择在新建
              this.templateVisible = true
            }
          } else {
            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
          }
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    handleTemplateCancel() {
      this.templateVisible = false
    },
    selectedDeliveryTemplate() {
      if (this.templateNumber) {
        const that = this
        this.submitLoading = true
        let template = this.templateOpts.filter((item) => {
          return item.value == that.templateNumber
        })
        let params = {
          templateNumber: this.templateNumber,
          templateName: template[0].title,
          templateVersion: template[0].version,
          templateAccount: template[0].account,
          busAccount: '',
          saleDeliveryNoticeList: []
        }
        that.templateVisible = false
        that.submitLoading = false
        if (this.url.noticeToDelivery == '') {
          this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_forwardingOrderURLCannotEmpty`, '转发货单URL不能为空!'))
          return
        }
        params.saleDeliveryNoticeList = that.$refs.listPage.$refs.listGrid.getCheckboxRecords()
        params.busAccount = that.$refs.listPage.$refs.listGrid.getCheckboxRecords()[0].toElsAccount
        that.postUpdateData(this.url.noticeToDelivery, params)
      }
    },
    postUpdateData(url, row) {
      this.$refs.listPage.confirmLoading = true
      httpAction(url, row, 'post')
        .then((res) => {
          if (res.success) {
            this.sourceData = res.result || []
            this.currentEditRow = {
              templateNumber: res.result[0].templateNumber,
              templateAccount: res.result[0].templateAccount,
              templateVersion: res.result[0].templateVersion,
              busAccount: res.result[0].busAccount,
              elsAccount: res.result[0].elsAccount,
              noticeNumber: res.result[0].deliveryNumber
            }
            this.showNoticeToDeliveryPage = true
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.confirmLoading = false
        })
    },
    // 拒绝
    publishDeliveryNotice() {
      const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (!rows.length) {
        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`, '请勾选需要发送数据'))
        return
      }
      for (let i = 0; i < rows.length; i++) {
        let item = rows[i]
        //被锁定的行不能执行任何操作
        if (item.locked == '1') {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,已经被锁定，暂时不能执行任何操作!'))
          return
        }
        if (item.noticeStatus != '1') {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cdSeRtAPzExKoRdXRLWPKxOtkrcW_f8dd5e38`, '行送货通知单当前状态不是待供应商确认，暂时不能操作该行!'))
          return
        }
      }
      rows.map((item) => {
        item.fbk1 = '0'
        item.replyQuantity = item.requireQuantity
        return item
      })

      this.tableHeaderData = JSON.parse(JSON.stringify(rows))
      // 调用弹窗组件方法
      this.$refs.childMmodel.open()
    },
    // 确认
    publishDeliveryNoticeConfirm() {
      const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
      if (!rows.length) {
        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseCheckNeedSnedData`, '请勾选需要发送数据'))
        return
      }
      for (let i = 0; i < rows.length; i++) {
        let item = rows[i]
        //被锁定的行不能执行任何操作
        if (item.locked == '1') {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineHasBeenLockedNoPerationCanBePerformedTemporarily`, '行,已经被锁定，暂时不能执行任何操作!'))
          return
        }
        if (item.noticeStatus != '1') {
          this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第') + (i + 1) + this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cdSeRtAPzExKoRdXRLWPKxOtkrcW_f8dd5e38`, '行送货通知单当前状态不是待供应商确认，暂时不能操作该行!'))
          return
        }
      }
      console.log(moment().format('YYYY-MM-DD'))
      // 在这里加二次确认前的校验
      const callback = () => {
        const url = ''
        const params = rows.map((item) => {
          item.fbk1 = '1'
          // item.replyDate = moment().format('YYYY-MM-DD')
          item.replyQuantity = item.requireQuantity
          if (item._id) {
            delete item.id
          }
          delete item._id
          return item
        })
        console.log('params', params)
        // 在这里加所需的校验

        postAction(this.url.sendToPurchase, params).then((res) => {
          const type = res.success ? 'success' : 'error'
          this.$message[type](res.message)
          if (type == 'success') {
            this.$refs.listPage.loadData() // 刷新页面
          }
        })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确定'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WKQRLRic_e7c8ee94`, '是否确认勾选行？'),
        onOk() {
          callback && callback()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    confirmChanges(rows) {
      // 在这里加二次确认前的校验
      const callback = () => {
        const url = ''
        const params = rows.map((item) => {
          // item.fbk1 = '2'
          if (item._id) {
            delete item.id
          }
          delete item._id
          return item
        })
        // 在这里加所需的校验
        postAction(this.url.sendToPurchase, params).then((res) => {
          const type = res.success ? 'success' : 'error'
          this.$message[type](res.message)
          if (type == 'success') {
            this.$refs.listPage.loadData() // 刷新页面
          }
        })
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_FK_c764b`, '拒绝'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLFKRic_66a10bc9`, '是否确认拒绝勾选行？'),
        onOk() {
          callback && callback()
        },
        onCancel() {
          console.log('Cancel')
        }
      })
    },
    activeMethod({ row, column }) {
      if (row.fbk1 === '1') {
        row.replyDate = row.replyDate ? row.replyDate : formatDate(new Date().getTime(), 'yyyy-MM-dd')
        row.replyQuantity = row.replyQuantity ? row.replyQuantity : row.requireQuantity
      }
      if (row.fbk1 === '0' && (column.property === 'replyQuantity' || column.property === 'replyDate' || column.property === 'responsibleParty' || column.property === 'responsibleReason' || column.property === 'supplierRemark')) {
        return true
      }
      if (row.fbk1 === '1' && (column.property === 'replyQuantity' || column.property === 'replyDate' || column.property === 'supplierRemark')) {
        return true
      }
      return false
    },
    handleExportXls() {
      this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ItdSeRt_dec1c291`, '订单送货通知单'))
    }
  }
  // mounted(){
  //     // 设置编辑行模式
  //     this.$refs.listPage.$refs.listGrid.editConfig.mode = 'row'
  // }
}
</script>
