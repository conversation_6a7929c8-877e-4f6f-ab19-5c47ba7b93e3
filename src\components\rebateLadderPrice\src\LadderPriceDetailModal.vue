<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    centered
    :width="800"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderSetting`, '阶梯设置')"
    :footer="null">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="ladderQuantity"
              show-overflow
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '阶梯信息')"
              :columns="tableHeaderColumn"
              :data="tableData"
              :edit-config="{trigger: 'click', mode: 'cell'}"
              :radio-config="{highlight: true, reserve: true}"
            >
              <template #role_computeMode="{ row }">
                <a-select 
                  v-model="row.type"
                  defaultValue="请选择"
                  :dropdownMatchSelectWidth="false"
                  @change="selectChanges(row)"
                  allow-clear
                  disabled
                >
                  <a-select-option 
                    v-for="item in sexList1" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
export default {
    name: 'SetLadderModal',
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        isEmit: {
            type: Boolean,
            default: false
        },
        form: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            tableData: [],
            headObj: {},
            ladderQuantity: '',
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            currentRow: null,
            tableHeaderData: [],
            sexList1: [
                { value: 'fix', label: '固定值' },
                { value: 'rate', label: '比例值%' },
                { value: 'union', label: '每单位返' }
            ],
            tableHeaderColumn: [
                // { type: 'radio', width: 40, align: 'center'},
                { field: 'ladder', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级'), width: 170},
                { field: 'ladderQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderCount`, '阶梯数量'), width: 100},
                { field: 'type', title: '计算方式', width: 150, slots: {default: 'role_computeMode', edit: 'role_computeMode' } },
                { field: 'fix', title: '固定值', width: 100}, 
                { field: 'rate', title: '比例值%'},   
                { field: 'union', title: '每单位返'} 
            ]
        }
    },
    mounted () {
    },
    methods: {
        open (row, curFieldValue) {
            let that = this
            this.madalVisible = true
            this.currentRow = row
            let ladderJson = curFieldValue || row.ladderPriceJson || ''
            if(ladderJson){
                const list = JSON.parse(ladderJson)
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData(list)
                })
            }else{
                this.$nextTick(() => {
                    that.$refs.headerGrid.loadData([])
                })
            }
        },
        priceChangeEvent1 (rows, rowIndex, actKey) {
            if(!rows.row.type) return
            if(rows.row.type !==actKey){
                setTimeout(()=>{
                    rows.row[actKey] =''
                })
            }
        },
        selectChanges (row){
            if(!row.type) return
            let arr= ['fix', 'rate', 'union']
            arr.forEach(element => {
                if(element != row.type){
                    row[element] = ''
                }  
            })
        },
        goBack () {
            this.$emit('hide')
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 1000px;
            background-color: #fff
        }
    }
</style>