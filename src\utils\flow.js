import { ajaxFindDictItems } from '@/api/api'
import { USER_INFO } from '@/store/mutation-types'
import moment from 'moment'
import Vue from 'vue'

const purchaseElsAccount = '********'
const flowTitleConfigDictCode = 'config_template_flow_title'
const REGEX = /\$\{([A-Za-z0-9_]+)\}/g

/**
 * 获取记录在字典的模板流程标题配置
 * 
 * @returns
 */
function queryDictItemList() {
  return new Promise((resolve, reject) => {
    ajaxFindDictItems({
      busAccount: purchaseElsAccount,
      dictCode: flowTitleConfigDictCode
    }).then((res) => {
      if (res.success) {
        const result = res.result || []
        let dictItemList = []
        result.forEach(item => {
          dictItemList.push({
            templateNumber: item.title,
            configVal: item.value
          })
        })
        resolve(dictItemList)
      }
    }).catch(err => {
      console.error('获取字典异常', err)
      resolve([])
    })
  })
}

/**
 * 解析配置
 * 
 * @param {*} configVal 
 */
function convertByConfig(configVal, bizData) {
  let title = configVal
  if (!title || title == null) {
    return null;
  }

  if (!Vue.ls.get(USER_INFO)) {
    console.log("未找到登录信息")
    return null;
  }

  // 发起日期
  title = title.replaceAll('${startDay}', moment().format('YYYY-MM-DD'));
  // 发起人姓名
  title = title.replaceAll('${startUserName}', Vue.ls.get(USER_INFO).realname);

  let matchArray = title.matchAll(REGEX)
  if (matchArray && matchArray != null && matchArray.length != 0) {
    matchArray.forEach(matchItem => {
      let matchStr = matchItem[0];
      let bizParamName = matchStr.replace('${', '').replace('}', '')

      let bizParamVal = bizData[bizParamName];
      bizParamVal = !bizParamVal || bizParamVal == null ? '' : bizParamVal

      title = title.replaceAll(matchStr, bizParamVal)
    })
  }

  return title;
}

const FlowUtil = {

  /**
   * 根据模板编码生成流程标题
   * 
   * @param {*} bizData 业务数据对象(需包含业务模板编码字段)
   * @param {*} defaultTitle 默认标题, 即原始逻辑生成的标题
   * @return 流程标题
   */
  convertFlowTitle: async function (bizData, defaultTitle) {
    if (!defaultTitle || defaultTitle == null) {
      defaultTitle = ''
    }
    try {
      if (!bizData || bizData == null) {
        return defaultTitle
      }

      let templateNumber = bizData['templateNumber']
      if (!templateNumber || templateNumber == null || templateNumber == '') {
        return defaultTitle
      }

      let dictItemList = await queryDictItemList();
      if (!dictItemList || dictItemList == null || dictItemList.length == 0) {
        return defaultTitle
      }

      let configItem = dictItemList.find(item => item.templateNumber == templateNumber);
      if (!configItem || configItem == null ||
        !configItem.configVal || configItem.configVal == null || configItem.configVal == '') {
        return defaultTitle
      }

      let title = convertByConfig(configItem.configVal, bizData)
      if (!title || title == null) {
        return defaultTitle
      }

      return title
    } catch (e) {
      console.error('根据配置生成流程标题异常: ' + templateNumber, e)
    }

    return defaultTitle
  }

}

export {
  FlowUtil
}