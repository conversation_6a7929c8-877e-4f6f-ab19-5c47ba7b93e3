<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <div
        class="container"
        :style="style">
        <div v-if="show">
          <finalQuoteList 
            :pageStatus="'edit'"
            :canfinalQuoteList="canfinalQuoteList"
            @back="back"
            determineType="total"
            :show="showfinalQuoteList"
            :currentEditRow="resultData"
            v-if="showfinalQuoteList" />
          <div v-else>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
            </titleTrtl>
            <vxe-grid
              v-bind="gridConfig"
              ref="table"
              :data="tableData"
              :columns="tableColumns"
              show-overflow="title" >
            </vxe-grid>
          </div>
        </div>
      </div>
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"/>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import flowViewModal from '@comp/flowView/flowView'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import finalQuoteList from './finalQuoteList'
export default {
    mixins: [tableMixins],
    name: 'DetermineTheWinner',
    components: {
        titleTrtl,
        ContentHeader,
        flowViewModal,
        finalQuoteList
    },
    props: {
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            show: false,
            flag: false,
            confirmLoading: false,
            checkEnable: false,
            canfinalQuoteList: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    field: 'scopeSort',
                    width: '80'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQsBL_c7668749`, '是否中标人'),
                    'field': 'affirm',
                    width: '160',
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox v-model={row[column.property]} disabled={ this.pageStatus == 'detail' && this.formData.status && this.formData.status != '0'}></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            showfinalQuoteList: false,
            height: 0,
            currentEditRow: {},
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningFinalCandidate',
                add: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/add',
                edit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/edit',
                submit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/submit',
                cancelAudit: '/a1bpmn/audit/api/cancel',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryInfo: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInfoBySubpackage'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage',
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        btns () {
            let btn = []
            // 刚进去或者保存过后为新建状态0，展示保存发布按钮
            if (this.formData.status == '0' || !this.formData.status) {
                this.canfinalQuoteList = true
                if (!this.showfinalQuoteList) {
                    btn = [
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refresh`, '刷新'), type: 'primary', click: this.getData },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                    ]
                    //评标方式 0-全部、1-线上、2-线下*/
                    let { evaluationType} = this.subPackageRow
                    if (evaluationType == '1') {
                        btn.unshift(
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAessu_119c780`, '发起最终报价'), type: 'primary', click: this.finalQuote }
                        )
                    }
                }
            }
            //点击发布后已发布状态status == 1以及审批中状态aduitStatus == 1，
            if(this.formData.status == '1' && this.formData.auditStatus == '1'){
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'), type: 'primary', click: this.withdraw }
                ]
            }
            if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
                btn.push(
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow }
                )
            }
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
            return btn
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.formData.status == '0' || !this.formData.status) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        async save (callback = null) {
            let params = Object.assign({}, this.formData)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            params['bidWinningAffirmPriceItemVoList'] = this.tableData.map(item => {
                item.affirm = item.affirm ? 1 : 0
                return item
            })
            // 拼接数据
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            let saveFlag = '0'
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.flag=true
                    saveFlag  = '1'
                    let {bidWinningAffirmPriceItemVoList = [], ...others} = res.result || {}
                    this.formData = others
                    bidWinningAffirmPriceItemVoList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                        })
                        item.affirm = item.affirm == '1' ? true : false
                    })
                    this.tableData = bidWinningAffirmPriceItemVoList
                }
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
                if (saveFlag == '1') {
                    this.getData()
                }
            })
        },
        publish () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    that.submit()
                }
            })
        },
        async submit () {
            let params = Object.assign({}, this.formData)
            params['bidWinningAffirmPriceItemVoList'] = this.tableData.map(item => {
                item.affirm = item.affirm ? 1 : 0
                console.log(item)
                return item
            })
            console.log(params)
            this.confirmLoading = true
            postAction(this.url.submit, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
            })
        },
        withdraw (){
            let that = this
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData('/a1bpmn/audit/api/cancel', params)
                }
            })
        },
        showFlow (){
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getData()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                // this.$emit('resetCurrentSubPackage')
                this.resetCurrentSubPackage()
                this.confirmLoading = false
            })
        },
        getData () {
            this.$emit('reset')
        },
        getColumn () {
            let params = {
                subpackageId: this.subId
            }
            this.confirmLoading = true
            getAction(this.url.queryPrice, params).then(res => {
                if (res.code == 200 && res.result) {
                    const resultData = res.result
                    let columns = []
                    resultData.forEach(data => {
                        let obj = {
                            title: data.title,
                            children: []
                        }
                        let columnChildren = []
                        data.quoteColumnList.forEach(column => {
                            column['field'] = `${column['field']}_${data['id']}`
                            columnChildren.push(column)
                        })
                        obj.children = columnChildren
                        columns.push(obj)
                    })
                    
                    this.tableColumns = this.tableColumns.filter(column => {
                        if (!column.hasOwnProperty('children')) {
                            return column
                        }
                    })
                    // if(this.tableColumns.length == 3){
                    this.tableColumns.splice(2, 0, ...columns)
                    // }
                }
                this.confirmLoading = false
            }, () => {
                this.confirmLoading = false
            })
        },
        init (data) {
            this.show = false
            this.confirmLoading = true
            this.height = document.documentElement.clientHeight
            let {bidWinningAffirmPriceItemVoList = [], ...others} = data
            this.formData = others
            bidWinningAffirmPriceItemVoList && bidWinningAffirmPriceItemVoList.forEach(item => {
                item.saleQuoteColumnVOS.forEach(vos => {
                    item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                    item.affirm = item.affirm == '1' ? true : false
                })
                item.affirm = item.affirm == '1' ? true : false
            })
            this.tableData = bidWinningAffirmPriceItemVoList
            //评标方式 0-全部、1-线上、2-线下*/
            let {evaluationType} = this.subPackageRow
            if (evaluationType == '1') {
                this.getColumn()
            } else {
                let hasEvaPrice = false
                this.tableColumns.forEach(item=>{
                    if(item.field == 'quote') hasEvaPrice = true
                })
                if(!hasEvaPrice){
                    this.tableColumns.splice(1, 0, {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                        field: 'quote'
                    })
                }
            }
            setTimeout(() => {
                this.confirmLoading = false
                this.show = true
            }, 100)
        },
        finalQuote () {
            this.showfinalQuoteList = true
        },
        back () {
            this.showfinalQuoteList = false
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{
    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
:deep(.vxe-table--render-wrapper) {
    max-height: 550px;
}
</style>




