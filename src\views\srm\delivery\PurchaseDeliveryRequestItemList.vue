<!--
 * @Author: gongzhirong
 * @Date: 2022-06-14 10:07:27
 * @LastEditTime: 2022-08-09 16:23:14
 * @LastEditors: gongzhirong
 * @Description: 
-->
<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
    />

    <delivery-request-item-allot
      v-if="deliveryRequestItemAllotShow"
      :current-edit-row="currentEditRow"
      @close="deliveryRequestItemAllotShow = false"
      @success-cb="modalFormOk"
      @reloadColunm="reloadModalColunm"
    />

    <!-- 转送货通知单 -->
    <delivery-request-items-allot
      v-if="deliveryRequestItemsAllotShow"
      :current-edit-rows="currentEditRows"
      @close="deliveryRequestItemsAllotShow = false"
      @success-cb="modalFormOk"
      @reloadColunm="reloadModalColunm"
    />
  </div>
</template>
<script>
import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction,postAction } from '@/api/manage'

const DeliveryRequestItemAllot = () => import('./components/deliveryRequestItemAllot')
const DeliveryRequestItemsAllot = () => import('./components/deliveryRequestItemsAllot')

export default {
  mixins: [ListMixin],
  components: {
    DeliveryRequestItemAllot,
    DeliveryRequestItemsAllot
  },
  data() {
    return {
      showEditPage: false,
      deliveryRequestItemAllotShow: false,
      deliveryRequestItemsAllotShow: false,
      pageData: {
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
            fieldName: 'keyWord',
            placeholder: '请输入关键字'
          }
        ],
        form: {
          keyWord: ''
        },
        button: [
          { label: '转送货通知单', type: 'primary', clickFn: this.toDeliveryNotice },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', type: 'primary', clickFn: this.handleExportXls },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns },
          { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_zRRl_2ef12bd0`, '批量关闭'), icon: 'close', type: 'primary', clickFn: this.handleCloseSingles },
        ],
        optColumnList: [
          { type: 'view', title: '分配', clickFn: this.allot, allow: this.allowAllot },
          { type: 'view', title: '关闭', clickFn: this.close, allow: this.allowClose },
          { type: 'edit', title: '退回', clickFn: this.sendBack, allow: this.allowRefund },
          { type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operaRecord`, '记录'), clickFn: this.handleRecord }
        ],
        optColumnWidth: 200
      },
      url: {
        list: '/delivery/purchaseDeliveryRequestItem/list',
        transfer: '/delivery/purchaseDeliveryRequestItem/tacticsTransfer',
        add: '/delivery/purchaseDeliveryRequestItem/add',
        delete: '/delivery/purchaseDeliveryRequestItem/delete',
        deleteBatch: '/delivery/purchaseDeliveryRequestItem/deleteBatch',
        importExcelUrl: 'delivery/purchaseDeliveryRequestItem/importExcel',
        changeVersion: '/delivery/purchaseDeliveryRequestItem/upgradeVersion',
        columns: 'PurchaseDeliveryRequestItemList',
        exportXlsUrl: '/delivery/purchaseDeliveryRequestItem/exportXls',
        sendBackUrl: '',
        closeUrl: '/delivery/purchaseDeliveryRequestItem/close'
      },

      currentEditRows: []
    }
  },
  methods: {
    /**
     * @description: 退回
     */
    sendBack({ id }) {
      this.$confirm({
        title: '确认退回',
        content: '是否退回已选数据',
        okText: '确定',
        okType: 'danger',
        cancelText: '关闭',
        onOk: async () => {
          try {
            this.$refs.listPage.loading = true
            const params = { id }
            const res = await getAction('/delivery/purchaseDeliveryRequestItem/refund', params)
            if (res.success) {
              this.modalFormOk()
              this.$message.success(res.message)
            } else {
              this.$message.error(res.message)
            }
          } catch (e) {
            console.log(e)
          } finally {
            this.$refs.listPage.loading = false
          }
        },
        onCancel() {}
      })
    },

    allowRefund(row) {
      if (row.itemStatus === '1' && !this.btnInvalidAuth('purchaseDeliveryRequestItem:refund')) {
        return false
      } else {
        return true
      }
    },

    allowAllot(row) {
      if (!this.btnInvalidAuth('purchaseDeliveryRequestItem:allot')) {
        if (row.allocatableQuantity <= 0 || row.itemStatus == '5' || row.itemStatus == '4') {
          return true
        }
      }
      return false
    },
    allowClose(row) {
      if (!this.btnInvalidAuth('purchaseDeliveryRequestItem:close')) {
        if (row.itemStatus == '5' || row.itemStatus == '4' || row.itemStatus == '3') {
          return true
        }
      }
      return false
    },
    //批量关闭
    handleCloseSingles() {
        const that = this
        const rows = this.$refs.listPage.$refs.listGrid.getCheckboxRecords()
        console.log(rows,'当前勾选数据信息测试')
        if (!rows.length) {
            this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#`, '请勾选需要关闭的数据'))
            return
        }
        for(let i=0;i<rows.length;i++){
            let item = rows[i]
            if(item.itemStatus=='3') {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+'['+(i+1)+']行当前已分配状态，不能关闭操作!')
                return
            }else if(item.itemStatus=='4'){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+'['+(i+1)+']行当前已退回状态，不能关闭操作!')
                return
            }else if(item.itemStatus=='5'){
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectedPaged`, '选择的第')+'['+(i+1)+']行当前已关闭状态，不能关闭操作!')
                return
            }
        }
        this.$confirm({
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_zRRl_2ef12bd0`, '批量关闭'),
            content: this.$srmI18n(`${this.$getLangAccount()}#`, '是否确定关闭选中数据,关闭后不可再进行数量分配'),
            onOk () {
                postAction('/delivery/purchaseDeliveryRequestItem/batchClose', rows).then(res => {
                    const type = res.success ? 'success' : 'error'
                    that.$message[type](res.message)
                    that.$refs.listPage.loadData() // 刷新页面
                })
            },
            onCancel () {
                console.log('Cancel')
            }
        })
    },
      /**
     * @description: 分配
     */
    allot(row) {
      this.currentEditRow = row
      this.deliveryRequestItemAllotShow = true
    },
    close(row) {
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#`, '关闭操作'),
        content: this.$srmI18n(`${this.$getLangAccount()}#`, '关闭后不可再进行数量分配，是否确认关闭?'),
        onOk() {
          getAction('/delivery/purchaseDeliveryRequestItem/close', row).then((res) => {
            if (res.success) {
              that.modalFormOk()
              that.$message.success(res.message)
            } else {
              that.$message.warning(res.message)
            }
          })
        },
        onCancel() {}
      })
    },
    // 转送货通知单
    toDeliveryNotice() {
      let listGrid = this.$refs.listPage.$refs.listGrid
      let checkList = listGrid.getCheckboxRecords()
      console.log(checkList)

      if (checkList.length === 0) return this.$message.warning('请选择送货申请单！')

      let flag = false
      for (var i = 0; i < checkList.length; i++) {
        let listItem = checkList[i]
        if (listItem.allocatableQuantity == 0) {
          flag = true
          this.$message.warning(`送货申请单[${listItem.requestNumber}]行号[${listItem.itemNumber}]的可分配数量为0!`)
        }
      }

      if (flag) return

      this.currentEditRows = checkList
      this.deliveryRequestItemsAllotShow = true
    },

    // 分配创建送货通知单成功回调
    modalFormOk(row) {
      // 跳转到送货通知单编辑页面
      if (row && row.id) {
        localStorage.setItem('toPurchaseDeliveryNoticeByOrderList', JSON.stringify({ id: row.id }))
        this.$router.push({
          path: '/srm/delivery/PurchaseDeliveryNoticeByOrderList'
        })
      }
      // 新增/修改 成功时，重载列表
      this.$refs.listPage.loadData()
    },

    // 刷新转送货通知单列
    reloadModalColunm(type) {
      console.log(123, type)
      let key = type == 'multiple' ? 'deliveryRequestItemsAllotShow' : 'deliveryRequestItemAllotShow'
      this[key] = false
      let st = setTimeout(() => {
        this[key] = true
        clearTimeout(st)
        st = null
      }, 200)
    }
  }
}
</script>
