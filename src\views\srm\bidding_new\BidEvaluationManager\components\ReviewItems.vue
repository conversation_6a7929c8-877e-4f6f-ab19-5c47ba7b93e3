<template>
  <!-- 资格审查 -->
  <div class="ReviewItems">
    <a-spin :spinning="confirmLoading">
      <div class="review-items-top">
        <a-row>
          <a-col :span="12">
            <span>{{ currentRow.title }}</span>
          </a-col>
          <a-col 
            :span="12" 
            style="text-align: right;">
            <!-- v-if="currentRow['editStatus']" -->
            <a-button
              type="primary"
              @click="preView">{{ $srmI18n(`${$getLangAccount()}#i18n_field_QIUB_2f624453`, '文件预览') }}</a-button>
            <a-button
              v-if="currentRow['editStatus']"
              type="primary"
              @click="saveData('')"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_save`, '保存') }}</a-button>
            <a-button 
              type="primary"
              v-if="currentRow['editStatus']"
              @click="publishData"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_submit`, '提交') }}</a-button>
            <a-button 
              @click="goBack"
              style="margin-left: 10px;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
          </a-col>
        </a-row>
      </div>
      <div class="review-items-grid">
        <div
          v-if="!currentRow.summary"
          class="grid-ul">
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_seq`, '序号') }}</li>
            <li
              v-for="(item, index) in reviewList"
              :key="item.regulationId"
            >{{ index + 1 }}</li>
            <li v-if="currentRow.summary"></li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例') }}</li>
            <li
              v-for="review in reviewList"
              :key="review.regulationId">
              <overflowspan>{{ review.regulationName }}</overflowspan>
            </li>
            <li v-if="currentRow.summary">{{ $srmI18n(`${$getLangAccount()}#i18n_field_yV_fe747`, '结论') }}</li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述') }}</li>
            <li
              v-for="review in reviewList"
              :key="review.regulationId + review.regulationName">
              <overflowspan>
                {{ regulationNameMap[review.regulationId] }}
              </overflowspan>
            </li>
            <li v-if="currentRow.summary"></li>
          </ul>
          <ul 
            class="ul" 
            v-for="supplier in resultData.supplierList" 
            :key="supplier.supplierAccount">
            <li v-if="supplier.invalid == '1'">
              <overflowspan>
                {{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已否决') }})
              </overflowspan>
            </li>
            <li v-else>
              <overflowspan>
                {{ supplier.supplierName }}
              </overflowspan>
            </li>
            <template v-if="resultData.evaResultListMap">
              <template v-if="resultData.evaResultListMap[supplier.supplierAccount] " >
                <li
                  :key="review.regulationId"
                  class="warning-tip-p"
                  v-for="review in resultData.evaResultListMap[supplier.supplierAccount]">
                  <a-radio-group 
                    :disabled="!currentRow['editStatus']"
                    v-model="review.result"
                    :options="options" />
                  <span
                    v-if="review.result == null && tipStatus"
                    class="warning-tip">{{ $srmI18n(`${$getLangAccount()}#i18n_field_ridli_f7201fd9`, '该选项必选') }}</span>
                  <a-icon
                    type="edit"
                    theme="twoTone"
                    @click="showLeaderOpinion(review)"/>
                </li>
              </template>
              <template v-else>
                <li
                  v-for="review in reviewList"
                  :key="review.regulationId"><span>/</span></li>
              </template>
            </template>
            
          </ul>
        </div>
        <!-- 汇总进入显示该模块 -->
        <div
          class="grid-ul"
          v-else>
          <ul>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_title_seq`, '序号') }}</li>
            <li
              v-for="(item, index) in resultListSummary"
              :key="item.id">{{ index + 1 }}</li>
            <li></li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例') }}</li>
            <li
              v-for="resultList in resultListSummary"
              :key="resultList.id">
              <overflowspan>
                {{ resultList.name }}
              </overflowspan>
            </li>
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_yV_fe747`, '结论') }}</li>
          </ul>
          <ul class="ul">
            <li>{{ $srmI18n(`${$getLangAccount()}#i18n_field_TvMW_30358fcb`, '条例描述') }}</li>
            <template v-if="resultListSummary.length > 0">
              <li
                v-for="resultList in resultListSummary"
                :key="resultList.id + resultList.name">
                <overflowspan>
                  {{ regulationNameMap[resultList.id] }}
                </overflowspan>
              </li>
              <li></li>
            </template>
            <li v-else></li>
          </ul>
          <ul 
            class="ul" 
            v-for="supplier in supplierListSummary" 
            :key="supplier.supplierAccount">
            <li v-if="supplier.invalid == '1'">{{ supplier.supplierName }}({{ $srmI18n(`${$getLangAccount()}#i18n_alert_IuB_16c86ba`, '已否决') }})</li>
            <li v-else>{{ supplier.supplierName }}</li>
            <li
              v-for="resultList in resultListSummary"
              :key="resultList.id">
              <a-radio-group 
                :disabled="!currentRow['editStatus']"
                v-model="resultList[supplier.supplierAccount]"
                :options="options" />
            </li>
            <li v-if="currentRow.summary && supplier.evaGroupResult == '1'">
              <span
                style="cursor: pointer; color: blue;" 
                @click="summaryFn(supplier.supplierAccount, supplier.supplierName)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Bn_f3da2`, '符合') }}</span>
            </li>
            <li v-else-if="currentRow.summary">
              <span
                style="cursor: pointer; color: blue;"
                @click="summaryFn(supplier.supplierAccount, supplier.supplierName)">{{ $srmI18n(`${$getLangAccount()}#i18n_field_xBn_1343c6f`, '不符合') }}</span>
            </li>
          </ul>
        </div>
      </div>

      <!-- 汇总详情弹窗 -->
      <a-modal
        v-drag     
        v-model="visible" 
        :width="800"
        :title="supplierName" 
        :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭')">
        <template slot="footer">
          <a-button @click="() => {this.visible=false}">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Rl_a72da`, '关闭') }}</a-button>
        </template>
        <vxe-grid
          v-bind="gridOptions">
          <template #default_result="{row, column}">
            <span v-if="row[column['property']] == '1'">{{ $srmI18n(`${$getLangAccount()}#i18n_field_Bn_f3da2`, '符合') }}</span>
            <span v-else>{{ $srmI18n(`${$getLangAccount()}#i18n_field_xBn_1343c6f`, '不符合') }}</span>
          </template>
        </vxe-grid>
      </a-modal>
    </a-spin>

    <!-- 文件预览 -->
    <PreView 
      ref="pv" 
      :currentRow="currentRow" 
      :supplierList="supplierList"></PreView>
    <LeaderOpinionModal
      ref="LeaderOpinionModal"
      :pageStatus="!currentRow['editStatus'] ? 'detail' : 'edit'"
      @success="LeaderOpinionSuccess"
      typeNum="noLeader"/>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'
import { gridOptionsMixin } from '../public/gridOptionsMixin.js'
import PreView from '../public/preView'
import LeaderOpinionModal from './LeaderOpinionModal'
import overflowspan from './overflowspan'
export default {
    mixins: [gridOptionsMixin],
    components: {
        PreView,
        LeaderOpinionModal,
        overflowspan
    },
    props: {
        currentRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        headParams () {
            let {checkType, currentStep, processType } = this.currentRow
            let xNodeId = `${checkType}_${processType}_${currentStep}`
            return {xNodeId}
        }
        // supplierList () {
        //     return this.resultData.supplierList || []
        // }
    },
    watch: {
        resultData (val) {
            this.supplierList = val.supplierList
            console.log('watch', val)
        }
    },
    data () {
        return {
            supplierList: [],
            supplierListSummary: [],
            resultListSummary: [],
            confirmLoading: false,
            tipStatus: false,
            options: [
                {value: '1', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bn_f3da2`, '符合')},
                {value: '0', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xBn_1343c6f`, '不符合')}
            ],
            resultData: {},
            reviewList: [],
            visible: false,
            gridOptions: {
                border: true,
                resizable: true,
                showOverflow: true,
                height: 300,
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                align: 'center',
                toolbarConfig: {
                    enabled: false // 禁用自定义工具
                },
                columns: [],
                data: []
            },
            supplierName: '',
            regulationNameMap: {},
            currentItem: {}
        }
    },
    created () {
        this.gridOptions = Object.assign(this.gridOptions, this.gridConfig)
        this.getData()
    },
    methods: {
        preView () { // 文件预览
            const pv = this.$refs.pv
            pv.queryFiel()
        },
        screenColumnsData (evaList) {
            let old = [], 
                columns = [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), type: 'seq', width: 50},
                    {field: 'regulationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUTv_40ed7967`, '评审条例')}
                ]
            console.log('2!!', evaList)
            evaList.forEach((item, index) => {
                if (index == 0) {
                    const obj = {field: item.judgesTaskHeadId, title: item.judgesName, slots: {default: 'default_result'}}
                    columns.push(obj)
                    old.push(item.judgesTaskHeadId) // 数据只保存一次
                } else {
                    if (!old.includes(item.judgesTaskHeadId)) {
                        const obj = {field: item.judgesTaskHeadId, title: item.judgesName, slots: {default: 'default_result'}}
                        columns.push(obj)
                        old.push(item.judgesTaskHeadId) // 数据只保存一次
                    }
                }
            })
            this.gridOptions.columns = columns
            this.screenData(evaList, old)
        },
        screenData (evaList, old) {
            let headIds = [], 
                regulationId = [], // 记录去重的数据
                fields = []
            evaList.forEach((item, index) => {
                if (regulationId.includes(item.regulationId)) { // 评审条例 ID 存在时的操作
                    fields.forEach(field => {
                        if (field.regulationId == item.regulationId) { // 判断条例id是否一致
                            field[item.judgesTaskHeadId] = item.result // 在原有的数据新增其他专家的评审结果
                        }
                    })
                } else { // 评审条例 ID 不存在时的操作
                    for (let i=0; i<old.length; i++) {
                        if (old[i] == item.judgesTaskHeadId) {
                            const obj = {
                                regulationName: item.regulationName, 
                                regulationId: item.regulationId
                            }
                            obj[old[i]] = item.result
                            fields.push(obj)
                        }
                    }
                    regulationId.push(item.regulationId)
                }
            })
            this.gridOptions.data = fields
            console.log('fields', fields)
        },
        // 汇总详情方法 
        summaryFn (supplierAccount, supplierName) {
            this.supplierName = supplierName
            const {evaGroupId, evaInfoId} = this.currentRow
            const params = {
                evaGroupId: evaGroupId,
                evaInfoId: evaInfoId,
                supplierAccount: supplierAccount.split('_')[0] || ''
            }
            this.visible = true
            let columns = [], data = []
            getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierByEvaGroupId', params).then(res => {
                if (res.code == '200' && res.result) {
                    let {evaRegulationResultList} = res.result
                    console.log(evaRegulationResultList || [])
                    this.screenColumnsData(evaRegulationResultList || [])
                    // this.screenData(evaRegulationResultList)
                }
            })
        },
        // 获取评审项的数据 ?evaGroupId=1518091956243259394
        getData () {
            const {evaGroupId, id, summary, evaInfoId} = this.currentRow
            let params = {
                evaGroupId: evaGroupId
            }
            !summary && (() => {params['judgesTaskItemId'] = id})()
            summary && (() => {params['evaInfoId'] = evaInfoId})()
            this.confirmLoading = true
            this.regulationNameMap = {}
            if (!summary) { // 不是从汇总按钮进入的
                getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySupplierEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                    if (res.code == 200) {
                        const {result = {} } = res
                        const {evaResultListMap = {}, evaluationGroupVO: {tenderEvaluationTemplateRegulationInfoList = []}} = result
                        this.resultData = result
                        if (evaResultListMap) {
                            this.reviewList = Object.keys(evaResultListMap).length > 0 ? evaResultListMap[Object.keys(evaResultListMap)[0]] : []
                            console.log('this.reviewList', this.reviewList)
                        }
                        if (tenderEvaluationTemplateRegulationInfoList?.length > 0) {
                            for (let k of tenderEvaluationTemplateRegulationInfoList) {
                                this.regulationNameMap[k.id] = k.regulationDesc
                            }
                        }
                    }else{
                        this.$message.error(res.message)
                    }
                    
                }).finally(() => {
                    this.confirmLoading = false
                })
            } else {
                getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/querySummaryEvaGroupResultByGroupId', params, {headers: this.headParams}).then(res => {
                    if (res.code == 200) {
                        const {result = {} } = res
                        const {resultList = {}, supplierList=[], evaluationGroup: {tenderEvaluationTemplateRegulationInfoList = []}} = result
                        this.supplierList = [...supplierList]
                        supplierList.forEach(supplier => {
                            supplier.supplierAccount = `${supplier.supplierAccount}_result`
                        })
                        this.resultListSummary = resultList
                        this.supplierListSummary = supplierList
                        console.log(this.resultListSummary, this.supplierListSummary)
                        if (tenderEvaluationTemplateRegulationInfoList?.length > 0) {
                            for (let k of tenderEvaluationTemplateRegulationInfoList) {
                                this.regulationNameMap[k.id] = k.regulationDesc
                            }
                        }
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
            }
        },
        formatStatus (status) {
            switch (status) {
            case '0':
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bn_f3da2`, '符合')
            case '1':
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_field_xBn_1343c6f`, '不符合')
            default:
                return ''
            }
        },
        // 提交数据
        saveData (path=null) {
            // 简单校验
            let evaBoolean = false
            this.resultData.evaResultListMap && (() => {
                const evaObj = this.resultData.evaResultListMap
                Object.keys(evaObj).forEach(key => {
                    evaObj[key].forEach(item => {
                        item.judgesTaskItemId = this.currentRow.id
                        item.judgesTaskHeadId = this.currentRow.judgesTaskHeadId
                        if (item.result == null) {
                            evaBoolean = true
                        }
                    })
                })
            })()
            
            if (evaBoolean) {
                // this.$message.warning('存在未完成评审的条例，不允许提交！')
                this.tipStatus = true
                return false
            }

            this.resultData.supplierList && this.resultData.supplierList.forEach(data => {
                data['judgesTaskItemId'] = this.currentRow.id || ''
            })

            let url = path ? path : '/tender/evaluation/purchaseTenderProjectBidEvaHead/saveSupplierEvaGroupResult'
            this.confirmLoading = true
            postAction(url, this.resultData, {headers: this.headParams}).then(res => {
                if (res.code == 200) {
                    this.$message.success(res.message)
                    if (path) {
                        this.goBack()
                    }
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        publishData () {
            let path = '/tender/evaluation/purchaseTenderProjectBidEvaHead/publishSupplierEvaGroupResult'
            this.saveData(path)
        },
        // 返回
        goBack () {
            this.$parent.reviewStatus = false
        },
        // 评审意见
        showLeaderOpinion (item) {
            this.currentItem = item
            this.$refs.LeaderOpinionModal.open({data: item, title: item.regulationName, opinionCode: 'remark'})
        },
        LeaderOpinionSuccess (data) {
            this.$set(this.currentItem, 'remark', data.remark)
        }
    }
}
</script>

<style lang="less" scoped>
  .ReviewItems {
    height: 100%;
    background-color: #fff;
    padding: 15px 10px;
  }
  .review-items-top {
    line-height: 30px;

    .ant-col-12:nth-child(1) {
      font-size: 16px;
      font-weight: 600;
    }
  }
  .review-items-grid {
    margin-top: 15px;

    .grid-ul {
      display: flex;

      ul:nth-child(1) {
        width: 80px;
      }
      ul {
        text-align: center;

        li:nth-child(1) {
          background-color: #e4e4e4;
        }
        li {
          border: 1px solid #f0eeee;
          height: 40px;
          line-height: 40px;
          &.warning-tip-p{
            min-width: 180px;
            position: relative;
          }
          .warning-tip {
            width: 100%;
            font-size: 12px;
            height: 16px;
            line-height: 16px;
            position: absolute;
            top: -2px;
            left: -10px;
            color: red;
          }
        }
      }
      .ul {
        flex-grow: 1;
      }
      
    }
  }
</style>
