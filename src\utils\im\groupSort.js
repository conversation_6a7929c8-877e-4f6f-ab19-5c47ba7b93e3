import Sortable from 'sortablejs'
import { imUrl } from './tools'
const { BASE_URL } = imUrl()
function bindEvent (layim) {
    let warpDom = document.querySelector('.layui-layim-main .layim-list-friend')
    const sortTableEX = Sortable.create(warpDom, {
        dataIdAttr: 'data-groupid', 
        onEnd: (res) => {
            console.log(res)
            console.log(sortTableEX.toArray())
            let url = `${BASE_URL}/userGroup/groupDrag`
            let token = localStorage.getItem('t_token')
            let obj = sortTableEX.toArray() || []
            window.layui.$.ajax({
                headers: {
                    'X-Access-Token': token
                },
                contentType: 'application/json; charset=UTF-8',
                url,
                type: 'post',
                data: JSON.stringify(obj), 
                success: (res) => {
                    if (res.success) {
                        let cache = layim.cache()
                        let newCache = []
                        // 更新缓存
                        obj.forEach(x => {
                            let cur = cache.friend.find(s => s.id == x)
                            cur && newCache.push(cur)
                        })
                        cache.friend = newCache
                    } else {
                        window.layer.msg(res.message)
                    }
                } })
        }
    })
}
export function init (layim) {
    bindEvent(layim)
}