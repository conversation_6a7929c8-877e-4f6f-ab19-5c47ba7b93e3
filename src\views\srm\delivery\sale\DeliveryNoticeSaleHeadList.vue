<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <Delivery-Notice-Sale-Head-Modal
      ref="modalForm"
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
      @ok="modalFormOk"
    />
    <View-Delivery-Notice-Sale-Modal
      ref="detailPage"
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideDetail"
    />
  </div>
</template>

<script>
import DeliveryNoticeSaleHeadModal from './modules/DeliveryNoticeSaleHeadModal'
import ViewDeliveryNoticeSaleModal from './modules/ViewDeliveryNoticeSaleModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'DeliveryNoticeSaleHeadList',
    mixins: [listPageMixin],
    components: {
        DeliveryNoticeSaleHeadModal,
        ViewDeliveryNoticeSaleModal
    },
    data () {
        return {
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'), icon: 'download', folded: false, clickFn: this.handleExportXls},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', folded: false, clickFn: this.settingColumns}
                ],
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_oddNum`, '单号'),
                        fieldName: 'noticeNumber',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterOddNum`, '请输入单号')
                    }, 
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'noticeStatus',
                        dictCode: 'isrmDeliveryNoticeStatus'
                    }
                ],
                form: {
                    keyWord: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.showDetail},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEditInner, showCondition: this.showEditCondition}
                ]
            },
            url: {
                list: '/delivery/deliveryNoticeSaleHead/list',
                delete: '/delivery/deliveryNoticeSaleHead/delete',
                deleteBatch: '/delivery/deliveryNoticeSaleHead/deleteBatch',
                exportXlsUrl: '/delivery/deliveryNoticeSaleHead/exportXls',
                importExcelUrl: '/delivery/deliveryNoticeSaleHead/importExcel',
                columns: 'isrmDeliveryNoticeSaleHead'          
            }
        }
    },
    computed: {

    },
    created () {

    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deliveryNotice`, '送货通知单'))
        },
        showDetail (row){
            this.currentEditRow = row
            this.showDetailPage = true
        },
        showEditCondition (row) {
            if(row.noticeStatus == '1') {
                return true
            }else {
                return false
            }
        },
        hideDetail () {
            this.showDetailPage = false
        }
    }
}
</script>
