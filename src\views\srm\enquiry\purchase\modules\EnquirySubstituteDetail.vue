<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-04-27 15:34:49
 * @LastEditors: LOK
 * @LastEditTime: 2022-08-22 15:10:19
 * @Description: 采购协同/寻源协同/询价代报价-详情
-->
<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :reloadData="handleReloadData" />
    <set-ladder-price-modal
      :current-edit-row="currentEditRow"
      ref="ladderPage" />
    <sale-edit-cost
      ref="costform"
      :current-edit-row="costEditRow"></sale-edit-cost>
    <!-- 加载配置文件 -->

    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"/>
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>
<script lang="jsx">
import { EditMixin } from '@comp/template/edit/EditMixin'
import { postAction, getAction, downFile } from '@/api/manage'
import SetLadderPriceModal from '@views/srm/enquiry/sale/modules/SetLadderPriceModal'
import SaleEditCost from '@views/srm/enquiry/sale/modules/SaleEditCost'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import moment from 'moment'
import {BatchDownloadBtn} from '@comp/template/business/class/batchDownloadBtn'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'
import {formatDate, getLangAccount, srmI18n} from '@/utils/util.js'

export default {
    name: 'EnquirySubstituteDetail',
    mixins: [EditMixin],
    components: {
        SetLadderPriceModal,
        SaleEditCost,
        ItemImportExcel
    },
    data () {
        let batchDownloadBtn = new BatchDownloadBtn({pageCode: 'purchaseAttachmentList'})
        batchDownloadBtn.setUrl('sale')
        return {
            stageTypeData: [],
            costEditRow: {},
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleEnquiryItemList',
                            columns: [],
                            buttons: [
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem},
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'), params: this.importParams, click: this.importExcel },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_eportExcel`, '导出Excel'), click: this.exportExcel }

                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'),
                        groupCode: 'fileDemandInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleAttachmentDemandList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                                { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120 },
                                { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                                { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                            ]
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleAttachmentList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                                { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                                { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                                { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                                { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                                { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                                { field: 'sourceNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'), width: 130 },
                                { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                                { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120},
                                { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                            ],
                            buttons: [{ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                dictCode: 'srmFileType', type: 'upload', businessType: 'enquiry',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack, showCondition: this.showButton },
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch,
                                authorityCode: 'enquiry#purchaseEnquiryHead:deleteBatch'},
                            {
                                title: srmI18n(`${getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
                                label: srmI18n(`${getLangAccount()}#i18n_field_zRIK_2ef0bbc8`, '批量下载'),
                                msgType: 'batchDownload',
                                key: 'batchDownload',
                                type: 'check',
                                beforeCheckedCallBack: rowList => {
                                    this.batchDownload2(rowList)
                                }
                            }],
                            showOptColumn: true,
                            optColumnList: [
                                { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadFile },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                                { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                            ]
                        }
                    }
                ],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_RLsu_38d6fc68`, '确认报价'), type: 'primary', click: this.confirm, showCondition: this.showConfirmButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save, showCondition: this.showButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'), type: 'primary', click: this.quote, showCondition: this.showButton },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/enquiry/enquirySubstituteHead/queryById',
                save: '/enquiry/saleEnquiryHead/save',
                confirm: '/enquiry/saleEnquiryHead/confirm',
                quote: '/enquiry/saleEnquiryHead/quote',
                upload: '/attachment/saleAttachment/upload',
                download: '/attachment/saleAttachment/download',
                import: '/els/base/excelByConfig/importExcel',
                export: '/base/excelByConfig/exportExcel'
            },
            ladderSlots: {
                default: ({ row, column }) => {
                    const tpl = (
                        <div style="position: relative;min-height: 30px;padding-right: 20px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
                            <a-tooltip placement="topLeft" overlayClassName="tip-overlay-class">
                                <template slot="title">
                                    <div>
                                        <vxe-table auto-resize border row-id="id" size="mini" data={this.initRowLadderJson(row[column.property])}>
                                            <vxe-table-column type="seq" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_seq`, '序号')} width="80"></vxe-table-column>
                                            <vxe-table-column field="ladder" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')} width="140"></vxe-table-column>
                                            <vxe-table-column field="price" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价')} width="140"></vxe-table-column>
                                            <vxe-table-column field="netPrice" title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_netPrice`, '不含税价')} width="140"></vxe-table-column>
                                        </vxe-table>
                                    </div>
                                </template>
                                {this.defaultRowLadderJson(row[column.property])}
                            </a-tooltip>
                            <a style="position: absolute;display: inline-block;font-size: 16px;right: 0px; top: 3px;z-index: 10;">
                                <a-icon
                                    type="stock"
                                    onClick={() => {
                                        this.setLadder && this.setLadder(row)
                                    }}
                                />
                            </a>
                        </div>
                    )
                    if (row && row.quotePriceWay == 1) {
                        return tpl
                        // let label = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        // if (row.ladderPriceJson) {
                        //     let itemList = JSON.parse(row.ladderPriceJson)
                        //     if (itemList[0].price) {
                        //         label = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价')
                        //     }
                        // }
                        // return [<a onClick={() => this.setLadder(row)}>{label}</a>]
                    } else {
                        return ''
                    }
                }
            },
            costSlots: {
                default: ({ row }) => {
                    if (row && row.quotePriceWay == 2) {
                        let label = row.price ? this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quotationPrice`, '已报价') : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noQuotationPrice`, '未报价')
                        return [<a onClick={() => this.openCost(row)}>{label}</a>]
                    } else {
                        return ''
                    }
                }
            },
            currentRow: {}
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let busAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${busAccount}/sale_enquiry_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    methods: {
        batchDownload2 (rowList){
            let checkboxRecords = rowList
            if(checkboxRecords.length<=0){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#`, '请选择要下载的附件'))
                return
            }
            let elsAccount = this.$ls.get('Login_elsAccount')
            // 判断
            let flag = false
            let fromData = this.currentEditRow
            for(let i=0;i<rowList.length;i++){
                let row = checkboxRecords[i]
                if(fromData.enquiryStatus === '1' && row.uploadElsAccount !=elsAccount) {
                    this.$message.warning('所选行中存在开标前不允许查看的文件！')
                    flag = true
                    return
                }
            }
            if (flag) {
                return
            }
            new BatchDownloadBtn().batchDownloadZip(checkboxRecords)
        },
        attrHandle () {
            return {
                // 待报价的数据，这个附件不发送的
                uploadType: 'enquirySubstitute'
            }
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            console.log(jsonData)
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                if (Array.isArray(arr)) {
                    arr.forEach((item, index)=> {
                        let ladder = item.ladder
                        let price = item.price || ''
                        let str = `${ladder},${price}`
                        let separator = index===arr.length-1? '': ';'
                        arrString +=str+ separator
                    })
                }
            }
            return arrString
        },
        checkTableValidate () {
            const currentDate= moment().format('YYYY-MM-DD')
            const params = this.$refs.editPage.getPageData()
            let saleEnquiryItemList = params.saleEnquiryItemList
            if (saleEnquiryItemList?.length > 0) {
                let i = 1
                for (let item of saleEnquiryItemList) {
                    if(item.effectiveDate){
                        if(!(moment(item.effectiveDate).isSame(currentDate) || moment(item.effectiveDate).isAfter(currentDate))) {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, `询价行信息中第 ${i} 行价格生效日期需大于等于当前日期！`))
                            return false
                        }
                    } 
                    if (item.expiryDate && item.effectiveDate) {
                        if (!moment(item.effectiveDate).isBefore(item.expiryDate)) {
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, `询价行信息中第 ${i} 行价格失效日期需大于价格生效日期！`))
                            return false
                        }
                    }

                    i++
                }
            }

            return true
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEnquiryStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.purchaseAttachmentDemandList = res.result.purchaseAttachmentDemandList.map((i) => {
                    i.stageType_dictText = this.stageTypeData.find((item) => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },

        downloadFile (row){
            let elsAccount = this.$ls.get('Login_elsAccount')
            // 判断
            let fromData = this.currentEditRow
            if(fromData.enquiryStatus != '7' && row.uploadElsAccount != elsAccount) {
                this.$message.warning('开标前不允许查看文件！')
                return
            }
            this.$refs.editPage.handleDownload(row)
        },
        
        preViewEvent (row) {
            let elsAccount = this.$ls.get('Login_elsAccount')
            // 判断
            let fromData = this.currentEditRow
            if(fromData.enquiryStatus != '7' && row.uploadElsAccount != elsAccount) {
                this.$message.warning('开标前不允许查看文件！')
                return
            }
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        beforeHandleData (data) {
            data.itemColumns.forEach((item) => {
                if (item.field == 'ladderPriceJson') {
                    item.slots = this.ladderSlots
                }
                if (item.field == 'costFormJson') {
                    item.slots = this.costSlots
                }
                //供应商税率为是时,税码可弹框
                if (item.field == 'taxCode' && this.currentEditRow['supplierTaxRate'] == '1') {
                    item.fieldType = 'selectModal'
                    item.extend.modalParams['elsAccount'] = this.currentEditRow['busAccount']
                }
                //供应商税率为否时，税码不可弹框
                if(item.field === 'taxCode' && this.currentEditRow['supplierTaxRate'] != '1'){
                    item.fieldType = ''
                }


            })
        },
        //控制行信息税码弹框不可编辑
        disableHandle(row){
            //议价中或已悔标
            if(this.currentEditRow['enquiryStatus'] === '7' || this.currentEditRow['enquiryStatus'] === '11'){
                //不是重报价 && 不是报价中
                if(row.itemStatus !== '8' && row.itemStatus !== '1'){
                    return true
                }
            }
            //是否报价为否
            return row.quotePrice=='0'
        },

        importExcel () {
            const form = this.$refs.editPage.getPageData() || {}
            let params = {'handlerName': 'saleEnquiryItemExcelRpcServiceImpl', 'roleCode': 'sale',
                'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')}
            if (form.id){
                params.id = form.id
            }
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'), 'saleEnquiryItemList')
        },
        importParams () {
            const form = this.$refs.editPage.getPageData()
            return { id: form.id, templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'saleEnquiryItemExcelRpcServiceImpl', roleCode: 'sale',
                excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')}
        },
        importCallBack (result) {
            if (result.file.status === 'done') {
                let response = result.file.response
                if (response.success) {
                    let itemGrid = this.$refs.editPage.$refs.saleEnquiryItemList[0]
                    let excelData = response.result.dataList
                    for (let index in excelData) {
                        for (let filed in excelData[index]) {
                            let vaule = excelData[index][filed]
                            if (vaule) {
                                let item = itemGrid.getTableData().fullData[index]
                                item[filed] = vaule
                            }
                        }
                    }
                    // 不需要插入的 原本就是下下来导进去
                    // itemGrid.insertAt(excelData, -1)
                    let head = this.currentEditRow || {}
                    //计算不含税价，不含税总额，含税总额
                    if(head.quoteType == '0'){
                        for (let item of itemGrid.getTableData().fullData) {
                            item['price'] = parseFloat(item.price).toFixed(6)
                            //不含税价
                            item['netPrice'] = (item.price / (1 + parseFloat(item.taxRate)/100)).toFixed(6)
                            //含税总额
                            item['taxAmount'] = (item.price * parseFloat(item.requireQuantity)).toFixed(6)
                            //不含税总额
                            item['netAmount'] = (item.netPrice * parseFloat(item.requireQuantity)).toFixed(6)
                        }
                    }else{
                        for (let item of itemGrid.getTableData().fullData) {
                            item['netPrice'] = parseFloat(item.netPrice).toFixed(6)
                            item['price'] = (item.netPrice * (1 + parseFloat(item.taxRate)/100)).toFixed(6)
                            item['taxAmount'] = (item.price * parseFloat(item.requireQuantity)).toFixed(6)
                            item['netAmount'] = (item.netPrice * parseFloat(item.requireQuantity)).toFixed(6)
                        }
                    }
                } else {
                    this.$message.warning(response.message)
                }
            }
        },
        exportExcel () {
            const form = this.$refs.editPage.getPageData()
            let params = { id: form.id, templateAccount: form.templateAccount, templateNumber: form.templateNumber, templateVersion: form.templateVersion, handlerName: 'saleEnquiryItemExcelRpcServiceImpl', roleCode: 'sale',
                excelName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息')}
            downFile(this.url.export, params)
                .then((data) => {
                    if (!data) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                        return
                    }
                    if (typeof window.navigator.msSaveBlob !== 'undefined') {
                        window.navigator.msSaveBlob(new Blob([data]), '报价Excel.xlsx')
                    } else {
                        let url = window.URL.createObjectURL(new Blob([data]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', '报价Excel.xlsx')
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    }
                })
                .finally(() => {
                    this.gridLoading = false
                })
        },
        confirm () {
            const params = this.$refs.editPage.getPageData()
            this.$refs.editPage.confirmLoading = true
            if (!this.checkTableValidate()) return
            postAction(this.url.confirm, params)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$refs.editPage.queryDetail(params.id)
                    } else {
                        this.$message.warning(res.message)
                    }
                })
                .finally(() => {
                    this.$refs.editPage.confirmLoading = false
                })
        },
        save () {
            const params = this.$refs.editPage.getPageData()
            if (!this.checkTableValidate()) return
            console.log(params)
            this.$refs.editPage.confirmLoading = true
            postAction(this.url.save, params)
                .then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$refs.editPage.queryDetail(params.id)
                    } else {
                        this.$message.warning(res.message)
                        this.$refs.editPage.confirmLoading = false
                    }
                })
                .catch(() => {
                    this.$refs.editPage.confirmLoading = false
                })
        },
        setPromise () {
            let that = this
            let promise = this.pageData.groups.map(group => {
                if (group.groupCode === 'itemInfo') {
                    return new Promise((resolve, reject) => {
                        const ruleItem = Object.keys(that.$refs.editPage.$refs[group.custom.ref][0].editRules)
                        const params = this.$refs.editPage.getPageData()
                        if (ruleItem && ruleItem.length > 0) {
                            const nullIndex = []
                            const saleEnquiryItemList = params.saleEnquiryItemList
                            saleEnquiryItemList.forEach((i, index) => {
                                const nullItems = ruleItem.filter(r => !i[r])
                                if (nullItems.length > 0 && i.quotePrice === '1') {
                                    nullIndex.push(index + 1)
                                }
                            })
                            if (nullIndex && nullIndex.length > 0) {
                                that.$message.warning(`询价行信息：请完善第 ${nullIndex.toString()} 行必填项`)
                                reject()
                            }
                        }
                        resolve()
                    })
                } else {
                    if (group.type == 'grid') {
                        return that.$refs.editPage.$refs[group.custom.ref][0].validate(true)
                    } else {
                        return that.$refs.editPage.$refs[group.groupCode][0].validate()
                    }
                }
            })
            return promise
        },
        async quote () {
            // const $table = this.$refs.editPage.$refs.saleEnquiryItemList[0]
            // const errMap = await $table.validate(true).catch((errMap) => errMap)
            if (!this.checkTableValidate()) return
            // if (errMap) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_cVHlSdJOxeRW_eb3ee28b`, '行信息必填项校验不通过！'))
            //     return false
            // }
            const callback = () => {
                const params = this.$refs.editPage.getPageData()
                postAction(this.url.quote, params).then((res) => {
                    if (res.success) {
                        this.$message.success(res.message)
                        this.$emit('hide')
                    } else {
                        let resMsg = res.message;
                        let newMsg = resMsg;
                        if(!!resMsg && resMsg.indexOf("\n") >= 0) {
                        const h = this.$createElement;
                        let strList = resMsg.split("\n");
                        strList = strList.map((str, strIndex) => {
                            return h(strIndex === 0? 'span' : 'div',null,str);
                        })
                        newMsg = h('span', null, strList)
                        }
                        this.$message.warning(newMsg)
                    }
                })
            }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.currentStep = i
                        return
                    }
                }
                if (flag) 
                    this.$confirm({
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherQuote`, '确认是否报价'),
                        onOk () {
                            callback && callback()
                        }
                    })
            }).catch(err => {
                console.log(err)
            })
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            let pageData = this.$refs.editPage.getPageData()
            let saleEnquiryItemList = pageData.saleEnquiryItemList || []
            let materialMap = saleEnquiryItemList.reduce((acc, obj) => {
                acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                return acc
            }, {})
            result.forEach(item => {
                let number = item.itemNumber
                if (number && materialMap[number] && !item.materialName) {
                    item.materialNumber = materialMap[number].split('_')[0]
                    item.materialName = materialMap[number].split('_')[1]
                }
            })
            fileGrid.insertAt(result, -1)

        },
        deleteFilesEvent (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            getAction('/attachment/saleAttachment/delete', { id: row.id }).then((res) => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        setLadder (row) {
            if (!row.taxCode) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterTaxCode`, '请先选择税码！'))
                return
            }
            this.currentRow = row
            let head = this.$refs.editPage.getPageData()
            this.$refs.ladderPage.open(row, head)
        },
        setLadderCallBack (itemList) {
            this.currentRow['ladderPriceJson'] = JSON.stringify(itemList)
            const current = this.getQuantityPrice(this.currentRow)
            const { price, netPrice } = current
            let requireQuantity = this.currentRow.requireQuantity || 1
            this.currentRow['price'] = price // 含税价
            this.currentRow['netPrice'] = netPrice // 未税价
            this.currentRow['taxAmount'] = (price * requireQuantity).toFixed(6)
            this.currentRow['netAmount'] = (netPrice * requireQuantity).toFixed(6)
        },
        // 根据当前询价行 需求数量获取阶梯报价应得报价行
        getQuantityPrice (row) {
            const json = JSON.parse(row.ladderPriceJson)
            const quantityList = json.map((item) => {
                return Number(item.ladderQuantity)
            })
            const requireQuantity = row.requireQuantity || quantityList[quantityList.length - 1]
            quantityList.push(requireQuantity)
            quantityList.sort(function (a, b) {
                return a - b
            })
            const indexs = []
            quantityList.forEach((i, index) => {
                if (i === Number(requireQuantity)) {
                    indexs.push(index)
                }
            })
            const index = indexs.pop()
            const current = json[index === 0 ? index : index - 1]

            return current
        },
        openCost (row) {
            if (!row.taxCode) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterTaxCode`, '请先选择税码！'))
                return
            }
            this.currentRow = row
            let costJson = JSON.parse(row.costFormJson)
            costJson['itemId'] = row.id
            this.costEditRow = costJson
            let data = costJson['data'] || {}
            let endTimeQue = row.quoteEndTime > formatDate(new Date().getTime(), 'yyyy-MM-dd hh:mm:ss')
            // 单 1 报价中、2 已报价、8 重报价 且 未到报价截止日期
            endTimeQue = endTimeQue && ['1', '2', '8'].includes(this.currentEditRow.enquiryStatus)
            // 1 报价中、8 重报价、未到报价截止日期
            let containerStatus = row.itemStatus == '1' || row.itemStatus == '8' || endTimeQue ? 'edit' : 'detail'
            this.$refs.costform.open(data, containerStatus)
        },
        costCallBack (extendAllData) {
            const params = this.$refs.editPage.getPageData()
            //报价项 1:不含税价;0:含税价
            let quoteType = params.quoteType == '1' ? 'netPrice' : 'price'
            let costJson = JSON.parse(this.currentRow['costFormJson'])
            let allData = extendAllData.allData || []
            costJson['data'] = allData
            //分组信息
            let costGruops = []
            let costPrice = 0
            extendAllData.pageConfig.groups.forEach((gruop) => {
                let costGroup = {}
                costGroup['groupCode'] = gruop['groupCode']
                costGroup['groupName'] = gruop['groupName']
                costGroup['groupType'] = gruop['groupType']
                costGroup['totalValue'] = gruop['total']['totalValue'] || 0
                costGroup[quoteType] = costGroup['totalValue']
                if (params.quoteType == '1') {
                    let price = (costGroup[quoteType] * (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                    costGroup['price'] = price
                } else {
                    let netPrice = (costGroup[quoteType] / (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                    costGroup['netPrice'] = netPrice
                }
                costGruops.push(costGroup)
                costPrice += costGroup['totalValue']
            })
            costJson['groups'] = costGruops
            this.currentRow['costFormJson'] = JSON.stringify(costJson)
            this.currentRow[quoteType] = costPrice
            // 分别计算行内含税或未税单价，以及含税、未税总额
            if (params.quoteType == '1') {
                let price = (this.currentRow[quoteType] * (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                this.currentRow['price'] = price
            } else {
                let netPrice = (this.currentRow[quoteType] / (1 + parseFloat(this.currentRow.taxRate)/100)).toFixed(6)
                this.currentRow['netPrice'] = netPrice
            }
            this.currentRow['taxAmount'] = (this.currentRow.price * this.currentRow.requireQuantity).toFixed(6)
            this.currentRow['netAmount'] = (this.currentRow.netPrice * this.currentRow.requireQuantity).toFixed(6)
        },
        showConfirmButton () {
            let editPage = this.$refs.editPage
            if (editPage) {
                const { quoteConfirmFlag } = editPage.getPageData() || {}
                return quoteConfirmFlag === '0'
            }
        },
        showButton () {
            let editPage = this.$refs.editPage
            if (editPage) {
                const { enquiryStatus } = editPage.getPageData() || {}
                // return enquiryStatus == '1' || enquiryStatus == '7'
                //报价中 || 未报价 || 议价中 || 已悔标
                return enquiryStatus == '1' || enquiryStatus === '3' || enquiryStatus == '7' || enquiryStatus == '11'
            }
            return false
        }
    }
}
</script>
