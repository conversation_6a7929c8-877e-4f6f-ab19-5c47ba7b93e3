<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url"/>
    <a-modal
      v-drag
      v-model="previewModal"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览')"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>

    <a-modal
      v-drag
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_contractReturn`, '合同退回')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterContractReturnDesc`, '请输入合同退回备注')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
    <view-item-diff-modal ref="viewDiffModal"/>
    <His-Contract-Item-Modal ref="hisContractItemModal"/>
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError"/>
  </div>
</template>

<script>
//import {DetailMixin} from '@comp/template/detail/DetailMixin'
import {EditMixin} from '@comp/template/edit/EditMixin'
import HisContractItemModal from './SaleHisContractItemModal'
import ViewItemDiffModal from './SaleViewItemDiffModal'
import {getAction} from '@/api/manage'
import {axios} from '@/utils/request'

export default {
    name: 'SaleContractHeadModal',
    mixins: [EditMixin],
    components: {
        HisContractItemModal,
        ViewItemDiffModal
    },
    data () {
        return {
            previewModal: false,
            auditVisible: false,
            previewContent: '',
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回'),
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractLineInfo`, '合同行信息'),
                        groupType: 'item',
                        groupCode: 'itemInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleContractItemList',
                            columns: [],
                            buttons: []
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractLibrary`, '合同条款库'),
                        groupCode: 'itemContentInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleContractContentItemList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'itemId',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectNumber`, '项目编号'),
                                    width: 120
                                },
                                {
                                    field: 'itemName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectName`, '项目名称'),
                                    width: 120
                                },
                                {
                                    field: 'itemType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_projectType`, '项目类型'),
                                    width: 120,
                                    dictCode: 'srmItemType'
                                },
                                {
                                    field: 'itemVersion',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_projectVersion`, '项目版本'),
                                    width: 120
                                },
                                {
                                    field: 'changeFlag',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_changeIdentification`, '变更标识'),
                                    width: 120,
                                    editRender: {
                                        name: '$switch',
                                        type: 'visible',
                                        props: {closeValue: '0', openValue: '1', disabled: true}
                                    }
                                },
                                {
                                    field: 'sourceType_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    width: 120,
                                    dictCode: 'srmContractContentSourceType'
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 150,
                                    align: 'left',
                                    slots: {
                                        default: ({row}) => {
                                            let resultArray = []
                                            resultArray.push(<a
                                                title={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看')}
                                                onClick={() => this.viewDetail(row)}>查看</a>)
                                            if (row.changeFlag == '1') {
                                                resultArray.push(<a title="比对" style="margin-left:8px"
                                                    onClick={() => this.viewDiff(row)}>比对</a>)
                                            }
                                            return resultArray
                                        }
                                    }
                                }
                            ],
                            buttons: []
                        }
                    },
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'),
                        groupCode: 'fileInfo',
                        type: 'grid',
                        custom: {
                            ref: 'saleAttachmentList',
                            columns: [
                                {type: 'checkbox', width: 40},
                                {
                                    type: 'seq',
                                    width: 60,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    field: 'fileName',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                                    width: 120
                                },
                                {
                                    field: 'uploadTime',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                                    width: 120
                                },
                                {
                                    field: 'uploadElsAccount_dictText',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                                    width: 120
                                },
                                {
                                    field: 'itemNumber',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'),
                                    width: 120
                                },
                                {
                                    field: 'grid_opration',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                                    width: 120,
                                    align: 'center',
                                    slots: {default: 'grid_opration'}
                                }
                            ],
                            buttons: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                    type: 'upload',
                                    businessType: 'contract',
                                    callBack: this.uploadCallBack
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                                    click: this.deleteBatch
                                }
                            ],
                            showOptColumn: true,
                            optColumnList: [
                                {
                                    type: 'download',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'),
                                    authorityCode: 'contract#saleContractHead:view',
                                    clickFn: this.downloadEvent
                                },
                                {
                                    type: 'preView',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                                    clickFn: this.preViewEvent
                                },
                                {
                                    type: 'delete',
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                                    clickFn: this.deleteFilesEvent
                                }
                            ]
                        }
                    }
                ],
                publicBtn: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'),
                        authorityCode: 'contract#saleContractHead:getPreviewData',
                        type: 'primary',
                        click: this.preview
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractConfirmation`, '合同确认'),
                        type: 'primary',
                        authorityCode: 'contract#saleContractHead:confirmed',
                        click: this.confirm,
                        showCondition: this.showOptConditionBtn
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回'),
                        type: 'primary',
                        authorityCode: 'contract#saleContractHead:refund',
                        click: this.refund,
                        showCondition: this.showOptConditionBtn
                    },
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/contract/saleContractHead/queryById',
                confirm: '/contract/saleContractHead/confirmed',
                refund: '/contract/saleContractHead/refund',
                upload: '/attachment/saleAttachment/upload'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_contract_${templateNumber}_${templateVersion}.js?t=` + time
        }
    },
    mounted () {
        if (this.currentEditRow.contractType === '3') {
            this.pageData.groups.splice(0, 1)
        }
    },
    methods: {
        preViewEvent (row) {
            let preViewFile = row
            this.$previewFile.open({params: preViewFile})
        },
        showOptConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            let contractStatus = params.contractStatus
            if (contractStatus == '2') {
                return true
            } else {
                return false
            }
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        deleteBatch () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        refund () {
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractReturn`, '合同退回')
        },
        handleOk () {
            this.getData(this.url.refund, {refundRemark: this.opinion})
        },
        viewDiff (row) {
            this.$refs.viewDiffModal.open(row)
        },
        viewDetail (row) {
            this.$refs.hisContractItemModal.open(row)
        },
        preview () {
            let contentGrid = this.$refs.editPage.$refs.saleContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            this.$refs.editPage.confirmLoading = true
            getAction('/contract/saleContractHead/getPreviewData', {id: params.id}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        previewPdf () {
            let contentGrid = this.getItemGridRef('purchaseContractContentItemList')
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.confirmLoading = true
            axios({
                url: '/contract/saleContractHead/download',
                responseType: 'blob',
                params: {id: this.currentEditRow.id}
            }).then((res) => {
                if (res) {
                    debugger
                    let url = window.URL.createObjectURL(new Blob([res], {type: 'application/pdf'}))
                    window.open(url)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        itemView () {
            let contentGrid = this.$refs.editPage.$refs.saleContractContentItemList[0]
            if (!contentGrid.getTableData().tableData.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noLineInfo`, '无条款行信息'))
                return
            }
            this.$refs.editPage.confirmLoading = true
            getAction('/contract/purchaseContractHead/getPreviewData', {id: this.currentEditRow.masterContractId}).then((res) => {
                if (res.success) {
                    this.previewModal = true
                    this.previewContent = res.result
                }
            }).finally(() => {
                this.$refs.editPage.confirmLoading = false
            })
        },
        confirm () {
            let that = this
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractConfirmation`, '合同确认')
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_thisOperationWillConfirmContractConfirm`, '此操作将确认合同，是否确认?'),
                onOk: function () {
                    that.getData(that.url.confirm, null)
                }
            })
        },
        getData (url, param) {
            let params = this.$refs.editPage ? this.$refs.editPage.getPageData() : {}
            if (!param) {
                param = {}
            }
            param['id'] = params.id
            getAction(url, param).then(res => {
                if (res.success) {
                    this.form = res.result
                    this.auditVisible = false
                    this.opinion = ''
                    this.$message.success(res.message)
                    this.goBack
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.auditVisible = false
                this.init()
                this.$parent.modalFormOk()
            })
        }
    }
}
</script>
