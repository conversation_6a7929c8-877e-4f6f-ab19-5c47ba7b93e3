<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"></business-layout>
    </a-spin>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <select-Data-Modal
      ref="selectDataModal"
      @ok="selectDataOk"/>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import selectDataModal from './selectDataModal'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction } from '@/api/manage'
import {BUTTON_PUBLISH, BUTTON_SAVE} from '@/utils/constant.js'
import {getLangAccount} from '@/utils/util'

export default {
    name: 'SaleAddCostEdit',
    mixins: [businessUtilMixin],
    components: {
        fieldSelectModal,
        selectDataModal,
        BusinessLayout
    },
    data () {
        return {
            businessRefName: 'businessRef',
            requestData: {
                detail: {
                    url: '/finance/saleAddCost/queryById',
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                saleAttachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            action: '/attachment/saleAttachment/upload', // 必传
                            businessType: 'addCost', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'),
                        click: this.deleteBatch
                    }
                ],
                addCostItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary'},
                    click: this.businessGridAddPopup
                }, 
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iFBuujwj_d1a685f6`, '选择附加费用来源'),
                    type: 'primary',
                    click: this.selectSourceEvent
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete',
                    click: this.businessGridDelete
                }
                ]
            },
            pageFooterButtons: [
                {   
                    ...BUTTON_SAVE,
                    title: this.$srmI18n(`${getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/finance/saleAddCost/edit'
                    }
                },
                {
                    ...BUTTON_PUBLISH,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/finance/saleAddCost/publish'
                    },
                    authorityCode: 'finance#saleAddCost:publish'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                add: '/finance/saleAddCost/add',
                edit: '/finance/saleAddCost/edit',
                detail: '/finance/saleAddCost/queryById',
                public: '/finance/saleAddCost/publish',
                upload: '/attachment/saleAttachment/upload',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let busAccount = this.currentEditRow.templateAccount|| this.currentEditRow.busAccount
            let time = new Date().getTime()
            return `${busAccount}/sale_addCost_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        selectDataOk () {
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            this.handleHeaderFields({pageData: pageConfig, flag: true})
        },
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.costNumber,
                actionRoutePath: '/srm/finance/purchase/PurchaseAddCostList,/srm/finance/sale/SaleAddCostList'
            }
        },
        // 处理最后的数据
        handleAfterDealSource (pageConfig, resultData) {
            if (resultData?.addCostItemList?.length) { // 行信息有值就置灰表头
                this.handleHeaderFields({pageData: pageConfig, flag: true})
            }
        },
        handleBeforeRemoteConfigData (){
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachment`, '附件'),
                        groupNameI18nKey: '',
                        groupCode: 'saleAttachmentList',
                        groupType: 'item',
                        sortOrder: '6',
                        extend: {
                            optColumnList: [
                                { key: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), click: this.downloadEvent },
                                { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), click: this.preViewEvent },
                                {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFilesEvent}
                            ]
                        }
                    }
                ],
                formFields: [],
                itemColumns: [
                    { 
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'),
                        fieldLabelI18nKey: '',
                        field: 'fileName',
                        width: 200
                    },
                    { 
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime',
                        width: 180
                    },
                    { 
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'),
                        fieldLabelI18nKey: '',
                        field: 'uploadElsAccount_dictText',
                        width: 120
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子账号'),
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        width: 120
                    },
                    {
                        groupCode: 'saleAttachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                        fieldLabelI18nKey: '',
                        field: 'grid_opration',
                        width: '100',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        },
        selectSourceEvent (row) {
            let pageData=this.getAllData() || {}
            if (!pageData.toElsAccount) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_ViFRdX_f2ffa076`, '请选择供应商！'))
                return
            }
            if (!pageData.company) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectCompanyTips`, '请选择公司！'))
                return
            }
            let params = {}
            params.toElsAccount = pageData.toElsAccount
            params.saleElsAccount = pageData.toElsAccount
            params.company = pageData.company
            params.companyCode = pageData.company
            this.$refs.selectDataModal.open(params)
        },
        preViewEvent (Vue, row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        beforeHandleData (data) {
            data.formFields.forEach(item => {
                if(item.fieldName == 'taxCode'){
                    item.extend.modalParams.elsAccount = this.currentEditRow.busAccount
                }
                if(item.fieldName == 'toElsAccount'){
                    item.fieldLabel = '采购方els账号'
                }
            })
        },
        goBack () {
            this.$emit('hide')
        },
        uploadCallBack (result) {
            let fileGrid =this.getItemGridRef('saleAttachmentList')
            fileGrid.insertAt(result, -1)
        },
        downloadEvent (Vue, row) {
            if(!row.fileName){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineNotDownload`, '该行没有可下载的附件'))
                return
            }
            this.attachmentDownloadEvent(row)
        },
        deleteFilesEvent (Vue, row) {
            const fileGrid = this.getItemGridRef('saleAttachmentList')
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 表格通用删除
        businessGridDelete ({ groupCode = '' }) {
            if (!groupCode) {
                return
            }
            let itemGrid = this.getItemGridRef(groupCode)
            const { pageConfig = {} } = this.getBusinessExtendData(this.businessRefName)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
            let {fullData} = itemGrid.getTableData()
            if (fullData?.length == 0) {
                this.handleHeaderFields({pageData: pageConfig, flag: false})
            }
        },
        deleteBatch () {
            const fileGrid = this.getItemGridRef('saleAttachmentList')
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        }
    }
}
</script>
