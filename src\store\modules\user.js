import Vue from 'vue'
import { login, logout, phoneLogin, loginByToken } from '@/api/login'
import { LOGIN_RESULT_DATA, ACCESS_TOKEN, SRM_VERSION, USER_ELS_ACCOUNT, USER_SUB_ACCOUNT, USER_INFO, USER_COMPANYSET, USER_AUTH, SYS_BUTTON_AUTH, DEFAULT_TOGGLE_SETPATH, SET_BUSINESS_RULE_LIST } from '@/store/mutation-types'
import { welcome } from '@/utils/util'
import { queryPermissionsByUser, queryFontList } from '@/api/api'
import { getAction } from '@/api/manage'

const user = {
    state: {
        token: '',
        elsAccount: '',
        subAccount: '',
        realname: '',
        welcome: '',
        avatar: '',
        permissionList: [],
        openKeyPath: '',
        setPath: false,
        info: {},
        sysAuth: [],
        pageDocuments: [],
        taskInfo: {},
        taskBtn: [],
        businessRuleList: [],
        companySet: {},
        loginResultData: {}
    },
    getters: {
        getCompanySet (state) {
            return state.companySet
        }
    },
    mutations: {
        SET_TOKEN: (state, token) => {
            state.token = token
        },
        SET_NAME: (state, { elsAccount, subAccount, realname, welcome }) => {
            state.elsAccount = elsAccount
            state.subAccount = subAccount
            state.realname = realname
            state.welcome = welcome
        },
        SET_AVATAR: (state, avatar) => {
            state.avatar = avatar
        },
        SET_OPENDKEYPATH: (state, path) => {
            state.openKeyPath = path
        },
        TOGGLE_SETPATH: (state, bool) => {
            Vue.ls.set(DEFAULT_TOGGLE_SETPATH, bool)
            state.setPath = bool
        },
        SET_PERMISSIONLIST: (state, permissionList) => {
            state.permissionList = permissionList
        },
        SET_INFO: (state, info) => {
            state.info = info
        },
        SET_COMPANYSET: (state, companySet) => {
            state.companySet = companySet
        },
        SET_SYS_AUTH: (state, sysAuthList) => {
            state.sysAuth = sysAuthList
        },
        SET_BUSINESS_RULE_LIST: (state, businessRuleList) => {
            state.businessRuleList = businessRuleList
        },
        SET_PAGE_DOCS: (state, pageDocuments) => {
            state.pageDocuments = pageDocuments
        },
        SET_TASK: (state, task) => {
            state.taskInfo = task
        },
        SET_TASKBTN: (state, taskBtn) => {
            state.taskBtn = taskBtn
        },
        SET_LOGIN_RESULT_DATA: (state, data)=> {
            state.loginResultData = data
        }
    },

    actions: {
    // 登录提交需要存储的数据
        LoginSuccess ({ commit }, response) {
            return new Promise((resolve, reject) => {
                if (response.success) {
                    Vue.ls.set('THIRD_USER_UUID', '', 3 * 24 * 60 * 60 * 1000)
                    successCallback(commit, response)
                    resolve(response)
                } else {
                    reject(response)
                }
            })
        },

        // CAS验证登录
        ValidateLogin ({ commit }, userInfo) {
            return new Promise((resolve, reject) => {
                getAction('/cas/client/validateLogin', userInfo)
                    .then((response) => {
                        console.log('----cas 登录--------', response)
                        if (response.success) {
                            successCallback(commit, response)
                            resolve(response)
                        } else {
                            resolve(response)
                        }
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },
        // 登录
        Login ({ commit }, userInfo) {
            return new Promise((resolve, reject) => {
                login(userInfo)
                    .then((response) => {
                        if (response.code == '200') {
                            successCallback(commit, response)
                            resolve(response)
                        } else {
                            reject(response)
                        }
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },

        LoginByToken ({ commit }, userInfo) {
            return new Promise((resolve, reject) => {
                loginByToken(userInfo)
                    .then((response) => {
                        if (response.code == '200') {
                            successCallback(commit, response)
                            resolve(response)
                        } else {
                            reject(response)
                        }
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },
        //手机号登录
        PhoneLogin ({ commit }, userInfo) {
            return new Promise((resolve, reject) => {
                phoneLogin(userInfo)
                    .then((response) => {
                        if (response.code == '200') {
                            successCallback(commit, response)
                            resolve(response)
                        } else {
                            reject(response)
                        }
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },
        //获取字体库
        GetFontList () {
            let params = {
                keyWord: '',
                pageSize: 20,
                pageNo: 1,
                filter: {},
                column: 'id',
                order: 'desc'
            }
            return new Promise((resolve, reject) => {
                queryFontList(params)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((res) => {
                        reject(res)
                    })
            })
        },
        // 获取用户信息
        GetPermissionList ({ commit }) {
            return new Promise((resolve, reject) => {
                let v_token = Vue.ls.get(ACCESS_TOKEN)
                let params = { token: v_token }
                queryPermissionsByUser(params)
                    .then((response) => {
                        if (response && response.success) {
                            const docs = response.result.doc
                            const menuData = response.result.menu
                            const { path = '' } = menuData.find((n) => n.children && n.children.length) || {}
                            commit('SET_OPENDKEYPATH', path)
                            const authData = response.result.auth
                            const allAuthData = response.result.allAuth
                            // 设置成当前登录人权限
                            commit('SET_SYS_AUTH', authData)
                            sessionStorage.setItem(USER_AUTH, JSON.stringify(authData))
                            sessionStorage.setItem(SYS_BUTTON_AUTH, JSON.stringify(allAuthData))
                            if (menuData && menuData.length > 0) {
                                commit('SET_PERMISSIONLIST', menuData)
                            } else {
                                console.error('getPermissionList: permissions must be a non-null array !')
                                reject({ result: { menu: [] } })
                            }
                            // 设置页面的doc文档
                            if (docs && docs.length) {
                                commit('SET_PAGE_DOCS', docs)
                            }
                            resolve(response)
                        } else {
                            reject(response)
                        }
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },

        // 登出
        Logout ({ commit, state }) {
            return new Promise((resolve) => {
                let logoutToken = state.token
                commit('SET_TOKEN', '')
                commit('SET_PERMISSIONLIST', [])
                Vue.ls.remove(ACCESS_TOKEN)
                Vue.ls.remove(LOGIN_RESULT_DATA)
                //console.log('logoutToken: '+ logoutToken)
                logout(logoutToken)
                    .then(() => {
                        //var sevice = "http://"+window.location.host+"/";
                        //var serviceUrl = encodeURIComponent(sevice);
                        //window.location.href = this.$variateConfig['casPrefixUrl']+"/logout?service="+serviceUrl;
                        resolve()
                    })
                    .catch(() => {
                        resolve()
                    })
            })
        }
    }
}

// 登录成功后公共方法提取
function successCallback (commit, response) {
    const result = response.result || {}
    const userInfo = result.userInfo || {}
    const companySet = result.companySet || {}
    const businessRuleList = companySet.businessRuleList || []
    localStorage.setItem('t_token', result.token)
    Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(SRM_VERSION, result.srmVersion, 3 * 24 * 60 * 60 * 1000)
    Vue.ls.set(USER_ELS_ACCOUNT, userInfo.elsAccount, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(USER_SUB_ACCOUNT, userInfo.subAccount, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(USER_COMPANYSET, companySet, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(SET_BUSINESS_RULE_LIST, businessRuleList, 7 * 24 * 60 * 60 * 1000)
    Vue.ls.set(LOGIN_RESULT_DATA, result, 3* 24 * 60 * 60 * 1000)
    commit('SET_LOGIN_RESULT_DATA', result)
    commit('SET_TOKEN', result.token)
    commit('SET_INFO', userInfo)
    commit('SET_COMPANYSET', companySet)
    commit('SET_BUSINESS_RULE_LIST', businessRuleList)
    commit('SET_NAME', { elsAccount: userInfo.elsAccount, subAccount: userInfo.subAccount, realname: userInfo.realname, welcome: welcome() })
    commit('SET_AVATAR', userInfo.avatar)
}
export default user
