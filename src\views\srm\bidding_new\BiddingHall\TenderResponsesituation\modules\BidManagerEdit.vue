<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="showLayOut"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :remoteJsFilePath="remoteJsFilePath"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { baseMixins } from '../../../plugins/baseMixins.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { postAction, getAction} from '@/api/manage'
import { add } from '@/utils/mathFloat.js'

export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            default: () => {},
            type: Object
        },
        propOfCheckType: {
            default: () => {
                return ''
            },
            type: [String, Number]
        }
    },
    mixins: [businessUtilMixin, baseMixins],
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subId () {
            return this.subpackageId()
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            noSubmit: true,
            businessRefName: 'businessRef',
            pageStatus: 'edit',
            confirmLoading: false,
            showLayOut: false,
            externalToolBar: {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            disabledItemNumber: true,
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectPurchaseBid', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: '关联tab',
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/purchase/supplierTenderProjectPurchaseBid/edit'
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}_0`}
                        }
                    },
                    key: 'save',
                    show: this.noSubmit,
                    showMessage: true,
                    // click: this.handleSave,
                    handleBefore: this.handleSubmitBefore,
                    handleAfter: this.handleSubmitAfter

                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/purchase/supplierTenderProjectPurchaseBid/submit'
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}_0`}
                        }
                    },
                    show: this.noSubmit,
                    key: 'submit',
                    showMessage: true,
                    handleBefore: this.handleSubmitBefore,
                    handleAfter: this.handleSubmitAfter
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            requestData: {
                detail: {
                    url: '/tender/purchase/supplierTenderProjectPurchaseBid/queryById', 
                    args: (that) => {
                        return { id: that.currentEditRow.id }
                    },
                    config: () => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${this.propOfCheckType || ''}_0`}
                        }
                    }
                }
            },
            url: {},
            listMsg: [],
            userInfo: {},
            projectObj: {},
            remoteJsFilePath: ''
        }
    },
    mounted (){
        if(!this.type){
            let { type } = this.currentEditRow
            this.type = type
        }
        
    },
    created () {
        let { checkType, tenderType } = this.subpackage
        let { type } = this.currentEditRow
        this.type = type
        console.log(this.currentEditRow)
        let noticeType = ''
        // （分包拿邀请状态tenderType）是邀请0，noticeType传0，否则{
        //      if checkType = 1，noticeType=2
        //      else if checkType = 0, 先判断分包中的checkType是否为0预审，是0预审，若从预审购标新增进来的，noticeType传1；从购标新增进来的，noticeType传0
        // }
        if(tenderType == '0'){
            noticeType = '0'
        }else{
            if(checkType == '1')noticeType = '2'
            else{
                if(type == '3')noticeType = '1'
                else if(type == '5'){
                    noticeType = '0'
                }
            }
        }
        
        
        if (!this.currentEditRow.id) {
            this.confirmLoading = true
            const params = {
                subpackageId: this.subId
                // checkType: this.checkType || '',
                // noticeType: noticeType
            }
            getAction('/tender/purchase/supplierTenderProjectPurchaseBid/queryOrder', params, {headers: {xNodeId: `${this.propOfCheckType}_0`}}
            ).then(res => {
                console.log('res', res)
                if(res.success) {
                    // 订单信息
                    this.listMsg = res.result.projectPurchaseBidOrderVOList
                    // 公告id
                    this.noticeId=res.result.noticeId
                    this.combination=res.result.projectPurchaseBidOrderVOList.combination || ''
                    this.getBusinessTemplate1()
                }else {
                    this.$emit('hide')
                    this.$message.warning(res.message)
                }
                this.confirmLoading = false
            })
        } else {
            this.showLayOut = true
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            this.remoteJsFilePath =  `${account}/purchase_SupplierTenderProjectPurchaseBid_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        attrHandle (){
            return {
                sourceNumber: this.tenderCurrentRow.tenderProjectNumber || this.tenderCurrentRow.id || '',
                actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'
            }
        },
        // handleSave (){
        //     this.confirmLoading = true
        //     let url=this.currentEditRow.id?'/purchase/supplierTenderProjectPurchaseBid/edit':'/purchase/supplierTenderProjectPurchaseBid/add'
        //     postAction(url, this.param).then(res => {
        //         if (res.success) {
        //             this.$message.success(res.message)
        //             this.currentEditRow.id=res.result.id?res.result.id:''
        //         }else{
        //             this.$message.warning(res.message)
        //         }
                
        //     }).finally(() => {
        //         this.confirmLoading = false
        //     })
        // },
        // 获取业务模板信息
        async getBusinessTemplate1 () {
            
            // const currentEditRow = await this.getBusinessTemplate('SupplierTenderProjectPurchaseBid')
            // if (!currentEditRow) {
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
            //     return
            // }
            // this.currentEditRow = Object.assign(this.currentEditRow, currentEditRow)
            // this.remoteJsFilePath = `${this.currentEditRow['templateAccount']}/purchase_SupplierTenderProjectPurchaseBid_${this.currentEditRow['templateNumber']}_${this.currentEditRow['templateVersion']}`
            // this.confirmLoading = false
            // this.showLayOut = true
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectPurchaseBid'}
            this.confirmLoading = true
            var that = this
            getAction('/template/templateHead/getListByType', params).then(res => {
                console.log('res', res)
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                templateNumber: item.templateNumber,
                                templateName: item.templateName,
                                templateVersion: item.templateVersion,
                                templateAccount: item.elsAccount
                            }
                        })
                        that.currentEditRow = options[0]
                        that.currentEditRow['tenderProjectPurchaseBidOrderVOList'] = that.listMsg

                        that.remoteJsFilePath = `${that.currentEditRow['templateAccount']}/purchase_SupplierTenderProjectPurchaseBid_${that.currentEditRow['templateNumber']}_${that.currentEditRow['templateVersion']}`
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
                this.confirmLoading = false
                this.showLayOut = true
            })
        },
        handleSubmitBefore (args) {
            console.log(this.type)

            var that = this

            return new Promise((resolve) => {
                let allData = args.allData
                if(!allData.supplierName){
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VSMeBtLRL_9963d032`, '请填写投标单位名称'))
                    this.confirmLoading = false
                    return
                }
                console.log(allData)
                allData.subpackageId=that.subId
                allData.tenderProjectName=that.tenderCurrentRow.tenderProjectName
                allData.tenderProjectNumber=that.tenderCurrentRow.tenderProjectNumber
                allData.templateAccount=that.currentEditRow.templateAccount || ''
                allData.templateVersion=that.currentEditRow.templateVersion || ''
                allData.templateNumber=that.currentEditRow.templateNumber || ''
                allData.templateName=that.currentEditRow.templateName || ''
                allData.noticeId=that.noticeId||allData.noticeId
                allData.checkType=that.type == '3' ? '0' : '1'
                // allData.saleTenderInvoiceInfoList&&allData.saleTenderInvoiceInfoList.map(item => {
                //     item.payMoney = allData.payType.payMoney||''
                //     item.payType = allData.payType.payType||''
                //     item.remark = allData.payType.remark||''
                // })
                console.log(allData)

                let params = {
                    allData: allData
                }
                // let target = [params.allData['invoiceInfo'], params.allData['payType']]
                // this.$set(params.allData, 'saleTenderInvoiceInfoList', target)
                // // params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]

                params.allData['saleTenderInvoiceInfoList'] = [Object.assign(params.allData['invoiceInfo'], params.allData['payType'])]
                console.log(params)
                args = Object.assign({}, args, params)
                console.log(args)
                resolve(args)
            })
        },
        handleSubmitAfter (args){
            return new Promise(resolve => {
                this.noSubmit = false
                return resolve(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log('resultData', resultData)
            console.log('pageConfig', pageConfig)
            // let {consortiumBidding} = resultData.consortiumBidding || ''
            console.log('this.subpackage', this.subpackage)
            // 非预审值
            let resultConsortiumBidding = this.subpackage.resultConsortiumBidding || ''
            console.log('resultConsortiumBidding', resultConsortiumBidding)
            // 预审值
            let preConsortiumBidding = this.subpackage.preConsortiumBidding || ''
            console.log('preConsortiumBidding', preConsortiumBidding)
            // 因为响应的购标页面有两个公告，就有两个consortiumBidding值（是否允许）,需要通过propOfCheckType判断预审非预审
            let consortiumBidding = this.propOfCheckType == 0 ? preConsortiumBidding : resultConsortiumBidding
            console.log('consortiumBidding', consortiumBidding)
            
            // 编辑赋值 payMoney saleAmount  tenderProjectPurchaseBidOrderVOList
            const tenderProjectPurchaseBidOrderVOList = resultData['tenderProjectPurchaseBidOrderVOList']
            // 累加所有文件的标价总和
            let payMoneys = 0
            if (tenderProjectPurchaseBidOrderVOList && tenderProjectPurchaseBidOrderVOList.length > 0) {
                tenderProjectPurchaseBidOrderVOList.forEach(item => {
                    const saleAmount = item.saleAmount ? parseFloat(item.saleAmount) : 0
                    payMoneys = add(payMoneys, saleAmount)
                })
            }
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        let {invoice, 
                            invoiceType,
                            enterpriseName, 
                            dutyParagraph, 
                            enterpriseAddress, 
                            enterprisePhone,
                            depositBank,
                            bankNumber } = saleTenderInvoiceInfoList[0]
                        group['formModel'] = {
                            invoice: invoice, 
                            invoiceType: invoiceType,
                            enterpriseName: enterpriseName, 
                            dutyParagraph: dutyParagraph, 
                            enterpriseAddress: enterpriseAddress, 
                            enterprisePhone: enterprisePhone,
                            depositBank: depositBank,
                            bankNumber: bankNumber }
                    }
                    if (group.groupCode == 'payType') {
                        let {payType, payMoney, remark} = saleTenderInvoiceInfoList[0]
                        
                        group['formModel'] = {payType, payMoney: payMoney || payMoneys, remark}
                        // group['formModel'] = Object.assign(group['formModel'], saleTenderInvoiceInfoList[0], {payMoney: payMoney})
                    }
                })
            } else {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'payType') {
                        group['formModel']['payMoney'] = payMoneys
                    }
                })
            }
            // let saleTenderInvoiceInfoList = resultData.saleTenderInvoiceInfoList && resultData.saleTenderInvoiceInfoList.length > 0 ? resultData.saleTenderInvoiceInfoList[0] : {}
            // pageConfig.groups[2].formModel.invoiceType = saleTenderInvoiceInfoList.invoiceType || ''
            // pageConfig.groups[2].formModel.invoice = saleTenderInvoiceInfoList.invoice || ''
            // pageConfig.groups[2].formModel.enterpriseName = saleTenderInvoiceInfoList.enterpriseName || ''
            // pageConfig.groups[2].formModel.dutyParagraph = saleTenderInvoiceInfoList.dutyParagraph || ''
            // pageConfig.groups[2].formModel.enterpriseAddress = saleTenderInvoiceInfoList.enterpriseAddress || ''
            // pageConfig.groups[2].formModel.enterprisePhone = saleTenderInvoiceInfoList.enterprisePhone || ''
            // pageConfig.groups[2].formModel.depositBank = saleTenderInvoiceInfoList.depositBank || ''
            // pageConfig.groups[2].formModel.bankNumber = saleTenderInvoiceInfoList.bankNumber || ''

            // // 编辑赋值
            // // const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            // const tenderProjectPurchaseBidOrderVOList = resultData['tenderProjectPurchaseBidOrderVOList']
            // // 累加所有文件的标价总和
            // let payMoney = 0
            // if (tenderProjectPurchaseBidOrderVOList && tenderProjectPurchaseBidOrderVOList.length > 0) {
            //     tenderProjectPurchaseBidOrderVOList.forEach(item => {
            //         const saleAmount = item.saleAmount ? parseFloat(item.saleAmount) : 0
            //         payMoney = add(payMoney, saleAmount)
            //     })
            // }
            // debugger
            // pageConfig.groups[3].formModel.payMoney = payMoney || ''
            // pageConfig.groups[3].formModel.payType = saleTenderInvoiceInfoList.payType || ''
            // pageConfig.groups[3].formModel.remark = saleTenderInvoiceInfoList.remark || ''
            // //2002-5-20这里将create里请求到的list数据插入

            if(consortiumBidding != '1'){
                pageConfig.groups[0].formModel.combination='0'
                pageConfig.groups[0].formModel.combinationName=''
            }else{
                pageConfig.groups[0].formModel.combination='1'
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let flag = (consortiumBidding !== '1')
            let nameFlag = (pageConfig.groups[0].formModel.combination == '0')
            // let disabledFlag = (opt == '1')
            setDisabledByProp('combination', flag)
            setDisabledByProp('combinationName', nameFlag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
            
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            let validateFlag = (consortiumBidding !== '1')
            let validNameFlag = (pageConfig.groups[0].formModel.combination == '0')

            // let delayCountValidateFlag = (opt == '1')

            setValidateRuleByProp('combination', validateFlag)
            setValidateRuleByProp('combinationName', validNameFlag)
            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierName') {
                    formModel[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
                if (key == 'toElsAccount') {
                    formModel[key] = resultData[key] || this.userInfo.elsAccount
                }
            }

            // // 编辑赋值
            // const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            // if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
            //     pageConfig.groups.forEach(group => {
            //         if (group.groupCode == 'invoiceInfo') {
            //             group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
            //         }
            //         if (group.groupCode == 'payType') {
            //             group['formModel'] = Object.assign({}, group['formModel'], saleTenderInvoiceInfoList[0])
            //         }
            //     })
            // }


            // 判断是否有 单位名称，没有就取用户信息里面的
            // pageConfig.groups[0].formModel.supplierName = pageConfig.groups[0].formModel.supplierName || this.userInfo.enterpriseName

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        }
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>
