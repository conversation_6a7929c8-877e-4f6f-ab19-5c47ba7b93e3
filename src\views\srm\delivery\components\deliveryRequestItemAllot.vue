<!--
 * @Author: gongzhirong
 * @Date: 2022-06-20 14:22:02
 * @LastEditTime: 2022-08-09 16:22:16
 * @LastEditors: gongzhirong
 * @Description: 
-->
<template>
  <div>
    <vxe-modal
      value
      @hide="close"
      width="68%"
      :loading="modalLoading"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据')"
    >
      <template>
        <vxe-grid
          ref="grid"
          v-bind="gridConfig"
          :data="tableData"
          @page-change="pageChange"
          :loading="tableLoading"
          border="full"
        >
          <template v-slot:toolbar_buttons>
            <a-form-model
              layout="inline"
              :model="searchParams"
              @submit="search"
              @submit.native.prevent
            >
              <a-form-model-item>
                <a-input
                  style="width: 380px"
                  v-model="searchParams.keyWord"
                  :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
                >
                  <a-icon
                    slot="prefix"
                    type="search"
                    style="color: rgba(0, 0, 0, 0.25)"
                  />
                </a-input>
              </a-form-model-item>
              <a-form-model-item>
                <a-button
                  type="primary"
                  html-type="submit"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}
                </a-button>
                <a-button
                  style="margin-left: 10px"
                  @click="settingColumns"
                >
                  {{ $srmI18n(`${$getLangAccount()}#i18n_title_listCustom`, '列自定义') }}
                </a-button>
              </a-form-model-item>
            </a-form-model>
            <div class="numberShow">可分配数量： {{ currentEditRow.allocatableQuantity || 0 }}</div>
          </template>

          <template #assigned-quantity="{ row }">
            <a-input-number
              v-model="row.assignedQuantity"
              :min="0"
              :max="row.allocatableQuantity || 0"
              :precision="2"
              @blur="assignedQuantityBlur(row)"
            />
          </template>
        </vxe-grid>

        <div class="footer">
          <a-button
            type="primary"
            @click="allotSubmit"
          >
            {{ $srmI18n(`${$getLangAccount()}#i18n_btn_zEGcIdSeRt_c0b39796`, '分配创建送货通知单') }}
          </a-button>
          <a-button @click="close">
            {{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}
          </a-button>
        </div>
      </template>
    </vxe-modal>
    <column-setting
      ref="columnSettingModal"
      :hiddenFieldColors="true"
      :customReload="true"
      @success="reloadColunm"
    ></column-setting>
  </div>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import columnSetting from '@comp/template/columnSetting'
export default {
  components: { columnSetting },
  props: {
    currentEditRow: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      modalLoading: false,
      tableLoading: false,
      searchParams: {},
      currentParams: {},
      gridConfig: {
        align: 'center',
        size: 'small',
        height: '580',
        // checkboxConfig: {
        //     trigger: 'row'
        // },
        // mouseConfig: {
        //     selected: true
        // },
        // editConfig: {
        //     mode: 'cell'
        // },
        columns: [],
        editRules: {
          assignedQuantity: [
            {
              required: true,
              validator: this.assignedQuantityValidator
            }
          ]
        },
        toolbarConfig: {
          slots: {
            buttons: 'toolbar_buttons'
          }
        },
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 500,
          align: 'right',
          pageSizes: [20, 50, 100, 200, 500],
          layouts: ['Sizes', 'PrevPage', 'Number', 'NextPage', 'FullJump', 'Total'],
          perfect: true
        }
      },
      tableData: []
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    search() {
      this.currentParams.keyWord = this.searchParams.keyWord
      this.getData()
    },
    assignedQuantityValidator({ row }) {
      // 判断是否已勾选
      const s = this.$refs.grid.isCheckedByCheckboxRow(row)
      if (s && !(row.assignedQuantity > 0)) {
        return new Error('分配数量必填！')
      }
    },
    async getData() {
      try {
        this.tableLoading = true
        const { currentPage, pageSize } = this.gridConfig.pagerConfig
        const params = {
          pageNo: currentPage,
          pageSize: pageSize,
          keyWord: this.currentParams.keyWord,
          company: this.currentEditRow.company,
          factory: this.currentEditRow.factory,
          purchaseOrg: this.currentEditRow.purchaseOrg,
          materialNumber: this.currentEditRow.materialNumber,
          id: this.currentEditRow.id
        }
        const res = await getAction('/delivery/purchaseDeliveryRequestItem/extractOrder', params)
        if (res.success && res.result) {
          this.tableData = res.result.records
          this.gridConfig.pagerConfig.total = res.result.total
          this.gridConfig.pagerConfig.currentPage = res.result.current
        } else {
          // this.$message.error(res.message)
          this.gridConfig.pagerConfig.currentPage = 1
          this.gridConfig.pagerConfig.total = 0
          this.tableData = []
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.tableLoading = false
      }
    },
    /**
     * @description: 分配并创建送货通知单
     */
    async allotSubmit() {
      try {
        this.modalLoading = true
        const grid = this.$refs.grid
        const errorList = await grid.validate(this.tableData)
        if (errorList) return
        const selectedList = grid.getCheckboxRecords()
        if (selectedList.length < 1) return this.$message.error('请设置需要分配的物料！')

        const { id } = this.currentEditRow
        const params = selectedList
        const { message, success, result } = await postAction(`/delivery/purchaseDeliveryRequestItem/toCreateDelivery/${id}`, params)
        if (success) {
          this.$message.success(message)
          this.$emit('success-cb', result && result[0] ? result[0] : null)
          this.close()
        } else {
          this.$message.error(message)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.modalLoading = false
      }
    },
    pageChange({ currentPage, pageSize }) {
      this.gridConfig.pagerConfig.currentPage = currentPage
      this.gridConfig.pagerConfig.pageSize = pageSize
      this.getData()
    },

    // 分配数量输入框失焦时，如果数量大于0，自动勾选
    assignedQuantityBlur(row) {
      if (row.assignedQuantity > 0) {
        const grid = this.$refs.grid
        grid.setCheckboxRow(row, true)
      }
    },

    async getColumns() {
      try {
        let res = await getAction('/base/userColumnDefine/queryCurrentUserColumnDefine/PurchaseDeliveryRequestAllocationList')
        let columns = res.result.map((item) => {
          let newItem = {
            field: item.columnCode,
            title: this.$srmI18n(`${this.$getLangAccount()}#${item.columnNameI18nKey}`, item.columnName),
            width: item.columnWidth,
            showOverflow: true,
            visible: item.hidden != 1,
            align: item.alignType,
            resizable: true,
            fixed: (item.fixType == 'left' || item.fixType == 'right')? item.fixType : null,
          }
          // if (['订单可分配数量', '分配数量'].includes(item.columnName)) {
          //   newItem.fixed = 'right'
          // }
          if (item.columnName == '分配数量') {
            newItem = {
              ...newItem,
              editRender: {},
              slots: { default: 'assigned-quantity' }
            }
          }
          return newItem
        })
        columns.unshift(
          ...[
            { type: 'checkbox', width: 60, fixed: 'left' },
            { type: 'seq', title: '序号', width: 50, fixed: 'left' }
          ]
        )
        this.gridConfig.columns = columns
      } catch (error) {}
    },

    // 列自定义
    settingColumns() {
      this.$refs.columnSettingModal.open('PurchaseDeliveryRequestAllocationList')
    },

    reloadColunm() {
      this.$emit('reloadColunm', 'single')
    }
  },
  async created() {
    await this.getColumns()
    await this.getData()
  }
}
</script>
<style scoped lang="less">
.footer {
  margin-top: 18px;
  text-align: center;
  .ant-btn + .ant-btn {
    margin-left: 58px;
  }
}

:deep(.vxe-toolbar .vxe-buttons--wrapper) {
  justify-content: space-between;

  .numberShow {
    font-size: 20px;
    margin-right: 100px;
  }
}
</style>
