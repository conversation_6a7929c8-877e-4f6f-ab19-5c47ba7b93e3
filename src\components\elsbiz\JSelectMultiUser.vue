<template>
  <j-select-biz-component
    :value="value"

    name="用户"
    display-key="realname"

    :list-url="url.list"
    :columns="columns"
    query-param-text="账号"

    v-on="$listeners"
    v-bind="$attrs"
  />
</template>

<script>
import JSelectBizComponent from './JSelectBizComponent'

export default {
    name: 'JSelectMultiUser',
    components: { JSelectBizComponent },
    props: ['value'],
    data () {
        return {
            url: { list: '/account/elsSubAccount/list' },
            columns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), align: 'center', width: 100, dataIndex: 'realname' },
                { title: '账号', align: 'center', width: 100, dataIndex: 'username' },
                { title: '电话', align: 'center', width: 100, dataIndex: 'phone' },
                { title: '出生日期', align: 'center', width: 100, dataIndex: 'birthday' }
            ]
        }
    },
    watch: {
        $attrs: {
            deep: true,
            immediate: true,
            handler (val) {
                if (!val.returnKeys) {
                    val.returnKeys = ['id', 'username']
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped></style>