<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show="!showDetailPage && !showEditPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
    />
    <!-- 详情界面 -->
    <SalePaymentApplyDetail
      v-if="showDetailPage"
      ref="detailPage" 
      :current-edit-row="currentEditRow" 
      @hide="hideEditPage" />
  </div>
</template>
<script>
import SalePaymentApplyDetail from './modules/SalePaymentApplyDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import { httpAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
export default {
    mixins: [ListMixin],
    components: {
        SalePaymentApplyDetail
    },
    data () {
        return {
            showEditPage: false,
            tabsList: [],
            pageData: {
                businessType: 'paymentApply',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNBVUVty_5712f75`, '请输入付款申请单号')
                    }
                ],
                form: {
                    keyWord: '',
                    name: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'paymentApply#salePaymentApplyHead:view'},
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat, allow: this.allowChat}
                ],
                optColumnWidth: 80
            },
            url: {
                list: '/finance/salePaymentApplyHead/list',
                columns: 'SalePaymentApplyHead'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/finance/salePaymentApplyHead/counts')
    },
    methods: {
        handleChat (row) {
            let { id } = row
            let recordNumber = row.paymentApplyNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'SalePaymentApply', url: this.url || '', recordNumber})
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>