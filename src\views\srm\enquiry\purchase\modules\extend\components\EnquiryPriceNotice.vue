<template>
  <a-modal
    centered
    v-drag
    :mask-closable="false"
    :title="title"
    :visible="visible"
    :confirmLoading="loading"
    :width="580"
    @cancel="visible = false"
    @ok="handleOk">
    <a-select
      style="width: 100%"
      v-model="form.pricingNotice"
      :options="options"/>
  </a-modal>
</template>

<script>
import {getAction} from '@api/manage'
import {ajaxFindDictItems} from '@api/api'

export default {
    data (){
        return{
            form: {},
            options: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_GRIueRId_3aaaeb60`, '设置定价通知对象'),
            visible: false,
            loading: false
        }
    },
    methods: {
        handleOk (){
            this.visible = false
            this.loading = true
            getAction('/enquiry/purchaseEnquiryHead/pricingNotice', this.form).then(res => {
                if(res.success){
                    this.$notification.success({description: res.message, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_tkLRW_b20577c3`, '操作成功')})
                    this.$emit('success')
                }else{
                    this.$notification.warning({description: res.message, message: '警告'})
                }
            }).finally(() => {
                this.loading = false
            })
        },
        open ({headId, pricingNotice}){
            const params = {busAccount: this.$ls.get('Login_elsAccount'), dictCode: 'srmPricingNotice'}
            ajaxFindDictItems(params).then(res => {
                if(res.success){
                    this.options = res.result.map(option => ({label: option.text, value: option.value}))
                    this.$set(this.form, 'headId', headId)
                    this.$set(this.form, 'pricingNotice', pricingNotice)
                    this.visible = true
                }
            })
        }
    },
    name: 'EnquiryPriceNotice'
}
</script>
