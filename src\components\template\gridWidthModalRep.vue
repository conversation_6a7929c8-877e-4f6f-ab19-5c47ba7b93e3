<template>
  <div>
    <a-modal
      v-drag    
      centered
      :title="modalTitle"
      :width="960"
      :visible="visible"
      :maskClosable="false"
      :confirmLoading="confirmLoading"
      @ok="selectedOk"
      @cancel="close">
      <div id="modalFromRep">
        <a-input-search
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputThekeyword`, '请输入关键字')"
          style="width: 300px;margin-right:8px"
          v-model="form.keyWord"
          @search="onSearch"
          enterButton />
        <a-checkbox
          v-model="checkStatus">
          反选
        </a-checkbox>
      </div>
      <div class="vxeGridDisplay">
        <vxe-grid
          border
          resizable
          show-overflow
          highlight-hover-row
          height="450"
          row-id="id"
          size="small"
          :loading="loading"
          ref="selectGrid"
          :seq-config="{startIndex: (tablePage.currentPage - 1) * tablePage.pageSize}"
          :data="tableData"
          :pager-config="(isTree || !needPager) ? null : tablePage"
          :radio-config="selectModel === 'single' ? checkedConfig : undefined"
          :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
          :tree-config="isTree ? treeConfig : null"
          :columns="columns"
          @checkbox-change="selectChangeEvent1"
          @page-change="handlePageChange">
        </vxe-grid>
        <vxe-grid
          border
          resizable
          show-overflow
          highlight-hover-row
          height="450"
          row-id="id"
          size="small"
          ref="selectGridTwo"
          :data="tableDataTwo"
          :columns="columns"
          :checkbox-config="selectModel === 'multiple' ? checkedConfig : undefined"
          @checkbox-change="selectChangeEvent2"
        >
        </vxe-grid>
      </div>
      
    </a-modal>
  </div>
</template>

<script>
import { httpRequest, getAction } from '@/api/manage'
export default {
    name: 'GridWidthModalRep',
    props: {
        isEmit: {
            type: Boolean,
            default: false
        },
        requestMethod: {
            type: String,
            default: 'get'
        },
        isTree: {
            type: Boolean,
            default: false
        },
        treeConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        pageConfigData: {
            type: Object,
            default () {
                return {}
            }
        },
        modalTitle: {
            type: String,
            default () {
                return this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SelectData`, '选择数据')
            }
        },
        handleListData: {
            type: Function,
            default: null
        },
        row: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            confirmLoading: false,
            columns: [],
            url: '',
            selectModel: 'multiple',
            checkedConfig: {highlight: true, reserve: true, trigger: 'row'},
            queryParams: {},
            tableData: [],
            form: {
                keyWord: ''
            },
            needPager: true,
            tablePage: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
                align: 'left',
                pageSizes: [10, 20, 50, 100, 200, 500],
                // layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump'],
                perfect: true
            },
            tableDataTwo: [],
            checkStatus: false
        }
    },
    methods: {
        onCheckChange (e){
      
            this.checkStatus=e.target.checked
        
        },
        selectChangeEvent1 (data){
            let tableDataTwo = [...this.tableDataTwo, ...data.records, ...data.reserves]
            //删除arr中的重复对象
            var newArr= []
            var arrId = []
            for(var item of tableDataTwo){
                if(arrId.indexOf(item['id']) == -1){
                    arrId.push(item['id'])
                    newArr.push(item)
                }
            }
            this.tableDataTwo = newArr
            // this.tableDataTwo = [...new Set(tableDataTwo)]
        },
        selectChangeEvent2 (data){
            this.tableDataTwo = this.tableDataTwo.filter(res => res.id !== data.records[0]?.id)
            // 把左侧表格的数据取消选中
            this.$refs.selectGrid.setCheckboxRow(data.records[0], false)
        },
        loadData (params, extend) {
            this.loading = true
            httpRequest(this.url, params, this.requestMethod).then((res) => {
                this.loading = false
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                if (this.handleListData) { // 单独处理list数据
                    return this.handleListData(this, res)
                }
                const flag = this.isTree
                    ? res.success && res.result && res.result.length
                    : res.success && res.result && res.result.records && res.result.records.length
                if (flag) {
                    let result = this.isTree ?  res.result : res.result.records
                    result = result || []
                    this.tableData = result
                    this.tablePage.total = res.result.total
                    if (extend && extend.action === 'init') {
                        // 初始化清空选项 包括跨页勾选的
                        if (this.selectModel === 'single') {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearRadioReserve()
                        }else {
                            this.$refs.selectGrid && this.$refs.selectGrid.clearCheckboxReserve()
                        }
                    }
                } else {
                    this.tableData = []
                    this.tablePage.total = 0
                    this.tablePage.currentPage = 1
                }
            })
        },
        // 获取右侧列表数据 
        getRightData (){
            this.tableDataTwo = []
            let ids = ''
            const rebateProductUrl='/material/purchaseMaterialHead/queryPageListByCalculation'
            const sourceRestrictionUrl ='/supplier/supplierMaster/queryPageListByCalculation'
            const storeRestrictionUrl = '/org/purchaseOrganizationInfo/queryPageListByCalculation'
            if(this.url == rebateProductUrl){
                ids =  this.row.rebateProduct
            }else if(this.url == sourceRestrictionUrl){
                ids =  this.row.sourceRestriction
            }else if(this.url == storeRestrictionUrl){
                ids =  this.row.storeRestriction
            }
            let params = {frozenFunctionValue: '1', ids}
            if(ids == '' || !ids) {
                return
            }
            httpRequest(this.url, params, this.requestMethod).then((res) => {
                if (!res.success) {
                    this.$message.error(res.message)
                    return
                }
                this.tableDataTwo = res.result.records
                for(let i= 0; i<this.tableDataTwo.length;i++){
                    this.$refs.selectGrid.setCheckboxRow(this.tableDataTwo[i])
                }
            })
        },
        open (url, params, columns, selectModel, checkedConfig) {
            // 打开清空keyWord
            this.form.keyWord = ''
            this.tablePage.currentPage = 1
            let tableColumns = columns ? [...columns] : []
            this.queryParams = {pageSize: this.tablePage.pageSize, pageNo: this.tablePage.currentPage}
            checkedConfig ? this.checkedConfig = {...this.checkedConfig, ...checkedConfig} : ''
            this.url = url
            if (selectModel) {
                this.selectModel = selectModel
            }
            tableColumns.unshift({ type: 'seq',  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), width: 60 })
            if (this.selectModel === 'single') {
                tableColumns.unshift({ type: 'radio', width: 40 })
            }else if (this.selectModel === 'multiple') {
                tableColumns.unshift({ type: 'checkbox', width: 40 })
            }
            tableColumns.forEach(col=>{ // 国际化处理
                if(col.fieldLabelI18nKey){
                    col.title = this.$srmI18n(`${this.$getLangAccount()}#${col.fieldLabelI18nKey}`, col.title)
                }
            })
            this.columns = tableColumns
            if (params) {
                this.queryParams = Object.assign({}, this.queryParams, params)
            }
            this.loadData(this.queryParams, {action: 'init'})
            this.getRightData()
            this.visible = true
        },
        close () {
            this.visible = false
        },
        selectedOk () {
            let selectedData = this.tableDataTwo
            if (selectedData.length) {
                this.visible = false
                if (this.pageConfigData.itemColumns) { // 表行
                    selectedData.forEach(item => {
                        this.pageConfigData.itemColumns.forEach(el => {
                            if (el.defaultValue && (item[el.field] == '' || item[el.field] == null)) { // 模板有默认值且当前表单返回没有值
                                item[el.field] = el.defaultValue
                            }   
                        })
                    })
                }
                if (this.isEmit) {
                    this.$emit('ok', selectedData)
                } else {
                    this.$parent.fieldSelectOk(selectedData)
                }
            }else {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
            }
        },
        handlePageChange ({ currentPage, pageSize }) {
            this.tablePage.currentPage = currentPage
            this.tablePage.pageSize = pageSize
            let params = Object.assign({}, this.queryParams, {pageSize: pageSize, pageNo: currentPage})
            this.loadData(params)
        },
        onSearch (keyWord) {
            this.form.keyWord = keyWord
            this.queryParams = Object.assign({}, this.queryParams, {keyWord: this.form.keyWord })
            let params = Object.assign({}, this.queryParams, { pageNo: 1})
            this.loadData(params)
        }
    }
}
</script>
<style scoped lang='less'>
#modalFromRep{
   
   display: flex;
   align-items: center;
   margin-bottom: 8px;
}
    .vxeGridDisplay{
        display: flex;
        justify-content: space-between;
        .vxe-grid{
            width: 49%;
        }
        // .vxe-pager.size--small{
        //     height: 80px;
        // }
    }
</style>