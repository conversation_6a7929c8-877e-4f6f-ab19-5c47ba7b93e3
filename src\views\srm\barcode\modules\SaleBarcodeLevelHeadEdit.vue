<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        :isEmit="true"
        @ok="checkItemSelectOk"
        ref="fieldSelectModal" />
      <field-select-modal
        :isEmit="true"
        @ok="checkSupplierSelectOk"
        ref="SupplierFieldSelectModal" />
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'SaleBarcodeLevelHeadEdit',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            refresh: true,
            requestData: {
                detail: {
                    url: '/base/barcode/saleBarcodeLevelHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
                barcodeLevelItemList: [{
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                    key: 'gridAdd',
                    attrs: { type: 'primary' },
                    click: this.addItemBarcode
                }, {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                    key: 'gridDelete'
                }]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/base/barcode/saleBarcodeLevelHead/edit'
                    },
                    key: 'save',
                    showMessage: true,
                    handleBefore: this.handleSaveBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publish`, '发布'),
                    args: {
                        url: '/base/barcode/saleBarcodeLevelHead/publish'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish',
                    handleBefore: this.handlePublishBefore,
                    handleAfter: this.handlePublishAfter,
                    show: this.handleShowFn
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/saleBarcodeLevelHead/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_barcodeLevel_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        // 处理发布回调之前的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.barcodeLevelItemList || !args.allData.barcodeLevelItemList.length) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_JtToxOLV_7dd590e7`, '子级条码不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        handleSaveBefore (args) {
            return new Promise((resolve, reject) => {
                // 逻辑判断
                if (!args.allData.barcode) {
                    this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ToxOLVW_fc0b7831`, '条码不能为空！'))
                    reject(args)
                }else {
                    resolve(args)
                }
            })
        },
        // 处理发布回调之后的逻辑
        // 统一为异步方式，入参为 配置 + 页面所有数据 { Vue, pageConfig, btn, allData }
        handlePublishAfter (args) {
            return new Promise(resolve => {
                return resolve(args)
            })
        },
        handleShowFn () {
            let rs = true
            if (this.currentEditRow && this.currentEditRow.id) {
                rs = true
            } else {
                rs = false
            }
            return rs
        },
        goBack () {
            this.$parent.hideEditPage()
        },
        addItemBarcode ({ Vue, pageConfig, btn, groupCode }) {debugger
            let itemGrid = this.getItemGridRef(groupCode)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)
    
            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    acc[obj.field] = obj.defaultValue || ''
                    return acc
                }, {})
            // row.printNumber = this.getBusinessExtendData(this.businessRefName).allData.printNumber
            itemGrid.insertAt([row], -1)
        }

    }
}
</script>