<template>
  <div class="page-container">
    <edit-layout
      refresh
      ref="editPage"
      :page-data="pageData"
      :currentEditRow="currentEditRow"
      :url="url">
      <!-- 测试分组插槽 -->
      <!-- <template #remoteJs="{ pageData, resultData }">
        <div class="pageData">
            <h1>pageData</h1>
            {{ pageData }}
        </div>

        <div class="resultData">
            <h1>resultData</h1>
            {{ resultData }}
        </div>
      </template> -->
    </edit-layout>
    <field-select-modal
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <set-confirm-item-modal ref="setConfirmItemModal" />

    <ItemImportExcel
      ref="itemImportExcel"
      @importCallBack="importCallBack"/>
  </div>
</template>

<script lang="jsx">
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {downFile, getAction, postAction } from '@/api/manage'
import SetConfirmItemModal from './SetConfirmItemModal'
import moment from 'moment'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { ajaxFindDictItems } from '@/api/api'
import ItemImportExcel from '@comp/template/import/ItemImportExcel'

export default {
    name: 'EbiddingBuyEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal,
        SetConfirmItemModal,
        ItemImportExcel
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            selectType: 'material',
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingItemList',
                        columns: [],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addEbiddingItem},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteItemEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dISV_2766e3d4`, '向下填充'), key: 'fillDown', type: 'tool-fill', beforeCheckedCallBack: this.fillDownGridItem},
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_importExcel`, '导入Excel'),
                                params: this.importParams,
                                click: this.importExcel

                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_templeteDownload`, '模板下载'),
                                click: this.downloadTemplate
                            }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingSupplierList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 150 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), field: 'supplierCode', width: 150 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'),
                                field: 'needCoordination',
                                width: 120,
                                dictCode: 'srmSupplierCoordinationWay',
                                slots: {default: 'renderDictLabel'}
                            }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addSupplierEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteSupplierEvent}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'), groupCode: 'confirmItem', type: 'grid', custom: {
                        ref: 'purchaseEbiddingConfirmList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'confirmDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'), width: 220, editRender: {name: '$input'} },
                            { field: 'must', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: {name: '$switch', props: {openValue: '1', closeValue: '0'}} },
                            { field: 'writeType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'), width: 120, dictCode: 'inspection_item_write_type', editRender: {name: '$select', options: []} },
                            { field: 'confirmItemList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'), width: 120,
                                slots: {
                                    default: ({row}) =>{
                                        if (row && ['0', '1'].includes(row.writeType)) {
                                            let label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notSet`, '未设置')
                                            if (row.confirmItemList && row.confirmItemList.length > 0) {
                                                label =this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isSet`, '已设置')
                                            }
                                            return [
                                                <a onClick={() => this.setConfrimItem(row)}>{label}</a>
                                            ]
                                        } else {
                                            return ''
                                        }
                                    }
                                }
                            },
                            { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), editRender: {name: '$input'} }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '添加'), type: 'primary', click: this.addConfirm},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteConfirm}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120, dictCode: 'srmFileType', editRender: {name: '$select', options: []} },
                            { field: 'stageType',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'),
                                width: 120,
                                dictCode: 'srmEbiddingStageType',
                                editRender: {
                                    // enabled: true
                                    // name: '$select', options: []
                                }, 
                                slots: {
                                    default: ({row}) => {
                                        let dictcode = 'srmEbiddingStageType'
                                        return [
                                            row['stageType'] ? this.getDictLabel(row['stageType'], dictcode) : ''
                                        ]
                                    },
                                    edit: ({row}) => {
                                        const form = this.$refs.editPage.getPageData()
                                        // 0 邀请竞价
                                        let dictcode = form.ebiddingScope === '0' ? 'srmEbidding4StageType' : 'srmEbiddingStageType'
                                        return [
                                            <m-select configData={row} getPopupContainer={triggerNode => {
                                                return triggerNode.parentNode || document.body
                                            }}
                                            v-model={row.stageType}
                                            placeholder={this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseChoose`, '请选择')}
                                            dict-code={dictcode} />
                                        ]
                                    }
                                }
                            },
                            { field: 'required', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120, cellRender: {name: '$switch', props: {openValue: '1', closeValue: '0'}} },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220, editRender: {name: '$input'} }
                        ],
                        rules: {fileType: [{required: true, message: '必填!'}], stageType: [{required: true, message: '必填!'}]},
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), type: 'primary', click: this.addFileDemand},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deleteFileDemand}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'),
                                width: 120, required: true
                            //   dictCode: 'srmFileType',
                            //   editRender: {name: '$select', options: []}
                            },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                dictCode: 'srmFileType', type: 'upload', businessType: 'ebidding',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack},
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), 
                                click: this.deleteBatch
                            }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publishEvent, showCondition: this.showPublishConditionBtn,
                        authorityCode: 'ebidding#purchaseEbiddingHead:publish' },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/ebidding/purchaseEbiddingHead/add',
                edit: '/ebidding/purchaseEbiddingHead/edit',
                detail: '/ebidding/purchaseEbiddingHead/queryById',
                public: '/ebidding/purchaseEbiddingHead/publish',
                isExistFrozenSource: 'supplier/supplierMaster/isExistFrozenSource',
                submit: '/a1bpmn/audit/api/submit',
                upload: '/attachment/purchaseAttachment/upload',
                materialEbidding: '/inquiry/searchSource/materialEbidding',
                import: '/els/base/excelByConfig/importExcel',
                downloadTemplate: '/base/excelByConfig/downloadTemplate'
            },
            currentRow: {},
            optionsMap: []
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_ebidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        this.getDictData()
    },
    methods: {

        init () {
            // queryDetail方法已经处理了id,可以直接调用
            if (this.currentEditRow) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id, (data) => {
                    this.editFormData = data
                    let attachmentList = this.editFormData.purchaseAttachmentList||[]
                    let purchaseRequestItemList = this.editFormData.purchaseEbiddingItemList || []
                    let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
                        acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                        return acc
                    }, {})
                    attachmentList.forEach(item => {
                        let number = item.itemNumber
                        if (number && materialMap[number] && !item.materialName) {
                            item.materialNumber = materialMap[number].split('_')[0]
                            item.materialName = materialMap[number].split('_')[1]
                        }
                    })
                })
            }
        },

        downloadTemplate () {
            const form = this.$refs.editPage.getPageData()
            let params = {'id': form.id, 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'purchaseEbiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase'}
            if (!params.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsMWFWKIKIr_de818fdb`, '请先保存数据，再下载模板！'))
                return
            } else {
                downFile(this.url.downloadTemplate, params).then((data) => {
                    if (!data) {
                        this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileDownloadFailed`, '文件下载失败'))
                        return
                    }
                    if (typeof window.navigator.msSaveBlob !== 'undefined') {
                        window.navigator.msSaveBlob(new Blob([data]), 'template.xlsx')
                    } else {
                        let url = window.URL.createObjectURL(new Blob([data]))
                        let link = document.createElement('a')
                        link.style.display = 'none'
                        link.href = url
                        link.setAttribute('download', this.$srmI18n(`${this.$getLangAccount()}#`, '竞价行导入模板') + '.xlsx')
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link) //下载完成移除元素
                        window.URL.revokeObjectURL(url) //释放掉blob对象
                    }
                }).finally(() => {
                    this.gridLoading = false
                })
            }
        },
        importExcel () {
            const form = this.$refs.editPage.getPageData()
            let params = {'handlerName': 'purchaseEbiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase',
                'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息')
            }
            if (form.id){
                params.id = form.id
            }
            this.$refs.itemImportExcel.open(params, this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'), 'purchaseBiddingItemList')
        },
        importParams () {
            const form = this.$refs.editPage.getPageData()
            let params = { 'templateAccount': form.templateAccount, 'templateNumber': form.templateNumber, 'templateVersion': form.templateVersion,
                'handlerName': 'purchaseEbiddingItemExcelRpcServiceImpl', 'roleCode': 'purchase',
                'excelName': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息')}
            if (form.id){
                params.id = form.id
            }
            return params
        },
        importCallBack (result) {
            if (result.file.status === 'done') {
                let response = result.file.response
                if (response.success) {
                    let itemGrid = this.$refs.editPage.$refs.purchaseEbiddingItemList[0]
                    let insertData = response.result.dataList
                    this.pageConfig.itemColumns.forEach(item => {
                        if (item.defaultValue) {
                            insertData.forEach(insert => {
                                if (!insert[item.field]) {
                                    insert[item.field] = item.defaultValue
                                }
                            })
                        }
                    })
                    itemGrid.insertAt(insertData, -1)
                } else {
                    this.$message.warning(response.message)
                }
            }
        },
        attrHandle () {
            let cn = ''
            if (this.currentEditRow.ebiddingNumber) {
                cn = this.currentEditRow.ebiddingNumber
            } else {
                const params = this.$refs.editPage.getPageData()
                cn = params.ebiddingNumber
            }
            return {
                sourceNumber: cn,
                actionRoutePath: '/srm/ebidding/EbiddingBuyHeadList,/srm/ebidding/sale/EbiddingSaleHeadList'
            }
        },
        getDictData () {
            let dictCodeArr = [
                {code: 'srmEbiddingStageType', dict: 'srmEbiddingStageType'}
            ]
            dictCodeArr.map(item => {
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: item.dict
                }
                ajaxFindDictItems(postData).then(res => {
                    if(res.success) {
                        let options = res.result.map(item2 => {
                            return {
                                value: item2.value,
                                label: item2.text,
                                title: item2.title
                            }
                        })
                        this.optionsMap[item.code] = options
                    }
                })
            })
        },
        // 通过value显示label
        getDictLabel (value, dict) {
            let currentValueArr = value.split(',') || []
            if (dict) {
                let dictItem = this.optionsMap[dict].filter((opt) => {
                    return currentValueArr.includes(opt.value)
                }).map(item => item.label)
                return dictItem.length ? dictItem.join('；'): currentValueArr[0]
            } else {
                return value
            }
        },
        checkValidate (form){
            const currentDate= moment().format('YYYY-MM-DD')
            if(form.effectiveDate && !(moment(form.effectiveDate).isSame(currentDate) || moment(form.effectiveDate).isAfter(currentDate))) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期需大于等于当前日期！'))
                return false
            }
            if(form.expiryDate  && form.effectiveDate ){
                if(!moment(form.effectiveDate).isBefore(form.expiryDate)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_umKXBATfUumbXBA_4246ea19`, '价格失效日期需大于价格生效日期！'))
                    return false
                }
            }
            // 需要应标:needEcho (需要 1 / 不需要 0 ) 竞价范围 ebiddingScope(公开 1 / 邀请 0)
            // 公开竞价：报名截止时间 applyEndTime < 应标截止时间 echoEndTime < 开始时间 beginTime
            // 邀请竞价需要应标：应标截止时间 echoEndTime < 开始时间 beginTime
            if(!form.needEcho || form.needEcho =='0' ) return true
            if(form.ebiddingScope =='1' && form.applyEndTime  && form.echoEndTime  && form.beginTime){
                if(!moment(form.echoEndTime).isBefore(form.beginTime)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OuvKKITfUdByRKIW_551186f3`, '竞价开始时间需大于应标截止时间！'))
                    return false
                }
                if(!moment(form.applyEndTime).isBefore(form.beginTime)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OuvKKITfUsRyRKIW_5a9921fe`, '竞价开始时间需大于报名截止时间！'))
                    return false
                }
                if(!moment(form.applyEndTime).isBefore(form.echoEndTime)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_dByRKITfUsRyRKIW_47b8f0c5`, '应标截止时间需大于报名截止时间！'))
                    return false
                }
            }
            if(form.ebiddingScope == '0'&& form.echoEndTime  && form.beginTime ) {
                if(!moment(form.echoEndTime).isBefore(form.beginTime)){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_OuvKKITfUdByRKIW_551186f3`, '竞价开始时间需大于应标截止时间！'))
                    return false
                }
            }
            return true
        },
        setConfrimItem (row) {
            this.currentRow = row
            this.$refs.setConfirmItemModal.open(row)
        },
        confirmItemOk (itemList) {
            this.currentRow['confirmItemList'] = itemList
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        addEbiddingItem () {
            this.selectType = 'material'
            this.$refs.fieldSelectModal.requestMethod = 'get'
            const form = this.$refs.editPage.getPageData()
            const { mustMaterialNumber = '1' } = form
            if(mustMaterialNumber == '1'){
                let url = '/material/purchaseMaterialHead/list'
                let columns = [
                    {
                        field: 'cateCode',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationCode`, '物料分类编号'),
                        width: 150
                    },
                    {
                        field: 'cateName',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassification`, '物料分类名称'),
                        width: 150
                    },
                    {field: 'materialNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialNumber`, '物料编码'), width: 150},
                    {field: 'materialDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 150},
                    {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200},
                    {field: 'materialSpec', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 200}
                ]
                this.$refs.fieldSelectModal.open(url, {blocDel: '0', freeze: '0'}, columns, 'multiple')
            }else{
                let itemGrid = this.$refs.editPage.$refs.purchaseEbiddingItemList[0]
                let itemData = {}
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        itemData[item.field] = item.defaultValue
                    }
                })
                if(form.taxCode){
                    itemData['taxCode'] = form.taxCode
                    itemData['taxRate'] = form.taxRate
                }
                itemGrid.insertAt([itemData], -1)
            }
        },
        showPublishConditionBtn () {
            let params = this.$refs.editPage ? this.$refs.editPage.form : {}
            if ((params.id) && this.$hasOptAuth('ebidding#purchaseEbiddingHead:publish')) {
                return true
            }else{
                return false
            }
        },
        deleteItemEvent () {
            let itemGrid = this.$refs.editPage.$refs.purchaseEbiddingItemList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        fieldSelectOk (data) {
            if(this.selectType == 'material'){
                const form = this.$refs.editPage.getPageData()
                let itemGrid = this.$refs.editPage.$refs.purchaseEbiddingItemList[0]
                let { fullData } = itemGrid.getTableData()
                let materialList = fullData.map(item => {
                    return item.materialId
                })
                //过滤已有数据
                let insertData =  data//data.filter(item => {return !materialList.includes(item.id)})
                this.pageConfig.itemColumns.forEach(item => {
                    if(item.defaultValue) {
                        insertData.forEach(insert => {
                            if(!insert[item.field]){
                                insert[item.field] = item.defaultValue
                            }
                        })
                    }
                })
                insertData.forEach(insert => {
                    insert['materialId'] = insert['id']
                    if (form.currency) {
                        insert['currency'] = form.currency
                    }
                    if (form.taxCode) {
                        insert['taxCode'] = form.taxCode
                        insert['taxRate'] = form.taxRate
                    }
                })
                let param = {
                    'purOrgCode': form.purchaseOrg,
                    'materialDataVos': data
                }
                postAction(this.url.materialEbidding, param).then(res => {
                    if(res.success){
                        itemGrid.insertAt(insertData, -1)
                    } else {
                        this.$confirm({
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLSu_38d85f7b`, '确认添加'),
                            content: res.message,
                            onOk: function () {
                                itemGrid.insertAt(insertData, -1)
                            }
                        })
                    }
                })
                //itemGrid.insertAt(insertData, -1)
            } else {
                let supplierGrid = this.$refs.editPage.$refs.purchaseEbiddingSupplierList[0]
                let { fullData } = supplierGrid.getTableData()
                let supplierList = fullData.map(item => {
                    return item.toElsAccount
                })
                // 过滤已有数据
                let insertData = data.filter(item => {
                    return !supplierList.includes(item.toElsAccount)
                })
                insertData = insertData.map(item => {
                    return {
                        toElsAccount: item.toElsAccount,
                        supplierCode: item.supplierCode,
                        supplierName: item.supplierName,
                        needCoordination: item.needCoordination,
                        supplierStatus_dictText: item.supplierStatus_dictText
                    }
                })
                supplierGrid.insertAt(insertData, -1)
            }
        },
        addSupplierEvent () {
            this.selectType = 'supplier'
            this.$refs.fieldSelectModal.requestMethod = 'post'
            let url = '/supplier/supplierMaster/list'
            let columns = [
                {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号'), width: 150},
                {field: 'supplierCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), width: 150},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 200},
                {field: 'supplierStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierState`, '供应商状态'), width: 200},
                {field: 'cateName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCategory`, '供应商品类'), width: 200},
                {field: 'needCoordination_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), width: 200}
            ]
            // 获取供应商范围参数
            const form = this.$refs.editPage.getPageData()
            // 供应商冻结功能检查
            if (form.purchaseEbiddingItemList && form.purchaseEbiddingItemList.length>0) {
                // 设置供应商信息和询价行信息
                const array = new Array()
                form.purchaseEbiddingItemList.forEach(a => {
                    let item = a.factory + ':' + a.cateCode
                    if (!array.includes(item)) {
                        array.push(item)
                    }
                })
                form.purchaseOrgItemList = array.join(',')
            }else {
                form.purchaseOrgItemList = ''
            }
            const { supplierScope = '', purchaseOrgItemList = '' } = form
            this.$refs.fieldSelectModal.open(url, { supplierStatus: supplierScope, frozenFunctionValue: '2', purchaseOrgItemList: purchaseOrgItemList }, columns, 'multiple')
        },
        deleteSupplierEvent () {
            let supplierGrid = this.$refs.editPage.$refs.purchaseEbiddingSupplierList[0]
            let checkboxRecords = supplierGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            supplierGrid.removeCheckboxRow()
        },
        addConfirm () {
            let confirmGrid = this.$refs.editPage.$refs.purchaseEbiddingConfirmList[0]
            confirmGrid.insertAt({ must: '0' }, -1)
        },
        checkTableValidate (){
            const params = this.$refs.editPage.getPageData()
            let purchaseEbiddingItemList = params.purchaseEbiddingItemList
            if( purchaseEbiddingItemList?.length>0){
                let i = 1
                for(let item of purchaseEbiddingItemList){
                    if(item.expiryDate && item.effectiveDate){
                        if(!moment(item.effectiveDate).isBefore(item.expiryDate)){
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#`, `竞价行信息中第 ${i} 行价格失效日期需大于价格生效日期！`))
                            return false
                        }
                    }
                    i++
                }
            }
            return true
        },
        deleteConfirm () {
            let confirmGrid = this.$refs.editPage.$refs.purchaseEbiddingConfirmList[0]
            let checkboxRecords = confirmGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            confirmGrid.removeCheckboxRow()
        },
        addFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            demandGrid.insertAt({ required: '0' }, -1)
        },
        deleteFileDemand () {
            let demandGrid = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            let checkboxRecords = demandGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFWFW_f352d52c`, '请选择数据！'))
                return
            }
            demandGrid.removeCheckboxRow()
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            let pageData = this.$refs.editPage.getPageData()
            let purchaseRequestItemList = pageData.purchaseEbiddingItemList || []
            let materialMap = purchaseRequestItemList.reduce((acc, obj) => {
                acc[obj.itemNumber] = obj.materialNumber+'_'+ obj.materialName
                return acc
            }, {})
            result.forEach(item => {
                let number = item.itemNumber
                if (number && materialMap[number] && !item.materialName) {
                    item.materialNumber = materialMap[number].split('_')[0]
                    item.materialName = materialMap[number].split('_')[1]
                }
            })

            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            //如果删除的数据有和登录人账号不一致的
            if('purchaseRequest' !== row.businessType && (user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount)){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.purchaseAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user === row.uploadElsAccount) {
                        if( subAccount === row.uploadSubAccount || 'purchaseRequest' === row.businessType){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/purchaseAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        saveEvent () {
            this.$refs.editPage.postData()
        },
        async publishEvent () {
            const $table = this.$refs.editPage.$refs.purchaseAttachmentDemandList[0]
            const errMap = await $table.validate(true).catch(errMap => errMap)
            if (errMap) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BITVVtJOxeR_fd0dbeeb`, '附件需求清单校验不通过'))
                return false
            }
            const form = this.$refs.editPage.getPageData()
            if(!this.checkValidate(form)) return
            if(!this.checkTableValidate())return
            // 供应商冻结功能检查
            if ((form.purchaseEbiddingItemList && form.purchaseEbiddingItemList.length>0) && (form.purchaseEbiddingSupplierList && form.purchaseEbiddingSupplierList.length>0)) {
                // 设置供应商信息和询价行信息
                form['purchaseOrgItemList'] = form.purchaseEbiddingItemList
                form['supplierItemList'] = form.purchaseEbiddingSupplierList
                postAction(this.url.isExistFrozenSource, form).then(rest =>  {
                    if (rest.success) {
                        // 发布
                        this.$refs.editPage.handleSend()
                    } else {
                        this.$message.warning(rest.message)
                    }
                })
            } else {
                // 发布
                this.$refs.editPage.handleSend()
            }
        }
    }
}
</script>
<style lang="scss" scoped>
// :deep() textarea.ant-input{
//     resize:none !important
// }
</style>
