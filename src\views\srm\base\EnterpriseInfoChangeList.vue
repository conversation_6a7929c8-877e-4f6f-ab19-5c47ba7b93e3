<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"
      :url="url" />

    <!-- 数据编辑界面 -->
    <EnterpriseInfoChangeEdit
      v-if="showEditPage"
      ref="editPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />

    <!-- 数据详情界面 -->
    <EnterpriseInfoChangeDetail
      immediate
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />

    <!-- 驳回意见 start -->
    <rejection-modal
      v-model="rejectionVisible"
      v-if="rejectionVisible"
      :currentEditRow="currentEditRow"
    />
    <!-- 驳回意见 end -->
  </div>
</template>
<script>
import EnterpriseInfoChangeEdit from './modules/EnterpriseInfoChangeEdit'
import EnterpriseInfoChangeDetail from './modules/EnterpriseInfoChangeDetail'
import RejectionModal from '../supplier/components/RejectionModal'
import { srmI18n, getLangAccount } from '@/utils/util'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EnterpriseInfoChangeEdit,
        EnterpriseInfoChangeDetail,
        RejectionModal
    },
    data () {
        return {
            rejectionVisible: false,
            showEditPage: false,
            showDetailPage: false,
            pageData: {
                businessType: 'supplier',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHtyWnRWWWeyWnRXRL_78e60fda`, '变更单号/采购ELS账号/采购商名称')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: srmI18n(`${getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, clickFn: this.showHelpText},
                    {label: srmI18n(`${getLangAccount()}#i18n_title_attachment`, '说明附件'), icon: 'file-pdf', folded: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: srmI18n(`${getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'enterpriseChangeList#enterpriseChangeList:view'},
                    {type: 'edit', title: srmI18n(`${getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEditBtn, authorityCode: 'enterpriseChangeList#enterpriseChangeList:edit'},
                    {type: 'audit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleInvalid, allow: this.allowDeleteBtn, authorityCode: 'enterpriseChangeList#enterpriseChangeList:invalid'},
                    // {type: 'audit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), clickFn: this.handleRelease, allow: this.allowReleaseBtn, authorityCode: 'enterpriseChangeList#enterpriseChangeList:release'},
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_mArMII_1efd8763`, '查看驳回意见'), clickFn: this.handleViewRejectInfo, authorityCode: 'enterpriseChangeList#enterpriseChangeList:viewRejectInfo'}
                ]
            },
            tabsList: [],
            url: {
                list: '/supplier/supplierInfoChangeHead/supplierList',
                add: '/supplier/supplierInfoChangeHead/add',
                delete: '/supplier/supplierInfoChangeHead/delete',
                invalid: '/supplier/supplierInfoChangeHead/invalid',
                release: '/supplier/supplierInfoChangeHead/releaseByIds',
                isHaveNewAndWaitConfirm: '/supplier/supplierInfoChangeHead/isHaveNewAndWaitConfirm',
                deleteBatch: '/supplier/supplierInfoChangeHead/deleteBatch',
                exportXlsUrl: 'supplier/supplierInfoChangeHead/exportXls',
                importExcelUrl: 'supplier/supplierInfoChangeHead/importExcel',
                supplierListUrl: '/supplier/supplierMaster/list',
                columns: 'enterpriseInfoChangeList'
            }
        }
    },
    mounted () {
        this.serachCountTabs('/supplier/supplierInfoChangeHead/supplierCount')
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls('供应商信息变更头表')
        },
        // 判断编辑是否可以点击
        allowEditBtn (row){
            let staus = row.status
            let publishStatus = row.publishStatus

            // 单据状态为“新建”、发布状态为“未发布”时允许编辑，其余状态不允许
            if ((staus==='0' && publishStatus==='0') || (staus==='5' && publishStatus==='1')) {
                return false
            } else {
                return true
            }
        },
        // 判断删除、作废按钮是否可以点击
        allowDeleteBtn (row){
            let staus = row.status
            let publishStatus = row.publishStatus

            // 单据状态为“新建”、发布状态为“未发布”其余状态不允许
            if ((staus==='0' && publishStatus==='0') || (staus==='3' && publishStatus==='1')) {
                return false
            } else {
                return true
            }
        },
        // 发布按钮控制
        allowReleaseBtn (row){
            let staus = row.status
            let publishStatus = row.publishStatus

            // 单据状态为“新建”、发布状态为“未发布”其余状态不允许
            if ((staus==='0' && publishStatus==='0') || (staus==='5' && publishStatus==='1')) {
                return false
            } else {
                return true
            }
        },
        // 判断确认、驳回按钮是否可以点击
        allowConfirmBtn (row){
            let staus = row.status

            // 只能确认或者驳回单据状态为“待确认”的单据，不满足条件的单据，【确认/驳回】按钮置灰。
            if (staus==='3') {
                return false
            } else {
                return true
            }
        },
        // 作废操作
        handleInvalid (row){
            const id = row.id
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSurWantVoid`, '是否确定作废?'),
                onOk: function () {
                    getAction(that.url.invalid, {id: id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            // 刷新列表
                            that.$refs.listPage.loadData()
                        }
                    })
                }
            })
        },
        // 发布操作
        handleRelease (row){
            const id = row.id
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    getAction(that.url.release, {id: id}).then(res => {
                        const type = res.success ? 'success' : 'error'
                        that.$message[type](res.message)
                        if(res.success){
                            // 刷新列表
                            that.$refs.listPage.loadData()
                        }
                    })
                }
            })
        },
        // 查看驳回意见打开弹出框操作
        handleViewRejectInfo (row) {
            this.currentEditRow = row
            this.rejectionVisible = true
        }
    }
}
</script>