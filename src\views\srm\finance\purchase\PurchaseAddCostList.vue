<template>
  <div style="height:100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab" />
    <PurchaseAddCostEdit
        v-if="showEditPage"
        ref="editPage"
        :current-edit-row="currentEditRow"
        @hide="hideEditPage"/>
      <!-- 详情页面 -->
    <purchaseAddCost-detail
      v-if="showDetailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import PurchaseDeductCostEdit from './modules/PurchaseDeductCostEdit'
import PurchaseAddCostEdit from './modules/PurchaseAddCostEdit'
import PurchaseAddCostDetail from './modules/PurchaseAddCostDetail'
import {ListMixin} from '@comp/template/list/ListMixin'
import {getAction} from '@/api/manage'
import layIM from '@/utils/im/layIM.js'

export default {
    mixins: [ListMixin],
    components: {
        PurchaseAddCostDetail,
        PurchaseAddCostEdit,
        PurchaseDeductCostEdit
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'addCost',
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),  icon: 'plus', clickFn: this.handleAdd, type: 'primary',
                        authorityCode: 'finance#purchaseAddCost:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleSpecialByView, authorityCode: 'finance#purchaseAddCost:view'},
                    {type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.showEditCondition, authorityCode: 'finance#purchaseAddCost:edit'
                    },
                    {type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteEvent, allow: this.showDeleteCondition, authorityCode: 'finance#purchaseAddCost:delete'
                    },
                    {type: 'chat', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'), clickFn: this.handleChat},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'), clickFn: this.handleRecord}
                ],
                optColumnWidth: 150,
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNujty_c91c347a`, '请输入费用单号')
                    }
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/finance/purchaseAddCost/list',
                add: '/finance/purchaseAddCost/add',
                delete: '/finance/purchaseAddCost/delete',
                deleteBatch: '/finance/purchaseAddCost/deleteBatch',
                exportXlsUrl: 'finance/purchaseAddCost/exportXls',
                importExcelUrl: 'finance/purchaseAddCost/importExcel',
                columns: 'purchaseAddCostList'
            },
            tabsList: []
        }
    },
    mounted () {
        // this.serachTabs('srmAddCostConfirmStatus', 'confirmStatus')
        this.serachCountTabs('/finance/purchaseAddCost/counts')
    },
    methods: {
        deleteEvent(row) {
            if('1' == row.costStatus) {
                this.$message.error(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_BuujtIIexiTQG_af1a7711`, '附加费用单已对账，不允许删除'))
                return
            }
            this.confirmLoading = true
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteChosedData`, '是否删除选中数据'),
                onOk: function () {
                    getAction(that.url.delete, {id: row.id}).then(res => {
                        if(res.success) {
                            that.$message.success(that.$srmI18n(`${that.$getLangAccount()}#i18n_title_operationSuccess`, '操作成功！'))
                            that.searchEvent()
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                    })
                }
            })
        },
        showEditCondition (row) {
            if ('0' == row.confirmStatus && row.createAccount == row.elsAccount) {
                return false
            }else {
                return true
            }
        },
        showDeleteCondition (row) {
            if (row.createAccount == row.elsAccount) {
                return false
            }else {
                // 不可操作
                return true
            }
        },
        handleChat (row) {
            let { id } = row
            let recordNumber = row.costNumber || id
            // 创建
            layIM.creatGruopChat({id, type: 'PurchaseAddCost', url: this.url || '', recordNumber})
        },
        handleExportXls () {
            this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_procurementAddCostManag`, '采购附加费用管理'))
        }
    }
}
</script>