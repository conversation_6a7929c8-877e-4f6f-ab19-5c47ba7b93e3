// 🔍 物料主数据页签优化验证脚本
console.log('🔍 验证物料主数据页签优化...');

// 检查当前页签
const activeTab = document.querySelector('.ant-tabs-tab-active');
console.log('当前页签:', activeTab?.textContent?.trim());

// 检查页签内容
const activePane = document.querySelector('.ant-tabs-tabpane-active');
if (activePane) {
    console.log('✅ 找到活动页签面板');
    
    // 查找所有可能的表格容器
    const allTables = activePane.querySelectorAll('.vxe-table, .ant-table, .lineWrap, .vxe-grid');
    console.log('表格数量:', allTables.length);
    
    // 详细检查每个表格
    allTables.forEach((table, index) => {
        console.log(`表格 ${index + 1}:`, table.className);
        console.log(`表格 ${index + 1} 高度:`, table.offsetHeight + 'px');
    });
    
    if (allTables.length > 0) {
        const table = allTables[0];
        const windowHeight = window.innerHeight;
        const tableHeight = table.offsetHeight;
        const utilization = (tableHeight / windowHeight * 100).toFixed(1);
        
        console.log('✅ 找到表格!');
        console.log('窗口高度:', windowHeight + 'px');
        console.log('表格高度:', tableHeight + 'px');
        console.log('空间利用率:', utilization + '%');
        console.log('优化效果:', utilization > 85 ? '🎉 优秀' : utilization > 70 ? '✅ 良好' : '⚠️ 需要改进');
        
        // 检查是否应用了优化样式
        const hasOptimization = table.style.height || table.style.minHeight;
        console.log('是否应用优化样式:', hasOptimization ? '✅ 是' : '❌ 否');
    } else {
        console.log('❌ 未找到表格，可能页签内容为空或结构不同');
        console.log('页签内容HTML:', activePane.innerHTML.substring(0, 500) + '...');
    }
} else {
    console.log('❌ 未找到活动页签面板');
    
    // 检查是否在详情页面
    const detailLayout = document.querySelector('.page-container');
    if (detailLayout) {
        console.log('✅ 在详情页面中');
        
        // 查找所有页签
        const allTabs = document.querySelectorAll('.ant-tabs-tab');
        console.log('所有页签:', Array.from(allTabs).map(tab => tab.textContent.trim()));
        
        // 查找所有表格
        const allTables = document.querySelectorAll('.vxe-table, .ant-table, .lineWrap, .vxe-grid');
        console.log('页面中所有表格数量:', allTables.length);
        
        // 检查页签容器的CSS类
        const tabPanes = document.querySelectorAll('.ant-tabs-tabpane');
        console.log('页签面板数量:', tabPanes.length);
        tabPanes.forEach((pane, index) => {
            console.log(`页签面板 ${index + 1} 类名:`, pane.className);
            const tables = pane.querySelectorAll('.vxe-table, .vxe-grid, .lineWrap');
            console.log(`页签面板 ${index + 1} 表格数量:`, tables.length);
        });
    }
}

// 检查是否应用了物料主数据优化样式
const itemInfoTab = document.querySelector('.itemInfoTab');
const brandInfoTab = document.querySelector('.brandInfoTab');
const measurementUnitTab = document.querySelector('.measurementUnitTab');
const attachmentTab = document.querySelector('.attachmentTab');

console.log('🎯 检查优化样式应用情况:');
console.log('物料行信息Tab (.itemInfoTab):', itemInfoTab ? '✅ 找到' : '❌ 未找到');
console.log('品牌信息Tab (.brandInfoTab):', brandInfoTab ? '✅ 找到' : '❌ 未找到');
console.log('计量单位Tab (.measurementUnitTab):', measurementUnitTab ? '✅ 找到' : '❌ 未找到');
console.log('附件Tab (.attachmentTab):', attachmentTab ? '✅ 找到' : '❌ 未找到');

// 如果找到了优化的Tab，检查其表格
if (itemInfoTab) {
    const tables = itemInfoTab.querySelectorAll('.vxe-table, .lineWrap');
    console.log('物料行信息Tab中的表格数量:', tables.length);
    if (tables.length > 0) {
        const table = tables[0];
        console.log('物料行信息表格高度:', table.offsetHeight + 'px');
        console.log('物料行信息表格样式:', table.style.cssText);
    }
}
