<template>
  <div>
    <a-cascader
      :field-names="realfieldNames"
      :show-search="{ filter }"
      :options="realOptions"
      :disabled="disabled"
      :value="realValue"
      v-bind="$attrs"
      v-on="mixListeners" />
  </div>
</template>

<script>
import { areas } from '@/store/area'
import {ajaxFindDictItems} from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'MCascader',
    model: {
        prop: 'value',
        event: 'change'
    },
    props: {
        value: {
            type: String, 
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        },
        mode: {
            type: String,
            default: ''
        },
        options: {
            type: Array,
            default: () => null
        },
        fieldNames: {
            type: Object,
            default: () => {
                return { label: 'label', value: 'value', children: 'children' }
            }
        }
    },
    data () {
        return {
            loading: false,
            realValue: [],
            realOptions: this.options || areas,
            realfieldNames: this.fieldNames
        }
    },
    computed: {
        mixListeners () {
            let vm = this
            // `Object.assign` 将所有的对象合并为一个新对象
            return Object.assign({},
                // 我们从父级添加所有的监听器
                this.$listeners,
                // 然后我们添加自定义监听器，
                // 或覆写一些监听器的行为
                {
                    // 这里确保组件配合 `v-model` 的工作
                    change (val, selectedOptions) {
                        console.log('val', val)
                        vm.$emit('change', val.join(','), selectedOptions)
                    }
                }
            )
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                this.realValue = val && val.split(',') || []
            }
        },
        mode: {
            immediate: true,
            handler (val) {
                if (val) {
                    this.initDictData()
                }
               
            }
        }
    },
    methods: {
        filter (inputValue, path) {
            return path.some(option => option.title.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
        },
        loadSuccess () {
            this.$emit('load', this.realOptions)
        },
        initDictData () {
            // 级联的数据字典需要映射
            this.realfieldNames = { label: 'title', value: 'value', children: 'children' } 
            if(this.mode) {
                //根据字典Code, 初始化字典数组
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: this.mode
                }             
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        this.realOptions = res.result
                        this.loadSuccess()
                    }
                })
            }
        }
    }
}
</script>
