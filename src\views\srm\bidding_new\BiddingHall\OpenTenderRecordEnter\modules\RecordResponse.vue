<template>
  <div v-if="ifshow">
    <titleCrtl>
      {{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_ddRt_27f9e68f`, '响应名单')) }}

      <template slot="right">
        <a-button
          v-if="formData.status != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == 1"
          @click="addInviteItem"
          type="primary">{{ dealLabel($srmI18n(`${$getLangAccount()}#i18n_field_SuRdX_3498bfe8`, '添加供应商')) }}</a-button>
      </template>
    </titleCrtl>
    <list-table
      ref="tenderOpenBidRecordSupplierList"
      :statictableColumns="statictableColumns"
      :pageData="pageData"
      setGridHeight="500"
      :fromSourceData="formData.tenderOpenBidRecordSupplierList"
      :showTablePage="false"> </list-table>
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>
<script lang="jsx">
import listTable from '../../components/listTable'
import titleCrtl from '../../components/title-crtl'
import { getAction } from '@/api/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'

export default {
    mixins: [baseMixins],
    props: {
        formData: {
            default: () => {},
            type: Object
        }
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        }
    },
    components: {
        listTable,
        titleCrtl,
        fieldSelectModal
    },
    data () {
        return {
            statictableColumns: [
                // let attachmentFileType = this.checkType == '0' ? 'preAttachmentFileType' : 'attachmentFileType'

                { type: 'checkbox', width: 40, fixed: 'left' },
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
                    fieldLabelI18nKey: '',
                    field: 'supplierName',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KnDRL_37c77362`, '联合体名称'),
                    fieldLabelI18nKey: '',
                    field: 'combinationName',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                // 此处放插槽，放置文件
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBQI_2e6335e1`, '投标文件'),
                    fieldLabelI18nKey: '',
                    width: 180,
                    field: 'attachmentDTOList',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    slots: {
                        default: ({ row, column }) => {
                            if (row.attachmentDTOList) {
                                let fileListDoms = row.attachmentDTOList.map((item, index, arr) => {
                                    // 已发布,则没有删除按钮
                                    if(this.formData.status == '1'){
                                        return (
                                            <div>
                                                <span style='color: blue;style="margin-right: 4px"'>{item.fileName}</span>
                                                <a style="margin: 0 4px" onClick={() => this.preViewEvent(item)}>
                                                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                                                </a>
                                                <a onClick={() => this.downloadEvent(item)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}</a>
                                            </div>
                                        )
                                    }else{
                                        return (
                                            <div>
                                                <span style='color: blue;style="margin-right: 4px"'>{item.fileName}</span>
                                                <a-icon
                                                    type="delete"
                                                    onClick={() => this.handleDeleteFile(item, index, arr)}/>
                                                <a style="margin: 0 4px" onClick={() => this.preViewEvent(item)}>
                                                    {this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览')}
                                                </a>
                                                <a onClick={() => this.downloadEvent(item)}>{this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载')}</a>
                                            </div>
                                        )
                                    }
                                    
                                })
                                return [
                                    // <span >{row.purchaseAttachmentDemandDTOList[0]}</span>
                                    // row['purchaseAttachmentDemandDTOList'].length> 0 ? <div ><span style='color: blue' onClick={() => this.preViewEvent(row)}>{row['purchaseAttachmentDemandDTOList'][0]['fileName']} </span><a-icon type="delete" onClick={() => row.purchaseAttachmentDemandDTOList = []}/> </div>: ''
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            data={{ headId: row.id || '', businessType: 'biddingPlatform', sourceNumber: this.formData.tenderProjectNumber || this.formData.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                            onChange={(e) => this.handleUploadChange(e, row)}
                                        >
                                            <a-button onClick={(e) => this.handleRow(e, row)}> {this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'))}</a-button>
                                        </a-upload>
                                        {...fileListDoms}
                                    </div>
                                ]
                            } else {
                                return [
                                    <div>
                                        <a-upload
                                            name={this.file}
                                            multiple={true}
                                            showUploadList={false}
                                            action={this.uploadUrl}
                                            headers={this.uploadHeader}
                                            accept={this.accept}
                                            data={{ headId: row.id || '', businessType: 'biddingPlatform', sourceNumber: this.formData.tenderProjectNumber || this.formData.id || '', actionRoutePath: '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开' }}
                                            onChange={(e) => this.handleUploadChange(e, row)}
                                        >
                                            <a-button onClick={(e) => this.handleRow(e, row)}> {this.dealLabel(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'))}</a-button>
                                        </a-upload>
                                    </div>
                                ]
                            }
                        }
                    }
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                    fieldLabelI18nKey: '',
                    field: 'quote',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: '',
                    required: '1'
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, ' 联系人'),
                    fieldLabelI18nKey: '',
                    field: 'contacts',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    fieldLabelI18nKey: '',
                    field: 'contactsPhone',
                    fieldType: 'input',
                    align: 'center',
                    headerAlign: 'center',
                    defaultValue: ''
                },
                {
                    fixed: 'right',
                    groupCode: 'tenderOpenBidRecordSupplierList',
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    field: 'title',
                    width: 100,
                    slots: { default: 'grid_opration' }
                }
            ],
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: { 'X-Access-Token': this.$ls.get('Access-Token') },
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            ifshow: false,
            tenderOpenBidRecordSupplierList: [],
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        allow: this.delDisabled,
                        clickFn: this.handleDelete
                    }
                ]
            }
        }
    },
    methods: {
        delDisabled (row) {
            // 状态为1，已发布，不允许删除
            return (this.formData.status == '1')
        },
        beforeUpload (e) {
            console.log('this.formData.id1', this.formData.id)
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                e.stopPropagation()
            }
        },
        handleRow (e, row) {
            console.log('this.formData.id2', this.formData.id)
            this.row = row
            if (!row.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWsM_40db030c`, '请先保存'))
                e.stopPropagation()
            }
            if(this.formData.status == '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
                e.stopPropagation()
            }
        },
        // 添加供应商弹窗
        addInviteItem () {
            let param, url
            let columns = []
            if (this.subPackageRow.tenderType == 0 || (this.currentNode().extend.checkType == 1 && this.subPackageRow.checkType == 0)) {
                // 查邀请供应商
                // param = {
                //     subpackageId: this.subPackageRow.id
                //     // headers: {xNodeId: this.currentNode().nodeId}
                // }
                url = '/tender/purchase/supplierTenderProjectMasterInfo/list'
                columns = [
                    { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称') },
                    { field: 'contacts', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, '联系人') },
                    { field: 'contactsPhone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactPhone`, '联系电话') }
                ]
            } else {
                // 从供应商库中选择合格及潜在供应商
                param = encodeURI('[{"logicSymbol":"in","fieldCode":"supplierStatus","fieldType":"dict","dictCode":"srmSupplierStatus","fieldValue":"1,2","joiner":"AND"}]')
                url = '/supplier/supplierMaster/contactList?functionName='+encodeURI('报价')+'&superQueryParams=' + param
                columns = [
                    { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称') },
                    { field: 'functionName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, '联系人') },
                    { field: 'functionTelphone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactPhone`, '联系电话') }
                ]
            }

            let supplierAccounts
            if (this.formData.tenderOpenBidRecordSupplierList ?? '' != '') {
                supplierAccounts = this.formData.tenderOpenBidRecordSupplierList.map((item) => item.supplierAccount)
            } else {
                supplierAccounts = []
            }
            // 已选的不能在勾选
            let checkedConfig = {
                visibleMethod: ({ row }) => {
                    let flag = true
                    let supplierAccount = row.toElsAccount || row.supplierAccount
                    if (supplierAccounts.includes(supplierAccount)) flag = false
                    return flag
                }
            }
            this.$refs.fieldSelectModal.open(url, { headId: this.formData.id, subpackageId: this.subPackageRow.id }, columns, 'multiple', checkedConfig)
        },
        fieldSelectOk (data) {
            let supplierAccounts
            if (this.formData.tenderOpenBidRecordSupplierList ?? '' != '') {
                supplierAccounts = this.formData.tenderOpenBidRecordSupplierList.map((item) => item.supplierAccount)
            } else {
                supplierAccounts = []
            }
            var subpackageList = {}
            var subpackage = {}
            let { supplierAccount, supplierName, tenderProjectName, subpackageName, headId, id } = this.subPackageRow
            let subpackageMsg = { supplierAccount, supplierName, tenderProjectName, subpackageName }
            subpackageMsg.tenderProjectId = headId
            subpackageMsg.subpackageId = id
            data.forEach((item) => {
                subpackage['supplierName'] = item.supplierName
                subpackage['contacts'] = item.functionName || item.contacts
                subpackage['contactsPhone'] = item.functionTelphone || item.contactsPhone
                subpackage['supplierAccount'] = item.supplierAccount || item.toElsAccount
                subpackageList = { ...subpackageMsg, ...subpackage }
                if (!supplierAccounts.includes(subpackage.supplierAccount)) {
                    this.formData.tenderOpenBidRecordSupplierList.push(subpackageList)
                }
            })
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }, row) {
            console.log('123')
            console.log(row)
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if (row.id) {
                this.$refs.tenderOpenBidRecordSupplierList.loading = true
                if (file.status === 'done') {
                    if (file.response.success) {
                        let { fileName, filePath, fileSize, id } = file.response.result
                        let { headId } = row.id
                        let fileListData = {
                            uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                            uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                            name: fileName,
                            url: filePath,
                            uid: id,
                            fileName,
                            filePath,
                            fileSize,
                            id,
                            headId
                        }
                        // if (!this.formData.purchaseAttachmentList) this.$set(this.formData, 'purchaseAttachmentList', [])
                        console.log('row', row)
                        this.formData.tenderOpenBidRecordSupplierList.forEach((item) => {
                            if (item.supplierAccount == row.supplierAccount) {
                                item.attachmentDTOList ||= []
                                item.attachmentDTOList.push(fileListData)
                                console.log(item)

                            }
                        })
                        this.$refs.tenderOpenBidRecordSupplierList.loading = false

                        // attachmentDTOList.push(fileListData)

                        // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
                        // this.$refs.tenderOpenBidRecordSupplierList.insertAt(fileListData, -1)
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                    this.$refs.tenderOpenBidRecordSupplierList.loading = false
                }
            }
        },
        handleDelete (deleteItem) {
            let list = this.formData.tenderOpenBidRecordSupplierList
            let targetIndex

            list.forEach((item, index) => {
                if (item.supplierAccount == deleteItem.supplierAccount) targetIndex = index
                console.log('@', item, deleteItem)
            })
            this.formData.tenderOpenBidRecordSupplierList.splice(targetIndex, 1)
            // 仅对表格数据进行操作，再在父组件获取子组件table表格数据进行保存发布
            // this.$refs.tenderOpenBidRecordSupplierList.fromSourceData.splice(index, 1)
        },
        handleDeleteFile (target, targetIndex, arr) {
            if(this.formData.status == '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzExqtk_128cab96`, '当前状态不可操作'))
                return
            }
            arr.splice(targetIndex, 1)
        },
        async downloadEvent (row) {
            row.subpackageId = this.subPackageRow.id
            let {message: url} = await getAttachmentUrl(row)
            this.$refs.tenderOpenBidRecordSupplierList.loading=true
            getAction(url, {}, {
                responseType: 'blob'
            }).then((res) => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.$refs.tenderOpenBidRecordSupplierList.loading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.subPackageRow.id
            this.$previewFile.open({ params: row })
        }
    },
    mounted () {
        this.formData.tenderOpenBidRecordSupplierList ||= []
        // console.log(this.currentNode().extend.checkType)
        if (this.currentNode().extend.checkType == '0') {
            this.statictableColumns = this.statictableColumns.filter((item) => {
                console.log(item.field != 'quote')

                return item.field != 'quote'
            })
        }
        this.ifshow = true
    }
}
</script>
<style lang="less" scoped>
:deep(.vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell) {
    max-height: none;
}
:deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell) {
    max-height: none;
}
</style>
