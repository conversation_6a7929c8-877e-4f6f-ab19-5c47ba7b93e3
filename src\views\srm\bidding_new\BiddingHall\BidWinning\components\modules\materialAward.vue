<template>
  <div class="normalAward">
    <div
      class="searchLeftBox"
      :style="{height: `${height}px`}">
      <searchbox
        v-if="showSearchbox"
        :Format="{field: 'materialId', title: 'materialName'}"
        @searchChange="searchChange"
        :searchData="searchData"
        :height="height"/>
    </div>
    <div
      class="infoRightBox"
      :style="{height: `${height}px`}">
      <div style="width: 100%">
        <a-collapse
          :default-active-key="tableData[0].materialId"
          v-model="activeKey"
          accordion>
          <a-collapse-panel
            v-for="(materialItem, index) in tableData"
            :show-arrow="false"
            :class="activeKey == materialItem.materialId ? 'activeKey': ''"
            :key="materialItem.materialId">
            <template #header>
              <span
                class="margin-r-10 wfont titelc"
              >{{ materialItem.materialName }}{{ `（${materialItem.materialId}）` }}</span>
              <span
                class="margin-r-10 awardStatus titelc"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_field_lBzE_ab986290`, '授标状态：') }}<span :class="awardType(materialItem.winningAffirmMaterialList, 'awardStatus') ? 'success' : 'error'">{{ awardType(materialItem.winningAffirmMaterialList, 'awardStatus') ? '已授标' : '未授标' }}</span></span>
              <span
                class="margin-r-10 titelc"
              >{{ $srmI18n(`${$getLangAccount()}#i18n_field_lBlv_ab7c8de4`, '授标比例：') }}{{ awardType(materialItem.winningAffirmMaterialList, 'awardQuotaScale') }}%</span>
              <span class="titelc">
                {{ $srmI18n(`${$getLangAccount()}#i18n_field_lBRdXVt_72e2bc3c`, '授标供应商清单：') }}
                <a-tooltip placement="bottomRight">
                  <template slot="title">
                    <div
                      v-for="(item, i) in awardType(materialItem.winningAffirmMaterialList, 'awardSupplier')"
                      :key="i">{{ item }}</div>
                  </template>
                  <span class="titelc">{{ $srmI18n(`${$getLangAccount()}#i18n_field_mARdXVt_a55654b7`, '查看供应商清单') }}</span>
                </a-tooltip>
              </span>
            </template>
            <div>
              <list-table
                ref="purchaseTenderProjectAttachmentInfoList"
                :statictableColumns="statictableColumns"
                :pageStatus="'detail'"
                :fromSourceData="materialItem.winningAffirmMaterialList"
                :showTablePage="false"
              >
              </list-table></div>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
</template>
<script lang="jsx">
import searchbox from './searchbox'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import { uniqBy } from 'lodash'
export default {
    props: {
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        },
        pageStatus: {
            type: String,
            default: 'edit'
        }
    },
    components: {
        searchbox,
        listTable
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        }
    },
    data () {
        return {
            height: 0,
            keyWord: [],
            material: [],
            tableData: [],
            showSearchbox: false,
            activeKey: null,
            statictableColumns: [
                { type: 'seq', width: 60, fixed: 'left', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称'), 'field': 'supplierName', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料分组'), 'field': 'materialGroup', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), 'field': 'materialDesc', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), 'field': 'materialNumber', width: 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'), 'field': 'price', width: 100},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netPrice`, '净价'), 'field': 'netPrice', width: 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'), 'field': 'taxCode', width: 80},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'), 'field': 'taxRate', width: 80},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_jXBA_305f4179`, '有效日期'), 'field': 'effectiveDate', width: 120, 'fieldType': 'date'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unableDate`, '失效日期'), 'field': 'expiryDate', width: 120, 'fieldType': 'date'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lB_c757f`, '授标'), fixed: 'right', 'field': 'award', 'fieldType': 'switch', width: 100, enabled: this.pageStatus == 'edit', 
                    bindFunction: (row, column, v) => {
                        if (v == '0') this.$set(row, 'quotaScale', '')
                    }
                },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzlv_2e27a637`, '拆分比例'), fixed: 'right', 'field': 'quotaScale', 'fieldType': 'number', width: 100, enabled: this.pageStatus == 'edit'}
            ]
        }
    },
    methods: {
        awardType (winningAffirmMaterialList, type) {
            let flag = false
            let awardQuotaScale = 0
            let awardSupplierList = []
            for (let item of winningAffirmMaterialList) {
                if (item.award == '1') {
                    flag = true
                    awardSupplierList.push(item.supplierName)
                    awardQuotaScale+= item.quotaScale
                }
            }
            if (type == 'awardQuotaScale') {
                return awardQuotaScale
            } else if (type == 'awardSupplier') {
                return awardSupplierList
            } else {
                return flag
            }
        },
        searchChange (key = []) {
            this.keyWord = key
            this.handleInitData()
        },
        handleInitData () {
            this.tableData = this.materialList
            this.activeKey = this.tableData[0].materialId
            if (this.keyWord.length > 0) {
                this.tableData = this.tableData.filter(row => {
                    return this.keyWord.includes(row.materialId)
                })
            }
        },
        init () {
            // 物料行以物料为分组
            let materialIdMap = {}
            let resultData = JSON.parse(JSON.stringify(this.resultData))
            for (let item of resultData.winningAffirmMaterialList) {
                if (!materialIdMap[item.materialId]) materialIdMap[item.materialId] = {winningAffirmMaterialList: [], materialName: item.materialName, materialNumber: item.materialNumber, materialId: item.materialId}
                materialIdMap[item.materialId]['winningAffirmMaterialList'].push(item)
            }
            this.materialList = Object.values(materialIdMap)
            this.searchData = this.materialList.map(({materialName, materialId}) => {
                return {materialName, materialId}
            })
            // 数组去重
            this.searchData = uniqBy(this.searchData, 'materialId')
            this.searchChange()
            this.showSearchbox = true
        },
        // 向外抛数据
        externalAllData () {
            let params = []
            this.materialList.map(item => {
                item.winningAffirmMaterialList.map(row => {
                    params.push(row)
                })
            })
            return params
        }
    },
    created () {
        this.height = document.documentElement.clientHeight - 180
        this.init()
    }
}
</script>
<style lang="less" scoped>
.normalAward{
    :deep(.ant-collapse-content-box){
        padding: 0;
    }   
}
.red {
    color: red;
}
.fl{
    float: left;
}
.fr{
    float: right;
}
.clearFix{
    clear: both;
}
.normalAward{
    display: flex;
}
.margin-r-10{
    margin-right: 10px;
}
.awardStatus{
    .success {
        color: #45e9aa;
    }
    .error{
        color: red;
    }
}
.awardNumber{
    color: #fff;
}
.searchLeftBox{
    min-width: 200px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    flex: 10%;
    margin-right: 10px;
}
    
.infoRightBox{
    overflow: auto;
    flex: 88%;
    padding: 5px;
    border: 1px solid #e8e8e8;
    min-width: 400px;
    display: flex;
}
:deep(.ant-collapse){
    border: none;
}
:deep(.ant-collapse > .ant-collapse-item) {
    border: 1px solid #d9d9d9;
}
.wfont{
    font-size: 18px;
}
.activeKey{
    background: #1890ff;
    border-radius: 20px 20px 0 0;
    .titelc{
        color: #fff;
    }
}
</style>
