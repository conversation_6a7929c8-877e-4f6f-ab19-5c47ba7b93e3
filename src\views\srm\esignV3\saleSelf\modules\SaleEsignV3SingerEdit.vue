<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <a-modal
      v-drag
      v-model="previewModal"
      title="预览"
      :footer="null"
      :width="1000">
      <div
        style="width:210mm;margin:0 auto;padding:2.54mm 3.18mm;border:1px solid #ccc;overflow: auto;"
        v-html="previewContent"></div>
    </a-modal>
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
      v-drag
      v-model="visible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_PeIL_39fb9595`, '签章定位')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="layout.labelCol"
        :wrapper-col="layout.wrapperCol">
        <a-form-model-item label="关键字">
          <a-input
            v-model="form.keyword" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { axios } from '@/utils/request'
import { REPORT_ADDRESS } from '@/utils/const.js'

export default {
    name: 'SaleEsignV3SingerEdit',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            rowIndex: -1,
            sealAeraIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            previewModal: false,
            previewContent: '',
            visible: false,
            form: {
                keyword: ''
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessType`, '业务类型'),
                                    fieldName: 'busType',
                                    dictCode: 'electronicSignatureBusType',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_businessNumber`, '业务单号'),
                                    fieldName: 'busNumber',
                                    disabled: true,
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_WhoWillStampFirst`, '哪方先盖章'),
                                    fieldName: 'firstSeal',
                                    dictCode: 'srmSignatoryType',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessAssociationID`, '业务关联id'),
                                    fieldName: 'relationId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                                    fieldName: 'filesName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'),
                                    fieldName: 'filesId',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_checkisSuccessfullyUploaded`, '文件是否上传成功'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
                                    fieldName: 'signerVindicateStatus',
                                    dictCode: 'srmSignerVindicateStatus',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_zyH3twLIV67xJOvs`, '供方是否线上盖章'),
                                    fieldName: 'onlineSealed',
                                    dictCode: 'yn',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_BqQfRcWCQGdlnH74`, '供方是否上传盖章文件'),
                                    fieldName: 'signFileUploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_autoArchiving`, '是否自动归档'),
                                    fieldName: 'autoArchiving',
                                    dictCode: 'yn',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherTheProcessIsAutomaticallyStarted`, '流程是否自动开启'),
                                    fieldName: 'autoInitiate',
                                    dictCode: 'yn',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWyRKI_ed45f8ea`, '签署截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType',
                                    required: '1',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'),
                                    disabled: true
                                }
                            ]
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RCPWL_91e5ef48`, '供方签署人'), groupCode: 'saleSignersList', type: 'grid', custom: {
                        ref: 'saleSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'signerType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'), value: '2'}
                            ]} },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            {
                                field: 'signFieldStyle',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120,
                                editRender: {
                                    name: '$select', options: [
                                        {label: '单页签署', value: '1'},
                                        {label: '骑缝签署', value: '2'}
                                    ]
                                }
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_SuPWRC_72eda045`, '添加签署公司'),  type: 'primary', click: this.addPurchaseSignEvent, showCondition: this.showAddPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QGPWRC_a5504d04`, '删除签署公司'), click: this.deleteSaleSignEvent, showCondition: this.showDeleteSaleSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: '设置签署区域', clickFn: this.getSignArea },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addSaleSignerEvent}
                        ],
                        rules: {
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            accountId: [{required: true, message: '[签署人E签宝账号]不能为空'}],
                            signerType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[PWCAc]xOLV_d16c9bf3`, '[签署方类型]不能为空')}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nCPWL_36613b74`, '采方签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            // { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 230, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'autoArchive', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQJOWe_3ca5f518`,
                                '是否自动落章'), width: 120, editRender: {name: '$select', options: [
                                {label: '否', value: '0'},
                                {label: '是', value: '1'}]}},
                            { field: 'signerType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWCAc_7b6c5b5`, '签署方类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`, '个人'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_legalPerson`, '法定代表人'), value: '2'}
                            ]} },
                            { field: 'signArea', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zoneSigningKey`, '签署区域'), width: 120 },
                            { field: 'signWord', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signAreaKeyword`, '签署区域关键字'), width: 120 },
                            {
                                field: 'signFieldStyle',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeMVK_55b1010`, '签章区样式'),
                                width: 120,
                                editRender: {
                                    name: '$select', options: [
                                        {label: '不限', value: '0'},
                                        {label: '单页签署', value: '1'},
                                        {label: '骑缝签署', value: '2'}
                                    ]
                                }
                            },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 120 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                            { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SignSRMaccount`, '签署用户SRM账号'), width: 120 },
                            { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDEPsey_25baae92`, '签署用户E签宝账号'), width: 120 },
                            { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWjDcR_f5c3f89d`, '签署用户姓名'), width: 120 }
                        ]
                    } }
                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sMGhd_905e2e4b`, '保存并发送'), type: 'primary', click: this.saveEvent },
                    { title: '签署文件预览', type: 'primary', click: this.preview },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_by6FtppjfaHaxK4d`, '签署文件下载'), type: 'primary', click: this.download },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                edit: '/esignv3/elsEsignV3Flow/edit',
                detail: '/esignv3/elsEsignV3Flow/queryById',
                keyWordToAera: '/esignv3/elsEsignV3Flow/keyWordToAera',
                downloadFile: '/contract/purchaseContractHead/download',
                downloadOrderFile: '/esignv3/elsEsignV3Flow/downloadOrder',
                upload: '/attachment/saleAttachment/upload',
                viewEsignFile: '/esignv3/elsEsignV3Flow/viewEsignFile'
            }
        }
    },
    computed: {
    },
    mounted () {
        this.init()
    },
    methods: {
        // 删除方法
        deleteFile () {
            const fileGrid = this.$refs.editPage.$refs.saleAttachments[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        uploadCallBack (result) {
            const saleAttachments = this.$refs.editPage.getPageData().saleAttachments
            let itemGrid = this.$refs.editPage.$refs.saleAttachments[0]
            if(saleAttachments.length>0){
                const ids = saleAttachments.map(n => (n.id)).join(',')
                const params = {
                    ids
                }
                getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                    if (res.success) {
                        itemGrid.remove()
                        itemGrid.insertAt(result, -1)
                    }
                })
            }else{
                itemGrid.insertAt(result, -1)
            }
        },
        // getSignArea (row, column, $rowIndex){
        getSignArea (row){
            this.selectType = 'keyWord'
            this.sealAeraIndex = 0
            this.visible = true
        },
        handleOk () {
            if(!this.form.keyword){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RIJxOLV_594d82ac`, '关键字不能为空'))
                return
            }
            const form = this.$refs.editPage.getPageData()
            form.saleSignersList[this.sealAeraIndex].signWord = this.form.keyword
            form.saleSignersList[this.sealAeraIndex].signArea = ''
            let param = {elsAccount: form.elsAccount, signWord: this.form.keyword, filesId: form.filesId}
            this.confirmLoading = true
            let columns = [
                { field: 'page', title: '页码', width: 200 },
                { field: 'positionX', title: '横轴(X)', width: 180 },
                { field: 'positionY', title: '纵轴(Y)', width: 180 }
            ]
            this.$refs.fieldSelectModal.open(this.url.keyWordToAera, param, columns, 'single')
            this.visible = false
        },
        getSeal (row){
            const form = this.$refs.editPage.getPageData()
            if(!row.signerType){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWiFPWCAcW_752c471d`, '请先选择签署方类型！'))
                return
            }
            if(!row.psnId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWGRPWL_eab9ad1c`, '请先设置签署人！'))
                return
            }
            this.selectType = 'addSigner'
            this.rowIndex = 0
            let url = '/esignv3/saleEsignV3Seals/getSignSeal'
            let columns = [
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 200 },
                { field: 'sealId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_PeWW_39f03bbd`, '签章id'), width: 180 },
                { field: 'sealName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 },
                { field: 'sealBizType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeESAc_49a8b9e7`, '印章业务类型'), width: 100 },
                { field: 'orgAuth_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQIHAElb_744f8381`, '是否已跨企业授权'), width: 100 }
            ]
            let params = {orgId: row.orgId, sealType: row.signerType, psnId: row.psnId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        showAddPurchaseSignEvent (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }
            return false
        },
        showDeleteSaleSignEvent (){
            if(this.currentEditRow.uploaded==='1'){
                return true
            }
            return false
        },
        addPurchaseSignEvent () {
            this.selectType = 'saleEsign'
            const form = this.$refs.editPage.getPageData()
            if(form.uploaded!=='1'){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PeQIsLXVShILreMU_88640b6a`, '签章文件还未上传，无法定位盖章区域'))
                return
            }
            let url = '/esignv3/saleEsignV3Org/saleList'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), width: 180 },
                { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_yVy933rH`, '机构ID'), width: 120 },
                { field: 'legalRepName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hIoBLcR_b362e8a`, '法定代表人姓名'), width: 120 }
            ]
            let params = {realnameStatus: 1}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addSaleSignerEvent (row, column, $rowIndex){
            this.selectType = 'saleEsigner'
            this.rowSignerIndex = $rowIndex
            const form = this.$refs.editPage.getPageData()

            let url = '/esignv3/saleEsignV3OrgPsn/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'psnAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPseDy_a4573ac2`, 'e签宝账号'), width: 180 },
                { field: 'psnName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), width: 180 },
                { field: 'psnId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPseyID_49891550`, 'E签宝账号ID'), width: 180 }
            ]
            let params = {orgId: row.orgId}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if(this.selectType == 'saleEsign'){
                let arr = data.map(({ id, ...others }) => ({ relationId: id, signType: '1', autoSign: '0', ...others, subAccount: '', psnId: '', psnName: '', psnAccount: ''}))
                let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
                itemGrid.remove()
                let { fullData } = itemGrid.getTableData()
                let regulations = fullData.map(item => {
                    return item.relationId
                })
                let insertData = arr.filter(item => {
                    return !regulations.includes(item.relationId)
                })
                itemGrid.insertAt(insertData)
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowIndex].sealIds = ids
                form.saleSignersList[this.rowIndex].orgAuth = data[0].orgAuth
            }else if(this.selectType === 'keyWord'){
                const { page = '', positionX = '', positionY = '' } = data[0] || {}
                let result = `${page}_${positionX}_${positionY}`
                // const form = this.$refs.editPage.getPageData()
                let param = form.saleSignersList[this.sealAeraIndex]
                param.signArea = result
            }else if(this.selectType === 'saleEsigner'){
                // const form = this.$refs.editPage.getPageData()
                form.saleSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.saleSignersList[this.rowSignerIndex].psnId = data[0].psnCode
                form.saleSignersList[this.rowSignerIndex].psnName = data[0].psnName
                form.saleSignersList[this.rowSignerIndex].psnAccount = data[0].psnAccount
                form.saleSignersList[this.rowSignerIndex].sealIds = ''
            }
        },
        deleteSaleSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.saleSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        preview () {
            let params= this.$refs.editPage.getPageData()
            if (!params.relationId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WiFne_bc5dc24c`, '先选择合同'))
                return
            }
            if(params.busType == 'order'){
                getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                    if(res.success){
                        window.open(res.result.downloadUrl)
                    }else{
                        this.$message.warning(res.message)
                    }
                })
            }else if(params.busType === 'reconciliationConfirmation') {
                getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                    if(res.success){
                        window.open(res.result.downloadUrl)
                    }else{
                        this.$message.warning(res.message)
                    }
                })
            } else {
                getAction('/contract/saleContractHead/getPreviewDataByRelationId', {id: params.relationId}).then((res) => {
                    if (res.success) {
                        this.previewModal = true
                        this.previewContent = res.result
                    }
                })
            }
        },
        download (){
            let params= this.$refs.editPage.getPageData()
            this.$refs.editPage.confirmLoading = true

            if(params.busType == 'order'){
                getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                    if(res.success){
                        params.token = this.$ls.get('Access-Token')
                        axios({
                            url: res.result.downloadUrl,
                            responseType: 'blob',
                            params: {token: params.token}
                        }).then(res => {
                            this.$refs.editPage.confirmLoading = false
                            let fieldName = params.busNumber+'_'+params.filesName+'.pdf'
                            const blob = new Blob([res])
                            const blobUrl = window.URL.createObjectURL(blob)
                            const a = document.createElement('a')
                            a.style.display = 'none'
                            a.href = blobUrl
                            a.download = fieldName
                            a.click()
                        })
                    }
                })
            }else {
                params.reportUrl = REPORT_ADDRESS
                params.token = this.$ls.get('Access-Token')
                axios({
                    url: this.url.downloadFile,
                    responseType: 'blob',
                    params: {id: params.relationId, reportUrl: params.reportUrl, token: params.token, createAccount: params.elsAccount, fileName: params.filesName}
                }).then(res => {
                    this.$refs.editPage.confirmLoading = false
                    let fieldName = params.busNumber+'_'+params.filesName+'.pdf'
                    const blob = new Blob([res])
                    const blobUrl = window.URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.style.display = 'none'
                    a.href = blobUrl
                    a.download = fieldName
                    a.click()
                })
            }

        },
        saveEvent () {
            const params = this.$refs.editPage.getPageData()
            this.$refs.editPage.confirmLoading = true
            //线上签署
            if(params.onlineSealed==='1'){
                if(params.saleSignersList.length<1){
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCPWLxOLV_68b60f58`, '供方签署人不能为空'))
                    this.$refs.editPage.confirmLoading = false
                    return
                }
                if(!params.saleSignersList[0].subAccount){
                    this.$refs.editPage.confirmLoading = false
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_[PWjDSRMey]xOLV_dc06458c`, '[签署用户SRM账号]不能为空'))
                    return
                }
                const signers = params.saleSignersList
                for(let signer of signers){
                    //是自动落章
                    if(signer.autoArchive == '1'){
                        if(signer.signerType  !== '1'){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWeRRuPWCAcLtR_ccdecfa0`, '自动落章只支持【签署方类型】为【机构】'))
                            return
                        }
                        if(!signer.sealIds){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWelTGRWe_75cb2b37`, '自动落章必须设置印章'))
                            return
                        }
                        if(!signer.signArea){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWelTGRPWLR_dfb08273`, '自动落章必须设置【签署位置】'))
                            return
                        }
                        if(signer.orgAuth !=='1'){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_JOWejWelTKIHAElbje_f35c8219`, '自动落章的印章必须是【已跨企业授权】的章'))
                            return
                        }
                    }
                    if(signer.signArea){
                        if(!signer.signFieldStyle){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWLRlTGRPeMVK_2df4eb41`, '设置了【签署位置】，必须设置【签章区样式】'))
                            return
                        }
                        if(signer.signFieldStyle !== '1'){
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWLRPeMVKlTLtEPe_d4b642fd`, '设置了【签署位置】，签章区样式必须为【单页签章】'))
                            return
                        }
                    }
                    if (signer.sealIds || signer.signArea) {
                        if (!signer.sealIds) {
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrPWMUlTGRWe_a65494a5`, '设置了签署区域必须设置印章'))
                            return
                        }
                        if (!signer.signArea) {
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWelTGRPWMU_913de1e5`, '设置了印章必须设置签署区域'))
                            return
                        }
                        if (!signer.signFieldStyle) {
                            this.$refs.editPage.confirmLoading = false
                            this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRrWeSPWMUlTiFPeMVK_42827fb1`, '设置了印章或签署区域必须选择签章区样式'))
                            return
                        }
                    }
                }

            }else{
                //线下签署，上传文件
                const files = params.saleAttachments
                if(!files || files.length<1){
                    this.$refs.editPage.confirmLoading = false
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIxOLV_d540bb93`, '签署文件不能为空'))
                    return
                }
                //前述状态“已签署”
                params.signFileUploaded='1'
            }
            //供方
            params.modifyPerson='1'
            postAction('/esignv3/elsEsignV3Flow/saleSend', params).then(res => {
                this.$refs.editPage.confirmLoading = false
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.goBack()
                }
            })

        }
    }
}
</script>