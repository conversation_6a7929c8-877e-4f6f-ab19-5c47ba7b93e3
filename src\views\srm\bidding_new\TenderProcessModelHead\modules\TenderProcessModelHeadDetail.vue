<template>
  <div class="purchaseSupplierCapacityHead">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        @handleTabChange="handleTabChange"
        v-on="businessHandler"
      >
        <template #tenderProcessModelItemListTab="{slotProps}">
          <nodeCheckBox
            ref="nodeCheckBox"
            :currentEditRow="currentEditRow"
            :disabled="true"
            :nodeData="nodeData"
          ></nodeCheckBox>
        </template>
      </business-layout>
      <a-modal
    v-drag    
        v-model="showNode"
        :footer="null"
        width="1200px"
        :title="$srmI18n(`${$getLangAccount()}#i18n_field_QLIrUB_58cb0bac`, '流程模板预览')" >
        <IotStep
          :stepList="periodTypeInfoArray"
          :allNodeMap="allNodeMap">
        </IotStep>
      </a-modal>

    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import nodeCheckBox from './components/nodeCheckBox'
import IotStep from './components/IotStep'
import {getAction, postAction, httpAction} from '@/api/manage'

export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout,
        IotStep,
        nodeCheckBox
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            showNode: false,
            requestData: {
                detail: { url: '/tender/tenderProcessModelHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {

            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_QLUB_3387242e`, '流程预览'),
                    click: this.handleShowNode,
                    show: this.showNodeView
                }
            ],
            nodeData: {},
            groupCode: '',
            periodTypeInfoArray: {},
            allNodeMap: {},
            url: {
                queryByGruop: '/tender/tenderProcessModelHead/queryNodeByGruop'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_tenderProcessModel_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleTabChange (tabCode) {
            this.groupCode = tabCode
            if (tabCode == 'tenderProcessModelItemList') {
                this.getNodeGruop()
            }
        },
        getNodeGruop () {
            let {allData} = this.$refs[`${this.businessRefName}`].extendAllData()
            this.confirmLoading = true
            postAction(this.url.queryByGruop, allData).then(res => {
                if (res.success) {
                    this.nodeData = res.result
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showNodeView () {
            if(this.groupCode == 'tenderProcessModelItemList') return true
            return false
        },
        handleShowNode () {
            let checkNode = this.$refs.nodeCheckBox.extendNodeData() || []
            let {mustNode, periodTypeInfo, notMustNode} = this.nodeData
            this.allNodeMap = {}
            this.periodTypeInfoArray = periodTypeInfo
            Object.values(mustNode).map(el => {
                el.map(item => {
                    if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                    this.allNodeMap[item.periodType].push(item)
                })
            })
            Object.values(notMustNode).map(el => {
                el.map(item => {
                    if (checkNode.includes(item.id)) {
                        if (!this.allNodeMap[item.periodType]) this.allNodeMap[item.periodType] = []
                        this.allNodeMap[item.periodType].push(item)
                    }
                })
            })
            this.showNode = true
        }


    }
}
</script>