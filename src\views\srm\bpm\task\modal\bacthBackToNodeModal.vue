<template>
  <div>
    <a-spin :spinning="loading">
      <a-form-model
        :model="form"
        :rules="rules"
        :ref="formName"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <!-- <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_userDeal`, '处理人')"
          prop="usersInfo">
          <a-tag
            v-if="singleUserData != null && singleUserData.id != null"
            size="large"
            color="blue"
            closable
            @close="delSingleUsers"
          >{{ singleUserData.fullName }}</a-tag
          >
          <div>
            <a-button
              type="primary"
              icon="user"
              @click="selectSingleShowUsers"></a-button>
          </div>
        </a-form-model-item> -->
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_priorityLevel`, '优先级')"
          prop="priority">
          <a-radio-group
            name="radioGroup"
            defaultValue="50"
            v-model="form.priority">
            <a-radio
              v-for="(item, index) in priorityMap"
              :key="index"
              :value="item.value">{{
                item.title
              }}</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_taskName`, '任务标题')"
          prop="taskTitle">
          <a-input
            v-model="form.taskTitle"
            clearable />
        </a-form-model-item>
        <a-form-model-item
          :label="$srmI18n(`${$getLangAccount()}#i18n_field_qdWII_181d02a0`, '备注/意见')"
          prop="option">
          <a-textarea
            show-word-limit
            v-model="form.option"
            allowClear
            type="textarea"
            autoSize />
        </a-form-model-item>
      </a-form-model>
    </a-spin>
    <field-select-modal ref="fieldSelectModal" />
  </div>
</template>
<script>
import modalMixins from '../../components/modal/modalMixins.js'
import {postAction} from '@/api/manage'
export default {
    mixins: [modalMixins],
    props: {
        taskInfoList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    data () {
        return {
            rules: {
                taskTitle: [
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ],
                option: [
                    { required: true, message: '请填写备注/意见', trigger: 'blur' },
                    {max: 500, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow500`, '内容长度不能超过500个字符')}
                ]
                // usersInfo: [{ required: true, message: '请选择处理人' } ]
            },
            singleUserData: {}
        }
    },
    methods: {
        delSingleUsers () {
            this.singleUserData = {}
        },
        handleConfirm () {
            this.cheackValidate().then(() => {
                let {taskTitle, option, priority} = this.form
                let params = {
                    operate: 'backToNode',
                    taskTitle,
                    option,
                    priority
                }
                params['taskInfoList'] = this.taskInfoList.map(item => {
                    return {
                        id: item.id,
                        taskTitle: item.taskTitle
                    }
                })
                this.loading = true
                postAction('/a1bpmn/audit/api/runtime/task/batch/reject', params).then(response => {
                    this.loading = false
                    if (response.code == 200) {
                        this.$emit('success')
                        if (response.result && response.result.length > 0) {
                            response.result.forEach((item, index) => {
                                this.$message.error(item)
                            })
                        }  else {
                            this.$message.success(response.message)
                        }
                    } else {
                        this.$message.error(response.message)
                    }
                }).finally(() => {
                    this.loading = false
                })
                this.loading = true
            })
        }
    },
    created () {
        this.getDictData('taskPriority', 'priorityMap')
    }
}
</script>
