<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        v-on="businessHandler"
      >
      </business-layout>
      <field-select-modal
        ref="fieldSelectModal"
        requestMethod="post"
        isEmit
        @ok="fieldSelectOk" />
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"
        :currentEditRow="currentEditRow"/>
      <a-modal
        v-drag    
        v-model="auditVisible"
        :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, ' 审批意见')"
        :okText="okText"
        @ok="handleOk">
        <a-textarea
          v-model="opinion"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, ' 请输入审批意见')"
          :auto-size="{ minRows: 3, maxRows: 5 }"
        />
      </a-modal>
      <remote-js
        v-if="showRemote"
        :src="fileSrc"
        @load="loadSuccess"
        @error="loadError" />
    </a-spin>
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction, httpAction} from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin'

export default {
    name: 'PurchaseBiddingProjectAudit',
    components: {
        BusinessLayout,
        fieldSelectModal,
        flowViewModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            auditVisible: false,
            opinion: '',
            isView: false,
            showRemote: false,
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            currentRow: {},
            currentUrl: '',
            cancelAuditShow: false,
            refresh: true,
            showHelpTip: false,
            editRowModal: false,
            previewModal: false,
            notShowTableSeq: true,
            editItemRow: {},
            currentItemContent: '',
            previewContent: '',
            confirmLoading: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            remoteJsFilePath: '',
            requestData: {
                detail: { url: '/bidding/purchaseBiddingProjectHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
                purchaseAmateurItemList: [

                ]
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditPass,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.auditReject,
                    show: this.syncShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    type: 'back',
                    click: this.goBackAudit
                }
            ],
            url: {

            }
        }
    },
    /*computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_biddingProject_${templateNumber}_${templateVersion}`
        }
    },*/
    mounted () {
        const  that = this
        getAction('/bidding/purchaseBiddingProjectHead/queryById', {id: this.currentEditRow.businessId}).then(res=>{
            if(res.success){
                if (res.result) {
                    console.log(res.result)
                    that.currentEditRow.templateNumber=res.result.templateNumber
                    that.currentEditRow.templateVersion=res.result.templateVersion
                    that.currentEditRow.templateAccount=res.result.templateAccount
                    that.currentEditRow.rootProcessInstanceId = res.result.flowId
                    that.currentEditRow.contractType = res.result.contractType
                    that.remoteJsFilePath = that.remoteJsFilePathFn()
                    that.isView = true
                } else {
                    that.$message.error('查询失败')
                }
            }
        })
    },
    methods: {
        remoteJsFilePathFn () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let elsAccount = this.currentEditRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${elsAccount}/purchase_biddingProject_${templateNumber}_${templateVersion}.js?t=`+time
        },
        fieldSelectOk () {},
        selectDataOk () {},
        syncShow ({ pageData }) {
            console.log('pageData', (pageData))
            return (pageData.auditStatus === '1')
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBackAudit()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = true
            })
        },
        goBack () {
            this.$emit('hide')
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
        },
        goBackAudit () {
            this.$parent.hideController()
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            if(!this.flowId){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noeStatusCantView`, '当前状态不能查看流程！'))
                return
            }
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        }
    }
}
</script>