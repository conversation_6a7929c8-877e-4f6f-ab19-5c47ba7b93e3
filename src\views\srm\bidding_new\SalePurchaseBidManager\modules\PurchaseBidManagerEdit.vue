<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :queryData="queryData"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleAfterDealSource="handleAfterDealSource"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { postAction, getAction} from '@/api/manage'
import { add } from '@/utils/mathFloat.js'

export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        EditFormLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            console.log('editrow', this.currentEditRow)
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_SupplierTenderProjectPurchaseBid_${templateNumber}_${templateVersion}`
            // return '100000/sale_SupplierTenderProjectPurchaseBid_TC2022042103_1'
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            pageStatus: 'edit',
            confirmLoading: false,
            externalToolBar: {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            disabledItemNumber: true,
                            itemInfo: [], // 必传
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectPurchaseBid', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        },
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            },
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/edit'
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save2',
                    // showMessage: true,
                    click: this.handleSave,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'submit2',
                    // showMessage: true,
                    click: this.handleSubmit,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            requestData: {
                detail: {
                    url: '/tender/sale/supplierTenderProjectPurchaseBid/queryById', 
                    args: (that) => {
                        return { 
                            id: that.currentEditRow.id || ''
                        }
                    },
                    config: (that) => {// 招投标平台新增预审、后审、一步、二步法，需要在查详情是添加请求头
                        return {
                            headers: {xNodeId: `${that.currentEditRow.checkType || ''}_0`}
                        }
                    }
                }
            },
            url: {
                queryById: '/tender/sale/supplierTenderProjectPurchaseBid/queryById',
                queryProjectInfo: '/tender/sale/supplierTenderProjectPurchaseBid/queryProjectInfo',
                add: '/tender/sale/supplierTenderProjectPurchaseBid/add',
                edit: '/tender/sale/supplierTenderProjectPurchaseBid/edit',
                submit: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
            },
            userInfo: {},
            projectObj: {}
        }
    },
    created () {
        if (JSON.stringify(this.queryData) != '{}') {
            this.userInfo = this.$ls.get(USER_INFO)
        }
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.tenderProjectNumber || this.currentEditRow.id,
                actionRoutePath: '采购商与供应商的路径逗号隔开,/bidder/PurchaseBidManagerList'
            }
        },
        handleSubmitBefore (args) {
            console.log('args', args)
            
            return new Promise((resolve) => {
                let allData = args.allData
                allData['saleTenderInvoiceInfoList'] = [Object.assign(allData['invoiceInfo'], allData['payType'])]
                allData['noticeId'] = this.queryData && this.queryData.businessId
                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')

                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)
            
            let {consortiumBidding} = resultData
            if(consortiumBidding == '0'){
                pageConfig.groups[0].formModel.combination='0'
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let flag = (consortiumBidding == '0')
            setDisabledByProp('combination', flag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            let validateFlag = (consortiumBidding == '0')
            setValidateRuleByProp('combination', validateFlag)

            let formModel = pageConfig.groups[0].formModel
            for(let key in resultData){
                formModel[key] = resultData[key]
                if (key == 'supplierAccount') {
                    formModel[key] = resultData[key] || this.$ls.get(USER_COMPANYSET).companyName
                }
            }
            const tenderProjectPurchaseBidOrderVOList = resultData['tenderProjectPurchaseBidOrderVOList']
            // 累加所有文件的标价总和
            let payMoneys = 0
            if (tenderProjectPurchaseBidOrderVOList && tenderProjectPurchaseBidOrderVOList.length > 0) {
                tenderProjectPurchaseBidOrderVOList.forEach(item => {
                    const saleAmount = item.saleAmount ? parseFloat(item.saleAmount) : 0
                    payMoneys = add(payMoneys, saleAmount)
                })
            }
            // 编辑赋值
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        let {invoice, 
                            invoiceType,
                            enterpriseName, 
                            dutyParagraph, 
                            enterpriseAddress, 
                            enterprisePhone,
                            depositBank,
                            bankNumber } = saleTenderInvoiceInfoList[0]
                        group['formModel'] = {
                            invoice: invoice, 
                            invoiceType: invoiceType,
                            enterpriseName: enterpriseName, 
                            dutyParagraph: dutyParagraph, 
                            enterpriseAddress: enterpriseAddress, 
                            enterprisePhone: enterprisePhone,
                            depositBank: depositBank,
                            bankNumber: bankNumber }
                        // group['formModel'] = Object.assign(group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                    if (group.groupCode == 'payType') {
                        let {payType, payMoney, remark} = saleTenderInvoiceInfoList[0]
                        
                        group['formModel'] = {payType, payMoney: payMoney || payMoneys, remark}
                        // group['formModel'] = Object.assign(group['formModel'], saleTenderInvoiceInfoList[0])
                    }
                })
            }


            // 判断是否有 单位名称，没有就取用户信息里面的
            // pageConfig.groups[0].formModel.supplierName = pageConfig.groups[0].formModel.supplierName || this.userInfo.enterpriseName

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || this.queryData.subpackageId || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        },
        handleSave () {
            const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditRow
            let params = Object.assign( this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
            const checkType = params['checkType'] || ''
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    // this.currentEditRow.id = res.result.id
                    // this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                    // this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmit () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then(res => {
                // let params = {
                //     ...this.currentEditRow,
                //     ...this.$refs[this.businessRefName].extendAllData().allData
                // }
                const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditRow
                let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            
                params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
                // params['noticeId'] = params['noticeId'] || this.queryData.businessId || ''
                const checkType = params['checkType'] || ''
                this.confirmLoading = true
                
                postAction(this.url.submit, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                    let type = res.success ? 'success': 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.$parent.showEditPage = false
                        this.$store.dispatch('SetTabConfirm', false)
                        this.$parent.searchEvent(false)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })

                // postAction(url, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                //     let type = res.success ? 'success': 'error'
                //     if (res.success) {
                //         params['id'] = res.result.id
                //         postAction(this.url.submit, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                //             let type = res.success ? 'success': 'error'
                //             this.$message[type](res.message)
                //             if (res.success) {
                //                 this.$parent.showEditPage = false
                //                 this.$store.dispatch('SetTabConfirm', false)
                //                 this.$parent.searchEvent(false)
                //             }
                //         })
                //         // this.currentEditRow.id = res.result.id
                //         // this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                //         // this.getData()
                //     } else {
                //         this.$message[type](res.message)
                //     }
                // }).finally(() => {
                //     this.confirmLoading = false
                // })
            }, error => {
                console.log('有一个没有填', error)
            })
        }
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>