<!--
 * @Author: your name
 * @Date: 2021-03-29 14:02:20
 * @LastEditTime: 2021-05-31 15:17:18
 * @LastEditors: your name
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\views\srm\contract\purchase\PurchaseContractTemplateItemHisModal.vue
-->
<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>

    <His-Contract-Item-Modal
      ref="hisContractItemModal"
      :current-edit-row="currentEditRow"
    />
    
  </div>
</template>

<script>
import HisContractItemModal from './modules/HisPurchaseContractTemplateItemModal'
import {listPageMixin} from '@comp/template/listPageMixin'

export default {
    name: 'ContractItemHisModal',
    mixins: [listPageMixin],
    components: {
        HisContractItemModal
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                button: [
                ],
                publicBtn: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: '', icon: '', clickFn: this.goBack}
                ],
                formField: [
                ],
                form: {
                    itemId: ''
                },
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleEdit}
                ]
            },
            url: {
                list: '/contract/purchaseContractLibrary/list',
                columns: 'purchaseContractLibraryList'
            }
        }
    },
    computed: {
        
    },
    created () {
        let itemId = this.currentEditRow.id
        this.pageData.form.itemId = itemId
    },
    methods: {
        handleEdit (row){
            this.$refs.HisContractItemModal.open(row)
        },
        goBack (){
            this.$emit('hide')
        }
    }
}
</script>
