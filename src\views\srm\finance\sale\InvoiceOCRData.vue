<template>
  <div style="height:100%">
    <list-layout
      ref="listPage"
      v-show=" !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab"/>
    <invoice-upload-form
      v-if="showUploadFilePage"
      ref="uploadForm"
      :single="true"
      :action="uploadUrl"
      :data="{businessType:'invoiceOCR', headId: 2}"
      @custom_upload_create="handleCreate"
      @hide="hideUploadFilePage" />
    <viewInvoiceOCR-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage" />
  </div>
</template>
<script>
import {ListMixin} from '@comp/template/list/ListMixin'
import InvoiceUploadForm from './modules/InvoiceUploadForm'
import ViewInvoiceOCRModal from './modules/ViewInvoiceOCRModal'
import { httpAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        InvoiceUploadForm,
        ViewInvoiceOCRModal
    },
    data () {
        return {
            
            uploadUrl: '/attachment/saleAttachment/upload',
            showDetailPage: false,
            showUploadFilePage: false,
            pageData: {
                businessType: 'paymentApply',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: '请输入发票号码'
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), clickFn: this.uploadFile, type: 'primary', authorityCode: 'finance#invoiceOCRData:add'},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'), icon: 'file-text', folded: true, isDocument: true, clickFn: this.showHelpText},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'), icon: 'file-pdf', folded: true, isDocument: true, clickFn: this.showHelpPDF}
                ],
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'finance#invoiceOCRData:view'},
                    {type: 'record', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hPOb_27f4bc0a`, '发票验真'), clickFn: this.invoiceVerification, authorityCode: 'reconciliation#saleInvoice:invoiceVerification'}
                ],
                optColumnWidth: 80
            },
            url: {
                list: '/reconciliation/invoiceOcrData/list',
                invoiceOCR: '/reconciliation/invoiceOcrData/invoiceOCR',
                invoiceVerification: '/reconciliation/invoiceOcrData/invoiceVerification',
                columns: 'InvoiceOcrDataTable'
            },
            tabsList: []
        }
    },
    methods: {
        handleCreate (result) {
            this.postUpdateData(this.url.invoiceOCR, result)
            this.hideUploadFilePage()
            this.searchEvent()
        },
        hideDetailPage (){
            this.showDetailPage = false
        },
        uploadFile (){
            this.showUploadFilePage = true
        },
        hideUploadFilePage (){
            this.showUploadFilePage = false
        },
        invoiceVerification (row){
            var param = []
            param.push(row)
            this.postUpdateData(this.url.invoiceVerification, param)
            this.searchEvent()
        },
        postUpdateData (url, row){
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        }
    }
}
</script>