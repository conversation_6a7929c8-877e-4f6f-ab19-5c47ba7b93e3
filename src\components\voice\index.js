import Vue from 'vue'
import Voice from './src/main.vue'

const VoiceBox = Vue.extend(Voice)
let instance
export default {
    play (options = {}) {
        if (!instance) {
            instance = new VoiceBox({
                el: document.createElement('div')
            })
        }
        if (instance) {
            document.body.appendChild(instance.$el)
            Vue.nextTick(() => {
                instance.init(options)
            })
        }
    },
    close () {
        if (instance) {
            instance.playing = false
        }
    }
}