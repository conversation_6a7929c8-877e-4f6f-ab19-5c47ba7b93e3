<template>
  <div class="Dataform">
    <a-form-model
      v-if="isEdit"
      ref="form"
      :model="formData"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol">
      <a-row :getterr="12">
        <a-col
          :span="solSpan"
          v-for="(item, index) in fields"
          :key="item.field + index">
          <template v-if="item.fieldType == 'input'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-input
                v-model="formData[item.field]"
                :disabled="item.disabled"
                @change="changeInputValue(formData[item.field], item)" />
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'number'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-input-number
                style="width: 100%;"
                v-model="formData[item.field]"
                :disabled="item.disabled"
                @change="changeInputValue(formData[item.field], item)" />
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'select'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <m-select
                v-model="formData[item.field]"
                :disabled="item.disabled"
                :disabledValueList="item.disabledValueList"
                @change="changeInputValue(formData[item.field], item)"
                :filterSelectList="item.filterSelectList || []"
                :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}`"
                :dict-code="item.dictCode" />
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'selectModal'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-input
                readOnly
                :disabled="item.disabled"
                :value="formData[item.field]"
                :placeholder="item.placeholder"
                @click="openSelectModal(item)">
                <a-icon
                  v-if="!item.disabled"
                  slot="suffix"
                  type="close-circle"
                  @click="clearInputValue(item)"></a-icon>
              </a-input>
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'dateTime'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <a-date-picker
                style="width: 100%"
                :disabled="item.disabled"
                @change="changeInputValue(formData[item.field], item)"
                :show-time="item.dataFormat && item.dataFormat.length > 10 ? true : false"
                :valueFormat="item.dataFormat || 'YYYY-MM-DD'"
                v-model="formData[item.field]"
                :placeholder="`${$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnter`, '请输入')}${$srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title)}`"
              />
            </a-form-model-item>
          </template>
          <template v-else-if="item.fieldType == 'switch'">
            <a-form-model-item
              :label="item.title"
              :prop="item.field">
              <m-switch
                :disabled="item.disabled"
                @change="changeInputValue(formData[item.field], item)"
                v-model="formData[item.field]" />
            </a-form-model-item>
          </template>
        </a-col>
      </a-row>
    </a-form-model>
    <div
      v-else
      class="description">
      <a-descriptions v-bind="descriptionsConfig">
        <a-descriptions-item
          v-for="item in fields"
          :key="item.field">
          <span slot="label">
            {{ $srmI18n(`${busAccount}#${item.fieldLabelI18nKey}`, item.title) }}
            <a-tooltip
              v-if="item.helpText"
              :title="item.helpText">
              <a-icon type="question-circle-o" />
            </a-tooltip>
          </span>
          <template>
            <span v-if="item.fieldType == 'switch' || item.fieldType === 'select' || item.fieldType === 'multiple'">
              {{ formData[item.field + '_dictText'] }}
            </span>
            <span
              :style="{ display: 'inline-block', 'min-height': fields.length === 1 ? '100px' : 'auto' }"
              v-else>
              {{ formData[item.field] }}
            </span>
          </template>
        </a-descriptions-item>
      </a-descriptions>
    </div>
    <field-select-modal
      isEmit
      :isTree="isTree"
      :treeConfig="treeConfig"
      @afterClearCallBack="
        (cb) => {
          this.handleSelectModalAfterClear(cb)
        }
      "
      @ok="handleSelectModalAfterSelect"
      ref="fieldSelectModal"
    />
  </div>
</template>
<script>
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { valitNumberLength } from '@views/srm/bidding_new/utils/index'

export default {
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        fields: {
            default: () => {
                return []
            },
            type: Array
        },
        validateRules: {
            default: () => {
                return {}
            },
            type: Object
        },
        // 页面的状态
        pageStatus: {
            type: String,
            default: 'edit'
        },
        solSpan: {
            type: [String, Number],
            default: 8
        },
        labelCol: {
            type: Object,
            default: () => {
                return { span: 9 }
            }
        },
        wrapperCol: {
            type: Object,
            default: () => {
                return { span: 15 }
            }
        }
    },
    components: {
        fieldSelectModal
    },
    watch: {
        fields () {
            this.initDetaultFunc()
            this.initValidateRules()
        }
    },
    data () {
        
        return {
            rules: {},
            curItem: {}
        }
    },
    computed: {
        isTree () {
            return (this.curItem.curConfig && this.curItem.curConfig.isTree) || false
        },
        treeConfig () {
            return (this.curItem.curConfig && this.curItem.curConfig.treeConfig) || {}
        },
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow) {
                if (this.currentEditRow.busAccount || this.currentEditRow.elsAccount) {
                    account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount
                }
            }
            return account
        },
        descriptionsConfig () {
            let config = {
                layout: 'horizontal',
                column: 2,
                size: 'small',
                bordered: true
            }
            if (this.fields && this.fields.length && this.fields.length === 1) {
                config.layout = 'vertical'
                config.column = 1
                config.size = 'middle'
            }
            return config
        },
        // 判断页面是编辑态还是详情态
        isEdit () {
            return this.pageStatus === 'edit'
        }
    },
    methods: {
        changeInputValue (value, item) {
            if (item && item.bindFunction && typeof item.bindFunction === 'function') {
                item.bindFunction(value, item, this.fields, this, this.formData)
            }
        },
        // selectModal 清除
        handleSelectModalAfterClear (cb) {
            cb && cb(this)
        },
        // selectModal 确认时
        handleSelectModalAfterSelect (data) {
            this.curItem.curConfig.bindFunction && this.curItem.curConfig.bindFunction.call(null, this, data)
        },
        //打开选择弹窗
        openSelectModal (item) {
            this.curItem = item
            if (!item.curConfig || !Object.keys(item.curConfig).length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_configurationParameter`, '请配置参数'))
                return
            }
            let modalParams = item.curConfig.modalParams || {}
            let beforeCheckedCallBack = item.curConfig.beforeCheckedCallBack || (() => window.Promise.resolve())
            let url = item.curConfig.modalUrl || ''
            let columns = item.curConfig.modalColumns || []
            let model = item.curConfig.selectModel || 'single'
            let params = {}
            if (typeof modalParams === 'function') {
                params = modalParams(this, this.formData)
            } else {
                params = modalParams
            }
            // 单选或多选可为方法拓展
            let modelParams = {}
            if (typeof model === 'function') {
                modelParams = model(this, this.formData)
            } else {
                modelParams = model
            }
            this.changeSelectModalValue(beforeCheckedCallBack)
                .then(() => {
                    this.$refs.fieldSelectModal.open(url, params, columns, modelParams)
                })
                .catch((errTxt) => {
                    this.$message.error(errTxt)
                    if (this.isRow) {
                        this.$emit('error', errTxt)
                    }
                })
        },
        clearInputValue (item) {
            this.curItem = item
            let beforeCheckedCallBack = item.curConfig.beforeCheckedCallBack || (() => window.Promise.resolve())
            let afterClearCallBack = item.curConfig.afterClearCallBack || ((f) => f)
            this.changeSelectModalValue(beforeCheckedCallBack)
                .then(() => {
                    if (item.curConfig.isClearCallBindFunction) {
                        this.clearCallBindFunction()
                    }
                    console.log(item)
                    this.$set(this.formData, item.field, '')
                    afterClearCallBack(this, this.formData)
                })
                .catch((errTxt) => {
                    this.$message.error(errTxt)
                })
        },
        // 统一表头、表行回调参数
        changeSelectModalValue (cb) {
            return cb && cb(this, this.formData)
        },
        getValidatePromise () {
            return this.$refs.form.validate()
        },
        async externalAllData (isValidate = true) {
            // let flag = isValidate ? await this.getValidatePromise() : false
            let flag = true
            if (isValidate) {
                flag = await this.getValidatePromise()
            }
            if (flag) {
                let params = Object.assign({}, this.formData)
                return params
            }
            return false
        },
        // 初始化传过来默认方法
        initDetaultFunc () {
            try {
                let that = this
                that.$nextTick(() => {
                    const formFields = that.fields || []
                    formFields.forEach((field) => {
                        // 判断是否有值 且 是否有默认值
                        if (!this.formData[field.field] && field.defaultValue) {
                            this.$set(this.formData, field.field, field.defaultValue)
                        }
                    })
                })
            } catch (error) {
                console.log(error)
            }
        },
        // 初始化校验
        initValidateRules () {
            if (this.validateRules) {
                this.rules = Object.assign({}, this.validateRules)
            }
            let checkNumber = (rule, cellValue, callback) => {
                if (cellValue) {
                    if (!valitNumberLength(cellValue, 8)){
                        return callback(new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HzxOBR_760160f9`, '长度不能超过') + '8'))
                    }
                }
                callback()
            }
            let ruleRequired = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_required`, '必填')
            this.fields.forEach(({field, title, required, fieldType, disabled}) => {
                if (!this.rules[field]) this.rules[field] = []
                if (required == '1') this.rules[field].push({ required: true, message: `${title}${ruleRequired}!` })
                switch (fieldType) {
                case 'input':
                    if (!disabled) this.rules[field].push({ max: 100, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_overflow100`, '内容长度不能超过100个字符') })
                    break
                case 'number':
                    this.rules[field].push({ validator: checkNumber})
                    break
                default:
                    break
                }
            })
        }
    },
    mounted () {
        this.initDetaultFunc()
        this.initValidateRules()
    }
}
</script>
<style lang="less" scoped>
.Dataform {
    background: #fff;
    padding: 12px;
}
</style>
