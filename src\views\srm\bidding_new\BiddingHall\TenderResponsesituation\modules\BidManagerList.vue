<template>
  <div>
    <listTable 
      ref="listTable"
      :fromSourceData="fromSourceData"
      :statictableColumns="tableColumns"
      :showTablePage="false"
      :pageData="pageData"
    />
  </div>
</template>
<script>
import listTable from '../../components/listTable'
export default {
    props: {
        fromSourceData: {
            default: () => {
                return []
            },
            type: Array
        },
        type: {
            default: () => {
                return ''
            },
            type: String
        }
    },
    components: {
        listTable
    },
    inject: ['tenderCurrentRow'],
    data () {
        return {
            pageData: {
                showOptColumn: true,
                optColumnList: [
                    {type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit },
                    {type: 'confirm', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirm`, '确认'), clickFn: this.handleConfirm, allow: this.allowConfirm}
                ],
                optColumnWidth: 120,
                optColumnAlign: 'center',
                superQueryShow: true,
                isOrder: {
                    column: 'id',
                    order: 'desc'
                } 
            },
            tableColumns: [
                {
                    'type': 'seq',
                    'width': 50,
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
                    'field': 'supplierName'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBzE_41c18e05`, '购标状态'),
                    'field': 'status_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBKI_41c038b8`, '购标时间'),
                    'field': 'createTime',
                    'width': 160
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
                    'field': 'sourceType_dictText',
                    'width': 120
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
                    'field': 'contacts'
                },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
                    'field': 'contactsPhone'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
                    width: 180,
                    fixed: 'right',
                    slots: { default: 'grid_opration' }
                }
            ]
        }
    },
    computed: {
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        }
    },
    methods: {
        handleView (row) {
            this.$emit('handleBidManagerViewPage', row)
        },
        handleConfirm (row){
            this.$emit('handleBidManagerConfirmPage', row)
        },
        allowConfirm (row){
            return (row.status !== '1')
        },
        allowEdit (row){
            if (this.applyRoleCanEdit) {
                return !(row.status == '0' && row.sourceType == '1')
            } else {
                return true
            }
        },
        handleEdit (row){
            this.$emit('handleBidManagerEditPage', row)
        }
    },
    created () {
        // if(this.type == 'prejudication'){
        //     this.tableColumns = [
        //         {
        //             'type': 'seq',
        //             'width': 50,
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
        //             'field': 'supplierName',
        //             'width': 250
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddzE_751366cf`, '预审购标状态'),
        //             'field': 'preStatus_dictText',
        //             'width': 160
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBKI_41c038b8`, '购标时间'),
        //             'field': 'createTime',
        //             'width': 160
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
        //             'field': 'sourceType_dictText',
        //             'width': 80
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
        //             'field': 'contacts',
        //             'width': 80
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
        //             'field': 'contactsPhone',
        //             'width': 80
        //         },
        //         {
        //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
        //             width: 180,
        //             fixed: 'right',
        //             slots: { default: 'grid_opration' }
        //         }
        //     ]
        // }else{
        //     this.tableColumns = [
        //         {
        //             'type': 'seq',
        //             'width': 50,
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'),
        //             'field': 'supplierName',
        //             'width': 250
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBzE_41c18e05`, '购标状态'),
        //             'field': 'status_dictText',
        //             'width': 160
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RBKI_41c038b8`, '购标时间'),
        //             'field': 'createTime',
        //             'width': 160
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dataSource`, '数据来源'),
        //             'field': 'sourceType_dictText',
        //             'width': 80
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contact`, '联系人'),
        //             'field': 'contacts',
        //             'width': 80
        //         },
        //         {
        //             'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_receivePhone`, '联系电话'),
        //             'field': 'contactsPhone',
        //             'width': 80
        //         },
        //         {
        //             title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
        //             width: 180,
        //             fixed: 'right',
        //             slots: { default: 'grid_opration' }
        //         }
        //     ]
        // }
    }
}
</script>

