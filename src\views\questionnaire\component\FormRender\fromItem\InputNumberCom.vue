<template>
  <a-form-item
    :label="index +'.' + field.label"
    :has-feedback="field.validateOption.icon"
    :validate-status="field.validateOption.status"
    :help="field.validateOption.message"
    :required="!!field.rules && field.rules.length > 0"
  >
    <a-input-number
      :disabled="field.disabled"
      :placeholder="field.placeholder"
      :min="field.exclusive ? field.exclusive.min : 0"
      :max="field.exclusive ? field.exclusive.max : 100"
      v-decorator="[field.id, {
        initialValue: field.initialValue
      }]" />
  </a-form-item>
</template>

<script>
export default {
    name: 'InputNumberCom',
    props: {
        field: {
            type: Object,
            required: true
        }
    },
    inject: ['index'],
    created () {
    }
}
</script>

<style scoped>

</style>
