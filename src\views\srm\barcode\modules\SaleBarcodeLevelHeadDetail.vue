<template>
  <div class="PurchaseBarcodeInfoHeadEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        ref="businessRef"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        modelLayout="masterSlave"
        pageStatus="detail"
        :handleAfterDealSource="handleAfterDealSource"
        v-on="businessHandler"
      >
      </business-layout>

    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { getAction, postAction } from '@/api/manage'

export default {
    name: 'SaleBarcodeLevelHeadDetail',
    components: {
        BusinessLayout,
        fieldSelectModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            requestData: {
                detail: {
                    url: '/base/barcode/saleBarcodeLevelHead/queryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                submit: '/base/barcode/saleBarcodeLevelHead/edit'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/sale_barcodeLevel_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        handleAfterDealSource (pageConfig, resultData) {
            let formModel = pageConfig.groups[0].formModel
            for (let key in resultData) {
                formModel[key] = resultData[key]
            }
        },
        goBack () {
            this.$parent.hideEditPage()
        }
    }
}
</script>