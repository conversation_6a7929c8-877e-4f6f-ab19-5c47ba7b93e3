<template>
  <a-modal
    v-drag    
    v-model="visible"
    :title="title"
    @ok="confirm">
    <a-spin :spinning="loading">
      <div>
        <a-select
          v-model="pricingNotice"
          style="width:100%">
          <a-select-option
            v-for="(item, index) in optionData"
            :key="index"
            :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'PricingNotice',
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_GRIueRId_3aaaeb60`, '设置定价通知对象'),
            loading: false,
            visible: false,
            headId: undefined,
            pricingNotice: undefined,
            optionData: []
        }
    },
    mounted () {
        this.loading = true
        postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'srmPricingNotice'}).then(res => {
            if(res.success === true){
                res.result.forEach(item => {
                    let dictData = {}
                    dictData['value'] = item.value
                    dictData['label'] = item.text
                    this.optionData.push(dictData)
                })
            }
        }).finally(() => {
            this.loading = false
        })
    },
    methods: {
        open (headId, pricingNotice) {
            this.headId = headId
            this.pricingNotice = pricingNotice
            this.visible = true
        },
        confirm (){
            this.loading = true
            getAction('/enquiry/purchaseEnquiryHead/pricingNotice', {headId: this.headId, pricingNotice: this.pricingNotice}).then(res => {
                if(res.success === true){
                    this.$message.success(res.message)
                    this.$parent.refresh()
                }else{
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.loading = false
                this.visible = false
            })
        }
    }
}
</script>