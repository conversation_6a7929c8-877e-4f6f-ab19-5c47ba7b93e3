<template>
  <div class="page-container">
    <detail-page
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"></detail-page>
    <!-- 查看流程 -->
    <!-- <a-modal
    v-drag    
      centered
      :width="960"
      :maskClosable="false"
      :visible="flowView"
      @ok="closeFlowView"
      @cancel="closeFlowView">
      <iframe
        style="width:100%;height:560px"
        title=""
        :src="currentBasePath + '/uflo/diagram?processInstanceId='+flowId"
        frameborder="0"></iframe>
    </a-modal> -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, '审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseEnterapprovalComments`, '请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>

<script>
import detailPage from '@comp/template/detailPage'
import { httpAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'

export default {
    name: 'PurchaseRequestAuditModal',
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    components: {
        flowViewModal,
        detailPage
    },
    data () {
        return {
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionApproval`, '采购申请审批'),
            confirmLoading: false,
            flowId: 0,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'baseForm',
                            form: {
                                requestNumber: '',
                                requestStatus: '0',
                                purchaseGroupCode: '',
                                purchaseGroupName: '',
                                purchaseOrgCode: '',
                                purchaseOrgName: '',
                                companyCode: '',
                                companyName: '',
                                planPurchase: '0',
                                applicant: '',
                                applyDate: '',
                                flowId: 0,
                                applyDept: '',
                                purchaseWay: '',
                                totalAmount: '',
                                remark: ''
                            },
                            list: [
                                {
                                    type: 'input', 
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRequisitionNo`, '采购申请单号'),
                                    fieldName: 'requestNumber'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_applicationFormStatus`, '申请单状态'),
                                    fieldName: 'requestStatus_dictText'
                                },
                                {
                                    type: 'dictSelect',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_baseForm65d_auditStatus`, '审批状态'),
                                    fieldName: 'auditStatus_dictText'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'),
                                    fieldName: 'companyCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchasingOrganization`, '采购组织'),
                                    fieldName: 'purchaseOrgCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseGroupCode`, '采购组'),
                                    fieldName: 'purchaseGroupCode'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_applicant`, '申请人'),
                                    fieldName: 'applicant'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_applyDept`, '申请部门'),
                                    fieldName: 'applyDept'
                                },
                                {
                                    type: 'date',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_applyDate`, '申请日期'),
                                    fieldName: 'applyDate'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseMethod`, '采购方式'),
                                    fieldName: 'purchaseWay'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_estimatedAmount`, '预估金额'),
                                    fieldName: 'totalAmount'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark'
                                }
                            ]
                        }
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_lineItem`, '行项目'),
                        content: {
                            type: 'table',
                            ref: 'purcaseRequestItemList',
                            columns: [
                                { 
                                    type: 'seq', width: 50,
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListf4c_rowNumber`, '行号'),
                                    field: 'itemNumber',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'),
                                    field: 'factoryCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_factoryCode_dictText`, '工厂名称'),
                                    field: 'factoryName',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialCode`, '物料编码'),
                                    field: 'materialCode',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'),
                                    field: 'materialDesc',
                                    width: 220
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpecification`, '物料规格'),
                                    field: 'materialSpec',
                                    width: 220
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroup`, '物料组'),
                                    field: 'materialGroupCode',
                                    width: 80
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialGroupName`, '物料组名称'),
                                    field: 'materialGroupName',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'),
                                    field: 'purchaseCycle',
                                    width: 100
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseType`, '采购类型'),
                                    field: 'purchaseType',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_demandDate`, '需求日期'),
                                    field: 'requireDate',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_needCout`, '需求数量'),
                                    field: 'requireQuantity',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_quantityUnit`, '数量单位'),
                                    field: 'quantityUnit',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_purchaseMassProdSampleItemListd03_verifyFinishTime`, '预估单价'),
                                    field: 'estimatePrice',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_subtotal`, '小计金额'),
                                    field: 'subtotalAmount',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'),
                                    field: 'currency_dictText',
                                    width: 150
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_costCenterName`, '成本中心'),
                                    field: 'costCenter',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceType`, '来源类型'),
                                    field: 'sourceType',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceOrderNumber`, '来源单号'),
                                    field: 'sourceNumber',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_sourceLineNumber`, '来源单行号'),
                                    field: 'sourceItemNumber',
                                    width: 120
                                },
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    field: 'remark',
                                    width: 220
                                }
                            ]
                        }
                    }
                ],
                url: {
                    detail: '/demand/purcaseRequestHead/queryDetailById'
                },
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), clickFn: this.auditPass},
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), clickFn: this.auditReject},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), clickFn: this.showFlow},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), clickFn: this.goBack}
                ]
            }
            
        }
    },
    mounted () {
       
    },
    created (){
      
    },
    methods: {
        goBack () {
            this.$parent.hideController()
        },
        auditPass (row){
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (row){
            this.currentRow = row
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        closeFlowView (){
            this.flowView = false
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        }
    }
}
</script>
