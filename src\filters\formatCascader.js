export default function (selectData, list, queryKey, resultKey='title') {
    if (!selectData) {
        return ''
    }
    // 递归获取value
    function findSupValue (data, condition, queryKey) {
        let key = queryKey || 'value'
        //传入
        const it = (i, n) => {
            if (i && i.length > 0) {
                for (let v of i) {
                    if (v[key] == n) {
                        return v
                    } else {
                        if (v.children && v.children.length > 0) {
                            let re = it(v.children, n)
                            if (re) {
                                return re
                            }
                        }
                    }
                }
            }
        }
        let ret = it(data, condition)
        return ret
    }
    const valArr = []
    const selectDataArr = selectData.split(',')
    selectDataArr.forEach(rs => {
        let ret = findSupValue(list, rs, queryKey)
        valArr.push(ret)
    })
    return valArr.map(rs => rs[resultKey]).join()
}