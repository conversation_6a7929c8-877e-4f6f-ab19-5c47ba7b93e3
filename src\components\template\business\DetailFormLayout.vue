<template>
  <div
    class="description"
    :style="minHeightStyle">
    <template v-if="group.formFields && group.formFields.length">
      <a-descriptions v-bind="descriptionsConfig">
        <a-descriptions-item
          v-for="field in formFields"
          v-show="!field.hide"
          :key="field.fieldName"
          :span="descriptionsSpan(field)">
          <span slot="label">
            <span
              class="item-required-icon"
              v-if="field.required==='1'">*</span>
            {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
            <a-tooltip
              v-if="field.helpText"
              :title="field.helpText">
              <a-icon type="question-circle-o"/>
            </a-tooltip>
          </span>
          <template>
            <!-- 阶梯价格 -->
            <template v-if="field.fieldName === 'ladderPriceJson'">
              <a-tooltip
                placement="top"
                v-if="group.formModel['ladderPriceJson']"
                overlayClassName="tip-overlay-class">
                <template slot="title">
                  <vxe-table
                    auto-resize
                    border
                    size="mini"
                    height="100%"
                    :data="initRowLadderJson(group.formModel['ladderPriceJson'])">
                    <vxe-table-column
                      type="seq"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details`, '序号')}`"
                      width="80"></vxe-table-column>
                    <vxe-table-column
                      field="ladderQuantity"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`"
                      width="140"></vxe-table-column>
                    <vxe-table-column
                      field="price"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                      width="140"></vxe-table-column>
                    <vxe-table-column
                      field="netPrice"
                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                      width="140"></vxe-table-column>
                  </vxe-table>
                </template>
                <div class="json-box"><a href="javascript: void(0)">{{ defaultRowLadderJson(group.formModel['ladderPriceJson']) }}</a></div>
              </a-tooltip>
            </template>
            <!--  超链接  -->
            <template v-else-if="field.extend && field.extend.linkConfig">
              <!--  外链  -->
              <a
                v-if="field.extend.exLink"
                :href="group.formModel[field.fieldName]"
                target="_blank">
                <span>{{ $srmI18n(`${$getLangAccount()}#${field.extend.linkConfig.titleI18nKey}`, field.extend.linkConfig.title) }}</span>
              </a>
              <!--  路由跳转  -->
              <span
                v-else
                :style="{'color':'#0000FF','cursor':'pointer'}"
                @click="getNewRouter(group.formModel[field.fieldName], field)">
                {{ group.formModel[field.fieldName] }}
              </span>
            </template>
            <div
              class="item-content"
              :class="{'item-required-text': field.required==='1' }"
              v-else>
              <!--  图片  -->
              <div v-if="field.fieldType == 'image' && group.formModel.hasOwnProperty(field.fieldName + '_new')">
                <template v-if="previewPictureText(group.formModel[field.fieldName + '_new'])">
                  <span
                    class="preview"
                    :key="idx"
                    v-for="(el, idx) of previewPictureText(group.formModel[field.fieldName + '_new'])"
                    @click.stop="previewPicture(el)">
                    <img
                      :key="idx"
                      :src="el"
                      alt=""/>
                  </span>
                </template>
              </div>
              <div v-else-if="field.fieldType == 'image'">
                <template v-if="previewPictureText(group.formModel[field.fieldName])">
                  <span
                    class="preview"
                    :key="idx"
                    v-for="(el, idx) of previewPictureText(group.formModel[field.fieldName])"
                    @click.stop="previewPicture(el)">
                    <img
                      :key="idx"
                      :src="el"
                      alt=""/>
                  </span>
                </template>
              </div>
              <div v-else-if="field.fieldType == 'customRender' || (field.fieldName === 'frozenFunction' && field.fieldType === 'customSelectModal')">
                <slot
                  name="customRender"
                  :field="field"
                  :group="group"></slot>
              </div>
              <span
                :style="{color: mapFieldColor(field, group.formModel)}"
                v-else-if="field.fieldType == 'switch' && group.formModel.hasOwnProperty(field.fieldName + '_new')">
                {{
                  ['1', 'Y'].includes(group.formModel[field.fieldName])
                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                }}
                <span>
                  ➔
                  <span style="color: #1890ff">
                    {{
                      ['1', 'Y'].includes(group.formModel[field.fieldName + '_new'])
                        ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                        : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                    }}
                  </span>
                </span>
              </span>
              <span v-else-if="(field.fieldType == 'input' || field.fieldType == 'selectModal' ) && group.formModel.hasOwnProperty(field.fieldName + '_new')">
                {{ group.formModel[field.fieldName] }}
                <span>
                  ➔
                  <span style="color: #1890ff">
                    {{ group.formModel[field.fieldName + '_new'] }}
                  </span>
                </span>
              </span>
              <span
                :style="{color: mapFieldColor(field, group.formModel)}"
                v-else-if="(field.fieldType == 'select' || field.fieldType === 'multiple') && group.formModel.hasOwnProperty(field.fieldName + '_new')">
                {{ group.formModel[field.fieldName + '_dictText'] }}
                <span>
                  ➔
                  <span style="color: #1890ff">
                    {{ group.formModel[field.fieldName + '_new_dictText'] }}
                  </span>
                </span>
              </span>
              <span
                :style="{color: mapFieldColor(field, group.formModel)}"
                v-else-if="field.fieldType == 'switch'">
                {{
                  ['1', 'Y'].includes(group.formModel[field.fieldName])
                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                }}
              </span>
              <span v-else-if="field.fieldType == 'richEditorModel'">
                <renderHtmlModal :content="group.formModel[field.fieldName]"/>
              </span>
              <span v-else-if="field.fieldType == 'treeSelect'">
                <span>{{ group.formModel[field.fieldName+'_dictText'] }}</span>
              </span>
              <span v-else-if="field.fieldType == 'currency'">
                <span>{{ formatCurrencyValue(group.formModel[field.fieldName], field) }}</span>
              </span>
              <span v-else-if="field.fieldType == 'float'">
                <span>{{ formatFloat(group.formModel[field.fieldName], field.dataFormat) }} </span>
              </span>
              <span v-else>
                <span
                  :style="{color: mapFieldColor(field, group.formModel)}"
                  v-if="field.fieldType == 'switch' || field.fieldType === 'select' || field.fieldType === 'multiple' || field.fieldType === 'cascader'">
                  {{ group.formModel[field.fieldName + '_dictText'] }}
                </span>
                <span
                  v-else-if="group.formModel.hasOwnProperty(field.fieldName + '_new')"
                  @click="renderValueClick(field)"
                  :style="{'display': 'inline-block', 'min-height': group.formFields.length===1? '100px': 'auto'}">
                  {{ group.formModel[field.fieldName] }}
                  <span>
                    ➔
                    <span style="color: #1890ff">
                      {{ group.formModel[field.fieldName + '_new'] }}
                    </span>
                  </span>
                </span>
                <span
                  v-else
                  @click="renderValueClick(field)"
                  :style="{'display': 'inline-block', 'min-height': group.formFields.length===1? '100px': 'auto'}">
                  {{ group.formModel[field.fieldName] }}
                </span>
              </span>
            </div>
          </template>
        </a-descriptions-item>
        <a-descriptions-item
          v-if="group.total.totalValue">
          <span slot="label">
            {{ $srmI18n(`${busAccount}#${group.groupNameI18nKey}`, group.groupName) }} {{ $srmI18n(`${$getLangAccount()}#${group.i18n_title_generalSummary}`, '总汇总') }}
          </span>
          <template>
            <div class="summary-message">
              <span class="summary-message-content">
                <span class="total-num">{{ group.total.totalValue }}</span></span>
            </div>
          </template>
        </a-descriptions-item>
      </a-descriptions>
    </template>
    <!-- 图片预览弹窗 -->
    <a-modal
      v-drag    
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel">
      <img
        alt="example"
        style="width: 100%"
        :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
import { bindfunctionMiddleware } from '@/utils/util'
import { currency, formatFloat} from '@/filters'

export default {
    name: 'DetailFormLayout',
    inject: ['tplRootRef'],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        // 传入归属方busAccount
        busAccount: {
            required: true,
            type: String,
            default: null
        },
        group: {
            type: Object,
            required: true,
            default () {
                return {}
            }
        },
        pageConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        gridConfig: {
            type: Object,
            default () {
                return {}
            }
        },
        displayModel: {
            type: String,
            default: 'tab'
        }
    },
    data () {
        return {
            previewImage: '',
            previewVisible: false
        }
    },
    computed: {
        formFields () {
            let hideFields = ['hiddenField']
            let rs = this.group.formFields.filter(rs => !hideFields.includes(rs.fieldType) && !rs.hide)
            return rs
        },
        descriptionsConfig () {
            let config = {
                layout: 'horizontal',
                column: 3,
                size: 'small',
                bordered: true
            }
            if (this.group.formFields && this.group.formFields.length && this.group.formFields.length === 1) {
                config.layout = 'vertical'
                config.column = 1
                config.size = 'middle'
            }
            return config
        },
        minHeightStyle () {
            if (this.displayModel === 'tab') {
                return { minHeight: `${this.gridConfig.height}px` }
            }
            return {}
        }
    },
    methods: {
        formatFloat,
        previewPictureText (value) {
            let result = ''
            if (value) {
                result = value.split(',')
            } 
            return result
        },
        formatCurrencyValue (val, config) {
            let extend = config.extend || {}
            let symbol = extend && extend.symol || ''
            let decimals = extend && extend.decimals ? Number(extend.decimals) : 2
            return currency(val, symbol, decimals)
        },
        //调整Descriptions.Item的span的数量
        descriptionsSpan (fields){
            if(fields.fieldType=='textArea') return 3
            let otherFields=this.formFields.filter((n) =>n.fieldType!=='textArea')
            if(fields.fieldName===otherFields[otherFields.length-1].fieldName){
                //最后一个字段填满该行
                return 3
            }
            return 1      
        },
        // 适配列自定义颜色配置
        mapFieldColor (field, form) {
            let color = ''        
            if (field && field.dictCode) {            
                if (this.currentEditRow && this.currentEditRow[`${field.fieldName}_dictText_fieldColors`] && this.currentEditRow[`${field.fieldName}_dictText_fieldColors`].length) {
                    let fieldColors = this.currentEditRow[`${field.fieldName}_dictText_fieldColors`]                
                    fieldColors.forEach((item)=> {
                        if (item.fieldName === form[field.fieldName]) {
                            color= item.fieldColor
                        }
                    })
                }
            }
            return color
        },
        renderValueClick (field) {
            this.$emit('renderValue-click', field)
        },
        handleCancel () {
            this.previewVisible = false
        },
        // 图片预览
        previewPicture (data) {
            if(data){
                // this.previewImage = this.imageUrl + data
                this.previewImage = data
                this.previewVisible = true
            }
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                arr.forEach((item, index) => {
                    let ladderQuantity = item.ladderQuantity
                    let price = item.price
                    let netPrice = item.netPrice
                    let str = `${ladderQuantity} ${price} ${netPrice} `
                    let separator = index === arr.length - 1 ? '' : ','
                    arrString += str + separator
                })
            }
            return arrString
        },
        // 超链接 Slot
        getNewRouter (value, item){
            let params = {
                _pageData: this.pageConfig, // 页面所有数据
                _cacheAllData: this.tplRootRef.getAllData(),
                _form: this.group.formModel,
                _value: value,
                _formField: item
            }
            let linkConfig = item?.extend?.linkConfig || {}
            if (item && item.bindFunction &&  typeof item.bindFunction === 'function') {
                bindfunctionMiddleware(this.tplRootRef, item.bindFunction, params)
            }
            if (item?.extend?.handleBefore && typeof item.extend.handleBefore === 'function') {
                let callbackObj = item.extend.handleBefore(this.tplRootRef, params, linkConfig) || {}
                linkConfig = { ...linkConfig, ...callbackObj }
            }
            if (value && linkConfig.actionPath && linkConfig.bindKey) {
                let query = {
                    [linkConfig.primaryKey]: this.group.formModel[linkConfig.bindKey],
                    ...linkConfig.otherQuery,
                    t: new Date().getTime()
                }
                this.$router.push({ path: linkConfig.actionPath.trim(), query: { ...query } })
            }
        }
    }
}
</script>
<style lang="less" scoped>
.summary-message {
  height: 14px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}

.summary-message-content {
  flex-grow: 1;
  font-weight: bolder;

  .total-num {
    font-size: 16px;
    color: red;
  }
}
.preview{
      cursor: pointer;
  display: inline-block;
    width: 80px;
    height: 80px;
    margin-right: 10px;
    img{
      display: block;
      width: 100%;
      height: 100%;
    }
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label),
:deep(.description .ant-descriptions-bordered .ant-descriptions-item-label){
  background: #F5F6F7;
}

:deep(.ant-descriptions-bordered .ant-descriptions-small .ant-descriptions-item-label){
  width: 16.66%;
  max-width: 16.66%;
}
:deep(.description .ant-descriptions-bordered .ant-descriptions-item-content){
  width: 16.66%;
  max-width: 16.66%;
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-content){
    width: 16.66%;
    max-width: 16.66%;
    color: #798087;
}
.item-content {    
    padding: 8px 16px;
    //min-height: 44px;
    //line-height: 28px;
    min-height:30px;
    line-height:16px;
}
.item-required-icon {
    color: #f5222d;
    font-size: 14px;
}
.item-required-text {
    background-color: #fff9f7;
    border: 1px solid #fdaf96;
    border-radius: 2px;
    color: #454f59;
    margin: 8px 8px;
    line-height: 13px;
    padding: 8px 8px;
    min-height: 28px;
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label){
  width: 6.66%;
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label),
:deep( .ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-content) {
    padding-top: 0  !important;
    padding-bottom: 0 !important;
}
:deep(.ant-descriptions-bordered.ant-descriptions-small .ant-descriptions-item-label){
    padding-right: 0 !important;
}
</style>
