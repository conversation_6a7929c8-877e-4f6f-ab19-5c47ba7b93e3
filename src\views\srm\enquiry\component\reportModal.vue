<template>
  <a-modal
    v-drag
    v-if="showVisible"
    :visible="showVisible"
    :width="'90%'"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_priceComparisonReport`, '比价报表')"
    :footer="null"
    @cancel="showVisible=false">
    <div>
      <a-popover
        :title="$srmI18n(`${$getLangAccount()}#i18n__GRIld_ec8c876e`, '设置对比项')"
        trigger="click"
        v-model="visiblePopover"
        placement="left">
        <template #content>
          <div>
            <a-checkbox-group
              v-model="compareItemChecked"
              style="width: 600px">
              <a-row>
                <a-col
                  :span="8"
                  v-for="item of compareItems"
                  :key="item.prop">
                  <a-checkbox :value="item.prop">{{ item.label }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>

            <div style="margin-top: 12px">
              <a-button
                type="primary"
                @click="allCheck('compare')">{{ allCompareCheckFlag ? $srmI18n(`${$getLangAccount()}#i18n_title_allCheck`, '全部勾选') : $srmI18n(`${$getLangAccount()}#i18n_title_cancleAllCheck`, '取消全选') }}</a-button>
              <a-button
                type="primary"
                @click="updateCompareItems">{{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}</a-button>
            </div>
          </div>
        </template>
        <a-button>{{ $srmI18n(`${$getLangAccount()}#i18n__GRIld_ec8c876e`, '设置对比项') }}</a-button>
      </a-popover>
      <a-popover
        :title="$srmI18n(`${$getLangAccount()}#i18n__GRRdX_ec5c3c9d`, '设置供应商')"
        v-model="visibleSupplierPopover"
        trigger="click"
        placement="left">
        <template #content>
          <div>
            <a-checkbox-group
              v-model="supplierChecked"
              style="width: 600px">
              <a-row>
                <a-col
                  :span="8"
                  v-for="item of suppliers"
                  :key="item.toElsAccount">
                  <a-checkbox :value="item.toElsAccount">{{ item.supplierName }}</a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
            <div style="margin-top: 12px">
              <a-button
                type="primary"
                @click="allCheck('supplier')">{{ allSupplierCheckFlag ? $srmI18n(`${$getLangAccount()}#i18n_title_allCheck`, '全部勾选') : $srmI18n(`${$getLangAccount()}#i18n_title_cancleAllCheck`, '取消全选') }}</a-button>
              <a-button
                type="primary"
                @click="updateSupplierItems"
                :loading="loading">{{ $srmI18n(`${$getLangAccount()}#i18n_title_define`, '确定') }}</a-button>
            </div>
          </div>
        </template>
        <a-button>{{ $srmI18n(`${$getLangAccount()}#i18n__GRRdX_ec5c3c9d`, '设置供应商') }}</a-button>
      </a-popover>
      <a-button
        type="primary"
        @click="exportCompare">{{ $srmI18n(`${$getLangAccount()}#i18n_title_export`, '导出') }}</a-button>
    </div>
    <div>
      <a-tabs
        v-model="tabPaneKey">
        <a-tab-pane
          forceRender
          key="compareNormal"
          :disabled="normalShowList.length === 0"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_title_conventionalPriceComparison`, '常规比价')">
          <div>
              <div class="nomarlTable">
                  <vxe-table
                      size="mini"
                      ref="compareGrid"
                      :border="true"
                      stripe
                      resizable
                      auto-resize
                      show-overflow
                      highlight-hover-row
                      :align="'center'"
                      height="400"
                      :loading="loading"
                      :scroll-y="{enabled: false}"
                      :cell-style="cellStyleNomal"
                      :merge-cells="mergeCellsNomal"
                      :data="normalShowList">
                      <vxe-column
                          field="materialNumber"
                          width="280"
                          :show-header-overflow="true"
                          :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')">
                          <template #default="{ row }">
                              <report-modal-material :row="row"></report-modal-material>
                          </template>
                      </vxe-column>
                      <vxe-column
                          field="compareLabel"
                          width="120"
                          :show-header-overflow="true"
                          :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                          <template #default="{ row }">
                              <div class="compare-label-class">{{ row.compareLabel }}</div>
                          </template>
                      </vxe-column>
                      <vxe-colgroup
                          v-for="sup of showSuppliers"
                          :show-header-overflow="true"
                          :key="sup.toElsAccount"
                          :title="sup.supplierName">
                          <vxe-column
                              width="180"
                              v-for="index of sup.maxQuoteCount"
                              :key="index"
                              :show-header-overflow="true"
                              :title="`${$srmI18n(`${$getLangAccount()}#i18n_field_n_7b2c`, '第')} ${index} ${$srmI18n(`${$getLangAccount()}#i18n_field_currentRound`, '轮')}`"
                              :field="`supplier_${sup.toElsAccount}_${index}`">
                              <template #default="{ row }">
                                  <div class="compare-label-class">{{ row[`supplier_${sup.toElsAccount}_${index}`] }}</div>
                              </template>
                          </vxe-column>
                      </vxe-colgroup>
                      <template #empty>
                          <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
                      </template>
                  </vxe-table>
              </div>
            <div class="vxe-pagin-new">
              <vxe-pager
                align="right"
                perfect
                :current-page.sync="tablePageNormal.currentPage"
                :page-size.sync="tablePageNormal.pageSize"
                :layouts="['PrevPage', 'JumpNumber','NextPage', 'FullJump', 'Total']"
                @page-change="v => handlePageChange(v, 'tablePageNormal')"
                :total="tablePageNormal.total">
              </vxe-pager></div>
          </div>
        </a-tab-pane>
        <a-tab-pane
          forceRender
          key="compareLadder"
          :disabled="ladderList.length === 0"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_field_yDlu_45de8c9c`, '阶梯比价')">
          <div>
            <vxe-table
              size="mini"
              ref="compareLadderGrid"
              :border="true"
              stripe
              resizable
              auto-resize
              show-overflow
              highlight-hover-row
              :align="'center'"
              height="400"
              :loading="loading"
              :scroll-y="{enabled: false}"
              :cell-style="cellStyleLadder"
              :merge-cells="mergeCellsLadder"
              :data="ladderShowList">
              <vxe-column
                field="materialNumber"
                width="280"
                :show-header-overflow="true"
                :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')">
                <template #default="{ row }">
                  <report-modal-material :row="row"></report-modal-material>
                </template>
              </vxe-column>
              <vxe-colgroup :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                <vxe-column
                  field="compareLabel"
                  width="120"
                  :show-header-overflow="true"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row.compareLabel }}</div>
                  </template>
                </vxe-column>
                <vxe-column
                  field="ladderLabel"
                  width="100"
                  :show-header-overflow="true"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderLeve`, '阶梯级')">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row.ladderLabel }}</div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup
                v-for="sup of showSuppliers"
                :show-header-overflow="true"
                :key="sup.toElsAccount"
                :title="sup.supplierName">
                <vxe-column
                  width="180"
                  v-for="index of sup.maxQuoteCount"
                  :key="index"
                  :show-header-overflow="true"
                  :title="`${$srmI18n(`${$getLangAccount()}#i18n_field_n_7b2c`, '第')} ${index} ${$srmI18n(`${$getLangAccount()}#i18n_field_currentRound`, '轮')}`"
                  :field="`supplier_${sup.toElsAccount}_${index}`">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row[`supplier_${sup.toElsAccount}_${index}`] }}</div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <template #empty>
                <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
              </template>
            </vxe-table>
            <div class="vxe-pagin-new">
              <vxe-pager
                align="right"
                perfect
                :current-page.sync="tablePageLadder.currentPage"
                :page-size.sync="tablePageLadder.pageSize"
                :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']"
                @page-change="v => handlePageChange(v, 'tablePageLadder')"
                :total="tablePageLadder.total">
              </vxe-pager></div>
          </div>
        </a-tab-pane>
        <a-tab-pane
          forceRender
          key="compareCost"
          :disabled="costList.length === 0"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_title_costPriceComparison`, '成本比价')">
          <div>
            <vxe-table
              size="mini"
              ref="compareCostGrid"
              :border="true"
              stripe
              resizable
              auto-resize
              show-overflow
              highlight-hover-row
              :align="'center'"
              height="400"
              :loading="loading"
              :scroll-y="{enabled: false}"
              :cell-style="cellStyleCost"
              :merge-cells="mergeCellsCost"
              :data="costShowList">
              <vxe-column
                field="materialNumber"
                width="280"
                :show-header-overflow="true"
                :title="$srmI18n(`${$getLangAccount()}#i18n_title_materialInfo`, '物料信息')">
                <template #default="{ row }">
                  <report-modal-material :row="row"></report-modal-material>
                </template>
              </vxe-column>
              <vxe-colgroup :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                <vxe-column
                  field="compareLabel"
                  width="120"
                  :show-header-overflow="true"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row.compareLabel }}</div>
                  </template>
                </vxe-column>
                <vxe-column
                  field="costLabel"
                  width="100"
                  :show-header-overflow="true"
                  :title="$srmI18n(`${$getLangAccount()}#i18n_field_Lvd_17d34dd`, '成本项')">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row.costLabel }}</div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <vxe-colgroup
                v-for="sup of showSuppliers"
                :show-header-overflow="true"
                :key="sup.toElsAccount"
                :title="sup.supplierName">
                <vxe-column
                  width="180"
                  v-for="index of sup.maxQuoteCount"
                  :key="index"
                  :show-header-overflow="true"
                  :title="`${$srmI18n(`${$getLangAccount()}#i18n_field_n_7b2c`, '第')} ${index} ${$srmI18n(`${$getLangAccount()}#i18n_field_currentRound`, '轮')}`"
                  :field="`supplier_${sup.toElsAccount}_${index}`">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row[`supplier_${sup.toElsAccount}_${index}`] }}</div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <template #empty>
                <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
              </template>
            </vxe-table>
            <div class="vxe-pagin-new">
              <vxe-pager
                align="right"
                perfect
                :current-page.sync="tablePageCost.currentPage"
                :page-size.sync="tablePageCost.pageSize"
                @page-change="v => handlePageChange(v, 'tablePageCost')"
                :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']"
                :total="tablePageCost.total">
              </vxe-pager></div>
          </div>
        </a-tab-pane>
        <a-tab-pane
          forceRender
          key="compareSupplier"
          :tab="$srmI18n(`${$getLangAccount()}#i18n_title_packagePriceComparison`, '打包比价')">
          <div>
            <vxe-table
              size="mini"
              ref="compareSupplierGrid"
              :border="true"
              stripe
              resizable
              auto-resize
              show-overflow
              highlight-hover-row
              :align="'center'"
              height="400"
              :loading="loading"
              :scroll-y="{enabled: false}"
              :cell-style="cellStyleSupplier"
              :data="supplierShowList">
              <!-- :merge-cells="mergeCellsSupplier" -->
              <vxe-column
                field="compareLabel"
                width="120"
                :show-header-overflow="true"
                :title="$srmI18n(`${$getLangAccount()}#i18n_field_Ild_166e8de`, '对比项')">
                <template #default="{ row }">
                  <div class="compare-label-class">{{ row.compareLabel }}</div>
                </template>
              </vxe-column>
              <vxe-colgroup
                v-for="sup of showSuppliers"
                :show-header-overflow="true"
                :key="sup.toElsAccount"
                :title="sup.supplierName">
                <vxe-column
                  width="180"
                  v-for="index of sup.maxQuoteCount"
                  :key="index"
                  :show-header-overflow="true"
                  :title="`${$srmI18n(`${$getLangAccount()}#i18n_field_n_7b2c`, '第')} ${index} ${$srmI18n(`${$getLangAccount()}#i18n_field_currentRound`, '轮')}`"
                  :field="`supplier_${sup.toElsAccount}_${index}`">
                  <template #default="{ row }">
                    <div class="compare-label-class">{{ row[`supplier_${sup.toElsAccount}_${index}`] }}</div>
                  </template>
                </vxe-column>
              </vxe-colgroup>
              <template #empty>
                <m-empty :noMsg="$srmI18n(`${$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据')" />
              </template>
            </vxe-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>
<script>
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

import ReportModalMaterial from './reportModalMaterial'
import {removeRepet} from '@/utils/util'
import { mul, div } from '@/utils/mathFloat.js'

// 对象数组排序
const compare = function (prop) {
    return function (obj1, obj2) {
        const val1 = parseInt(obj1[prop])
        const val2 = parseInt(obj2[prop])
        if (val1 < val2) {
            return -1
        } else if (val1 > val2) {
            return 1
        } else {
            return 0
        }
    }
}

export default {
    name: 'ReportModal',
    components: { ReportModalMaterial },
    data () {
        return {
            allCompareCheckFlag: true,
            loading: false,
            tablePageNormal: {
                total: 0,
                currentPage: 1,
                pageSize: 20,
                align: 'right',
                pageSizes: [10, 20],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            tablePageLadder: {
                total: 0,
                currentPage: 1,
                pageSize: 20,
                align: 'right',
                pageSizes: [10, 20],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },
            tablePageCost: {
                total: 0,
                currentPage: 1,
                pageSize: 20,
                align: 'right',
                pageSizes: [10, 20],
                layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'FullJump', 'PageCount', 'Total'],
                perfect: true
            },

            tabPaneKey: 'compareNormal',
            PRICE: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'),
            NET_PRICE: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lfu_1925b53`, '未税价'),
            NET_PRICE_S: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            height: '90%',
            showVisible: false,
            purchaseEnquiryItemList: [], //  所有物料
            compareItems: [],
            compareItemChecked: [], // 选中供应商（toElsAccount 数组）
            suppliers: [], // 所有可选择供应商（供应商对象 数组）
            supplierChecked: [], // 选中供应商（toElsAccount 数组）
            showSuppliers: [], // 选中显示的供应商（供应商对象 数组）

            showCompareItems: [], // 常规、供应商
            normalList: [], // 常规比价行数据
            normalShowList: [], // 常规比价行数据 显示数据
            mergeCellsNomal: [], // 合并单元格配置（常规）

            showLadderCompareItems: [], // 阶梯
            ladderList: [], // 阶梯比价行数据
            ladderShowList: [], // 阶梯比价行数据 显示数据
            mergeCellsLadder: [], // 合并单元格配置（阶梯）

            showCostCompareItems: [], // 成本
            costList: [], // 成本比价行数据
            costShowList: [], // 成本比价行数据 显示数据
            mergeCellsCost: [], // 合并单元格配置（成本）

            // { label: '拆分百分比%', prop: 'quotaScale' }, 
            showSupplierCompareItems: [ // 供应商对比项默认
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_price`, '含税价'), prop: 'price' },
                { label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lfu_1925b53`, '未税价'), prop: 'netPrice' }
            ],

            supplierList: [], // 供应商比价行数据
            supplierShowList: [], // 供应商比价行数据 显示数据
            mergeCellsSupplier: [], // 合并单元格配置（供应商）
            allSupplierCheckFlag: false,
            headId: null,
            // 新的接口和参数
            searchMaterialData: [],
            searchLadderData: [],
            searchCostData: [],
            allQuoteCount: 0, //总报价次数
            allSupplierCount: 0, //供应商数量
            searchMaterialPageNum: 1,
            searchLadderPageNum: 1,
            searchCostPageNum: 1,
            visiblePopover: false,
            visibleSupplierPopover: false
        }
    },
    computed: {},
    methods: {
        //分页
        handlePageChange ({ currentPage, pageSize }, key) {
            console.log('page-change', currentPage, pageSize, key)
            this[key].currentPage = currentPage
            // this[key].pageSize = pageSize
            // if (key === 'tablePageLadder') {
            //     this.searchLadderPageNum = currentPage
            // } else if (key === 'tablePageCost') {
            //     this.searchMaterialPageNum = currentPage
            // } else {
            //     this.searchCostPageNum = currentPage
            // }
            this.getDataPageChange(key)
        },
        async getDataPageChange (key) {
            let action = 'queryCompareNormalNewList'
            let list = this.searchMaterialData
            let pageSize = this[key].pageSize
            let pageNum = this[key].currentPage
            if (key === 'tablePageLadder') {
                list = this.searchLadderData
                action = 'queryCompareLadderNewList'
            }
            if (key === 'tablePageCost') {
                list = this.searchCostData
                action = 'queryCompareCostNewList'
            }
            let params = this.getSearchParams(list, pageNum, pageSize)
            this.loading = true
            const res = await postAction(`/enquiry/purchaseEnquiryItemHis/${action}`, {
                headId: this.headId, ...params
            }).finally(() => {
                this.loading = false
            })
            if (res.success) {
                if (key == 'tablePageLadder') {
                    this.setLadderList(res.result || [])
                    this.showLadderCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
                    this.sortItems()
                    this.setLadderItems()
                } else if (key == 'tablePageCost') {
                    this.setCostList(res.result || [])
                    this.showCostCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
                    this.sortItems()
                    this.setCostItems()
                } else {
                    this.normalList = res.result || []
                    this.showCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
                    this.sortItems()
                    this.setNomalItems()
                }
            }
        },
        exportCompare () {
            this.$emit('exportCompare')
        },
        // 截取非价格对比项 应显示对比项
        setCompareItems (priceCompareItemIndex, netPriceCompareItemIndex) {
            let newCompareItems = []
            if (priceCompareItemIndex !== -1 && netPriceCompareItemIndex !== -1) newCompareItems = this.showCostCompareItems.slice(2, this.showCostCompareItems.length + 1)
            else if ((priceCompareItemIndex !== -1 && netPriceCompareItemIndex === -1) || (priceCompareItemIndex === -1 && netPriceCompareItemIndex !== -1)) newCompareItems = this.showCostCompareItems.slice(1, this.showCostCompareItems.length + 1)
            else if (priceCompareItemIndex === -1 && netPriceCompareItemIndex === -1) newCompareItems = this.showCostCompareItems.slice(0, this.showCostCompareItems.length + 1)
            return newCompareItems
        },
        setCompareItemsLadder (priceCompareItemIndex, netPriceCompareItemIndex) {
            let newCompareItems = []
            if (priceCompareItemIndex !== -1 && netPriceCompareItemIndex !== -1) newCompareItems = this.showLadderCompareItems.slice(2, this.showLadderCompareItems.length + 1)
            else if ((priceCompareItemIndex !== -1 && netPriceCompareItemIndex === -1) || (priceCompareItemIndex === -1 && netPriceCompareItemIndex !== -1)) newCompareItems = this.showLadderCompareItems.slice(1, this.showLadderCompareItems.length + 1)
            else if (priceCompareItemIndex === -1 && netPriceCompareItemIndex === -1) newCompareItems = this.showLadderCompareItems.slice(0, this.showLadderCompareItems.length + 1)
            return newCompareItems
        },
        // 成本比价
        setCostItems () {
            if (this.costList.length === 0) return
            // 物料
            // const materials = this.purchaseEnquiryItemList.filter(i => i.quotePriceWay === '2')
            const materials = this.setMaterials(this.costList)
            // 应显示供应商
            this.showSuppliers = this.suppliers
                .filter(i => this.supplierChecked.includes(i.toElsAccount))
                .map(sup => {
                    const currentQuoteCount = this.costList
                        .filter(i => i.toElsAccount === sup.toElsAccount).map(i => i.quoteCount)
                    sup.maxQuoteCount = currentQuoteCount.length > 0 ? Math.max.apply(null, currentQuoteCount) : 1
                    return sup
                })

            // 总数据
            const data = []

            const materialCompareItems = {} // 各个物料应遍历的对比项对象 - 用来计算含税、未税阶梯合并下标

            let allIndex = 0 // 总数据下标计算
            let comparePriceMerges = [] // 第2列：含税价、未税价 合并行
            let compareItemMerges = [] // 第2、3列：非含税价、未税价 合并列
            // 对比项含税价项 下标
            const priceCompareItemIndex = this.showCostCompareItems.findIndex(i => i.label === this.PRICE)
            const netPriceCompareItemIndex = this.showCostCompareItems.findIndex(i => i.label === this.NET_PRICE || i.label === this.NET_PRICE_S)

            // 截取非价格对比项 应显示对比项
            let newCompareItems = this.setCompareItems(priceCompareItemIndex, netPriceCompareItemIndex)
            // 所有物料遍历
            materials.forEach((material) => {
                const { itemNumber } = material
                // 根据当前物料阶梯JSON 重置新对比项
                const currentMaterial = this.costList.find(i => {
                    return i.itemNumber === itemNumber && i.costFormJson
                })
                const newItems = [] // 应显示对比项
                this.showCostCompareItems.forEach(ci => {
                    if (ci.label === this.PRICE || ci.label === this.NET_PRICE || ci.label === this.NET_PRICE_S) {
                        if (currentMaterial && currentMaterial.costFormJson) {
                            // 获取 comparePriceMerges 第2列：含税价、未税价 合并行
                            const costLength = currentMaterial.costFormJson.length
                            if (this.PRICE === ci.label) { // 含税价 对比项合并
                                const pIndex = allIndex
                                const mergeItem = { row: pIndex, col: 1, rowspan: costLength, colspan: 1 }
                                comparePriceMerges.push(mergeItem)
                            } else if (this.NET_PRICE === ci.label || ci.label === this.NET_PRICE_S) { // 未税价 对比项合并
                                // 根据对比项是否包含 含税价项，控制未税价合并行下标
                                const npIndex = priceCompareItemIndex === -1 ? allIndex : allIndex + currentMaterial.costFormJson.length
                                const mergeItem = { row: npIndex, col: 1, rowspan: costLength, colspan: 1 }
                                comparePriceMerges.push(mergeItem)
                            }
                            currentMaterial.costFormJson.forEach(item => {
                                newItems.push({ label: ci.label, prop: item.groupCode, costLabel: item.groupName, isCost: true })
                            })
                        }
                    }
                })
                newItems.push(...newCompareItems)

                materialCompareItems[`${itemNumber}`] = newItems // 以行号为key，对比项数组为value
                // 应显示对比项遍历
                newItems.forEach((item, index) => {
                    allIndex += 1 // 每物料 获取一次
                    const newMaterial = JSON.parse(JSON.stringify(material))
                    newMaterial.compareLabel = item.label
                    newMaterial.compareCostItem = item.prop
                    newMaterial.costLabel = item.costLabel // 对比项显示
                    newMaterial.id = itemNumber + index
                    // 应显示供应商遍历，获取对应行对比项数据
                    this.showSuppliers.forEach(s => {
                        const { toElsAccount } = s
                        // 当前供应商行信息
                        const currentSupItems = this.costList.filter(i => {
                            return i.itemNumber === itemNumber && i.toElsAccount === toElsAccount
                        })
                        // 根据当前供应商行信息遍历获取对应对比项数据
                        currentSupItems.forEach(i => {
                            const key = `supplier_${toElsAccount}_${i.quoteCount}`
                            // 设置对比项应显示数据
                            if (item.isCost && item.label === this.PRICE) {
                                newMaterial[key] = i[item.prop] && i[item.prop].price || 0
                            } else if (item.isCost && (item.label === this.NET_PRICE || item.label === this.NET_PRICE_S)) {
                                newMaterial[key] = i[item.prop] && i[item.prop].netPrice || 0
                            } else {
                                newMaterial[key] = i[item.prop] || ''
                                compareItemMerges.push({ row: allIndex - 1, col: 1, rowspan: 1, colspan: 2 }) // 第2、3列：非含税价、未税价 合并列
                            }
                        })
                    })
                    data.push(newMaterial)
                })
            })

            // // 常规列表显示数据
            this.costShowList = data

            // // 合并下标、合并长度
            const indexs = [0]
            const lengths = []
            materials.forEach((m, index) => {
                const { itemNumber } = m
                const mLength = materialCompareItems[`${itemNumber}`].length
                lengths.push(mLength)
                indexs.push(indexs[index] + mLength)
            })
            // 根据合并行下标数组、合并行数 获得合并配置
            this.mergeCellsCost = lengths.map((i, index) => {
                return { row: indexs[index], col: 0, rowspan: i, colspan: 1 }
            })
            // 插入第2列：含税价、未税价 合并行
            this.mergeCellsCost.push(...comparePriceMerges)
            // 插入第2、3列：非含税价、未税价 合并列
            this.mergeCellsCost.push(...compareItemMerges)
        },
        // 成本比价单元格样式
        cellStyleCost ({ row, rowIndex, column }) {
            if (column.property.includes('supplier_')) {
                let {itemNumber } = row
                const info = column.property.split('_')
                const toElsAccount = info[1]
                const quoteCount = info[2] * 1
                // 获取当前行信息
                const item = this.costList.find(i => {
                    return i.itemNumber === itemNumber && i.toElsAccount === toElsAccount && i.quoteCount === quoteCount
                })
                const { compareLabel, compareCostItem } = row
                if (item && item.costFormJson) {
                    // 获取当前阶梯级
                    const costItem = item.costFormJson.find(i => i.groupCode === compareCostItem)
                    if (!costItem) return {}
                    if (compareLabel === this.PRICE && costItem.minPrice) {
                        return {
                            color: 'green'
                        }
                    } else if (compareLabel === this.PRICE && costItem.maxPrice) {
                        return {
                            color: 'red'
                        }
                    } else if ((compareLabel === this.NET_PRICE || compareLabel === this.NET_PRICE_S) && costItem.minNetPrice) {
                        return {
                            color: 'green'
                        }
                    } else if ((compareLabel === this.NET_PRICE || compareLabel === this.NET_PRICE_S) && costItem.maxNetPrice) {
                        return {
                            color: 'red'
                        }
                    }
                }
            }
        },
        // 阶梯比价
        setLadderItems () {
            if (this.ladderList.length === 0) return
            // 物料
            // const materials = this.purchaseEnquiryItemList.filter(i => i.quotePriceWay === '1')
            const materials = this.setMaterials(this.ladderList)
            // 应显示供应商
            this.showSuppliers = this.suppliers
                .filter(i => this.supplierChecked.includes(i.toElsAccount))
                .map(sup => {
                    const currentQuoteCount = this.ladderList
                        .filter(i => i.toElsAccount === sup.toElsAccount).map(i => i.quoteCount)
                    sup.maxQuoteCount = currentQuoteCount.length > 0 ? Math.max.apply(null, currentQuoteCount) : 1
                    return sup
                })

            // 总数据
            const data = []

            const materialCompareItems = {} // 各个物料应遍历的对比项对象 - 用来计算含税、未税阶梯合并下标

            let allIndex = 0 // 总数据下标计算
            let comparePriceMerges = [] // 第2列：含税价、未税价 合并行
            let compareItemMerges = [] // 第2、3列：非含税价、未税价 合并列
            // 对比项含税价项 下标
            const priceCompareItemIndex = this.showLadderCompareItems.findIndex(i => i.label === this.PRICE)
            const netPriceCompareItemIndex = this.showLadderCompareItems.findIndex(i => i.label === this.NET_PRICE || i.label === this.NET_PRICE_S)

            // 截取非价格对比项 应显示对比项
            let newCompareItems = this.setCompareItemsLadder(priceCompareItemIndex, netPriceCompareItemIndex)

            // 所有物料遍历
            materials.forEach((material) => {
                const { itemNumber } = material

                // 根据当前物料阶梯JSON 重置新对比项
                const currentMaterial = this.ladderList
                    .find(i => {
                        return i.itemNumber === itemNumber
                    })
                const newItems = [] // 应显示对比项
                this.showLadderCompareItems.forEach(ci => {
                    if (ci.label === this.PRICE || ci.label === this.NET_PRICE || ci.label === this.NET_PRICE_S) {

                        if (currentMaterial && currentMaterial.ladderPriceJson) {
                            // 获取 comparePriceMerges 第2列：含税价、未税价 合并行
                            const ladderLength = currentMaterial.ladderPriceJson.length
                            if (this.PRICE === ci.label) { // 含税价 对比项合并
                                const pIndex = allIndex
                                const mergeItem = { row: pIndex, col: 1, rowspan: ladderLength, colspan: 1 }
                                comparePriceMerges.push(mergeItem)
                            } else if (this.NET_PRICE === ci.label || ci.label === this.NET_PRICE_S) { // 未税价 对比项合并
                                const npIndex = priceCompareItemIndex === -1 ? allIndex : allIndex + currentMaterial.ladderPriceJson.length
                                const mergeItem = { row: npIndex, col: 1, rowspan: ladderLength, colspan: 1 }
                                comparePriceMerges.push(mergeItem)
                            }

                            currentMaterial.ladderPriceJson.forEach(item => {
                                newItems.push({ label: ci.label, prop: item.ladder, ladderLabel: item.ladder, isLadder: true })
                            })
                        }
                    }
                })
                newItems.push(...newCompareItems)

                materialCompareItems[`${itemNumber}`] = newItems // 以物料编码为key，对比项数组为value

                // 应显示对比项遍历
                newItems.forEach((item, index) => {
                    allIndex += 1 // 每物料 获取一次
                    const newMaterial = JSON.parse(JSON.stringify(material))
                    newMaterial.compareLabel = item.label
                    newMaterial.compareLadderItem = item.prop
                    newMaterial.ladderLabel = item.ladderLabel // 对比项显示
                    newMaterial.id = itemNumber + index
                    // 应显示供应商遍历，获取对应行对比项数据
                    this.showSuppliers.forEach(s => {
                        const { toElsAccount } = s
                        // 当前供应商行信息
                        const currentSupItems = this.ladderList
                            .filter(i => {
                                return i.itemNumber === itemNumber && i.toElsAccount === toElsAccount
                            })
                        // 根据当前供应商行信息遍历获取对应对比项数据
                        currentSupItems.forEach(i => {
                            const key = `supplier_${toElsAccount}_${i.quoteCount}`
                            // 设置对比项应显示数据
                            if (item.isLadder && item.label === this.PRICE) {
                                newMaterial[key] = i[item.prop].price
                            } else if (item.isLadder && (item.label === this.NET_PRICE || item.label === this.NET_PRICE_S)) {
                                newMaterial[key] = i[item.prop].netPrice
                            } else {
                                newMaterial[key] = i[item.prop]
                                compareItemMerges.push({ row: allIndex - 1, col: 1, rowspan: 1, colspan: 2 }) // 第2、3列：非含税价、未税价 合并列
                            }
                        })
                    })
                    data.push(newMaterial)
                })
            })

            // 常规列表显示数据
            this.ladderShowList = data

            // 合并下标、合并长度
            const indexs = [0]
            const lengths = []
            materials.forEach((m, index) => {
                const { itemNumber } = m
                const mLength = materialCompareItems[`${itemNumber}`].length
                lengths.push(mLength)
                indexs.push(indexs[index] + mLength)
            })
            // 根据合并行下标数组、合并行数 获得合并配置
            this.mergeCellsLadder = lengths.map((i, index) => {
                return { row: indexs[index], col: 0, rowspan: i, colspan: 1 }
            })
            // 插入第2列：含税价、未税价 合并行
            this.mergeCellsLadder.push(...comparePriceMerges)
            // 插入第2、3列：非含税价、未税价 合并列
            this.mergeCellsLadder.push(...compareItemMerges)
        },
        // 阶梯比价单元格样式
        cellStyleLadder ({ row, rowIndex, column }) {
            if (column.property.includes('supplier_')) {
                let {itemNumber } = row
                const info = column.property.split('_')
                const toElsAccount = info[1]
                const quoteCount = info[2] * 1
                // 获取当前行信息
                const item = this.ladderList
                    .find(i => {
                        return (i.itemNumber === itemNumber) && i.toElsAccount === toElsAccount && i.quoteCount === quoteCount
                    })
                const { compareLabel, compareLadderItem } = row
                if (item && item.ladderPriceJson) {
                    // 获取当前阶梯级
                    const ladderItem = item.ladderPriceJson.find(i => i.ladder === compareLadderItem)
                    if (compareLabel === this.PRICE && ladderItem.minPrice) {
                        return {
                            color: 'green'
                        }
                    } else if (compareLabel === this.PRICE && ladderItem.maxPrice) {
                        return {
                            color: 'red'
                        }
                    } else if ((compareLabel === this.NET_PRICE || compareLabel === this.NET_PRICE_S) && ladderItem.minNetPrice) {
                        return {
                            color: 'green'
                        }
                    } else if ((compareLabel === this.NET_PRICE || compareLabel === this.NET_PRICE_S) && ladderItem.maxNetPrice) {
                        return {
                            color: 'red'
                        }
                    }
                }
            }
        },
        // 供应商比价
        setSupplierItems () {
            // 应显示供应商
            this.showSuppliers = this.suppliers
                .filter(i => this.supplierChecked.includes(i.toElsAccount))
                .map(sup => {
                    const currentQuoteCount = this.supplierList
                        .filter(i => i.toElsAccount === sup.toElsAccount).map(i => i.quoteCount)
                    sup.maxQuoteCount = currentQuoteCount.length > 0 ? Math.max.apply(null, currentQuoteCount) : 1
                    return sup
                })
            // 总数据
            const data = []
            // 应显示对比项遍历
            this.showSupplierCompareItems.forEach((item, index) => {
                const newMaterial = JSON.parse(JSON.stringify(item))
                newMaterial.compareLabel = item.label
                newMaterial.id = item.prop + index
                // 应显示供应商遍历，获取对应行对比项数据
                this.showSuppliers.forEach(s => {
                    const { toElsAccount } = s
                    // 当前供应商行信息
                    const currentSupItems = this.supplierList.filter(i => i.toElsAccount === toElsAccount)
                    // 根据当前供应商行信息遍历获取对应对比项数据
                    currentSupItems.forEach(i => {
                        const key = `supplier_${toElsAccount}_${i.quoteCount}`
                        // 设置对比项应显示数据
                        newMaterial[key] = i[item.prop]
                    })
                })
                data.push(newMaterial)
            })

            // 常规列表显示数据
            this.supplierShowList = data
        },
        // 供应商比价单元格样式
        cellStyleSupplier ({ row, rowIndex, column }) {
            if (column.property.includes('supplier_')) {
                const info = column.property.split('_')
                const toElsAccount = info[1]
                const quoteCount = info[2] * 1
                const item = this.supplierList.find(i => i.toElsAccount === toElsAccount && i.quoteCount === quoteCount)
                const { maxPrice, maxNetPrice, minPrice, minNetPrice } = item || {}
                if (row.compareLabel === this.PRICE && minPrice) {
                    return {
                        color: 'green'
                    }
                } else if (row.compareLabel === this.PRICE && maxPrice) {
                    return {
                        color: 'red'
                    }
                } else if ((row.compareLabel === this.NET_PRICE || row.compareLabel === this.NET_PRICE_S) && minNetPrice) {
                    return {
                        color: 'green'
                    }
                } else if ((row.compareLabel === this.NET_PRICE || row.compareLabel === this.NET_PRICE_S) && maxNetPrice) {
                    return {
                        color: 'red'
                    }
                }
            }
        },
        setMaterials (list) {
            const materialList = list.map(i => {
                const {
                    materialDesc,
                    materialName,
                    materialNumber,
                    itemNumber,
                    materialSpec,
                    requireQuantity,
                    purchaseUnit_dictText,
                    futurePrice,
                    factory_dictText,
                    storageLocation_dictText,
                    quantityUnit_dictText,
                    conversionRate
                } = i
                return {
                    materialDesc,
                    materialName,
                    itemNumber,
                    materialNumber,
                    materialSpec,
                    requireQuantity,
                    purchaseUnit_dictText,
                    futurePrice,
                    factory_dictText,
                    storageLocation_dictText,
                    quantityUnit_dictText,
                    conversionRate
                }
            })
            const materials = removeRepet(materialList, 'itemNumber')
            return materials
        },
        // 常规比价
        setNomalItems () {
            if (this.normalList.length === 0) return
            // 物料
            // const materials = this.purchaseEnquiryItemList.filter(i => i.quotePriceWay === '0')
            const materials = this.setMaterials(this.normalList)
            // 应显示供应商
            this.showSuppliers = this.suppliers
                .filter(i => this.supplierChecked.includes(i.toElsAccount))
                .map(sup => {
                    const currentQuoteCount = this.normalList
                        .filter(i => i.toElsAccount === sup.toElsAccount).map(i => i.quoteCount)
                    sup.maxQuoteCount = currentQuoteCount.length > 0 ? Math.max.apply(null, currentQuoteCount) : 1
                    return sup
                })
            // 总数据
            const data = []
            const showCompareItems = this.showCompareItems
            // 所有物料遍历
            materials.forEach((material) => {
                const { itemNumber } = material
                // 应显示对比项遍历
                showCompareItems.forEach((item, index) => {
                    const newMaterial = JSON.parse(JSON.stringify(material))
                    newMaterial.compareLabel = item.label
                    newMaterial.id = itemNumber + index
                    // 应显示供应商遍历，获取对应行对比项数据
                    this.showSuppliers.forEach(s => {
                        const { toElsAccount } = s
                        // 当前供应商行信息
                        const currentSupItems = this.normalList
                            .filter(i => {
                                return i.itemNumber === itemNumber && i.toElsAccount === toElsAccount
                            })
                        // 根据当前供应商行信息遍历获取对应对比项数据
                        currentSupItems.forEach(i => {
                            const key = `supplier_${toElsAccount}_${i.quoteCount}`
                            // 设置对比项应显示数据
                            newMaterial[key] = i[item.prop]
                        })
                    })
                    data.push(newMaterial)
                })
            })

            // 常规列表显示数据
            this.normalShowList = data

            // 第一列（物料信息列），每compareItem 长度 行合并
            // 合并行数
            const rowspan = showCompareItems.length
            // 所有数据下标数组
            const indexs = data.map((i, index) => { return index })
            // 应合并行下标数组
            const rows = []
            indexs.forEach(i => {
                // 根据下标与合并行数获取应合并下标数组
                if (parseInt(i % rowspan) === 0) rows.push(i)
            })
            // 根据合并行下标数组、合并行数 获得合并配置
            this.mergeCellsNomal = rows.map(i => {
                return { row: i, col: 0, rowspan, colspan: 1 }
            })
        },
        // 常规比价单元格样式
        cellStyleNomal ({ row, rowIndex, column }) {
            if (column.property.includes('supplier_')) {
                let { itemNumber } = row
                const info = column.property.split('_')
                const toElsAccount = info[1]
                const quoteCount = info[2] * 1
                const item = this.normalList
                    .find(i => {
                        return i.itemNumber === itemNumber && i.toElsAccount === toElsAccount && i.quoteCount === quoteCount
                    })
                const { maxPrice, maxNetPrice, minPrice, minNetPrice } = item || {}
                if (row.compareLabel === this.PRICE && minPrice) {
                    return {
                        color: 'green'
                    }
                } else if (row.compareLabel === this.PRICE && maxPrice) {
                    return {
                        color: 'red'
                    }
                } else if ((row.compareLabel === this.NET_PRICE || row.compareLabel === this.NET_PRICE_S) && minNetPrice) {
                    return {
                        color: 'green'
                    }
                } else if ((row.compareLabel === this.NET_PRICE || row.compareLabel === this.NET_PRICE_S) && maxNetPrice) {
                    return {
                        color: 'red'
                    }
                }
            }
            if(column.property.includes('materialNumber')){
                return {
                    borderRight:'2px solid black'
                }
            }
        },
        // 重置阶梯比价数据
        setLadderList (ladderList = []) {
            const result = ladderList.map(item => {
                let newItem = item
                if (item.ladderPriceJson && typeof item.ladderPriceJson === 'object')
                    item.ladderPriceJson.forEach(ladder => {
                        newItem = {
                            ...newItem,
                            ...ladder
                        }
                    })
                return newItem
            })
            this.ladderList = result
        },
        // 重置成本比价数据
        setCostList (costList = []) {
            const result = costList.map(item => {
                let newItem = item
                if (item.costFormJson && typeof item.costFormJson === 'object')
                    item.costFormJson.forEach(cost => {
                        newItem = {
                            ...newItem,
                            ...cost
                        }
                    })
                if (item.costFormJson && typeof item.costFormJson === 'string'){
                    item.costFormJson = ''
                }
                return newItem
            })
            this.costList = result
        },
        getSearchParams (list, pageNum = 1, pageSize) {
            let params = {}
            let index = mul(Math.ceil(pageSize), (pageNum - 1))
            let arr = list.slice(index, index + Math.ceil(pageSize))
            if (arr.length > 0) {
                let materialNumberList = [], materialNameList = [], materialDescList = []
                arr.forEach(item => {
                    if(item.materialNumber) {
                        materialNumberList.push(item.materialNumber)
                    } else if(item.materialName) {
                        materialNameList.push(item.materialName)
                    } else {
                        materialDescList.push(item.materialDesc)
                    }
                })
                if (materialNumberList.length > 0) params['materialNumber'] = materialNumberList.join(',')
                if (materialNameList.length > 0) params['materialName'] = materialNameList.join(',')
                if (materialDescList.length > 0) params['materialDesc'] = materialDescList.join(',')
            }
            return params
        },
        async getData () {
            this.loading = true
            // 获取比价项数据字典
            await this.getStageTypeData()
            // 默认全部选中
            this.compareItemChecked = this.compareItems.filter(i => ['price', 'netPrice', 'taxRate', 'evaluationStatus_dictText'].includes(i.prop)).map(i => i.prop)

            // 供应商列表
            const suppliers = await getAction('/enquiry/purchaseEnquiryHead/querySupplier', {
                headId: this.headId
            })
            console.log(suppliers)
            if (suppliers && suppliers.success) {
                this.suppliers = suppliers.result.map(item => {
                    item.maxQuoteCount = 0
                    return item
                })
                this.supplierChecked = this.suppliers.map(item => item.toElsAccount)
            }

            let {itemList, quoteCount, supplierCount} = await getAction('/enquiry/purchaseEnquiryItemHis/queryCompareNormalPage', {
                headId: this.headId
            }).then(res => {
                if (res.success) return res.result
                else {
                    this.loading = false
                    return {}
                }
            })
            if(!itemList) return this.tabPaneKey = 'compareSupplier'
            this.searchMaterialData = itemList.filter(i => i.quotePriceWay == '0') // 常规报价
            this.tablePageNormal.total = this.searchMaterialData.length
            this.searchLadderData = itemList.filter(i => i.quotePriceWay == '1') // 阶梯报价
            this.tablePageLadder.total = this.searchLadderData.length
            this.searchCostData = itemList.filter(i => i.quotePriceWay == '2') // 成本报价
            this.tablePageCost.total = this.searchCostData.length
            this.allQuoteCount = quoteCount // 报价轮次
            this.allSupplierCount = supplierCount // 供应商数量
            // this.tablePageNormal.pageSize = 20
            // this.tablePageLadder.pageSize = div(200, mul(this.allQuoteCount, this.allSupplierCount))
            // this.tablePageCost.pageSize = div(200, mul(this.allQuoteCount, this.allSupplierCount))
            let normalParams = this.getSearchParams(this.searchMaterialData, this.tablePageNormal.currentPage, this.tablePageNormal.pageSize)
            let ladderParams = this.getSearchParams(this.searchLadderData, this.tablePageLadder.currentPage, this.tablePageLadder.pageSize)
            let costParams = this.getSearchParams(this.searchCostData, this.tablePageCost.currentPage, this.tablePageCost.pageSize)
            const apiQueryItems = postAction('/enquiry/purchaseEnquiryItemHis/queryCompareNormalNewList', {
                headId: this.headId, ...normalParams
            })
            const apiQueryItemsLadder = postAction('/enquiry/purchaseEnquiryItemHis/queryCompareLadderNewList', {
                headId: this.headId, ...ladderParams
            })
            const apiQueryItemsCost = postAction('/enquiry/purchaseEnquiryItemHis/queryCompareCostNewList', {
                headId: this.headId, ...costParams
            })
            const apiQueryItemsPack = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareSupplierList', {
                headId: this.headId
            })

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryItems, apiQueryItemsLadder, apiQueryItemsCost, apiQueryItemsPack
            ]
            Promise.all(handlePromise(promiseList)).then(res => {
                const [items, itemsLadder, itemsCost, itemsPack] = res || [] // , compare, ladder, cost

                if (items && items.status === 'success') {
                    this.normalList = items.res.result || []

                    this.showCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.normalList = this.normalList.sort(compare('itemNumber'))

                    this.setNomalItems()
                }
                if (itemsLadder && itemsLadder.status === 'success') {
                    this.setLadderList(itemsLadder.res.result || [])
                    this.showLadderCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.ladderList = this.ladderList.sort(compare('itemNumber'))
                    this.setLadderItems()
                }
                if (itemsCost && itemsCost.status === 'success') {
                    this.setCostList(itemsCost.res.result || [])

                    this.showCostCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.costList = this.costList.sort(compare('itemNumber'))
                    this.setCostItems()
                }
                if (itemsPack && itemsPack.status === 'success') {
                    this.supplierList = itemsPack.res.result || []

                    this.supplierList = this.supplierList.sort(compare('itemNumber'))
                    this.setSupplierItems()
                }
                // 判断数据量
                const normalData = this.normalList.filter(i => i.quotePriceWay === '0')
                const ladderData = this.ladderList.filter(i => i.quotePriceWay === '1')
                const costData = this.costList.filter(i => i.quotePriceWay === '2')
                this.tabPaneKey = 'compareSupplier'
                if (costData.length > 0 && this.costShowList.length > 0) {
                    this.tabPaneKey = 'compareCost'
                }
                if (ladderData.length > 0 && this.ladderShowList.length > 0) {
                    this.tabPaneKey = 'compareLadder'
                }
                if (normalData.length > 0 && this.normalShowList.length > 0) {
                    this.tabPaneKey = 'compareNormal'
                }
                this.loading = false
            }).catch(() => {
                this.loading = false
            })
        },
        allCheck (key) {
            if (key == 'compare') {
                this.allCompareCheckFlag = !this.allCompareCheckFlag
                this.compareItemChecked = this.allCompareCheckFlag ? [] : this.compareItems.map(item => item.prop)
            } else {
                this.allSupplierCheckFlag = !this.allSupplierCheckFlag
                this.supplierChecked = this.allSupplierCheckFlag ? [] : this.suppliers.map(item => item.toElsAccount)
            }
        },
        updateCompareItems () {
            // 需清空原合并项
            this.mergeCellsNomal = []
            this.mergeCellsLadder = []
            this.mergeCellsCost = []
            this.showCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
            this.showLadderCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
            this.showCostCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))
            // 重设各比价列表数据
            this.setNomalItems()
            this.setLadderItems()
            this.setCostItems()
            this.visiblePopover = false
        },
        updateSupplierItems () {
            if(this.loading) return
            let normalParams = this.getSearchParams(this.searchMaterialData, this.tablePageNormal.currentPage, this.tablePageNormal.pageSize)
            let ladderParams = this.getSearchParams(this.searchLadderData, this.tablePageLadder.currentPage, this.tablePageLadder.pageSize)
            let costParams = this.getSearchParams(this.searchCostData, this.tablePageCost.currentPage, this.tablePageCost.pageSize)
            const apiQueryItems = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareNormalNewList', {
                headId: this.headId,  ...normalParams
            })
            const apiQueryItemsLadder = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareLadderNewList', {
                headId: this.headId, ...ladderParams
            })
            const apiQueryItemsCost = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareCostNewList', {
                headId: this.headId, ...costParams
            })
            const apiQueryItemsPack = getAction('/enquiry/purchaseEnquiryItemHis/queryCompareSupplierList', {
                headId: this.headId
            })

            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({ status: res.success ?  'success' : 'error', res }), err => ({ status: 'error', err })))
            const promiseList = [
                apiQueryItems, apiQueryItemsLadder, apiQueryItemsCost, apiQueryItemsPack
            ]
            this.loading = true
            Promise.all(handlePromise(promiseList)).then(res => {
                const [items, itemsLadder, itemsCost, itemsPack] = res || []
                if (items && items.status === 'success') {
                    this.normalList = items.res.result || []
                    this.tablePageNormal.total = items.res.result.length

                    this.showCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.normalList = this.normalList.sort(compare('itemNumber'))
                    this.setNomalItems()
                }
                if (itemsLadder && itemsLadder.status === 'success') {
                    this.setLadderList(itemsLadder.res.result || [])
                    this.tablePageLadder.total = itemsLadder.res.result.length
                    this.showLadderCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.ladderList = this.ladderList.sort(compare('itemNumber'))
                    this.setLadderItems()
                }
                if (itemsCost && itemsCost.status === 'success') {
                    this.setCostList(itemsCost.res.result || [])
                    this.tablePageCost.total = itemsCost.res.result.length

                    this.showCostCompareItems = this.compareItems.filter(i => this.compareItemChecked.includes(i.prop))

                    this.costList = this.costList.sort(compare('itemNumber'))
                    this.setCostItems()
                }
                if (itemsPack && itemsPack.status === 'success') {
                    this.supplierList = itemsPack.res.result.records || []

                    this.supplierList = this.supplierList.sort(compare('itemNumber'))
                    this.setSupplierItems()
                }
                this.visibleSupplierPopover = false
            }).finally(() => {
                this.loading = false
            })
        },
        sortItems () {
            this.normalList = this.normalList.sort(compare('itemNumber'))
            this.ladderList = this.ladderList.sort(compare('itemNumber'))
            this.costList = this.costList.sort(compare('itemNumber'))
            this.supplierList = this.supplierList.sort(compare('itemNumber'))
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEnquiryCompare'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.compareItems = res.result.map(i => {
                    return { label: i.text, prop: i.value}
                }).filter(item => {
                    // 156576 【微服务】询价管理-新建询价单据，发布进入询价大厅，比价报表中去掉多余字段
                    return !['evaluationStatus_dictText', 'netAmount', 'taxAmount', 'priceUnit', 'currency_dictText', 'quoteEndTime', 'futurePrice'].includes(item.prop)
                })
                this.showCompareItems = this.compareItems
                this.showLadderCompareItems = this.compareItems
                this.showCostCompareItems = this.compareItems
            }
        },
        open (headId, purchaseEnquiryItemList){
            this.purchaseEnquiryItemList = purchaseEnquiryItemList

            this.compareItems = []
            this.showCompareItems = []
            this.showLadderCompareItems = []
            this.showCostCompareItems = []
            this.compareItemChecked = []
            this.suppliers = []
            this.supplierChecked = []
            this.showSuppliers = []
            this.normalList = []
            this.normalShowList = []
            this.mergeCellsNomal = []
            this.ladderList = []
            this.ladderShowList = []
            this.mergeCellsladder = []
            this.costList = []
            this.costShowList = []
            this.mergeCellsCost = []
            this.supplierList = []
            this.supplierShowList = []
            this.mergeCellsSupplier = []

            this.showVisible = true
            this.headId = headId
            this.getData()
        }
    }
}
</script>

<style lang="scss" scoped>
:deep(.vxe-cell){
    overflow: initial !important;
    text-overflow: ellipsis;
    // overflow: hidden;
    white-space: nowrap;
}
:deep(.header-class-name){
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.ant-btn {
margin-right: 8px;
}
.compare-label-class {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nomarlTable{
    :deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis){
        //border-bottom: 2px solid #007ff5;
        border-top: 2px solid black;
    }
}
</style>
