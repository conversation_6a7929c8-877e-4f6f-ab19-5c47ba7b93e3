<template>
  <div v-if="ifshow">
    <content-header
      :btns="btns"
    />
    <a-spin :spinning="confirmLoading">
      
      <SupplierMsg
        ref="response"
        :formData="formData"></SupplierMsg>
      <EvalutionFile
        ref="file"
        :formData="formData"></EvalutionFile>
    </a-spin>
  </div>
</template>
<script>
import EvalutionFile from './modules/EvalutionFile'
import SupplierMsg from './modules/SupplierMsg'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import ContentHeader from '../components/content-header'
  
export default {
    mixins: [baseMixins],
    components: {
        EvalutionFile,
        SupplierMsg,
        ContentHeader
    },
    props: {
    },
    inject: ['tenderCurrentRow', 'subpackageId', 'currentSubPackage'],
    computed: {
        subPackageRow () {
            return this.currentSubPackage()
        },
        currentRow () {
            return this.tenderCurrentRow()
        },  
        btns () {
            let btn = []
            if(this.formData.evaStatus != '1' && this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'){
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                ]
            }
            
            return btn
        }
        
          
    },
    
    data () {
        return {
            confirmLoading: false,
            ifshow: false,
            formData: [],
            purchaseTenderProjectAttachmentInfoList: [],
            pageData: {
                optColumnList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    methods: {
        save () {
            this.confirmLoading = true
            let params = this.formData
            let url = params.id ? '/tender/evaluation/purchaseTenderProjectBidEvaHead/editOfflineBidEvaluationResult' : '/tender/evaluation/purchaseTenderProjectBidEvaHead/saveOfflineBidEvaluationResult'
            let {checkType} = this.currentNode().extend
            params = {
                ...params,
                checkType
            }
            postAction(url, params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        publish (){
            this.confirmLoading = true
            let url = '/tender/evaluation/purchaseTenderProjectBidEvaHead/publishOfflineBidEvaluationResult'
            let params = this.formData
            let {checkType} = this.currentNode().extend
            params = {
                ...params,
                checkType
            }
            postAction(url, params).then(res=>{
                if(res.success){
                    this.$message.success(res.message)
                    this.getData()
                }else{
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async getData () {
            console.log(this.subPackageRow)
            let params = {
                // subpackageId: this.subPackageRow.subpackageId,
                // 先写死分包id，拿默认数据
                subpackageId: this.subPackageRow.id
            }
            await getAction('/tender/evaluation/purchaseTenderProjectBidEvaHead/queryOfflineBidEvaluationBySubpackageId', params, {headers: {xNodeId: this.getNodeParams().nodeId}}).then(res=>{
                if(res.success) {
                    this.formData = res.result
                    if(this.formData.bidEvaSupplierRecordList.length != 0){
                        this.formData.bidEvaSupplierRecordList.forEach(item2=>{
                            item2.forbiddenCandidate = item2.invalid == '1' ? true : false
                        })
                    }
                    // this.formData.subpackageId = this.subPackageRow.id
                    // this.formData.subpackageName = this.subPackageRow.subpackageName
                    // this.formData.tenderProjectId = this.subPackageRow.headId
                    // this.formData.tenderProjectName = this.subPackageRow.tenderProjectName
                    // this.$refs.response.init(res.result)
                    // this.$refs.file.init(res.result)
                    this.ifshow = true
                    console.log(this.formData)
                    console.log(this.subPackageRow)
                }else{
                    this.$message.error(res.message)
                }
            })
        }
    },
    async created () {
        await this.getData()
        // if (['1', '2'].includes(this.fromSourceData.status) || this.pageStatus == 'detail') this.externalToolBar = []
    }
}
</script>
  <style lang="less" scoped>
  
  </style>