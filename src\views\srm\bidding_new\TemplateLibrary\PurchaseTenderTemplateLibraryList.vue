<template>
    <div style="height:100%">
        <list-layout
        v-if="!showEditPage && !showDetailPage"
        ref="listPage"
        v-show="!showEditPage"
        :pageData="pageData"
        :url="url" />
      <!-- 表单区域 -->
      <purchase-tender-template-library-edit
        v-if="showEditPage"
        :current-edit-row="currentEditRow"
        ref="modalForm"
        @ok="modalFormOk"
        @hide="hideEditPage"
      />
      <purchase-tender-template-library-detail
        v-if="showDetailPage"
        ref="detailPage"
        :current-edit-row="currentEditRow"
        @hide="hideEditPage"
      />
    </div>
  </template>
  <script>
  import PurchaseTenderTemplateLibraryEdit from './modules/PurchaseTenderTemplateLibraryEdit'
  import PurchaseTenderTemplateLibraryDetail from './modules/PurchaseTenderTemplateLibraryDetail'
  import {ListMixin} from '@comp/template/list/ListMixin'
import { srmI18n, getLangAccount } from '@/utils/util'
  
  export default {
      mixins: [ListMixin],
      components: {
          PurchaseTenderTemplateLibraryEdit,
          PurchaseTenderTemplateLibraryDetail
      },
      data () {
          return {
              showEditPage: false,
              pageData: {
                  businessType: 'tenderTemplateLibrary',
                  formField: [
                  {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeName`, '请输入编码或名称')
                    }
                  ],
                  form: {
                  }
              },
              optColumnList: [
                  { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView},
                  { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: true, key: 'edit'},
                  { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: true, key: 'delete'}
              ],
              tabsList: [],
              url: {
                list: "/tender/template/purchaseTenderTemplateLibrary/list",
                delete: "/tender/template/purchaseTenderTemplateLibrary/delete",
                deleteBatch: "/tender/emplate/purchaseTenderTemplateLibrary/deleteBatch",
                exportXlsUrl: "tender/template/purchaseTenderTemplateLibrary/exportXls",
                importExcelUrl: "tender/template/purchaseTenderTemplateLibrary/importExcel",
                columns: 'PurchaseTenderTemplateLibraryList'
             },
          }
      },
      methods: {
      }
  }
  </script>