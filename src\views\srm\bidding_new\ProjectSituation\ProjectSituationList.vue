<template>
  <div style="height: 100%">
    <list-layout
      :tabsList="tabsList"
      ref="listPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab" />
  </div>
</template>
<script>
// import listTable from '../BiddingHall/components/listTable'
import { ListMixin } from '@comp/template/list/ListMixin'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { USER_INFO } from '@/store/mutation-types'
import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        // listTable
    },
    data () {
        return {
            subpackageList: [],
            confirmLoading: false,
            pageData: {
                businessType: 'biddingPlatform',
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNYBdIAySYBdIRL_4b01476f`, '请输入招标项目编号或招标项目名称')
                    }
                ],
                form: {
                    keyWord: ''
                },
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                defaultButton: [],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), authorityCode: 'tender#purchaseTenderProjectHead:queryById', clickFn: this.handleView }
                ],
                optColumnWidth: 270,
                superQueryShow: false 
            },
            // modalPageData: {
            //     optColumnList: [
            //     ]
            // },
            url: {
                list: '/tender/purchaseTenderProjectSubpackageInfo/list',
                columns: 'tenderAgencyProjectSubpackageList',
                queryById: '/tender/purchaseTenderProjectHead/queryById',
                getExecutorAuthorityUrl: '/tender/purchaseTenderProjectHead/getExecutorAuthority'
            },
            rolePermission: 0
        }
    },
    computed: {
        
    },
    methods: {
        getExecutorAuthority (row) {
            return getAction(this.url.getExecutorAuthorityUrl, {id: row.projectId})
        },
        getCurrentProjectRow (row) {
            return getAction(this.url.queryById, {id: row.projectId})
        },
        async handleView (row) {
            // 默认角色为0，只能查看
            this.$refs.listPage.loading = true
            let {result: applyRole = '0'} = await this.getExecutorAuthority(row)
            let {result: currentProjectRow} = await this.getCurrentProjectRow(row)
            this.$refs.listPage.loading = false
            currentProjectRow['applyRole'] = applyRole
            currentProjectRow['subpackageId'] = row.id
            this.$ls.set('SET_TENDERCURRENTROW', currentProjectRow)
            let _t = +new Date()
            const routeUrl = this.$router.resolve({
                path: '/biddingHall',
                query: {
                    _t
                }
            })
            window.open(routeUrl.href, '_blank')
        }
    },
    mounted (){
        console.log(this.$route.query)
        this.pageData.form = this.$route.query
    },
    created () {
        console.log(this.$route.query)
        this.pageData.form = this.$route.query
    },
    activated (){
        this.pageData.form = this.$route.query
        this._countTabsUrl = '/tender/purchaseTenderProjectSubpackageInfo/counts?agencyElsAccount='+this.$route.query.agencyElsAccount
    }
}
</script>