<template>
  <a-modal
    v-drag    
    v-model="madalVisible"
    centered
    :width="1000"
    title="变更列表数据"
    @ok="setLadderOk">
    <a-spin :spinning="confirmLoading">
      <div class="compare-page-container">
        <div class="page-content-right">
          <div style="margin-bottom: 12px">
            <vxe-grid
              border
              ref="headerGrid"
              row-id="adfas"
              auto-resize
              size="small"
              height="350"
              :title="$srmI18n(`${$getLangAccount()}#i18n_title_ladderInfo`, '发送列表数据')"
              :columns="tableHeaderColumn"
              :data="tableHeaderData"
              :edit-config="{trigger: 'click', mode: 'cell'}"
            >
              <template #requireDate_edit="{ row }">
                <a-date-picker
                  @change="onChangesafdsa"
                  v-model="row.requireDate"/>
              </template>
              <template #responsibleParty_edit="{ row }">
                <a-select 
                  v-model="row.responsibleParty"
                  placeholder="请选择"
                  :dropdownMatchSelectWidth="false"
                >
                  <a-select-option 
                    v-for="item in responsiblePartyList" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
              <template #srmRejectReason_edit="{ row }">
                <a-select 
                  v-model="row.responsibleReason"
                  placeholder="请选择"
                  :dropdownMatchSelectWidth="false"
                >
                  <a-select-option 
                    v-for="item in responsibleReasonList" 
                    :key="item.value">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </template>
            </vxe-grid>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>
<script>
import {List} from 'ant-design-vue'
import {  getAction, postAction } from '@/api/manage'
export default {
    name: 'SetLadderModal',
    props: {
        tableHeaderData: {
            type: Array,
            default: ()=> {
                return []
            }
        }
    },
    components: {
        AList: List,
        AListItem: List.Item
    },
    data () {
        return {
            confirmLoading: false,
            listData: [],
            madalVisible: false,
            tableDatas: [],
            tableHeaderColumn: [],
            
            responsiblePartyList: [],
            responsibleReasonList: []
        }
    },
    mounted () {
        this.getListHeader()
        this.getFindDictItems()
    },
    methods: {
        open () {
            this.madalVisible = true
        },
        onChangesafdsa (){

        },
        // 获取表头信息
        getListHeader (){
            let api = 'base/userColumnDefine/queryCurrentUserColumnDefineJson/purchaseDeliveryNoticeByOrderList'
            getAction(api).then(res=>{
                if(res.code == 200){
                    for( let vals of res.result){
                        vals['field'] = vals.dataIndex
                        if(vals.title == '需求数量' || vals.title == '需方备注'){
                            vals.fixed= 'left'
                            vals['editRender'] =  { name: '$input', props: {disabled: false}}
                        }
                        if(vals.title == '要求交期'){
                            vals.fixed= 'left'
                            vals.width= '150'
                            console.log(vals)
                            vals['slots']={ default: 'requireDate_edit', edit: 'requireDate_edit' } 
                        } 
                        if(vals.title == '责任方' ){
                            vals.fixed= 'left'
                            vals.width= '100'
                            vals['slots']={ default: 'responsibleParty_edit', edit: 'responsibleParty_edit' } 
                        }
                        if(vals.title == '责任原因' ){
                            vals.fixed= 'left'
                            vals.width= '100'
                            vals['slots']={ default: 'srmRejectReason_edit', edit: 'srmRejectReason_edit' } 
                        }
                    }
                    this.tableHeaderColumn = res.result
                }
            })
        },
        // 获取下拉框 信息
        getFindDictItems (){
            // eslint-disable-next-line no-undef
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'srmOrderDeliveryResponsible'}).then(res => {
                if(res.success === true){
                    res.result.forEach(item => {
                        let dictData = {}
                        dictData['value'] = item.value
                        dictData['label'] = item.text
                        this.responsiblePartyList.push(dictData)
                    })
                }
            }).finally(() => {
            })
            postAction('/base/dict/findDictItems', {busAccount: `${this.$getLangAccount()}`, dictCode: 'srmRejectReason'}).then(res => {
                if(res.success === true){
                    res.result.forEach(item => {
                        let dictData = {}
                        dictData['value'] = item.value
                        dictData['label'] = item.text
                        this.responsibleReasonList.push(dictData)
                    })
                }
            }).finally(() => {
            })
        },
        goBack () {
            this.$emit('hide')
        },
        setLadderOk (){
            this.$emit('ok', this.$refs.headerGrid.data)
            this.madalVisible = false
        }
    }
}
</script>
<style lang="less" scoped>
    .compare-page-container {
        display: flex;
        .page-content-right {
            flex: 1;
            width: 900px;
            background-color: #fff
        }
    }
</style>