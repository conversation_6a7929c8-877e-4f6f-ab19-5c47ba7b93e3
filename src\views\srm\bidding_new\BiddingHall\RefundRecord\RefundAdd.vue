<template>
  <div class="refund-add">
    <a-spin :spinning="loading">
      <ContentHeaderNew :btns="btns"></ContentHeaderNew>
      <!-- <a-steps
        :current="0"
        size="small">
        <a-step>
          <template slot="title">
            {{ $srmI18n(`${$getLangAccount()}#i18n_field_YVUVcVH_36e1add8`, '退款申请行信息') }}
          </template>
        </a-step>
      </a-steps> -->
      <div>
        <div class="btns">
          <a-button
            type="primary"
            style="margin-right: 20px;"
            @click="refundAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_title_add`, '新增') }}</a-button>
          <a-button @click="removeData">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
        </div>
        <div class="grid">
          <vxe-grid
            ref="refundGrid"
            v-bind="gridOptions">
            <template #refundAccount_edit="{row}">
              <a-input v-model="row.refundAccount"></a-input>
            </template>
            <template #refundName_edit="{row}">
              <a-input v-model="row.refundName"></a-input>
            </template>
            <template #interBankNo_edit="{row}">
              <a-input v-model="row.interBankNo"></a-input>
            </template>
            <template #bankName_edit="{row}">
              <a-input v-model="row.bankName"></a-input>
            </template>
            <template #refundAmount_edit="{row}">
              <a-input v-model="row.refundAmount"></a-input>
            </template>
            <template #remark_edit="{row}">
              <a-input v-model="row.remark"></a-input>
            </template>
          </vxe-grid>
        </div>
      </div>
    </a-spin>
    <field-select-modal
      :isEmit="true"
      :isTree="true"
      ref="fieldSelectModal"
      @ok="fieldSelectOk" />
  </div>
</template>

<script>
import ContentHeaderNew from '../components/content-header-new'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
export default {
    components: {
        ContentHeaderNew,
        fieldSelectModal
    },
    inject: ['resetCurrentSubPackage'],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            loading: false,
            btns: [
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'), type: 'primary', click: () => {this.submitRefund()}},
                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: () => {this.goBack()}}
            ],
            gridOptions: {
                border: true,
                showHeaderOverflow: true,
                showOverflow: true,
                align: 'center',
                headerAlign: 'center',
                height: '400',
                size: 'mini',
                toolbarConfig: {
                    enabled: false
                },
                editConfig: {
                    trigger: 'click',
                    mode: 'cell'
                },
                editRules: {
                    refundAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeylS_e286c8d5`, '退款账号必填')}],
                    refundName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyDRlS_5d10044b`, '退款账号户名必填')}],
                    bankName: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyvDclS_1eac9e0c`, '退款账号开户行必填')}],
                    refundAmount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vmYVHflS_65daf125`, '本次退款金额必填')}]
                },
                columns: [
                    { type: 'checkbox', width: 60 },
                    { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                    { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBLRL_9daf066b`, '投标人名称')},
                    { field: 'refundAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVey_43196f4f`, '退款账号'), editRender: {}, slots: { edit: 'refundAccount_edit' } },
                    { field: 'refundName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyDR_e2871045`, '退款账号户名'), editRender: {}, slots: { edit: 'refundName_edit' } },
                    { field: 'bankName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVeyvDc_6e512646`, '退款账号开户行'), editRender: {}, slots: { edit: 'bankName_edit' } },
                    { field: 'interBankNo', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_YVKEy_1feb0481`, '联行号'), editRender: {}, slots: { edit: 'interBankNo_edit' } },
                    { field: 'availableAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qjUfWjW_9ab8dcd1`, '可用余额（元）')},
                    { field: 'lockedAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIHfWjW_d5271fa9`, '锁定金额（元）')},
                    { field: 'refundAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vmYVHfWjW_57d92f2f`, '本次退款金额（元）'), editRender: {}, slots: { edit: 'refundAmount_edit' } },
                    { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), editRender: {}, slots: { edit: 'remark_edit' } }
                ],
                data: []
            }
        }
    },
    created () {
        this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHYVAvCK_7da085b6`, '保证金退款（其他方式）')
    },
    mounted () {
        this.currentEditRow['refundAmount'] = ''
        this.gridOptions.data.push(this.currentEditRow)
    },
    methods: {
        async submitRefund () { // 提交
            const gridObj = this.$refs.refundGrid
            const {tableData} = gridObj.getTableData()
            console.log(tableData)
            if (!tableData || tableData.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VVasiHYVeDVH_72eb799c`, '请新增保证金退款账户信息！'))
                return false
            }
            const errMap = await gridObj.fullValidate(true).catch(errMap => errMap)
            if (errMap) {
                return false
            }
            console.log(errMap)
            tableData.forEach(item => {
                item['refundType'] = '1'
            })
            this.loading = true
            postAction('/tender/supplier/purchaseTenderProjectMarginHead/refund', tableData).then(res => {
                const status = res.success ? 'success' : 'error'
                this.$message[status](res.message)
                if(res.code == 200) {
                    // this.$emit('resetCurrentSubPackage') || ''
                    this.resetCurrentSubPackage()
                    this.goBack()
                }
            }).finally(() => {
                this.loading = false
            })
        },
        removeData () { // 删除
            const gridObj = this.$refs.refundGrid
            const rows = gridObj.getCheckboxRecords(true)
            if(rows.length == 0) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFTPQGjWF_3294a8fb`, '请选择需要删除的数据！'))
                return false
            }
            gridObj.removeCheckboxRow()
        },
        refundAdd () { // 新增
            const columns = [
                {field: 'supplierAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierAccount`, '供应商ELS账号')},
                {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称')},
                {field: 'availableAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Uf_a3464`, '余额')},
                {field: 'lockedAmount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dIHfWjW_d5271fa9`, '锁定金额')}
            ]
            console.log(this.currentEditRow)
            const params = {
                subpackageId: this.currentEditRow.subpackageId || '',
                refundType: '1'
            }
            this.$refs.fieldSelectModal.open('/tender/supplier/purchaseTenderProjectMarginHead/queryMarginInfo', params, columns, 'multiple')
        },
        fieldSelectOk (rows) {
            console.log(rows)
            const rowList = [...this.gridOptions.data, ...rows]
            this.gridOptions.data = this.duplicateRemoval(rowList, 'supplierAccount')
        },
        duplicateRemoval (data, field) { // 去重数组对象
            let fields = []
            let arr = []
            for (var i = 0; i < data.length; i++) {
                if (fields.indexOf(data[i][field]) == -1) {
                    fields.push(data[i][field])
                    arr.push(data[i])
                }
            }
            return arr
        },
        goBack () { // 返回
            this.$parent.showRefundAddPage = false
            this.$parent.getqueryList()
            this.$parent.getNumberPrice()
            this.$route.meta.title = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_siHRv_a7253812`, '保证金管理')
        }
    }
}
</script>

<style lang="less" scoped>
.refund-add {

  .ant-steps {
    padding-left: 20px;
  }
  .btns {
    margin-top: 15px;
    padding: 0 20px;
  }
  .grid {
    margin-top: 10px;
  }
}
</style>