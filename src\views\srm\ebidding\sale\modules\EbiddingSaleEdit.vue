<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      refresh
      :current-edit-row="currentEditRow"
      :url="url"
      :reloadData="handleReloadData" />
    <field-select-modal 
      ref="fieldSelectModal" />
    <!-- 加载配置文件 -->
    <remote-js
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script lang="jsx">
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import moment from 'moment'
export default {
    name: 'EbiddingSaleEdit',
    mixins: [EditMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    components: {
        fieldSelectModal
    },
    data () {
        return {
            stageTypeData: [],
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleEbiddingItemList',
                        columns: [],
                        buttons: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'), groupCode: 'confirmItem', type: 'grid', custom: {
                        ref: 'saleEbiddingConfirmList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'confirmDesc', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'), width: 220 },
                            { field: 'must_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'writeType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'), width: 120 },
                            { field: 'content', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'), width: 220,
                                slots: {
                                    default: ({row}) =>{
                                        if (row && ['0', '1'].includes(row.writeType) && row.confirmItemList) {
                                            if (row.writeType === '0') {
                                                return [
                                                    (<a-select
                                                        vModel={row.content}
                                                        allowClear={true}
                                                        placeholder='请选择'
                                                    >
                                                        {
                                                            row.confirmItemList.map((item)=> {
                                                                return (
                                                                    <a-select-option value= {item.optionsCode}>
                                                                        { item.optionsName }
                                                                    </a-select-option>
                                                                )
                                                            })
                                                        }
                                                    </a-select>)
                                                ]
                                            }
                                            if (row.writeType === '1') {
                                                if (row.content && typeof row.content === 'string') row.content = row.content.split(',')
                                                else if (!row.content) row.content = []
                                                return [
                                                    (<a-select
                                                        mode='multiple'
                                                        vModel={row.content}
                                                        allowClear={true}
                                                        placeholder='请选择'
                                                    >
                                                        {
                                                            row.confirmItemList.map((item)=> {
                                                                return (
                                                                    <a-select-option value= {item.optionsCode}>
                                                                        { item.optionsName }
                                                                    </a-select-option>
                                                                )
                                                            })
                                                        }
                                                    </a-select>)
                                                ]
                                            }
                                        } else {
                                            return [(<a-input v-model={ row.content } />)]
                                        }
                                    }
                                }
                            },
                            { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), editRender: {name: '$input'} },
                            { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemarks`, '采购备注')}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentDemandList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ifRequired`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'fileSize', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bigSmall`, '大小'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                            { field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cdIRL_c7c40624`, '行项目名称'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                dictCode: 'srmFileType',
                                type: 'upload',
                                businessType: 'ebidding',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack
                            },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                publicBtn: [
                    // {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_OufY_390dbcd7`, '竞价大厅'), type: 'primary', click: this.openLobby},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidResponse`, '应标'), type: 'primary', click: this.accect},
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'), click: this.reject},
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/ebidding/saleEbiddingHead/queryById',
                accect: '/ebidding/saleEbiddingHead/acceptResponse',
                reject: '/ebidding/saleEbiddingHead/rejectResponse',
                upload: '/attachment/saleAttachment/upload',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            this.getStageTypeData()
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            return `${this.$variateConfig['configFiles']}/${account}/sale_ebidding_${templateNumber}_${templateVersion}.js`
        }
    },
    methods: {
        attrHandle () {
            let cn = ''
            if (this.currentEditRow.ebiddingNumber) {
                cn = this.currentEditRow.ebiddingNumber
            } else {
                const params = this.$refs.editPage.getPageData()
                cn = params.ebiddingNumber
            }
            return {
                sourceNumber: cn,
                actionRoutePath: '/srm/ebidding/EbiddingBuyHeadList,/srm/ebidding/sale/EbiddingSaleHeadList'
            }
        },
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEbiddingStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.saleAttachmentDemandList = res.result.saleAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        beforeHandleData (data) {
            if(this.currentEditRow['ebiddingWay'] === '0'){
                data.formFields.forEach(head => {
                    if(head.fieldName === 'taxCode' && this.currentEditRow['supplierTaxRate'] == '1'){
                        head.disabled = false
                        head.fieldType = 'selectModal'
                        head.extend.modalParams['elsAccount'] = this.currentEditRow['busAccount']
                    }
                })
            }else{
                data.itemColumns.forEach(item => {
                    //供应商税率为是时,税码可弹框
                    if(item.field == 'taxCode' && this.currentEditRow['supplierTaxRate'] == '1'){
                        item.fieldType = 'selectModal'
                        item.extend.modalParams['elsAccount'] = this.currentEditRow['busAccount']
                    }
                })
            }
        },
        downloadEvent (row) {
            this.$refs.editPage.handleDownload(row)
        },
        uploadCallBack (result) {
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            fileGrid.insertAt(result, -1)
        },
        deleteFilesEvent (row) {
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            //如果删除的数据有和登录人账号不一致的
            if(user !== row.uploadElsAccount || subAccount !== row.uploadSubAccount){
                this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBI_1168b35d`, '只能删除本人提交的附件'))
                return
            }
            
            let fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            getAction('/attachment/saleAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 批量删除
        deleteBatch () {
            let arr = []
            let delArr = []
            //大B
            let  user=this.$ls.get(USER_ELS_ACCOUNT)
            let subAccount = this.$ls.get('Login_subAccount')
            const fileGrid = this.$refs.editPage.$refs.saleAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            if(checkboxRecords && checkboxRecords.length>0){
                checkboxRecords.forEach(row=> {
                    //当前登录人等账号于查询的账号 大B
                    if (user == row.uploadElsAccount) {
                        if( subAccount==row.uploadSubAccount){
                            delArr.push(row)
                        }else{
                            arr.push(row.fileName)
                        }
                    }else{
                        arr.push(row.fileName)
                    }
                })
                //如果删除的数据有和登录人账号不一致的
                if(arr && arr.length>0){
                    let str = arr.join(',')
                    this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ROQGvLDJjBIWBIRLW_396e6396`, '只能删除本人提交的附件，附件名称：') +str+this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BjbWQG_3b4282f9`, '没有权限删除'))
                    return
                }
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/attachment/saleAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        accect () {
            const currentDate= moment().format('YYYY-MM-DD')
            const params = this.$refs.editPage.getPageData()
            const saleEbiddingItemList = params.saleEbiddingItemList
            let indexDates = []
            let indexDateOthers = []
            let indexDateCurs = []
            saleEbiddingItemList.forEach((i, index) => {
                if (!i.effectiveDate || !i.expiryDate) indexDates.push(index + 1)
                if (!(moment(i.effectiveDate).isSame(currentDate) || moment(i.effectiveDate).isAfter(currentDate))) indexDateCurs.push(index + 1)
                if (new Date(i.effectiveDate).getTime() >= new Date(i.expiryDate).getTime()) indexDateOthers.push(index + 1)
            })
            if (indexDates.length > 0) {
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息')} ${indexDates.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VMQbXBAWKXBA_2934ff40`, '请完善生效日期、失效日期')}`)
                return
            }
            if (indexDateCurs.length > 0) {
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息')} ${indexDateCurs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_umbXBATfUEUAPBAW_dd1edba9`, '价格生效日期需大于等于当前日期！')}`)
                return
            }
            if (indexDateOthers.length > 0) {
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息')} ${indexDateOthers.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KXBAdfUbXBA_4902668d`, '失效日期应大于生效日期')}`)
                return
            }

            const saleEbiddingConfirmList = params.saleEbiddingConfirmList
            let indexs = []
            saleEbiddingConfirmList.forEach((i, index) => {
                if (i.must === '1' && (!i.content || i.content.length === 0)) indexs.push(index + 1)
            })
            if (indexs.length > 0) {
                this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项')} ${indexs.toString()} : ${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VMQURid_3fe37571`, '请完善预制选项')}`)
                return
            }

            const _this = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bidResponse`, '应标'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLdBW_ebc9707f`, '是否确认应标?'),
                onOk () {
                    let params = JSON.parse(JSON.stringify(_this.$refs.editPage.getPageData()))
                    let saleEbiddingConfirms = params.saleEbiddingConfirmList.map(i => {
                        i.content = i.writeType === '1' ? (i.content ? i.content.join(',') : '') : (i.content || '')
                        return i
                    })
                    const param = { ...params, saleEbiddingConfirmList: saleEbiddingConfirms }
                    postAction(_this.url.accect, param).then(res => {
                        if (res.success) {
                            _this.$message.success(res.message)
                            _this.goBack()
                        } else {
                            _this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        reject () {
            const that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_refuse`, '拒绝'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLFKdBW_66c72994`, '是否确认拒绝应标?'),
                onOk: function () {
                    that.loading = true
                    getAction(that.url.reject, {id: that.currentEditRow.id}).then(res => {
                        if(res.success) {
                            that.$message.success(res.message)
                        }else {
                            that.$message.warning(res.message)
                        }
                    })  
                }
            })
        },
        openLobby () {
            const { ebiddingStatus, ebiddingStatus_dictText } = this.$refs.editPage && this.$refs.editPage.getPageData() || {}
            // 当竞价状态不是待竞价和竞价中时，不允许进入竞价大厅
            if(ebiddingStatus !== '3' && ebiddingStatus !== '4') return this.$message.warning(`${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_APzEL_666e77f5`, '当前状态为')}${ebiddingStatus_dictText}，${this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LiTHNOufY_b1ac6742`, '未允许进入竞价大厅')}`)
            this.$store.dispatch('SetTabConfirm', false)
            // if (this.currentEditRow.ebiddingWay === '2') {
            //     this.$router.push({
            //         path: '/ebidding/saleLobbyBatch',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber: this.currentEditRow.ebiddingNumber,
            //             currentItemNumber: this.currentEditRow.currentItemNumber || '1'
            //         }
            //     })
            // } else {
            //     this.$router.push({
            //         path: '/ebidding/saleLobbyNew',
            //         query: {
            //             id: this.currentEditRow.id,
            //             ebiddingNumber: this.currentEditRow.ebiddingNumber,
            //             currentItemNumber: this.currentEditRow.currentItemNumber || '1'
            //         }
            //     })
            // }
            // 竞价方式 - 0 英式，1 日式，2 荷式
            this.$router.push({
                path: '/ebidding/saleLobbyNew',
                query: {
                    id: this.currentEditRow.id,
                    ebiddingNumber: this.currentEditRow.ebiddingNumber,
                    currentItemNumber: this.currentEditRow.currentItemNumber || '1'
                }
            })
        }
    }
}
</script>