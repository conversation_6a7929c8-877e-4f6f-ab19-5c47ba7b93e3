import {textColor} from '@comp/chart/widget/utils/theme.js'
// 散点图
export default {
    backgroundColor: 'rgba(255, 255, 255, 0)',
    title: {
        show: true,
        text: '',
        top: 10,
        left: 'center',
        textStyle: {// 主标题文字样式
            color: textColor,
            fontWeight: 'normal',
            fontSize: 18
        },
        subtext: '',
        subtextStyle: { // 副标题文字样式
            color: textColor,
            fontWeight: 'normal',
            fontSize: 16
        }
    },
    legend: { // 图例
        show: true,
        left: 'left',
        orient: 'horizontal',
        textStyle: {
            color: '#000000',
            fontSize: 10
        }
    },
    grid: {// 直角坐标系轴 - 最外面的矩形 - 或者理解为坐标轴边距
        show: false,
        top: 60,
        right: 20,
        bottom: 60,
        left: 60
    },
    tooltip: { // 鼠标悬浮的提示语
        show: true,
        textStyle: {
            color: '#000000',
            fontWeight: 'normal',
            fontSize: 14
        }
    },
    color: ['#83bff6', '#23B7E5', '#61a0a8', '#d48265', '#91c7ae', '#83bff6', '#23B7E5', '#61a0a8', '#d48265', '#91c7ae', '#83bff6', '#23B7E5', '#61a0a8', '#d48265', '#91c7ae', '#83bff6', '#23B7E5', '#61a0a8', '#d48265', '#91c7ae', '#d48265', '#91c7ae'],
    xAxis: {
        axisLine: {         // X坐标轴
            show: true,
            lineStyle: {
                color: textColor
            }
        },
        axisLabel: {
            show: true,
            color: textColor,
            fontWeight: 'normal',
            fontSize: 14
        },
        axisTick: {         // X坐标轴刻度
            show: true
        },
        splitLine: {        // X坐标的网格线
            show: false
        }
    },
    yAxis: {
        axisLine: {         // Y坐标轴
            show: true,
            lineStyle: {
                color: textColor
            }
        },
        axisLabel: {
            show: true,
            color: textColor,
            fontWeight: 'normal',
            fontSize: 12
        },
        axisTick: {         // Y坐标轴刻度
            show: true
        },
        splitLine: {        // Y坐标的网格线
            show: false
        }
    },
    series: [
        {
            symbolSize: 15,
            data: [],
            type: 'scatter',
            itemStyle: {
                color: textColor // 散点颜色               
            }
        }
    ]
}













