<!--
 * @Author: fzb
 * @Date: 2022-02-21 16:28:52
 * @LastEditTime: 2022-03-01 17:15:56
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\chartsBar\src\main.vue
-->
<template>
  <vue-echarts
    :style="style"
    :option="widget.option"
    :update-options="{ notMerge: true }"
    autoresize
  />
</template>
<script>
import { chartsMixins } from '@comp/chart/widget/mixins/chartsMixins'
import _ from "lodash"
export default {
  name: 'ChartsRadar',
  mixins: [chartsMixins],
  computed: {
    option() {
      return {
        customRadius: this.widget.option.radar.customRadius,
        customOpacity: this.widget.option.radar.customOpacity,
      };
    }
  },
  watch: {
    option: {
      handler(val) {
        this.widget.option.radar.radius[0] = val.customRadius[0] + "%";
        this.widget.option.radar.radius[1] = val.customRadius[1] + "%";
        for (let i = 0; i < this.widget.option.series.length; i++) {
          const datas = this.widget.option.series[i].data;
          for (let j = 0; j < datas.length; j++) {
            datas[j].areaStyle.opacity = val.customOpacity;
          }
        }
      },
      deep: true,
    }
  },
  methods: {
    refreshWidgetData(data) {
      const option = this.widget.option;
      option.radar.indicator = data.indicator;
      option.series.splice(1, option.series.length - 1);
      for (let i = 0; i < data.series.length; i++) {
        let chartData = data.series[i];
        let item = option.series[i];
        if (!item) {
          option.series.push(_.cloneDeep(option.series[0]));
          item = option.series[i];
        }
        item.data = chartData.data;
        // 添加雷达区域面积属性
        item.data.forEach((val) => {
          val.areaStyle = {
            opacity: option.radar.customOpacity, // 为0时不绘制区域面积，可用该属性控制是否绘制区域面积
          };
        });
      }
    }
  },
};
</script>