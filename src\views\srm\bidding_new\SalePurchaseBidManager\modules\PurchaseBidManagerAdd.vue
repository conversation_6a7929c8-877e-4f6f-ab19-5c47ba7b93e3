<template>
  <div class="PurchaseBidManagerEdit">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="layoutShow"
        :ref="businessRefName"
        :currentEditRow="currentEditData"
        :remoteJsFilePath="remoteJsFilePath"
        :fromSourceData="fromSourceData"
        :externalToolBar="externalToolBar"
        :pageFooterButtons="pageFooterButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        :pageStatus="pageStatus"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'
import EditFormLayout from '@comp/template/business/EditFormLayout.vue'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { USER_INFO, USER_COMPANYSET } from '@/store/mutation-types'
import { postAction, getAction} from '@/api/manage'
import { add } from '@/utils/mathFloat.js'

export default {
    name: 'PurchaseBidManagerEdit',
    components: {
        BusinessLayout,
        EditGridLayout,
        EditFormLayout
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        queryData: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRef',
            pageStatus: 'edit',
            confirmLoading: false,
            // externalToolBar: {},
            pageFooterButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/edit'
                    },
                    // show: this.syncShow, // 同步校验显示方法
                    attrs: {
                        type: 'primary'
                    },
                    key: 'save2',
                    click: this.handleSave,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submit`, '提交'),
                    args: {
                        url: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
                    },
                    attrs: {
                        type: 'primary'
                    },
                    key: 'publish2',
                    // showMessage: true,
                    click: this.handleSubmit,
                    handleBefore: this.handleSubmitBefore
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                queryById: '/tender/sale/supplierTenderProjectPurchaseBid/queryById',
                queryProjectInfo: '/tender/sale/supplierTenderProjectPurchaseBid/queryPurchaseBidInfo',
                add: '/tender/sale/supplierTenderProjectPurchaseBid/add',
                edit: '/tender/sale/supplierTenderProjectPurchaseBid/edit',
                submit: '/tender/sale/supplierTenderProjectPurchaseBid/submit'
            },
            projectObj: {},
            fromSourceData: {},
            currentEditData: {},
            remoteJsFilePath: '',
            layoutShow: false
        }
    },
    computed: {
        externalToolBar () {
            return {
                attachmentList: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_UpAtachments`, '上传附件'),
                        key: 'upload',
                        args: {
                            property: 'label', // 可省略
                            itemInfo: [], // 必传
                            disabledItemNumber: true,
                            action: '/attachment/purchaseAttachment/upload', // 必传
                            businessType: 'SupplierTenderProjectPurchaseBid', // 必传,
                            itemNumberKey: 'materialNumber',
                            itemNumbeValueProp: 'value',
                            itemNumberLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RKWWW_b61bceb4`, '关联tab'),
                            headId: '', // 必传
                            modalVisible: false // 必传
                        }, 
                        attr: this.attrHandle,
                        callBack: this.uploadCallBack
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
                        key: 'gridDelete',
                        click: this.businessGridDelete
                    }
                ]
            }
        }
    },
    mounted () {
        this.getBusinessTemplate()
    },
    methods: {
        attrHandle (){
            return {
                sourceNumber: this.currentEditData.tenderProjectNumber || this.currentEditData.id || '',
                actionRoutePath: '采购商与供应商的路径逗号隔开,/bidder/PurchaseBidManagerList'
            }
        },
        // 获取业务模板信息
        getBusinessTemplate () {
            let params = {elsAccount: this.$ls.get('Login_elsAccount'), businessType: 'SupplierTenderProjectPurchaseBid'}
            this.confirmLoading = true
            getAction('/template/templateHead/getListByType', params).then(res => {
                if(res.success) {
                    if(res.result.length > 0) {
                        let options = res.result.map(item => {
                            return {
                                templateNumber: item.templateNumber,
                                templateName: item.templateName,
                                templateVersion: item.templateVersion,
                                templateAccount: item.elsAccount
                            }
                        })
                        this.currentEditData = options[0]
                        this.remoteJsFilePath = `${this.currentEditData['templateAccount']}/sale_SupplierTenderProjectPurchaseBid_${this.currentEditData['templateNumber']}_${this.currentEditData['templateVersion']}`
                        this.getData()
                    } else {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_setTemplateFirst`, '请先配置业务模板'))
                    }
                }else {
                    this.$message.warning(res.message)
                }
                this.confirmLoading = false
            })
        },
        handleSubmitBefore (args) {
            console.log('args', args)
            return new Promise((resolve) => {
                let allData = args.allData
                allData['saleTenderInvoiceInfoList'] = [Object.assign(allData['invoiceInfo'], allData['payType'])]
                allData['noticeId'] = this.queryData && this.queryData.businessId
                allData = Object.assign(allData, this.currentEditData)
                let params = {
                    allData: allData
                }
                args = Object.assign({}, args, params)
                resolve(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                ],
                itemColumns: [
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
                        fieldLabelI18nKey: '',
                        field: 'fileName'
                    },
                    {
                        groupCode: 'attachmentList',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'),
                        fieldLabelI18nKey: '',
                        field: 'uploadTime'
                    },
                    {   
                        groupCode: 'attachmentList',
                        fieldLabelI18nKey: '',
                        field: 'uploadSubAccount_dictText',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploaderPerson`, '上传人')
                    }
                ]
            }
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log(pageConfig, resultData)

            let {consortiumBidding} = this.queryData
            if(consortiumBidding == '0'){
                pageConfig.groups[0].formModel.combination='0'
            }
            let setDisabledByProp = (prop, flag) => {
                for (let group of pageConfig.groups) {
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            sub.disabled = flag
                            break
                        }
                    }
                }
            }
            let flag = (consortiumBidding == '0')
            setDisabledByProp('combination', flag)
            let setValidateRuleByProp = (prop, flag) => {
                let index = -1
                let fieldLabel
                let rule = {}
                for (let group of pageConfig.groups) {
                    
                    if (group.type) continue
                    let formFields = group.formFields || []
                    for (let sub of formFields) {
                        if (sub.fieldName === prop) {
                            fieldLabel = sub.fieldLabel
                            index = pageConfig.groups.indexOf(group)
                            break
                        }
                    }
                }


                rule[prop] = [{
                    required: !flag,
                    message: `${fieldLabel}必填`
                }]
                if (index !== -1) {
                    pageConfig.groups[index].validateRules = Object.assign({}, pageConfig.groups[index].validateRules, rule)
                }
            }

            let validateFlag = (consortiumBidding == '0')
            setValidateRuleByProp('combination', validateFlag)

            // 编辑赋值 payMoney saleAmount  tenderProjectPurchaseBidOrderVOList
            const tenderProjectPurchaseBidOrderVOList = resultData['tenderProjectPurchaseBidOrderVOList']
            // 累加所有文件的标价总和
            let payMoneys = 0
            if (tenderProjectPurchaseBidOrderVOList && tenderProjectPurchaseBidOrderVOList.length > 0) {
                tenderProjectPurchaseBidOrderVOList.forEach(item => {
                    const saleAmount = item.saleAmount ? parseFloat(item.saleAmount) : 0
                    payMoneys = add(payMoneys, saleAmount)
                })
            }
            const saleTenderInvoiceInfoList = resultData['saleTenderInvoiceInfoList']
            if (saleTenderInvoiceInfoList && saleTenderInvoiceInfoList.length > 0) {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'invoiceInfo') {
                        let {invoice, 
                            invoiceType,
                            enterpriseName, 
                            dutyParagraph, 
                            enterpriseAddress, 
                            enterprisePhone,
                            depositBank,
                            bankNumber } = saleTenderInvoiceInfoList[0]
                        group['formModel'] = {
                            invoice: invoice, 
                            invoiceType: invoiceType,
                            enterpriseName: enterpriseName, 
                            dutyParagraph: dutyParagraph, 
                            enterpriseAddress: enterpriseAddress, 
                            enterprisePhone: enterprisePhone,
                            depositBank: depositBank,
                            bankNumber: bankNumber }
                    }
                    if (group.groupCode == 'payType') {
                        let {payType, payMoney, remark} = saleTenderInvoiceInfoList[0]
                        
                        group['formModel'] = {payType, payMoney: payMoney || payMoneys, remark}
                        // group['formModel'] = Object.assign(group['formModel'], saleTenderInvoiceInfoList[0], {payMoney: payMoney})
                    }
                })
            } else {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'payType') {
                        group['formModel']['payMoney'] = payMoneys
                    }
                })
            }

            let that = this
            let itemInfo = pageConfig.groups
                .map(n => ({ label: n.groupName, value: n.groupCode }))
            // 上传 附件需要 headId
            that.externalToolBar['attachmentList'][0].args.headId = resultData.id || ''
            that.externalToolBar['attachmentList'][0].args.itemInfo = itemInfo
        },
        handleSave () {
            // let id = this.currentEditData.id || ''
            // console.log('this.currentEditData', this.currentEditData)
            // let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, this.currentEditData)
            const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditData
            let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
            
            const checkType = this.queryData.checkType || ''
            // params['id'] = id
            params['consortiumBidding'] = this.queryData.consortiumBidding || ''
            params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
            params['noticeId'] = this.queryData.businessId || ''
            params['checkType'] = checkType
            console.log('params', params)
            let url = params.id ? this.url.edit : this.url.add
            this.confirmLoading = true
            postAction(url, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    this.currentEditData.id = res.result.id
                    this.externalToolBar['attachmentList'][0].args.headId = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        handleSubmit () {
            const pageConfig = this.$refs[this.businessRefName].extendAllData()
            // 校验数据
            this.stepValidate(pageConfig).then(res => {
                const {templateAccount='', templateName='', templateNumber='', templateVersion=1, id=''} = this.currentEditData
                let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, {templateAccount, templateName, templateNumber, templateVersion, id})
                // let params = Object.assign(this.$refs[this.businessRefName].extendAllData().allData, this.currentEditData)
                params['consortiumBidding'] = this.queryData.consortiumBidding || ''
                const checkType = params['checkType'] || this.queryData.checkType || ''
                params['saleTenderInvoiceInfoList'] = [Object.assign(params['invoiceInfo'], params['payType'])]
                params['noticeId'] = params['noticeId'] || this.queryData.businessId || ''
                params['checkType'] = checkType
                // params['id'] = id

                this.confirmLoading = true
                postAction(this.url.submit, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                    let type = res.success ? 'success': 'error'
                    this.$message[type](res.message)
                    if (res.success) {
                        this.$parent.showAddPage = false
                        this.$store.dispatch('SetTabConfirm', false)
                        this.$parent.searchEvent(false)
                    }
                }).finally(() => {
                    this.confirmLoading = false
                })
                // postAction(url, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                //     let type = res.success ? 'success': 'error'
                //     this.$message[type](res.message)
                //     if (res.success) {
                //         params['id'] = res.result.id
                //         postAction(this.url.submit, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                //             let type = res.success ? 'success': 'error'
                //             this.$message[type](res.message)
                //             if (res.success) {
                //                 this.$parent.showAddPage = false
                //                 this.$store.dispatch('SetTabConfirm', false)
                //                 this.$parent.searchEvent(false)
                //             }
                //         })
                //     }
                // }).finally(() => {
                //     this.confirmLoading = false
                // })
            }, error => {
                console.log('有一个没有填', error)
            })
        },
        getData () {
            let url = '', checkType = ''
            let params = {}
            let cb = null
            if (this.currentEditData.id) {
                url = this.url.queryById
                checkType = this.currentEditData.checkType || this.queryData.checkType
                params = {id: this.currentEditData.id}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    this.currentEditData = Object.assign(data, this.currentEditData)
                }
            } else {
                url = this.url.queryProjectInfo
                checkType = this.queryData.checkType
                params = {subpackageId: this.queryData.subpackageId, noticeId: this.queryData.noticeId}
                cb = (data = {}) => {
                    this.fromSourceData = data
                    this.currentEditData = Object.assign(data, this.currentEditData)
                    let {elsAccount} = this.$ls.get(USER_INFO)
                    this.fromSourceData['supplierName'] = this.$ls.get(USER_COMPANYSET).companyName
                    this.fromSourceData['supplierAccount'] = elsAccount
                }
            }
            getAction(url, params, {headers: {xNodeId: `${checkType}_0`}}).then(res => {
                if (res.success) {
                    if (res.result) {
                        cb(res.result)
                    }
                }
            }).finally(() => {
                this.layoutShow = true
                this.confirmLoading = false
            })
        }
    }
}
</script>
<style lang="less" scoped>
:deep(.page-container .edit-page .page-content){
    flex: auto;
}
</style>
