<template>
  <div class="user-wrapper nav-right-part" :class="{ dark: setDarkThemeDefaulColor }">
    <div class="search-box" v-if="systemLayout === 'topmenu'">
      <div class="inner">
        <span class="search-button">
          <a-icon type="search" />
        </span>
        <a-select
          class="search-input"
          showSearch
          :showArrow="false"
          v-model="selectedMenu"
          :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_searchMenu`, '系统设置')"
          optionFilterProp="children"
          :filterOption="filterOption"
          :open="isMobile() ? true : null"
          :getPopupContainer="(node) => node.parentNode"
          :style="isMobile() ? { width: '100%', marginBottom: '50px' } : {}"
          @change="searchMethods"
          @blur="hiddenClick">
          <a-select-option
            v-for="(site, index) in searchMenuOptions"
            :key="index"
            :value="site.id">{{ site.meta.title }}</a-select-option>
        </a-select>
      </div>
    </div>
<!--    <span-->
<!--      v-if="showPurchaseShoppingMall"-->
<!--      class="toWebBidding header-notice top-icon"-->
<!--      @click="audioTransform()"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 6 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 6, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(6)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 6 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-Voice-Assistant"></icon-font>-->
<!--      <span v-if="currentActive === 6">语音助手</span>-->
<!--    </span>-->
<!--    <span-->
<!--      class="toWebBidding header-notice top-icon"-->
<!--      @click="toTanentPageHome()"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 7 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 7, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(7)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 7 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-company-pages"> </icon-font>-->
<!--      <span v-if="currentActive === 7">{{ $srmI18n(`${$getLangAccount()}#i18n_btn_AEdE_25190693`, '企业主页') }}</span>-->
<!--    </span>-->
<!--    <span-->
<!--      v-if="showPurchaseShoppingMall"-->
<!--      class="toWebBidding header-notice top-icon"-->
<!--      @click="toShoppingMall('1')"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 8 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 8, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(8)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 8 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-neicai-mall"> </icon-font>-->
<!--      <span v-if="currentActive === 8">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_AAeCnXL_27a092a4`, '企企通内采商城') }}</span>-->
<!--    </span>-->
<!--    <span-->
<!--      v-if="showSupperShoppingMall"-->
<!--      class="toWebBidding header-notice top-icon"-->
<!--      @click="toShoppingMall('3')"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 9 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 9, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(9)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 9 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-neicai-mall"> </icon-font>-->
<!--      <span v-if="currentActive === 9">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_AAeCnXL_27a092a4`, '企企通内采商城') }}</span>-->
<!--    </span>-->
    <!-- <span
      class="toWebBidding header-notice top-icon"
      @click="toWebFinancialCloud()"
      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 1 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"
      :class="{ active: currentActive === 1, change: topMenu_light || dark_defaultColor_notTopMenu }"
      @mouseenter="mouseEnter(1)"
      @mouseleave="mouseLeave"
    >
      <icon-font
        style="font-size: 16px; padding: 4px"
        class="icon"
        :style="{ color: currentActive === 1 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"
        type="icon-gongyingshangjinrong"> </icon-font>
      <span v-if="currentActive === 1">{{ $srmI18n(`${$getLangAccount()}#i18n_menu_AAeRdXBO_9a39a425`, '企企通供应商赋能') }}</span>
    </span> -->
<!--    <span-->
<!--      class="toWebBidding header-notice top-icon"-->
<!--      @click="toWebProcurement()"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 2 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 2, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(2)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 2 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-shangchenglogo"> </icon-font>-->
<!--      <span v-if="currentActive === 2">{{ $srmI18n(`${$getLangAccount()}#i18n_alert_AAenRT_6d92e591`, '企企通采购网') }}</span>-->
<!--    </span>-->

    <!-- </component> -->
    <!-- 聊天入口 -->
<!--    <span-->
<!--      @click="openChat"-->
<!--      class="header-notice top-icon"-->
<!--      :class="{ active: currentActive === 3, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      :style="{-->
<!--        background: currentActive === 3 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '',-->
<!--        color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff',-->
<!--      }"-->
<!--      @mouseenter="mouseEnter(3)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 3 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-liaotian"> </icon-font>-->
<!--      <span v-if="currentActive === 3">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_LS_fe21f`, '聊天') }}</span>-->
<!--    </span>-->
    <!-- 客服入口 -->
<!--    <span-->
<!--      @click="fetchCustomer"-->
<!--      class="header-notice top-icon"-->
<!--      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 4 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"-->
<!--      :class="{ active: currentActive === 4, change: topMenu_light || dark_defaultColor_notTopMenu }"-->
<!--      @mouseenter="mouseEnter(4)"-->
<!--      @mouseleave="mouseLeave"-->
<!--    >-->
<!--      <icon-font-->
<!--        style="font-size: 16px; padding: 4px"-->
<!--        class="icon"-->
<!--        :style="{ color: currentActive === 4 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultColor : '' }"-->
<!--        type="icon-a&#45;&#45;1srm_kefuzhongxin"> </icon-font>-->
<!--      <span v-if="currentActive === 4">{{ $srmI18n(`${$getLangAccount()}#i18n_dict_qB_b7fab`, '客服') }}</span>-->
<!--    </span>-->
    <span
      class="header-notice msg"
      :style="{ color: topMenu_light || dark_defaultColor_notTopMenu ? defaultColor : '#fff', background: currentActive === 5 && (topMenu_light || dark_defaultColor_notTopMenu) ? defaultBgColor : '' }"
      :class="{ active: currentActive === 5, change: topMenu_light || dark_defaultColor_notTopMenu }"
      @mouseenter="mouseEnter(5)"
      @mouseleave="mouseLeave">
      <header-notice
        :currentActive="currentActive"
        :changeBg="topMenu_light || dark_defaultColor_notTopMenu"
        :defaultColor="defaultColor" />
    </span>

    <!-- 语言切换 -->
<!--    <div class="user-menu-lang action">-->
<!--      <lang-switch />-->
<!--    </div>-->
    <a-dropdown>
      <span class="action action-full ant-dropdown-link user-dropdown-menu user-dropdown-menu-wrap">
        <a-avatar
          class="avatar"
          size="small"
          :src="getAvatar()" />
        <span class="user-menu-nickname"
        >{{ $srmI18n(`${$getLangAccount()}#i18n_title_welcome`, '欢迎您') }}，<span :title="nickname()">{{ nickname() }}</span></span
        >
      </span>
      <a-menu
        slot="overlay"
        class="user-dropdown-menu-wrapper">
        <a-menu-item
          key="2"
          @click="systemSetting">
          <a-icon type="tool" />
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_alert_iDGm_2f5ae5cd`, '整体风格') }}</span>
        </a-menu-item>
        <a-menu-item
          key="3"
          @click="personalSettings">
          <a-icon type="setting" />
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_personalSettings`, '个人设置') }}</span>
        </a-menu-item>
        <!-- <a-menu-item
          key="4"
          @click="updatePassword"
        >
          <a-icon type="setting" />
          <span>密码修改</span>
        </a-menu-item> -->
        <a-menu-item key="5">
          <a
            href="javascript:;"
            @click="handleLogout">
            <a-icon type="logout" />
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_layOutLogin`, '退出登录') }}</span>
          </a>
        </a-menu-item>
      </a-menu>
    </a-dropdown>
    <user-password ref="userPassword" />
    <setting-drawer
      ref="settingDrawer"
      :closable="true"
      :title="$srmI18n(`${$getLangAccount()}#i18n_alert_iDGm_2f5ae5cd`, '整体风格')" />
  </div>
</template>

<script>
import UserPassword from './UserPassword'
import SettingDrawer from '@/components/setting/SettingDrawer'
import layIM from '@/utils/im/layIM.js'
import HeaderNotice from './HeaderNoticeNew'
import { mapActions, mapGetters, mapState } from 'vuex'
import { mixin, mixinDevice } from '@/utils/mixin.js'
import { getAction } from '@/api/manage'
import LangSwitch from '@comp/page/langSwitch'
import { ACCESS_TOKEN, USER_ELS_ACCOUNT } from '@/store/mutation-types'
import { srmI18n, getLangAccount } from '@/utils/util'
const HOMEPAGE = process.env.VUE_APP_CONFIG_COMPANY_HOME_PAGE
export default {
    name: 'UserMenu',
    mixins: [mixinDevice, mixin],
    components: {
        HeaderNotice,
        UserPassword,
        SettingDrawer,
        LangSwitch
    },
    props: {
        theme: {
            type: String,
            required: false,
            default: 'dark'
        }
    },
    data () {
        return {
            currentActive: 0,
            searchMenuOptions: [],
            searchMenuComp: 'span',
            searchMenuVisible: true,
            selectedMenuUrl: null,
            selectedMenu: '',
            showPurchaseShoppingMall: this.$hasOptAuth('dashboard:mall:purchase'),
            showSupperShoppingMall: this.$hasOptAuth('dashboard:mall:supper')
        }
    },
    computed: {
        ...mapState({
            // 后台菜单
            permissionMenuList: (state) => state.user.permissionList,
            systemLayout: (state) => state.app.layout,
            systemTheme: (state) => state.app.theme
        }),
        topMenu_light () {
            return this.layoutMode === 'topmenu' && this.navTheme === 'light'
        },
        dark_defaultColor_notTopMenu () {
            return this.setDarkThemeDefaulColor && this.layoutMode !== 'topmenu'
        },
        // 获取项目的主题色
        defaultBgColor () {
            const bgColor = this.primaryColor + '17'
            return bgColor
        },
        defaultColor () {
            return this.primaryColor
        }
    },
    created () {
        let lists = []
        this.searchMenus(lists, this.permissionMenuList)
        this.searchMenuOptions = [...lists]
    },
    methods: {
        mouseEnter (i) {
            this.currentActive = i
        },
        mouseLeave () {
            this.currentActive = 0
        },
        
        //跳转到语音转文字
        audioTransform () {
            let self = this     
            let token =  this.$store.getters.token      
            var layerIndex = window.layui.layer.open({
                type: 2,
                shade: 0.1,
                shadeClose: true,
                title: '语音助手',
                btn: ['开始说话', srmI18n(`${getLangAccount()}#i18n_field_ML_b7804`, '完成')],
                btnAlign: 'c',
                yes: function (index, layero) {
                    const iframeWin = window[layero.find('iframe')[0]['name']]
                    iframeWin && iframeWin.recStart()
                }
                , btn2: function (index, layero) {
                    var iframeWin = window[layero.find('iframe')[0]['name']]                    
                    iframeWin && iframeWin.recStop(function (data) {
                        if (data && data.success) {
                            if (data?.error && data.error == 1) {
                                window.layer.msg(data.text)
                            } else {
                                const { result } = data
                                if (result.optType && result.optType == 'error') {
                                    window.layer.msg(result.optObject)
                                } else {
                                    window.layer.close(layerIndex)
                                    if (result.optType == 'openMenu') {
                                        if (result.optObject) {
                                            if (result.optObject.internalOrExternal) {
                                                window.open(result.optObject.url+`&token=${token}`, '_blank')
                                            } else {
                                                self.$router.push({
                                                    path: result.optObject.url
                                                })
                                            }                                            
                                        }                                        
                                    }
                                }
                            }
                        }
                    }, 'openMenu')
                    return false
                },
                success: function (layero, index) {
                    layero.find('.layui-layer-btn0').css({
                        'pointer-events': 'none',
                        opacity: 0.3
                    })
                    var iframeWin = window[layero.find('iframe')[0]['name']]
                    iframeWin && iframeWin.recOpen(function () {
                        layero.find('.layui-layer-btn0').css({
                            'pointer-events': 'auto',
                            opacity: 1
                        })
                    })
                },
                area: ['350px', '230px'],
                closeBtn: 1,
                resize: false,
                content: `${location.origin}/im/layim-v3.9.6/dist/css/modules/layim/html/voiceMorphing.html` //这里content是一个URL，如果你不想让iframe出现滚动条，你还可以content: ['http://sentsin.com', 'no']
            })
            setTimeout(function (){
                var btnSay = document.querySelector('.layui-layer.layui-layer-iframe .layui-layer-btn0')
                btnSay && btnSay.click()
            }, 1000)
        },
        // 跳转到企业主页
        toTanentPageHome () {
            let elsAccount = this.$ls.get(USER_ELS_ACCOUNT)
            window.open(`${HOMEPAGE}?elsAccount=${elsAccount}`, '_blank')
        },
        // 跳转到内采商城-区分采购方 dashboard:mall:purchase和供应方 dashboard:mall:supper
        toShoppingMall (siteType) {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VCtRLEHAAeCnXL_541dccbb`, '请点击确认登录企企通内采商城 '),
                onOk () {
                    const url = '/enterprise/elsEnterpriseInfo/getLoginMiningUrl'
                    const param = {}
                    param.siteType = siteType || '1'
                    // if (that.$hasOptAuth('dashboard:mall:purchase')) {
                    //     param.siteType = '1'
                    // }
                    // if (that.$hasOptAuth('dashboard:mall:supper')) {
                    //     param.siteType = '3'
                    // }
                    getAction(url, param).then((res) => {
                        if (!res.success) {
                            that.$message.error(res.message)
                            return
                        }
                        window.open(res.message)
                    })
                },
                onCancel () {}
            })
        },
        //跳转采购网
        toWebProcurement () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VCtRLEHAAenRTWW_b799d239`, '请点击确认登录企企通采购网 '),
                onOk () {
                    const url = '/enterprise/elsEnterpriseInfo/getLoginB2BUrl'
                    getAction(url, {}).then((res) => {
                        if (!res.success) {
                            that.$message.error(res.message)
                            return
                        }
                        window.open(res.message)
                    })
                },
                onCancel () {}
            })
        },
        //跳转企企通供应商赋能
        toWebFinancialCloud () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VCtRLEHAAeRdXBO_fd63acee`, '请点击确认登录企企通供应商赋能 '),
                onOk () {
                    const url = '/enterprise/elsEnterpriseInfo/getLoginFinancialCloud'
                    getAction(url, {}).then((res) => {
                        if (!res.success) {
                            that.$message.error(res.message)
                            return
                        }
                        window.open(res.message)
                    })
                },
                onCancel () {}
            })
        },
        fetchCustomer () {
            this.$emit('customerTap')
        },
        // 打开聊天
        openChat () {
            let LayimMain = (layIM.im.LAYIM && layIM.im.LAYIM.getLayimMain && layIM.im.LAYIM.getLayimMain()) || null
            if (!LayimMain) {
                // chat相关组件没有加载完
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_chatComponentLoading`, '聊天组件在拼命加载中~'))
                return
            }
            let chatSign = LayimMain.css('display')
            if (chatSign == 'none') {
                LayimMain.show()
            } else {
                LayimMain.hide()
            }
        },
        filterOption (input, option) {
            return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
        },
        showClick () {
            this.searchMenuVisible = !this.searchMenuVisible
        },
        hiddenClick () {
            this.shows = false
        },
        // update_begin author:sunjianlei date:******** for: 解决外部链接打开失败的问题
        searchMethods (value) {
            let route = this.searchMenuOptions.filter((item) => item.id === value)[0]
            this.selectedMenuUrl = route.path || ''
            this.selectedMenu = value
            if (route.meta.internalOrExternal === true || route.component.includes('layouts/IframePageView')) {
                let token = this.$ls.get(ACCESS_TOKEN)
                window.open(route.meta.url + `?token=${token}`, '_blank')
            } else {
                this.$router.push({ path: route.path })
            }
            // this.searchMenuVisible = false
        },
        /* update_begin author:zhaoxin date:20191129 for: 做头部菜单栏导航*/
        searchMenus (arr, menus) {
            for (let i of menus) {
                if (!i.hidden && 'layouts/RouteView' !== i.component) {
                    arr.push(i)
                }
                if (i.children && i.children.length > 0) {
                    this.searchMenus(arr, i.children)
                }
            }
        },
        ...mapActions(['Logout']),
        ...mapGetters(['nickname', 'avatar', 'userInfo']),
        getAvatar () {
            return `${this.avatar()}`
        },
        handleLogout () {
            const that = this

            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_areYouSureYouWantLogOff`, '真的要注销登录吗 ?'),
                onOk () {
                    return that
                        .Logout({})
                        .then(() => {
                            window.location.href = '/'
                            //window.location.reload()
                        })
                        .catch((err) => {
                            that.$message.error({
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_errors`, '错误'),
                                description: err.message
                            })
                        })
                },
                onCancel () {}
            })
        },
        updatePassword () {
            let elsAccount = this.userInfo().elsAccount
            let subAccount = this.userInfo().subAccount
            this.$refs.userPassword.show(elsAccount, subAccount)
        },
        systemSetting () {
            this.$refs.settingDrawer.showDrawer()
        },
        personalSettings () {
            this.$router.push({ path: '/personalSettingsIndex' })
        }
    },
    watch: {
        $route: function (newRoute) {
            // 处理菜单查找问题
            if (this.selectedMenu) {
                this.selectedMenu = newRoute.path && newRoute.path == this.selectedMenuUrl ? this.selectedMenu : ''
            }
        }
    }
}
</script>
<style lang="less" scoped>
.user-wrapper .search-input {
    // width: 180px;
    // color: inherit;

    :deep(.ant-select-selection) {
        background-color: inherit;
        border: 0;
        border-bottom: 1px solid white;
        &__placeholder,
        &__field__placeholder {
            color: inherit;
        }
    }
}
.user-wrapper .search-box {
    color: #858ebd;
    display: inline-flex;
    align-items: center;
    height: 45px;
    // padding-left: 42px;
    .inner {
        padding-right: 14px;
        border-radius: 20px;
        background: #f5f5fa;
        height: 28px;
        display: inline-flex;
        justify-content: center;
        :deep(.ant-select-dropdown-menu-item) {
            font-size: 13px;
        }
    }
    .search-input {
        width: 110px;
        height: 28px;
        opacity: 1;
        background: #f5f5fa;
        border-radius: 16px;
    }
    .search-button {
        font-size: 20px;
        color: #858ebd;
        width: 42px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    :deep(.ant-select) {
        .ant-select-selection__rendered {
            height: 28px;
            margin-left: 0;
            margin-right: 0;
            font-size: 13px;
        }
        .ant-select-selection--single {
            height: 28px;
        }
        .ant-select-selection {
            box-shadow: none;
        }
        .ant-select-selection-selected-value {
            line-height: 28px;
        }
    }
}

.user-wrapper .toWebBidding {
    > span {
        vertical-align: middle;
    }
    > img {
        height: 20px;
        width: 20px;
        // margin-right: 10px;
    }
}
.user-menu-lang {
    :deep(.ant-select-selection) {
        width: 62px;
    }
}
.header-notice {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.26);
    border-radius: 50%;
    display: flex;
    margin-left: 16px;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    // &:hover{
    //     width: unset;
    //     padding: 0 12px;
    //     border-radius: 42px;
    //     transition: padding 0.4s;
    // }
}
.active {
    width: unset;
    padding: 0 12px;
    border-radius: 42px;
    transition: padding 0.5s;
}
.change {
    background: #f5f5f5;
    .icon {
        color: #aeb1bb;
    }
}
.msg {
    margin-right: 10px;
}
.user-menu-nickname {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 300px;
}
.user-wrapper .action :deep(.avatar) {
    margin-bottom: 0px !important;
}
.user-wrapper .user-dropdown-menu-wrap {
    display: flex !important;
    align-items: center !important;
}
.user-dropdown-menu-wrapper.ant-dropdown-menu .ant-dropdown-menu-item {
    width: 100%;
}
</style>
<style scoped>
.logout_title {
    color: inherit;
    text-decoration: none;
}
</style>
