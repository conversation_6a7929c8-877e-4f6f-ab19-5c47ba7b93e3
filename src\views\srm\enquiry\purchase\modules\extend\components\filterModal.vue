<template>
  <div
    class="filterModal"
    @blur="showModal = !showModal">
    <a-input
      v-model="keywords"
      allowClear
      style="width: 238px; position: relative; z-index: 1000;"
      @click="showModal = true">
      <a-icon
        slot="addonAfter"
        @click="handleOk"
        type="search" />
    </a-input>
    <div
      class="filterBox"
      v-show="showModal">
      <div>
        
        <div class="checkboxList">
          <a-checkbox-group
            v-model="selectList"
            name="checkboxgroup"
            :options="plainOptions"
          />
        </div>
      </div>
      <div>
        <a-divider style="margin: 4px 0;" />
        <div class="flex">
          <a-button
            class="flex1"
            type="link"
            @click="handleOk">
            确定
          </a-button>
          <a-button
            class="flex1"
            type="link"
            @click="handleReset">
            重置
          </a-button>
        </div>
      </div>
    </div>
    <div
      class="mask"
      v-show="showModal"
      @click="showModal = !showModal">
    </div>
  </div>
</template>
<script>
export default {
    props: {
        filtersList: {
            type: Array,
            default: () => {
                return []
            }
        }
    },
    computed: {
        plainOptions () {
            if (!this.keywords) return this.filtersList
            return this.filtersList.filter(item => item.label.indexOf(this.keywords) != -1)
        }
    },
    data () {
        return{
            keywords: '',
            selectList: [],
            showModal: false
        }
    },
    methods: {
        handleOk () {
            this.$emit('handleFilter', this.selectList)
            this.showModal = false
        },
        handleReset () {
            this.selectList = []
            this.showModal = false
            this.$emit('reset')
        }
    }
}
</script>
<style lang="less" scoped>
    .filterModal{
        vertical-align: bottom;
        position: relative;
        display: inline-block;
    }
    .filterBox{
        background: #fff;
        padding: 0 4px;
        position: absolute;
        top: 34px;
        padding: 4px;
        border: 1px solid #ccc;
        left: -4px;
        z-index: 1000;
    }
    .checkboxList{
        max-height: 300px;
        padding:6px 2px 0;
        overflow-y: auto;
    }
    .flex{
        display: flex;
    }
    .flex1{
        flex: 1
    }
    .mask{
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 999;
        height: 100%;
    }
</style>