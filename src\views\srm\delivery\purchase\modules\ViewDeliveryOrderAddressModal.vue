<template>
  <div>
    <detail-page
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :pageData="pageData"></detail-page>

    <a-modal
    v-drag    
      forceRender
      :visible="editRowModal"
      title="查看"
      :width="800"
      @ok="closeEditModal"
      @cancel="closeEditModal">
      <j-editor
        v-if="editRowModal"
        v-model="currentItemContent"></j-editor>
    </a-modal>
  </div>
</template>

<script>
import detailPage from '@comp/template/detailPage'
import JEditor from '@comp/els/JEditor'
export default {
    name: 'ViewDeliveryOrderAddressModal',
    props: {
        currentEditRow: {
            type: Object,
            default: null
        }
    },
    components: {
        detailPage,
        JEditor
    },
    data () {
        return {
            editRowModal: false,
            currentItemContent: '',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shippingAddressDetail`, '收货地址详情'),
            confirmLoading: false,
            pageData: {
                panels: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        content: {
                            type: 'form',
                            ref: 'mainForm',
                            list: [
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_organizationType`, '组织类型'),
                                    fieldName: 'organizationType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_organizationType`, '组织类型'),
                                    dictCode: 'orgCategoryCode'
                                },
                                {
                                    type: 'select',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_organizationName`, '组织名称'),
                                    fieldName: 'organizationName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectOrganizationTips`, '请选择组织'),
                                    dictCode: 'selectModal',
                                    sourceUrl: '',
                                    columns: '',
                                    params: '',
                                    selectModel: ''
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receiveAddress`, '收货地址'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterRemark`, '请输入备注')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_consignee`, '收货人'),
                                    fieldName: 'consignee',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterConsignee`, '请输入收货人')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_receivedPhone`, '收货人联系电话'),
                                    fieldName: 'purchasePhone',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPurchasePhone`, '请输入收货人联系电话')
                                },
                                {
                                    type: 'addressCascader',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProvince`, '选择省份'),
                                    fieldName: 'selectAddress',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectProvince`, '选择省份'),
                                    state: 'provinceId',
                                    city: 'cityId',
                                    area: 'countyId'
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_adress`, '地址'),
                                    fieldName: 'address',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseEnterAdress`, '请输入地址')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_consigneeEmail`, '收货人的邮箱'),
                                    fieldName: 'consigneeEmail',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterConsigneeEmail`, '请输入收货人的邮箱')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_deliverCompany`, '收货公司'),
                                    fieldName: 'zip',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterDeliverCompany`, '请输入收货公司')
                                },
                                {
                                    type: 'input',
                                    label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_postCode`, '邮政编码'),
                                    fieldName: 'phone',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterPostalCode`, '请输入邮政编码')
                                }
                            ],

                            button: []
                        }
                    },
                    
                    {
                        title:this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_RHzLLD_f74c9a47`, '工厂仓位维护') ,
                        content: {
                            type: 'table',
                            ref: 'purchaseFactoryLocationList',
                            height: 400,
                            columns: [
                                { type:'checkbox',width: 40},
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                { field: 'factory', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RH_bb23d`, '工厂'), editRender:{name:'$select',dictCode:'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"'},width: 120},
                                { field: 'storageLocation', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageCode`, '仓位编码'), editRender:{name:'$input'},width: 120},
                                { field: 'storageLocationName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_storageName`, '仓位名称'),editRender:{name:'$input'}, width: 120 },
                                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qMSWR_c6d6d7bd`, '可卸货数量'),editRender:{name:'$input',props:{type:'number'}}, width: 120 },
                            ],
                            toolbarButton: [
                                {
                                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachmet`, '新增'), 
                                    type: 'primary', 
                                    clickFn: this.inSertCallBack, 
                                    showCondition:this.showAdd,
                                    },
                                { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatche`, '删除'), clickFn: this.deleteBatch,showCondition:this.showAdd },
                            ]
                            }
                    }
                ],
                url: {
                    detail: '/delivery/deliveryOrderAddress/queryById'
                }
            }

        }
    },
    mounted () {

    },
    created (){

    },
    methods: {
        hide () {
            this.$emit('hide')
        },
        editRow (row) {
            this.currentItemContent = row.msgContent
            this.editRowModal = true
        },
        closeEditModal () {
            this.editRowModal = false
        },
        
        showAdd(){
            if(this.pageData.form.organizationType === 'factory' && this.pageData.form.organizationName){
                return true
            }else return false
        },
        inSertCallBack(){
            let insertRow = {factory:this.defaultInsertFactory,storageLocation:'',storageLocationName:'',quantity:''}
            this.$refs.editPage.$refs.purchaseFactoryLocationList[0].insert(insertRow)
        },
        deleteBatch(){
            this.$refs.editPage.$refs.factory[0].removeCheckboxRow()
        }
    }
}
</script>
