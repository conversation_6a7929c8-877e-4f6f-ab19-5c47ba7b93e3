<template>
  <div style="height: 100%">
    <list-layout
      v-show="!showEditPage && !showDetailPage && !showHisPage && !showEsignPage && !showContractLockPage && !showEsignV3Page && !showFadadaPage && !showFadadaOnePage"
      ref="listPage"
      :pageData="pageData"
      :tabsList="tabsList"
      :currentEditRow="currentEditRow"
      :url="url"
      @afterChangeTab="handleAfterChangeTab"
    />
    <!-- 编辑 -->
    <purchaseContractHead-modal
      v-if="showEditPage"
      :currentEditRow.sync="currentEditRow"
      @hide="hideEditPageNew"
    />
    <!-- 详情 -->
    <viewPurchaseContractHead-modal
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :allowEdit="allowViewToEdit"
      @toEdit="handleEditFromViewPage"
      @hide="hideEditPage"
    />
    <!-- 历史记录 -->
    <purchaseContractHeadHis-modal
      v-if="showHisPage"
      :current-edit-row="currentEditRow"
      @hide="hideHisPage"
    />
    <!-- 发起签章 -->
    <EsignFlowEdit
      v-if="showEsignPage"
      :current-edit-row="currentEditRow"
      @hide="hideEsignPage"
    />
    <PurchaseEsignV3FlowEdit
      v-if="showEsignV3Page"
      :current-edit-row="currentEditRow"
      @hide="showEsignV3Page = false"
    />
    <ClContractEdit
      v-if="showContractLockPage"
      :current-edit-row="currentEditRow"
      @hide="hideContractLockPage"
    />
    <!-- 查看签署流程 -->
    <purchase-order-e-sign-select-modal
      ref="viewESignList"
      :columns="viewEsignColumns"
      :title="viewEsignTitle"
    />
    <!-- 归档文件下载 多版本时启动-->
    <purchase-order-e-sign-download-modal
      ref="downloadESignList"
      :columns="downloadEsignColumns"
      :title="downloadEsignTitle"
    />
    <EditFadadaSignTaskPurchaseModal
      v-if="showFadadaPage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideFadadaPage"
    />
    <EditFadadaSignTaskPurchaseOneModal
      v-if="showFadadaOnePage"
      :current-edit-row="currentEditRow"
      @handleChidCallback="handleChidCallback"
      @hide="hideFadadaPage"
    />
    <!--弹窗选择-->
    <field-select-modal ref="fieldSelectModal" />
    <!--比对合同标信息-->
    <a-modal v-model="showVSItemExcel" title="导入合同标信息进行比对" :footer="null" @cancel="handleCancel">
        <template>
            <div>
                <p>导入说明</p>
                <div>
                    <ul>
                        <li>单次导入数量建议控制在5000以内;</li>
                        <li>文件大小不能超过 10M;</li>
                        <li>仅支持 *.xls、*.xlsx 格式文件;</li>
                    </ul>
                </div>
            </div>
            <div>
                <a-upload-dragger
                    name="file"
                    :action="UploadVsUrl"
                    @change="handleChangeUpload"
                    :before-upload="beforeUploadFile"
                    :file-list="fileList"
                >
                    <p class="ant-upload-drag-icon">
                        <a-icon type="inbox" />
                    </p>
                    <p class="ant-upload-text">
                        点击或把文件拖拽到这个区域上传
                    </p>
                </a-upload-dragger>
            </div>
        </template>
    </a-modal>
  </div>
</template>
<script>
//import PurchaseContractHeadModal from './modules/EditPurchaseContractHeadModal'
import PurchaseContractHeadModal from './modules/EditPurchaseContractHeadModalNew'
//import ViewPurchaseContractHeadModal from './modules/ViewPurchaseContractHeadModal'
import ViewPurchaseContractHeadModal from './modules/ViewPurchaseContractHeadModalNew'
import PurchaseContractHeadHisList from './PurchaseContractHeadHisList'
import EsignFlowEdit from '@/views/srm/esign/modules/EsignFlowEdit'
import ClContractEdit from '@/views/srm/contractLock/modules/ClContractEdit'
import PurchaseEsignV3FlowEdit from '@/views/srm/esignV3/purchase/modules/PurchaseEsignV3FlowEdit'
import fieldSelectModal from '@comp/template/fieldSelectModal'

import { ListMixin } from '@comp/template/list/ListMixin'
import { getAction, httpAction, postAction } from '@/api/manage'
import layIM from '@/utils/im/layIM.js'
import PurchaseOrderESignSelectModal from '@views/srm/order/purchase/PurchaseOrderESignSelectModal'
import PurchaseOrderESignDownloadModal from '@views/srm/order/purchase/PurchaseOrderESignDownloadModal'
import EditFadadaSignTaskPurchaseModal from '@/views/srm/fadadaFasc/purchase/modules/EditFadadaSignTaskPurchaseModal'
import EditFadadaSignTaskPurchaseOneModal from '@/views/srm/fadadaFasc/purchase/modules/EditFadadaSignTaskPurchaseOneModal'
import Vue from 'vue'
import { USER_COMPANYSET } from '@/store/mutation-types'
import { handleDemandPool } from '@/utils/util'
import { REPORT_ADDRESS } from '@/utils/const.js'

export default {
  mixins: [ListMixin],
  components: {
    'purchaseContractHead-modal': PurchaseContractHeadModal,
    'viewPurchaseContractHead-modal': ViewPurchaseContractHeadModal,
    'purchaseContractHeadHis-modal': PurchaseContractHeadHisList,
    EsignFlowEdit,
    PurchaseOrderESignSelectModal,
    PurchaseOrderESignDownloadModal,
    PurchaseEsignV3FlowEdit,
    ClContractEdit,
    fieldSelectModal,
    EditFadadaSignTaskPurchaseModal,
    EditFadadaSignTaskPurchaseOneModal
  },
  data() {
    return {
      showHisPage: false,
      showEsignPage: false,
      showEsignV3Page: false,
      showContractLockPage: false,
      showFadadaOnePage: false,
      showFadadaPage: false,
      showVSItemExcel:false,
      UploadVsUrl:'',
      fileList:[],
      pageData: {
        businessType: 'contract',
        formField: [
          {
            type: 'input',
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
            fieldName: 'keyWord',
            placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_enterContractCodeOrName`, '请输入合同编码或合同名称')
          }
        ],
        button: [
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
            authorityCode: 'contract#purchaseContractHead:add',
            icon: 'plus',
            clickFn: this.handleAdd,
            type: 'primary'
          },
            {label: '合同头导入', icon: 'import', folded: false, type: 'upload', clickFn: this.importHeadExcel, authorityCode: "contractHead#inputExcel"},
            {label: '合同标的导入', icon: 'import', folded: false, type: 'upload', clickFn: this.importItemExcel, authorityCode: "contractItem#inputExcel"},
            // {label: '比对合同标信息', icon: 'import', folded: false, type: 'upload', clickFn: this.importVSItemExcel, authorityCode: "vsContractItem#inputExcel"},
            {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '导出'),
            authorityCode: 'contract#purchaseContractHead:export',
            icon: 'download',
            clickFn: this.handleExportXls
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
            icon: 'setting',
            clickFn: this.settingColumns
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_helpText`, '帮助说明'),
            icon: 'file-text',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpText
          },
          {
            label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachmentExplain`, '附件说明'),
            icon: 'file-pdf',
            folded: true,
            isDocument: true,
            clickFn: this.showHelpPDF
          }
        ],
        form: {
          keyWord: '',
          contractStatus: ''
        },
        optColumnList: [
          {
            type: 'view',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'),
            authorityCode: 'contract#purchaseContractHead:view',
            clickFn: this.handleView
          },
          {
            type: 'edit',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'),
            authorityCode: 'contract#purchaseContractHead:edit',
            clickFn: this.handleEdit,
            allow: this.showEditCondition
          },
          // {
          //   type: 'view',
          //   title: '提交OA审批',
          //   authorityCode: 'contract#purchaseContractHead:view',
          //   clickFn: this.handleView,
          //   allow: this.showOAButton
          // },
          {
            type: 'copy',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_updateVersion`, '更新版本'),
            authorityCode: 'contract#purchaseContractHead:upgradeVersion',
            clickFn: this.changeVersion,
            allow: this.showChangeVersionCondition
          },
          {
            type: 'delete',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'),
            authorityCode: 'contract#purchaseContractHead:delete',
            clickFn: this.handleDelete,
            allow: this.showDelCondition
          },
          {
            type: '',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
            authorityCode: 'contract#purchaseContractHead:editHead',
            clickFn: this.updateItem
          },
          {
            type: 'history',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_numberOfHistory`, '历史记录'),
            clickFn: this.handleHis,
            allow: this.showHisCondition
          },
          {
            type: 'database',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_initSignature`, '发起签署'),
            authorityCode: 'contractLock#signFlow:edit',
            clickFn: this.esignBtn,
            allow: this.showSignCondition,
            showCondition: this.showEsignBtn
          },
          {
            type: 'edit',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'),
            authorityCode: 'contractLock#signFlow:list',
            clickFn: this.handleESignView,
            allow: this.allowViewESign,
            showCondition: this.showEsignBtn
          },
          {
            type: 'edit',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKCPLAQI_7c722b1b`, '下载电签归档文件'),
            authorityCode: 'esign#esignFlow:down',
            clickFn: this.flowFileDownload,
            allow: this.allowDownloadESign,
            showCondition: this.showEsignBtn
          },
          {
            type: 'database',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_LAWuCPW_d31d6579`, '归档（非电签）'),
            authorityCode: 'contract#purchaseContractHead:archive',
            clickFn: this.archive,
            allow: this.showArchiveCondition
          },
          // {type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_zipDown`, '归档文件下载'), clickFn: this.flowFileDownload, allow: this.showFlowFileDownload},
          //{
          //  type: 'close-circle',
          //  title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
          //  authorityCode: 'contract#purchaseContractHead:cancel',
          //  clickFn: this.cancel,
          //  // allow: this.showCancelCondition
          //  allow: () => false
          //},
          {
            type: 'copy',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copy`, '复制'),
            authorityCode: 'contract#purchaseContractHead:copy',
            clickFn: this.handleCopy
          },
          {
            type: 'chat',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_communicate`, '沟通'),
            clickFn: this.handleChat,
            allow: this.allowChat
          },
          {
            type: 'record',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmRecord`, '记录'),
            clickFn: this.handleRecord
          },
          {
            type: 'edit',
            title: '撤回',
            authorityCode: 'contract#purchaseContractHead:purchaseCancelContract',
            clickFn: this.handlePurchaseCancelContract
          },
        ],
        optColumnWidth: 300
      },
      url: {
        list: '/contract/purchaseContractHead/list',
        add: '/contract/purchaseContractHead/add',
        delete: '/contract/purchaseContractHead/delete',
        editHead: '/contract/purchaseContractHead/editHead',
        deleteBatch: '/contract/purchaseContractHead/deleteBatch',
        importExcelUrl: 'contract/purchaseContractHead/importExcel',
        changeVersion: '/contract/purchaseContractHead/upgradeVersion',
        archive: '/contract/purchaseContractHead/archive',
        cancel: '/contract/purchaseContractHead/cancel',
        copy: '/contract/purchaseContractHead/copy',
        flowFileDownload: '/esign/elsEsign/busSignFileDownload',
        clFlowFileDownload: '/contractLock/elsClContract/downloadArchiveByRelationId',
        columns: 'purchaseContractHeadList',
        exportXlsUrl: '/contract/purchaseContractHead/exportXls',

        ExcelHead: 'purchaseContractHeadImportRpcExcel',
        ExcelItem: 'purchaseContractItemImportRpcExcel',
      },
      tabsList: [],
      viewEsignColumns: [
        { type: 'checkbox', width: 40, align: 'center' },
        {
          type: 'seq',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
          width: 60,
          align: 'center'
        },
        {
          field: 'createTime',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAKI_2839c904`, '发起时间'),
          align: 'center'
        },
        {
          field: 'filesName',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
          align: 'center'
        },
        {
          field: 'sendStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sendStatus`, '发送状态'),
          align: 'center'
        },
        {
          field: 'signerVindicateStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theSignatoryMaintainsTheState`, '签署人维护状态'),
          align: 'center'
        },
        {
          field: 'initiate_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_initiate`, '是否开启'),
          align: 'center'
        },
        {
          field: 'purchaseEsignStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseEsignStatus`, '采方签署状态'),
          align: 'center'
        },
        {
          field: 'saleEsignStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_saleEsignStatus`, '供方签署状态'),
          align: 'center'
        },
        {
          field: 'archiving_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_archiving`, '是否归档'),
          align: 'center'
        },
        {
          field: 'esignStatus_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_esignStatus`, '签署状态'),
          align: 'center'
        },
        {
          field: 'sendBack_dictText',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sendBack`, '是否退回'),
          align: 'center'
        },
        {
          field: 'grid_opration',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
          width: 120,
          align: 'center',
          slots: { default: 'grid_opration' }
        }
      ],
      viewEsignTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_mAPWQL_1184c464`, '查看签署流程'),
      downloadEsignColumns: [
        { type: 'checkbox', width: 40, align: 'center' },
        {
          type: 'seq',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'),
          width: 60,
          align: 'center'
        },
        {
          field: 'filesName',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'),
          align: 'center'
        },
        { field: 'updateTime', title: this.$srmI18n(`${this.$getLangAccount()}#`, '归档日期'), align: 'center' },
        {
          field: 'grid_opration',
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'),
          width: 120,
          align: 'center',
          slots: { default: 'grid_opration' }
        }
      ],
      downloadEsignTitle: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_IKLAQI_3fb14732`, '下载归档文件')
    }
  },
  computed: {
      allowViewToEdit() {
          if(!!this.currentEditRow) return !this.showEditCondition(this.currentEditRow);
          return false;
      }
  },
  mounted() {
    //this.serachTabs('srmReviewStatus', 'requestStatus')
    // this.serachTabs('srmContractStatus', 'contractStatus')
    this.serachCountTabs('/contract/purchaseContractHead/counts')
  },
  beforeRouteEnter(to, from, next) {
    if (Object.keys(to.query).length) {
      next((vm) => handleDemandPool(vm))
    } else {
      next()
    }
  },
  methods: {
      //比对导入的合同标信息
      importVSItemExcel(){
          this.showVSItemExcel = true
      },
      handleCancel(){
          this.showVSItemExcel = false
      },
      // 上传文件改变时的状态
      beforeUploadFile(file) {
          return new Promise((resolve) => {
              const isSize = file.size / 1024 / 1024
              const isExcel =
                  file.type ===
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'xlsx' ||
                  file.type === 'application/vnd.ms-excel'
              if (isSize > 10) {
                  this.$message.error('文件大小不可超过10M')
                  return false
              }
              if (!isExcel) {
                  this.$message.error('只支持上传excel文件')
                  return false
              }
              resolve(true)
          })
      },
      handleChangeUpload(info){
          let { fileList } = info
          if (info.file.status !== 'uploading') {
              if (info.fileList.length > 1) {
                  //限制只上传一个文件，再次上传时则替换(覆盖)以前的文件
                  info.fileList.splice(0, 1)
              }
              //info.file.response  这个是后端接口返回的数据
              // 文件上次格式错误
              if (info.file.response.res != 0) {
                  this.$message.error(info.file.response.msg)
                  this.fileList = []
                  return
              }
          }
          if (info.file.status === 'done') {
              this.$message.success('上传成功')
              this.showVSItemExcel = false
          } else if (info.file.status === 'error') {
              this.$message.error('上传失败')
          }
          this.fileList = [...fileList] //重点
      },
      formatTableData(data) {
          console.log('請求接口後格式化列表數據', data)
          data = data.map((item) => {
              if (item.totalTaxAmount === 0 || Math.abs(item.totalTaxAmount) > 0) {
                  item.totalTaxAmount = Number(item.totalTaxAmount).toFixed(2)
              }
              return item
          })
          return data;
      },
      importHeadExcel () {
          this.url.excelCode = this.url.ExcelHead
          this.excel(this.url.ExcelHead)
      },
      importItemExcel () {
          this.url.excelCode = this.url.ExcelItem
          this.excel(this.url.ExcelItem)
      },
      excel (code) {
          getAction('/base/excelHeader/getConfig/' + code, null).then(res => {
              if(res.success) {
                  let isPreview = res.result.previewData
                  let previewColumns = res.result.excelDetailList.map(item => {
                      return {
                          title: item.columnName,
                          field: item.columnCode,
                          width: 120,
                          dataType: item.dataType,
                          dataFormat: item.dataFormat
                      }
                  })
                  this.$refs.listPage.$refs.importExcel.open(isPreview, previewColumns, res.result.excelName, code,  this.handleBeforeImportExcel)
              }else {
                  this.$message.warning(res.message)
              }
          })
      },
    // 返回按钮
    hideEditPageNew() {
      let query = this.$route.query || {}
      if (query.linkFilter || query.open) {
        // 关闭超链接页面
        this.closeCurrent()
      }
      if (query.source == 'demand-pool') {
        this.$router.replace({ path: this.$route.path })
      }
      this.showEditPage = false
      this.showDetailPage = false
      this.showFadadaOnePage = false
      this.showFadadaPage = false
      this.$store.dispatch('SetTabConfirm', false)
      this.searchEvent(false)
      // 更新list页面tab count
      this.refreshCountTabs()
    },
    handleChat(row) {
      let { id } = row
      let recordNumber = row.contractNumber || id
      // 创建
      layIM.creatGruopChat({ id, type: 'PurchaseContractHead', url: this.url || '', recordNumber })
    },
    updateItem(row) {
      let item = {
        sourceUrl: '/account/elsSubAccount/list',
        params: {},
        columns: [
          {
            field: 'subAccount',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
            with: 150
          },
          {
            field: 'realname',
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'),
            with: 150
          }
        ]
      }
      this.currentRow = row
      this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel)
    },
    fieldSelectOk(data) {
      let that = this
      let elsBusinessTransferHis = {
        businessType: 'contract',
        businessTransferNumber: that.currentRow.contractNumber,
        businessTransferName: '综合采购合同' + that.currentRow.contractNumber + ':' + that.currentRow.contractName,
        transferEntity: (that.currentRow.purchasePrincipal ? that.currentRow.purchasePrincipal : '') + '->' + data[0].realname
      }
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_transfer`, '转办'),
        content: this.$srmI18n(`${this.$getLangAccount()}#`, '确认将此单据由 ' + that.currentRow.purchasePrincipal + ' 转办为') + ':' + data[0].subAccount + '_' + data[0].realname,
        onOk: function () {
          debugger
          that.currentRow.purchasePrincipal = data[0].subAccount + '_' + data[0].realname
          that.$set(that.currentRow, 'elsBusinessTransferHis', elsBusinessTransferHis)
          that.postUpdateData(that.url.editHead, that.currentRow)
        }
      })
    },
    allowChat(row) {
      if (row.contractStatus != '1') {
        return false
      } else {
        return true
      }
    },
    // 复制功能按钮
    handleCopy(row) {
      let that = this
      this.$confirm({
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_RLBR_38d5d63f`, '确认复制？'),
        onOk: () => {
          that.copyData(row)
        }
      })
    },
    // 复制数据请求接口
    copyData(row) {
      this.confirmLoading = true
      let param = { id: row.id }
      getAction(this.url.copy, param)
        .then((res) => {
          if (res.success) {
            this.$message.success(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_copySucceeded`, '复制成功！'))
            this.searchEvent()
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.confirmLoading = false
        })
    },
    handleExportXls() {
      this.$refs.listPage.handleExportXls(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaserContractHeader`, '采购方合同头'))
    },
    showHisCondition(row) {
      if (row.contractStatus === '1') {
        return true
      } else {
        if (row.contractVersion <= 1) {
          return true
        } else {
          return false
        }
      }
    },
    showChangeVersionCondition(row) {
      if (row.startSign == '1' && row.endSign != '2') {
        return true
      }
      if (row.contractStatus == '4') {
        return false
      } else {
        return true
      }
    },
    showOAButton(row) {
      // 合同状态 contractStatus = 3 ，供应商已确认，合同审核状态 auditStatus为 审批通过（2）或 无需审批（4），OA审批状态为 未审批（0），显示 提交OA审批 按钮
      if (row.contractStatus == '3' && (row.auditStatus == '2' || row.auditStatus == '4') && row.oaAuditStatus == 0 ) {
        return false
      }
      return true
    },
    showCancelCondition(row) {
      if (row.contractStatus === '5' && row.auditStatus === '2') {
        return false
      }
      if (row.contractStatus == '4' || row.contractStatus === '2') {
        return false
      } else {
        return true
      }
    },
    showEsignBtn() {
      let companySet = Vue.ls.get(USER_COMPANYSET) || {}
      let arr = companySet.electronicSignature?.split(',') || []
      for (let r of arr) {
        if (r == 'contract') {
          return true
        }
      }
      return false
    },
    showSignCondition(row) {
      if (row.sign == '1') {
        if (row.contractStatus === '3' && row.startSign !== '1') {
          return false
        }
        return true
      } else {
        return true
      }
    },
    showArchiveCondition(row) {
      if (row.contractStatus == '3' && row.sign !== '1') {
        return false
      } else {
        return true
      }
    },
    showEditCondition(row) {
      if (((row.contractStatus == '1' || row.contractStatus == '5') && row.auditStatus == '0') || row.auditStatus == '3') {
        return false
      } else {
        return true
      }
    },
    showDelCondition(row) {
      if ((row.contractStatus == '1' && row.auditStatus == '0') || row.auditStatus == '3') {
        return false
      } else {
        return true
      }
    },
    handleHis(row) {
      this.currentEditRow = row
      this.showHisPage = true
    },
    hideHisPage() {
      this.showHisPage = false
    },
    hideEsignPage() {
      this.showEsignPage = false
    },
    hideContractLockPage() {
      this.showContractLockPage = false
    },
    hideFadadaPage() {
      // this.showFadadaOnePage = false
      // this.showFadadaPage = false
      this.hideEditPageNew()
    },
    handleChidCallback() {
      this.hideFadadaPage()
    },
    submitCallBack(row) {
      this.currentEditRow = row
      this.showEditPage = true
      this.showDetailPage = false
      this.searchEvent()
    },
    handleViewESign(row) {
      let queryParams = { relationId: row.id }
      this.$refs.viewESignList.open(queryParams, 'esign/elsEsign/list')
    },
    handleDownloadESign(row) {
      let queryParams = { relationId: row.id }
      this.$refs.downloadESignList.open(queryParams, 'esign/elsEsign/list')
    },
    handleESignView(row) {
      let signType = row.signType
      if (signType == 'contractLock') {
        getAction('/contractLock/elsClContract/list', { relationId: row.id }).then((res) => {
          if (res.success) {
            console.log('res', res.result.records[0].id)
            this.$router.push({
              path: '/srm/contractLock/ClContractList',
              query: { id: res.result.records[0].id, pageShow: 'true' }
            })
            this.visible = false
          } else {
            this.$message.warning(res.message)
          }
        })
      } else if (signType == 'esignV3') {
        getAction('/esignv3/elsEsignV3Flow/list', { relationId: row.id }).then((res) => {
          if (res.success) {
            console.log('res', res.result.records[0].id)
            this.$router.push({
              path: '/srm/esignV3/purchase/PurchaseEsignV3List',
              query: { id: res.result.records[0].id, pageShow: 'true' }
            })
            this.visible = false
          } else {
            this.$message.warning(res.message)
          }
        })
      } else if (signType == 'fadada') {
        getAction('/electronsign/fadada/fadadaSignTaskPurchase/list', { busId: row.id }).then((res) => {
          if (res.success) {
            console.log('res', res.result.records[0].id)
            this.$router.push({
              path: '/srm/fadadaFasc/purchase/FadadaSignTaskPurchaseList',
              query: { id: res.result.records[0].id, pageShow: 'true' }
            })
            this.visible = false
          } else {
            this.$message.warning(res.message)
          }
        })
      } else {
        getAction('esign/elsEsign/list', { relationId: row.id }).then((res) => {
          if (res.success) {
            this.$router.push({
              path: '/srm/esign/EsignFlow',
              query: { id: res.result.records[0].id, pageShow: 'true' }
            })
            this.visible = false
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    },
    allowViewESign(row) {
      //返回 false是有权限true是没有权限
      if (row.sign == '1' && row.startSign == '1') {
        return false
      }
      return true
    },
    allowDownloadESign(row) {
      //返回 false是有权限true是没有权限
      if (row.sign == '1' && row.contractStatus == '6') {
        return false
      }
      return true
    },
    esignBtn(row) {
      let companySet = Vue.ls.get(USER_COMPANYSET) || {}
      let signType = companySet.signType

      let param = {
        relationId: row.id,
        toElsAccount: row.toElsAccount,
        purchaseName: row.purchaseName,
        supplierName: row.supplierName,
        busType: 'contract',
        documentName: row.contractName,
        busNumber: row.contractNumber,
        busVersion: row.contractVersion
      }

      switch (signType) {
        case 'contractLock':
          param.onlineSealed = '1'
          postAction('/contractLock/elsClContract/add', param).then((res) => {
            if (res.success) {
              this.currentEditRow = res.result
              this.showContractLockPage = true
            } else {
              this.$message.warning(res.message)
            }
          })
          break
        case 'esign':
          postAction('/esign/elsEsign/add', param).then((res) => {
            if (res.success) {
              this.currentEditRow = res.result
              this.showEsignPage = true
            } else {
              this.$message.warning(res.message)
            }
          })
          break
        case 'esignV3':
          postAction('/esignv3/elsEsignV3Flow/add', param).then((res) => {
            if (res.success) {
              this.currentEditRow = res.result
              this.showEsignV3Page = true
            } else {
              this.$message.warning(res.message)
            }
          })
          break
        case 'fadada':
          param = {
            busId: row.id,
            toElsAccount: row.toElsAccount,
            purchaseName: row.purchaseName,
            saleName: row.supplierName,
            busType: 'contract',
            signTaskSubject: row.contractName,
            busNumber: row.contractNumber
          }
          param.reportUrl = REPORT_ADDRESS
          param.token = this.$ls.get('Access-Token')
          postAction('/electronsign/fadada/fadadaSignTaskPurchase/add', param).then((res) => {
            if (res.success) {
              this.currentEditRow = res.result
              if (res.result.onlineSealed === '1') {
                this.showFadadaPage = true
              } else {
                this.showFadadaOnePage = true
              }
            } else {
              this.$message.warning(res.message)
            }
          })
          break
      }
    },
    cancelAuditCallBack() {
      this.showDetailPage = false
      this.showEditPage = true
      this.searchEvent()
    },
    changeVersion(row) {
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_versionUpdating`, '版本更新'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterVersionUpdatedContractCanModifiedNeedsApprovedSureUpdate`, '版本更新后可以修改合同但需重新审批，确认是否更新?'),
        onOk: function () {
          that.postData(row)
        }
      })
    },
    postData(row) {
      this.$refs.listPage.confirmLoading = true
      getAction(this.url.changeVersion, { id: row.id })
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            // 变更版本后进入 编辑页
            this.handleEdit(row)
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.confirmLoading = false
        })
    },
    archive(row) {
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LA_bf351`, '归档'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToArchive`, '确认是否归档?'),
        onOk: function () {
          that.postUpdateData(that.url.archive, row)
        }
      })
    },
    cancel(row) {
      let that = this
      this.$confirm({
        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'),
        content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmWhetherToVoid`, '确认是否作废?'),
        onOk: function () {
          that.postUpdateData(that.url.cancel, row)
        }
      })
    },
    postUpdateData(url, row) {
      this.$refs.listPage.confirmLoading = true
      httpAction(url, row, 'post')
        .then((res) => {
          if (res.success) {
            this.$message.success(res.message)
            this.searchEvent()
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.$refs.listPage.confirmLoading = false
        })
    },
    showFlowFileDownload(row) {
      //已归档，已发起签署流程，签署完成
      if (row.contractStatus === '6' && row.startSign === '1' && row.endSign === '2') {
        return false
      }
      return true
    },
    flowFileDownload(row) {
      let signType = row.signType
      console.log('signType:', signType)
      if (signType == 'contractLock') {
        getAction(this.url.clFlowFileDownload, { id: row.id, busType: 'contract' }).then((res) => {
          if (res.success) {
            if (!res.result.archiving || res.result.archiving == '0') {
              this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQLLLA_5e681f9d`, '签署流程未归档'))
              return
            }
            getAction(
              res.result.remoteFilePath,
              {},
              {
                responseType: 'blob'
              }
            ).then((ref) => {
              let url = window.URL.createObjectURL(new Blob([ref]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', res.result.documentName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            })
          } else {
            this.$message.warning(res.message)
          }
        })
      } else if (signType == 'esignV3') {
        getAction('/esignv3/elsEsignV3Flow/signFileDownloadByRelationId', { id: row.id }).then((res) => {
          if (res.success) {
            console.log(res.result)
            const result = res.result
            const files = result.files[0]
            getAction(
              files.downloadUrl,
              {},
              {
                responseType: 'blob'
              }
            ).then((res) => {
              let url = window.URL.createObjectURL(new Blob([res]))
              let link = document.createElement('a')
              link.style.display = 'none'
              link.href = url
              link.setAttribute('download', files.fileName)
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link) //下载完成移除元素
              window.URL.revokeObjectURL(url) //释放掉blob对象
            })
          } else {
            this.$message.warning(res.message)
          }
        })
      } else if (signType == 'fadada') {
        getAction('/electronsign/fadada/fadadaSignTaskPurchase/downloadArchiveByRelationId', { id: row.id, busType: 'contract' }).then((res) => {
          if (res.success) {
            window.open(res.message, '_blank')
          } else {
            this.$message.warning(res.message)
          }
        })
      } else {
        getAction(this.url.flowFileDownload, { id: row.id, busType: 'contract' }).then((res) => {
          if (res.success) {
            console.log(res.result)
            window.open(res.result[0].fileUrl)
          } else {
            this.$message.warning(res.message)
          }
        })
      }
    },
    handlePurchaseCancelContract(row) {
      let that = this
      this.$confirm({
        content: '确认撤回合同?',
        onOk: () => {
          postAction('/contract/purchaseContractHead/purchaseCancelContract?purchaseContractHeadId=' + row.id).then((res) => {
            if (res.success) {
              that.$message.success('成功')
              that.searchEvent()
              that.refreshCountTabs()
            } else {
              that.$message.warning(res.message)
            }
          })
        }
      })
    },
  }
}
</script>
