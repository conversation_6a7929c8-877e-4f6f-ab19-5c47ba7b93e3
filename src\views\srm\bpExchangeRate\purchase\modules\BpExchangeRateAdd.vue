<template>
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :requestData="requestData"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="tab"
        pageStatus="edit"
        v-on="businessHandler"
      >
      </business-layout>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import {
    BUTTON_SAVE,
    BUTTON_BACK
} from '@/utils/constant.js'
export default {
    mixins: [businessUtilMixin],
    name: 'EditMsgConfigModal',
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data () {
        return {
            requestData: {
                detail: { url: '/exchange/bpExchangeRate/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                {
                    ...BUTTON_SAVE,
                    args: {
                        url: '/exchange/bpExchangeRate/edit'
                    },
                    click: this.save,
                    authorityCode: 'srmExchangeRate#BpExchangeRate:add'
                },
                BUTTON_BACK
            ]
        }
    },
    methods: {
        handleAfterDealSource (pageConfig) {
            pageConfig.groups[0].formModel.exchangeUse = '0'
            pageConfig.groups[0].formModel.exchangeSource = '0'
        },
        save (args) {
            this.stepValidate(args).then(()=>{
                this.composeBusinessSave(args)
            })
        },
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIAy_32f9bfc1`, '汇率编码'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIAy_32f9bfc1`, '汇率编码'),
                        fieldName: 'exchangeNumber',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIjP_32f8c80c`, '汇率用途'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIjP_32f8c80c`, '汇率用途'),
                        fieldName: 'exchangeUse',
                        disabled: true,
                        dictCode: 'srmExchangeUse'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIwj_32f6fbeb`, '汇率来源'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIwj_32f6fbeb`, '汇率来源'),
                        fieldName: 'exchangeSource',
                        dictCode: 'srmExchangeSource',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_trSl_29425bc6`, '基准货币'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_trSl_29425bc6`, '基准货币'),
                        fieldName: 'originalCurrency',
                        dictCode: 'srmCurrency',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_trtL_293b4ca4`, '基准单位'),
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_trtL_293b4ca4`, '基准单位'),
                        fieldName: 'standardUnit',
                        dictCode: 'srmExchangeUnit',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_targetCurrency`, '目标货币'),
                        fieldName: 'targetCurrency',
                        dictCode: 'srmCurrency',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_targetCurrency`, '目标货币'),
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        sortOrder: '5',
                        fieldType: 'number',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exchange`, '汇率'),
                        fieldName: 'exchange',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_exchange`, '汇率'),
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        sortOrder: '5',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIMz_32f5c729`, '汇率年份'),
                        fieldName: 'exchangeYear',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MIMz_32f5c729`, '汇率年份'),
                        required: '1',
                        regex: /^[12][0-9]{3}$/,
                        alertMsg: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bpYearCorrect`, 'BP年，请书写正确的格式')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        sortOrder: '5',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_effectiveDate`, '生效时间'),
                        showTime: true,
                        valueFormat: 'YYYY-MM-DD HH:mm',
                        fieldName: 'effectiveTime',
                        placeholder: '',
                        required: '1'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        sortOrder: '5',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_expiryDate`, '失效时间'),
                        showTime: true,
                        valueFormat: 'YYYY-MM-DD HH:mm',
                        fieldName: 'expireTime',
                        placeholder: '',
                        required: '1'
                    }
                ]
            }
        }
    }
}
</script>