<template>
  <div
    class="page-container">
    <div class="edit-page">
      <a-spin :spinning="confirmLoading">
        <div class="page-content">
          <!-- 非 “不参加”情况 和 “待确认的查看情况” 才能展示投标确认函 -->
          <div v-if="currentEditRow.receiptStatus == '2' && currentEditRow.ifShow">
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBRLx_9e522675`, '投标确认函') }}</span>
            </titleTrtl>
            <vxe-grid
              border
              :height="300"
              ref="table"
              :data="tableData"
              :columns="tableColumns"
              :merge-cells="mergeCells"
              align="left"
              :show-header="false"
              show-overflow="title"
            >

              <!-- 第二列插槽 -->
              <template #default_nd="{row, rowIndex, column}">
                <!-- 第二列上传附件按钮 -->
                <div
                  v-if="rowIndex == '3'"
                  class="dropbox">
                  <a-upload
                    name="file"
                    :multiple="true"
                    :showUploadList="false"
                    :action="uploadUrl"
                    :headers="uploadHeader"
                    :accept="accept"
                    :data="{headId: currentEditRow.id, businessType: 'tenderInvitationSupplierReceipt', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                    :beforeUpload="beforeUpload"
                    @change="handleUploadChange"
                  >
                    <a-button
                      v-if="currentEditRow.receiptStatus !== '2'"
                      type="primary"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                  </a-upload>
                  <div
                    style="display: inline-block;" 
                    v-for="(fileItem, index) in tableMsg.attachmentDTOList"
                    :key="fileItem.id"
                  >
                    <span style="color: blue; cursor:pointer; margin:0 4px" >{{ fileItem.fileName }}</span>
                    <a-icon
                      type="delete"
                      v-if="currentEditRow.receiptStatus == '2'"
                      style="margin:0 4px"
                      @click="handleDeleteFile(index)"/>
                    <a
                      style="margin:0 4px"
                      @click="preViewEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a>
                    <a
                      @click="downloadEvent(fileItem)">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a>    
                  </div>
                </div>
                <!-- 第二列回执人名称输入框 -->
                <a-input
                  v-if="rowIndex == '2'"
                  :disabled="currentEditRow.receiptStatus == '2'"
                  v-model="row.nd"
                  size="small"></a-input>
                <!-- 第二列备注文本框 -->
                <a-textarea
                  v-if="rowIndex == '4'"
                  :disabled="currentEditRow.receiptStatus == '2'"
                  :auto-size="{ minRows: 3 }"
                  v-model="row.nd"
                  size="small">
                </a-textarea>
              </template>
              <!-- 第五列插槽 -->
              <template #default_five="{row, column}">
                <!-- 第五列回执人电话输入框 -->
                <a-input
                  v-model="row.five"
                  :disabled="currentEditRow.receiptStatus == '2'"
                  size="small"
                ></a-input>
              </template>
              <!-- 第一列的回执人、邀请回执、备注样式右悬浮调整 -->
              <template #default_st="{row, rowIndex, column}">
                <span
                  v-if="rowIndex != '0' && rowIndex != '1'"
                  style="display: block;float: right;">{{ row[column['property']] }}</span>
                <span v-else>{{ row[column['property']] }}</span>
                <span
                  v-if="rowIndex == 2 || rowIndex == 3"
                  style="display:block;float: right;color:red;margin-right: 8px">*</span>
              </template>

              <!-- 第四列的回执人电话样式右悬浮调整 -->
              <template #default_th="{row, rowIndex, column}">
                <span
                  v-if="rowIndex == '2'"
                  style="display: block;float: right;">{{ row[column['property']] }}</span>
                <span
                  v-if="rowIndex == 2"
                  style="display:block;float: right;color:red;margin-right: 8px">*</span>
              </template>
            </vxe-grid>
          </div>

          <!-- 下方的投标邀请函 -->
          <div>
            <titleTrtl>
              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_eBPVx_9eac8c14`, '投标邀请函') }}</span>
            </titleTrtl>
            <div
              class="ueditorDetail">
              <div
                v-html="textareaValue"
                style="height: 190px"></div>
            </div>  
          </div>
        </div>
        <div class="page-footer">
          <a-button @click="() => {this.$emit('hide')}">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
import ContentHeader from '../../components/content-header'
import titleTrtl from '../../components/title-crtl'
import { postAction, getAction } from '@/api/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import { USER_INFO } from '@/store/mutation-types'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'InviteReceiptDetail',
    components: {
        titleTrtl,
        ContentHeader
    },
    mixins: [tableMixins],
    inject: ['tenderCurrentRow'],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            ifDetail: true,
            attachmentList: [],
            attachmentDTOList: '',
            tableMsg: {},
            noticeContent: '',
            receiptPerson: '',
            receiptPhone: '',
            textareaValue: '',
            confirmLoading: false,
            tableColumns: [
                {
                    'field': 'st',
                    slots: { default: 'default_st' }
                },
                {
                    'field': 'nd',
                    editRender: {autoselect: true, placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VCtWN_63008e03`, '请点击输入...')}, 
                    slots: { default: 'default_nd' }
                },
                {
                    'field': '3nd'
                },
                {
                    'field': 'th',
                    slots: { default: 'default_th' }
                },
                {
                    'field': 'five',
                    editRender: {autoselect: true, placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VCtWN_63008e03`, '请点击输入...')}, 
                    slots: { default: 'default_five' }
                },
                {
                }
            ],
            tableData: [
                
            ],
            mergeCells: [
                { row: 0, col: 0, rowspan: 1, colspan: 6 },
                { row: 1, col: 0, rowspan: 1, colspan: 6 },
                { row: 2, col: 1, rowspan: 1, colspan: 2 },
                { row: 2, col: 4, rowspan: 1, colspan: 2 },
                { row: 3, col: 1, rowspan: 1, colspan: 5 },
                { row: 4, col: 1, rowspan: 1, colspan: 5 }

            ],
            specialFields: [],
            // showHeader: true,
            height: 0,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            url: {
                queryById: '/tender/sale/tenderInvitationSupplierReceipt/queryById',
                edit: '/tender/sale/tenderInvitationSupplierReceipt/edit',
                confirm: '/tender/sale/tenderInvitationSupplierReceipt/comfirm'
            }
        }
    },
    computed: {

    },
    methods: {
        editDisabledEvent ({ row, rowIndex }) {
            console.log(rowIndex)
            if (rowIndex === 3) {
                return false
            }
            return true
        },
        handleBack (){
            this.$emit('hide')
        },
        getData () {
            let params = {
                id: this.currentEditRow.id
            }
            this.confirmLoading = true
            getAction(this.url.queryById, params).then(res => {
                if(res.success) { 
                    console.log(res.result)
                    let {noticeContent = [], ...others} = res.result || {}
                    //拿到下面投标邀请函内容
                    this.textareaValue = noticeContent || []
                    // 拿到除了投标邀请函内容外的其他基本数据：包括回执人、回执电话、备注
                    this.tableMsg = others
                    console.log(this.tableMsg.attachmentDTOList)
                    if(others.attachmentDTOList.length != 0){
                        this.tableMsg.attachmentDTOList.forEach((item)=>{
                            // 将已上传的文件名都放到attachmentList数组
                            this.attachmentList.push(item.fileName)
                        })
                    }
                    
                }
            }).finally(() => {
                this.confirmLoading = false
                this.init()

            })
        },
        beforeUpload () {
            if (!this.tableMsg.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    let {fileName, filePath, fileSize, id, headId} = file.response.result
                    let fileListData = {
                        uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                        uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                        name: fileName,
                        url: filePath,
                        uid: id,
                        fileName, filePath, fileSize, id, headId
                    }
                    if (!this.tableMsg.attachmentDTOList) this.$set(this.tableMsg, 'attachmentDTOList', [])
                    this.tableMsg.attachmentDTOList.push(fileListData)
                    // 新添加的问题名追加到文件列表数组
                    this.attachmentList.push(fileName)
                    // 将文件列表数组转字符串（以;拼接）渲染到表格
                    this.$refs.table.getTableData().tableData[3].nd=this.attachmentList.join(';')
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'error') {
                this.$message.error(`文件上传失败: ${file.msg} `)
            } else if (file.status === 'error') {
                this.tableMsg.attachmentDTOList = fileList.map(res => {
                    let {fileName, filePath, fileSize, id, headId} = res.response.result
                    return {
                        name: fileName,
                        url: filePath,
                        uid: id,
                        fileName, filePath, fileSize, id, headId
                    }
                })
            }
            
        },
        handleDeleteFile (index) {
            this.tableMsg.attachmentDTOList.splice(index, 1)
        },
        async downloadEvent (row) {
            row.subpackageId = this.tenderCurrentRow.subpackageId
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', row.fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row) {
            row.subpackageId = this.tenderCurrentRow.subpackageId
            console.log('row', row)
            this.$previewFile.open({params: row })
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.tableData = [
                {
                    'st': `${this.tableMsg.purchaseEnterpriseName}:`
                },
                {
                    'st': `我单位已收到贵公司发出的分包单号为：${this.tableMsg.subpackageNumber},分包名称：${this.tableMsg.subpackageName}，我公司决定参与本项目的投标。特此回执。`
                },
                {
                    'st': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MRLW_28f8c0a9`, '回执人：'),
                    'nd': this.tableMsg.receiptPerson||'',
                    'th': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MRLCEW_cbd784a1`, '回执人电话：'),
                    'five': this.tableMsg.receiptPhone||''
                },
                {
                    'st': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PVMRW_35372e1a`, '邀请回执：'),
                    'nd': this.attachmentList.join(';')
                },
                {
                    'st': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_qdW_15c6279`, '备注：'),
                    'nd': this.tableMsg.remark || ''
                }
            ]
        }
    },
    created () {
        this.getData()
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
  margin-top: 20px;
}

.margin-r-10{
    margin-right: 10px
}
.label{
  text-align:right;
  padding-right: 10px;
}
.ueditorDetail{
    margin-top: 10px;
    border: 1px solid #e8e8e8;
    padding: 6px;
}
:deep(.vxe-table--render-default .vxe-body--column.col--ellipsis>.vxe-cell){
    max-height: none;
}
:deep(.vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis>.vxe-cell){
max-height: none;
}
</style>




