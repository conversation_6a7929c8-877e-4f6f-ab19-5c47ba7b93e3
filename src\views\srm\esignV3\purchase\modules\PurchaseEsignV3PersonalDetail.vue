<template>
    <div class="page-container">
        <detail-layout
            ref="detailPage"
            :currentEditRow="currentEditRow"
            :page-data="pageData"
            :url="url"/>
    </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'

export default {
    name: 'PurchaseEsignV3PersonalDetail',
    mixins: [DetailMixin],
    data() {
        return {
            selectType: 'esignPersonCertification',
            pageData: {
                form: {
                    subAccount: '',
                    applyUserName: '',
                    applyContact: '',
                    applyContactType: '',
                    mode: '',
                    modifyFields: '',
                    subAccountId: '',
                    paperType: '',
                    otherModes: '',
                    idCardNo: '',
                    bankMobile: ''
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_UzSQNJOHrA0MPEQA`, '个人认证信息'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_srmey_69b65ff`, 'srm账号'),
                                    fieldName: 'subAccount'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'),
                                    fieldName: 'psnAccount',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_EPsmLjDeyBK_b93e3b33`, 'E签宝个人用户账号标识'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                    fieldName: 'psnName',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iIyo_40ba5cff`, '证件号码'),
                                    fieldName: 'psnIdCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iIyo_40ba5cff`, '证件号码')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                                    fieldName: 'psnIdCardType_dictText',
                                    dictCode: 'psnIdCardType',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLlty_72c077b8`, '个人手机号'),
                                    fieldName: 'psnMobile',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_idXKREtltySWEmUQltyHjULi_b9d87b07`, '运营商实名登记手机号或银行卡预留手机号，仅用于认证')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLWEmy_fcd32f1c`, '个人银行卡号'),
                                    fieldName: 'bankCardNum',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_mLWEmy_fcd32f1c`, '个人银行卡号')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '页面中默认选择的实名认证方式'),
                                    fieldName: 'psnDefaultAuthMode_dictText',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsCLiFjKRLiCK_48c768ea`, '设置页面中默认选择的实名认证方式')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCKvL_5eba0f39`, '页面中可选择的个人认证方式范围'),
                                    fieldName: 'psnAvailableAuthModes_dictText',
                                    dictCode: 'psnDefaultAuthMode',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjmLLiCKvL_5eba0f39`, '设置页面中可选择的个人认证方式范围')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '页面中可编辑的个人信息字段'),
                                    fieldName: 'psnEditableFields_dictText',
                                    dictCode: 'psnEditableFields',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqAtjmLVHJO_bd7b6fe6`, '设置页面中可编辑的个人信息字段')
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjlbvL_72298d1`, '页面中可选择的授权范围'),
                                    fieldName: 'authorizedScopes_dictText',
                                    dictCode: 'authorizedScopes',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_GRELsqiFjlbvL_72298d1`, '设置页面中可选择的授权范围')
                                },
                                {

                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_shortLink`, '实名认证短链接'),
                                    fieldName: 'authShortYrl',
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },
                                    disabled: true
                                },
                                {

                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KRLiHKy_dc0044ba`, '实名认证长连接'),
                                    disabled: true,
                                    extend: {
                                        linkConfig: {
                                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_authenticationLink`, '认证链接'),
                                            titleI18nKey: 'i18n_title_authenticationLink'
                                        },
                                        exLink: true
                                    },
                                    fieldName: 'authUrl'
                                }
                            ]
                        }
                    }
                ],
                formFields: [],
                publicBtn: [
                    {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/esignv3/purchaseEsignV3Personal/queryById'
            }
        }
    },
    computed: {},
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (this.currentEditRow && this.currentEditRow.id) {
                this.$refs.detailPage.queryDetail(this.currentEditRow.id)
            } else {
                this.$refs.detailPage.queryDetail('')
            }
        }
    }
}
</script>
