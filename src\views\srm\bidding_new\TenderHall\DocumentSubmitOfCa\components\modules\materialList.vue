<template>
  <div
    v-if="currentRow"
    class="materialList">
    <a-tabs
      v-if="currentRow.saleQuoteMaterialDataList.length > 0"
      :activeKey="activeKey"
      @change="callback">
      <a-tab-pane
        v-for="(tabItem, index) in currentRow.saleQuoteMaterialDataList"
        :key="index + ''"
        :tab="tabItem.title">
      </a-tab-pane>
      <template
        slot="tabBarExtraContent"
      >
        <a-button
          size="small"
          style="margin-left: 10px"
          @click="back">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回' ) }}</a-button>
      </template>
    </a-tabs>
    <listTable
      ref="listTable"
      v-if="showListTable"
      :setGridHeight="setGridHeight"
      :pageStatus="'detail'"
      :fromSourceData="currentRow.saleQuoteMaterialDataList[activeKey]?.materialDataList || []"
      :showTablePage="false"
      :statictableColumns="statictableColumns"></listTable>

  </div>
</template>
<script>
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@/views/srm/bidding_new/BiddingHall/components/title-crtl'
import { baseMixins } from '@/views/srm/bidding_new/plugins/baseMixins'
import { add, sub, mul, div } from '@/utils/mathFloat.js'
export default {
    mixins: [baseMixins],
    props: {
        currentRow: {
            default: () => {
                return {}
            },
            type: Object
        },
        pageStatus: {
            default: 'edit',
            type: String
        },
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        show: {
            type: Boolean,
            default: false 
        }
    },
    components: {
        listTable,
        titleTrtl
    },
    computed: {
        isEdit () {
            return this.pageStatus == 'edit'
        },
        statictableColumns () {
            let enabled = this.isEdit
            return [
                { 'type': 'checkbox', 'width': 50, fixed: 'left' },
                { 'type': 'seq', 'width': 50, 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号'), fixed: 'left' },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_printerBrand`, '品牌'), 'field': 'brand', 'width': 100, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), 'field': 'materialNumber', 'width': 150, fixed: 'left' },
                {field: 'materialName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 120, fixed: 'left'},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialDesc`, '物料描述'), 'field': 'materialDesc', 'width': 150 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialModel`, '物料型号'), 'field': 'materialModel', 'width': 150 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialSpec`, '物料规格'), 'field': 'materialSpec', 'width': 150 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseCycle`, '采购周期'), 'field': 'purchaseCycle', 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseType`, '采购类型'), 'field': 'purchaseType', 'dictCode': 'srmOrderItemPurchaseType_dictText', 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireDate`, '需求日期'), 'field': 'requireDate', enabled, 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_title_effectiveDate`, '生效日期'), 'field': 'effectiveDate', fieldType: 'date', enabled, required: '1', 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_unableDate`, '失效日期'), 'field': 'expiryDate', fieldType: 'date', enabled, required: '1', 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_requireQuantity`, '需求数量'), 'field': 'requireQuantity', 'width': 120 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), 'field': 'purchaseUnit_dictText', 'width': 100},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_price`, '含税价'), 'field': 'price', fieldType: 'number', enabled, bindFunction: this.handlePriceBlur, 'width': 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_netPrice`, '净价'), 'field': 'netPrice', fieldType: 'number', enabled, bindFunction: this.handleNetPriceBlur, 'width': 120},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxCode`, '税码'), 'field': 'taxCode', 'width': 100 },
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxRate`, '税率'), 'field': 'taxRate', 'width': 100},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_targetPrice`, '目标价'), 'field': 'targetPrice', 'width': 100},
                { 'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_currency`, '币别'), 'field': 'currency', enabled: false, 'dictCode': 'srmCurrency', fieldType: 'select', 'width': 100}
            ]
        }
    },
    data () {
        return {
            activeKey: '0',
            showListTable: true,
            currentQuoteItem: {},
            setGridHeight: 400
        }
    },
    watch: {
        show (v) {
            if (v) {
                // 需求池转立项，物料行自带价格
                if (this.currentRow.saleQuoteMaterialDataList.length > 0) {
                    this.currentRow.saleQuoteMaterialDataList.map(item => {
                        if (item.materialDataList.length > 0) {
                            item.materialDataList.map(row => {
                                if (row.price) this.handlePrice(row)
                            })
                        }
                    })
                }
            }
        }
    },
    methods: {
        back () {
            this.$emit('back')
        },
        callback (v) {
            this.activeKey = v
        },
        reloadData () {
            this.$refs.listTable.reloadData(this.currentRow.saleQuoteMaterialDataList[this.activeKey].materialDataList)
        },
        // 计算报价列价格
        handlePrice (row) {
            // 报价列数据来源
            let {materialDataList, field} = this.currentRow.saleQuoteMaterialDataList[this.activeKey]
            let {quoteColumnSource, netPrice, price, requireQuantity, materialId} = row
            let total = 0
            let quoteColumnSourceMap = {
                0: () => { // 0手工输入
                }, 
                1: () => { // 含税总价之和
                    materialDataList.map(({price, requireQuantity}) => {
                        if (price) total = add(total, mul(price, requireQuantity))
                    })
                    this.$emit('setQuotePrice', {price: total, quoteColumnSource: 1, field} )
                },
                2: () => { // 含税单价之和
                    materialDataList.map(({price}) => {
                        if (price) total = add(total, price)
                    })
                    this.$emit('setQuotePrice', {price: total, quoteColumnSource: 2, field} )
                },
                3: () => { // 含税单价
                    this.$emit('setQuotePrice', {price: price, quoteColumnSource: 3, materialId} )
                },
                4: () => { // 含税总价
                    this.$emit('setQuotePrice', {price: mul(price, requireQuantity), quoteColumnSource: 4, materialId} )
                }
            }
            quoteColumnSourceMap[quoteColumnSource]()
        },
        // 含税单价, 实时计算不含税价
        handlePriceBlur (row, column, value = 0) { 
            let {taxRate} = row
            let netPrice = 0
            if (value && value !== '') {
                netPrice = div(value, div(add(taxRate, 100), 100)).toFixed(4) || 0 // 含税价计算未税价：未税价 = 含税价 / ((税率 + 100)/ 100)
            } 
            this.$set(row, 'netPrice', netPrice)
            setTimeout(() => {
                this.handlePrice(row)
            }, 50)
        },
        // 不含税单价, 实时计算含税价
        handleNetPriceBlur (row, column, value = 0) {
            let {taxRate} = row
            let price = 0
            if (value && value !== '') {
                price = mul(value, div(add(taxRate, 100), 100)).toFixed(4) || 0 // 未税价计算含税价：含税价 = 未税价 * ((税率 + 100)/ 100)
            }
            this.$set(row, 'price', price)
            setTimeout(() => {
                this.handlePrice(row)
            }, 50)
        }
    },
    created () {
        this.setGridHeight = document.documentElement.clientHeight - 430
    }
}
</script>

