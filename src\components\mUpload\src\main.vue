<template>
  <div class="m-upload">
    <span class="ant-upload-picture-card-wrapper">
      <div class="ant-upload-list ant-upload-list-picture-card">
        <a-upload
          ref="upload"
          list-type="picture-card"
          v-bind="attrs.uploadAttrs"
          :remove="remove"
          :disabled="$attrs.disabled"
          :multiple="multiple"
          :before-upload="beforeUpload"
          v-on="$listeners"
          :action="action"
          :file-list="localfileList"
          @preview="handlePreview"
          @change="handleUploadChange"
        >
          <div v-if="limit == 0 || localfileList.length < limit">
            <div class="uploadBtn">
              <a-icon type="plus" />
              <div class="ant-upload-text">
                {{ $srmI18n(`${$getLangAccount()}#i18n_title_upload`, '上传') }}
              </div>
            </div>
          </div>
        </a-upload>

      </div>
    </span>

    <a-modal
      v-drag    
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel">
      <img
        alt="example"
        style="width: 100%"
        :src="previewImage" />
    </a-modal>
  </div>
</template>

<script lang="jsx">
export default {
    inheritAttrs: false,
    name: 'MUpload',
    props: {
        value: {
            type: String,
            default: ''
        },
        canRemove: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            // domainURL: '',
            previewVisible: false,
            previewImage: '',
            action: '/els/attachment/purchaseAttachment/upload',
            localFilePath: [],
            localfileList: []
        }
    },
    computed: {
        limit () {
            const { limit = 0 } = this.$attrs || {} // 默认不限制 如限制数量建议 multiple 为fasle
            return limit
        },
        multiple () {
            const { multiple } = this.$attrs || {} 
            // multiple 默认为true
            return multiple === undefined ? true : multiple
        },
        // 父级传参
        attrs () {
            const { ...others } = this.$attrs || {}
            return {
                uploadAttrs: { ...others }
            }
        }
    },
    watch: {
        value: {
            immediate: true,
            handler (val) {
                if (val && typeof(val) == 'string') {
                    const value = val.split(',')
                    if (this.localFilePath.length !== value.length) {
                        this.localFilePath = value
                        this.localfileList = value.map( url => ({ name: 'image', status: 'done', url, thumbUrl: url, uid: +new Date() }))
                    }
                }
            }
        }
    },
    mounted () {
        // const { protocol = '', hostname = '' } = window.location || {}
        // this.domainURL = `${protocol}//${hostname}/opt/upFiles`
        // this.domainURL = 'https://v5-micro-cs.oss-cn-guangzhou.aliyuncs.com'
    },
    methods: {
        beforeUpload (file, uploadFiles) {
            const localLen = this.localfileList.length
            const nowLen = uploadFiles.length
            const len = localLen + nowLen
            if (this.limit && len > this.limit) {
                this.$message.error('超出限制数量了')
                return false
            }
            return true
        },
        remove () {
            if (!this.canRemove && this.$attrs.disabled) {
                return window.Promise.reject(this.$message.error('禁止删除'))
            }
            return window.Promise.resolve()
        },
        handleCancel () {
            this.previewVisible = false
        },
        handlePreview (files) {
            const url = files?.response?.result?.absoluteFilePath || files?.url || ''
            if (url) {
                this.previewImage = url
                this.previewVisible = true
            } else {
                this.$message.warning('当前文件无url！')
            }
        },
        handleUploadChange ({ file, fileList, event }) {
            if (file.status === 'done') {
                if (file.response.success) {
                    if (file.response.code === 201) {
                        let { message, result: { msg, fileUrl, fileName } } = file.response
                        // let href = this.$variateConfig['domainURL'] + fileUrl
                        let href = fileUrl
                        this.$warning({
                            title: message,
                            content: (
                                <div>
                                    <span>{msg}</span><br/>
                                    <span>
                                        { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_detailContent`, '具体详情请') }
                                        <a href={href} target="_blank" download={fileName}>
                                            { this.$srmI18n(`${this.$getLangAccount()}#i18n_title_download`, '点击下载') }
                                        </a>
                                    </span>
                                </div>
                            )
                        })
                    } else {
                        const filePath = file?.response?.result?.absoluteFilePath || ''
                        this.localFilePath.push(filePath)
                        const localFilePath = this.localFilePath.join(',')
                        console.log('filePath', localFilePath)
                        this.$emit('update:value', localFilePath)
                    }
                } else {
                    this.$message.error(`${file.name} ${file.response.message}.`)
                }
            } else if (file.status === 'uploading') {
                this.localfileList = fileList
            }  else if (file.status === 'error') {
                this.$message.error(
                    `${this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadError`, '文件上传失败')} : ${file.msg}`
                )
            } else if (file.status === 'removed') { // 删除
                const idx = this.localfileList.indexOf(file)
                this.localfileList.splice(idx, 1)
                const filePath = file?.response?.result?.absoluteFilePath || file?.url || ''
                const index = this.localFilePath.indexOf(filePath)
                this.localFilePath.splice(index, 1)
                const localFilePath = this.localFilePath.join(',')
                this.$emit('update:value', localFilePath)
            }
        }
    }
}
</script>

<style lang="less" scoped>
.m-upload {
    .uploadBtn {
        i {
            font-size: 32px;
            color: #999;
        }
        .ant-upload-text {
            margin-top: 8px;
            color: #666;
        }
    }
}
</style>
