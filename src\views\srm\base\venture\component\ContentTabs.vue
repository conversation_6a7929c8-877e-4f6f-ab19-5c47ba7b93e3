<template>
  <div class="table-content">
    <a-tabs
 
      :default-active-key="curKey"
      @change="tabsChange"
      v-if="Object.keys(tabData).length>0">
      <a-tab-pane
        v-for="(el) of tabs"
        :key="el.id"
      >
        <span slot="tab">
          {{ el.title }} <b
            class="red"
          >{{ Number(el.total) }}</b>
        </span>
      </a-tab-pane>
    </a-tabs>
    
    <TableLayout
      :tableConfig="tableConfig"
      ref="tablelayout"
      v-show="!showTableCrossPic"
      class="m-t-12"
    />

    <RelationGraph
      v-bind="{graphData}"
      ref="relationGraph"
      v-show="showTableCrossPic" 
      class="m-t-12"
    />
      
  </div>
</template>

<script>
//组件引入
import TableLayout from '../component/TableLayout'
import RelationGraph from '../component/RelationGraph'


//JS
import {getLangAccount, srmI18n} from '@/utils/util'
const commoColunm=[ //自定义列表相同属性
    {dataIndex: 'announcement_date', title: srmI18n(`${getLangAccount()}#i18n_field_sxA_17ce97a`, '报告期')},
    {dataIndex: 'dataSource', title: srmI18n(`${getLangAccount()}#i18n_field_dataSource`, '数据来源')},
    {dataIndex: 'relationship', title: srmI18n(`${getLangAccount()}#i18n_field_RKRH_26f26ae9`, '关联关系')}
]
export default {
    name: 'Contenttabls',
    props: {
        tabs: {//二级导航
            type: Array,
            default: ()=>[]
        },
        tabData: {//当前menus的数据
            type: Object,
            default: ()=>{}
        },
        curKey: {
            type: String,
            default: ''
        },
        curTabName: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            tableConfig: {
                loading: true,
                // toolbarConfig: {
                //     // zoom: true
                // },
                url: {},
                operationColunm: [],
                customColumn: {
                    SupplierCustomerList: [
                        {dataIndex: 'client_name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_customerName`, '客户名')},
                        {dataIndex: 'ratio', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_salesAmountTenThousand`, '销售金额（万元）')},
                        {dataIndex: 'amt', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_customerName`, '客户名')},
                        ...commoColunm
                    ],
                    SupplierSupplyList: [
                        {dataIndex: 'supplier_name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称')},
                        {dataIndex: 'ratio', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_procurementProportion`, '采购占比')},
                        {dataIndex: 'amt', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_procurementAmountTenThousand`, '采购金额（万元）')},
                        ...commoColunm
                    ],
                    SupplierHumanHoldingList: [
                        {
                            dataIndex: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_beneficiaryName`, '最终受益人名称'), slots: { default: 'num1_default'}
                        },
                        {dataIndex: 'percent', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stockPercent`, '持股比')},
                        {dataIndex: 'chainlist', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stockChainlist`, '持股链'), slots: { default: 'holding_face'}}
                       
                    ],
                    SupplierCompanyHolding: [
                        {
                            dataIndex: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierCompanyName`, '控股企业名称'), slots: { default: 'num1_default'}
                        },
                        {dataIndex: 'percent', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_investRadio`, '投资比例')},
                        {dataIndex: 'chainList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_investChainList`, '投资链'), slots: { default: 'holding_face'}}
                    ]
                }
            },
            graphData: {}
            
        }
    },
    components: {
        TableLayout,
        RelationGraph
    },
    computed: {
        //展示表格还是穿透图
        showTableCrossPic (){
            let showType=['股权穿透图', '疑似实际控制人']
            return  showType.includes(this.curTabName)
        }
    
    },
    created (){
      
    },
    methods: {
        //当前menu
        setCurMenu (){
            if(!this.showTableCrossPic){
                this.$refs.tablelayout.tablePage={
                    total: 0,
                    currentPage: 1,
                    pageSize: 20
                }
            }
        },
        //显示列表数据
        getTableColunmData (curIndex=0){
            this.$nextTick(()=>{
                let colunmUrl=this.curKey   
                let dataSet=this.curKey.replace(this.curKey[0], this.curKey[0].toLowerCase()) //处理二级tab的首字母大写，方便后续获取数据
                let otherId=this.tabs[curIndex].otherId
                const needCustomColumn=['SupplierCustomerList', 'SupplierSupplyList', 'SupplierHumanHoldingList', 'SupplierCompanyHolding']//需要前端提供列表名
                if(!this.showTableCrossPic){//展示表格还是穿透图
                    this.tableConfig.operationColunm=this.tabs[curIndex].operationColunm? [...this.tabs[curIndex].operationColunm]:[]
                    if(dataSet=='supplierCompanyHolding') {dataSet= dataSet+'List'}
                    this.tableConfig.url.colunm= colunmUrl
        
                    if(!needCustomColumn.includes(colunmUrl)){
                        let tableData=this.tabData[dataSet]?this.tabData[dataSet]:[]
                        let oldTableData=this.tabData[otherId]?this.tabData[otherId]:[]
                        if(otherId) this.$refs.tablelayout.getUrlList([...tableData, ...oldTableData])
                        else this.$refs.tablelayout.getUrlList(this.tabData[dataSet])

                    }else{
                        let dataTemp=[]
                        const mapHandle=()=>{
                            for(let i of this.tabData[dataSet]){
                                let pageBean=i.pageBean?JSON.parse(i.pageBean).result:[]
                                dataTemp.push(...pageBean)
                            }
                        }
                        if(this.curKey=='SupplierCustomerList'){
                            mapHandle()
                        }
                        if(this.curKey=='SupplierSupplyList'){
                            mapHandle()
                        }
                        if(this.curKey=='SupplierHumanHoldingList'){
                            for(let i of this.tabData[dataSet]){
                                if(typeof i.chainlist =='string'){
                                    i.chainlist=JSON.parse(i.chainlist)
                                }
    
                            }
                            dataTemp=this.tabData[dataSet]
                        }
                        if(this.curKey=='SupplierCompanyHolding'){
                        
                            for(let i of this.tabData[dataSet]){
                                i.chainlist=JSON.parse(i.chainList)
                            }

                            dataTemp=this.tabData[dataSet]
                        }

                        this.$refs.tablelayout.getCustomList(dataTemp)  

                    }
                }
                else{

                    if(this.curKey=='SupplierInvesttreeList'){
                        this.graphData={nodes: [{
                            text: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据'),
                            id: 'SupplierInvesttreeList-none',
                            color: '#2E4E8F'
                        }], links: [{
                            'from': 'SupplierInvesttreeList-none',
                            'to': ''
                        }]}
                        const datas=this.tabData[dataSet][0]
                        if(this.tabData[dataSet].length!=0){
              
                            let __graph_json_data={
                                'nodes': [{id: datas.id, text: datas.supplierName, 'color': '#2E4E8F'}],
                                'links': [],
                                'rootId': datas.id
                            }
                            let stash=(datas)=>{
                                let datasChild=JSON.parse(datas.children)
                                if(datasChild.length>0){
                                    for(let i of datasChild){
                                        __graph_json_data.nodes.push({
                                            id: i.id.toString(),
                                            text: i.name,
                                            color: '#4ea2f0'
                                        })
                                        __graph_json_data.links.push({
                                            from: datas.id.toString(),
                                            to: i.id.toString(),
                                            text: Number(i.percent)*100+'%'+`(${i.regStatus})`
                                        })
                                    }
                                }

                            }
                            stash(datas)
                            this.graphData=__graph_json_data
                        }
                      
                        this.$nextTick(()=>{
                            this.$refs.relationGraph.setGraphData()  
                        })
                    
                    }

                    if(this.curKey=='supplierActualControlList'){
                        this.graphData={nodes: [{
                            text: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PSWF_30240c1c`, '暂无数据'),
                            id: 'supplierActualControlList-none',
                            color: '#2E4E8F'
                        }], links: [{
                            'from': 'supplierActualControlList-none',
                            'to': ''
                        }]}
                        if(this.tabData[dataSet].length!=0){
                            const datas=this.tabData[dataSet][0]
                            const actualController=JSON.parse(datas.actualController)
                            const relationships=JSON.parse(datas.pathMap).p_0.relationships
                            console.log('relationships.properties.percent', relationships)
               
                            let __graph_json_data={
                                'nodes': [
                                    {id: actualController.hId.toString(), text: actualController.name, 'color': '#2E4E8F'},
                                    {id: datas.id.toString(), text: datas.supplierName, 'color': '#4ea2f0'}
                                ],
                                'links': [{
                                    from: actualController.hId.toString(),
                                    to: datas.id.toString(),
                                    text: relationships[0].properties.percent*100+'%'
                                }],
                                'rootId': actualController.hId.toString()
                            }
                            console.log('__graph_json_data', __graph_json_data)
                            this.graphData=__graph_json_data
                        }

                        this.$nextTick(()=>{
                            this.$refs.relationGraph.setGraphData()  
                        })

                    }
                }
            })

        },
        tabsChange (id) {
            this.$nextTick(()=>{
                console.log('tabsChange')
                this.tableConfig.loading=true
                // 根据接口type打开对应的tab
                for(let [index, val] of  this.tabs.entries()){
                    if(val.id===id){
                        this.$parent.curTabName=val.title
                        this.$parent.curKey=val.id
                        this.getTableColunmData(index)
                        break
                    }
                }
                
            })
        }
    }
}
</script>

<style lang="less" scoped>
.table-content{
  display: flex;
  flex-direction: column;
  background-color: #fff;
  flex: 1;
  :deep(.ant-form){
    display: none;
  }
  :deep(.grid-box){
    top: 0px;
    bottom: 0px;
  }
//   /deep/ .ant-tabs-nav-wrap{
//     background-color: #fff;
//   }
  .red{
    color:#F71515;
  }
  :deep(.ant-tabs-bar){
      margin-bottom: 0px;
  }
  .p-l-16 {
    padding-left: 16px;
  }
   .p-r-16 {
    padding-right: 16px;
  }
  .m-t-12{
    margin-top: 12px;
  }
  .p-8{
    padding: 8px;
  }
}
</style>