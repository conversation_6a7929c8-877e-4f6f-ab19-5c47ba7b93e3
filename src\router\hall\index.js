import inviteTendersRouter from './purchase/inviteTenders'
import bidEvaluationRouter from './purchase/bidEvaluation'
import openTenderRouter from './purchase/openTender'
import calibrationRouter from './purchase/calibration'
import { HallLayout } from '@/components/layouts'

const HallRouter = {
    path: '/hall',
    redirect: () => {
        return { name: 'announcement' }
    },
    name: 'hall',
    meta: {
        title: '自主招标大厅',
        titleI18nKey: 'i18n_menu_JdYBfY_2d6065db',
        icon: 'sliders',
        keepAlive: false
    },
    component: HallLayout,
    children: [
        inviteTendersRouter,
        openTenderRouter,
        bidEvaluationRouter,
        calibrationRouter
    ]
}

export default HallRouter