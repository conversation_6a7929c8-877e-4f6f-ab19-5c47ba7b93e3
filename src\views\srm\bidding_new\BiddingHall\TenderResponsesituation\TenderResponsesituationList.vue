<template>
  <div
    class="page-container"
    :style="{height: pageContentHeight}">
    <div class="edit-page">
      <a-spin :spinning="loading">
        <div
          class="page-content list" 
          v-if="show && !showSignUpRecordEditPage && !showBidManagerEditPage && !showPreBidManagerEditPage && !showSignUpRecordDetailPage && !showBidManagerDetailPage && !showResponseDetailPage && !showInvitationDetailPage"
        >
          <SupplierInformationList
            @refresh="refresh"
            :propOfCheckType="checkType"
            :formData="formData"/>
          <RecordList 
            @handleAddSignUp="handleAddSignUp"
            @handleAddBiddingItem="handleAddBiddingItem"
            @handleSighUpViewPage="handleSighUpViewPage"
            @handleSighUpEditPage="handleSighUpEditPage"
            @handleBidManagerViewPage="handleBidManagerViewPage"
            @handleBidManagerEditPage="handleBidManagerEditPage"
            @handleBidManagerConfirmPage="handleBidManagerConfirmPage"
            @handleResponsesViewPage="handleResponsesViewPage"
            @handleInviteViewPage="handleInviteViewPage"
            @handleInviteConfirmPage="handleInviteConfirmPage"
            @handleInviteDeletePage="handleInviteDeletePage"
            @activeTabsKey="activeTabsKey"
            @refresh="refresh"
            :formData="formData"
            :status="status"/>
        </div>
      </a-spin>
      <SignUpManagerEdit
        v-if="showSignUpRecordEditPage"
        :current-edit-row="currentEditRow"
        :propOfCheckType="propOfCheckType"
        @hide="hidePage"></SignUpManagerEdit>
      <BidManagerEdit
        v-if="showBidManagerEditPage"
        :current-edit-row="currentEditRow"
        :propOfCheckType="propOfCheckType"
        @hide="hidePage">
      </BidManagerEdit>
      <SignUpManagerDetail
        v-if="showSignUpRecordDetailPage"
        :current-edit-row="currentEditRow"
        :propOfCheckType="checkType"
        @hide="hidePage"></SignUpManagerDetail>
      <BidManagerDetail
        v-if="showBidManagerDetailPage"
        :current-edit-row="currentEditRow"
        :propOfCheckType="propOfCheckType"
        @hide="hidePage">
      </BidManagerDetail>
      <ResponseDetail
        v-if="showResponseDetailPage"
        :current-edit-row="currentEditRow"
        :propOfCheckType="propOfCheckType"
        @hide="hidePage">
      </ResponseDetail>
      <InviteReceiptDetail
        v-if="showInvitationDetailPage"
        :current-edit-row="currentEditRow"
        @hide="hidePage">
      </InviteReceiptDetail>
    </div>
  </div>
</template>
<script>
import SupplierInformationList from './modules/SupplierInformationList.vue'
import RecordList from './modules/RecordList.vue'
import BidManagerEdit from './modules/BidManagerEdit.vue'
import BidManagerDetail from './modules/BidManagerDetail.vue'
import ResponseDetail from './modules/ResponseDetail.vue'
import SignUpManagerEdit from './modules/SignUpManagerEdit.vue'
import SignUpManagerDetail from './modules/SignUpManagerDetail.vue'
import InviteReceiptDetail from './modules/InviteReceiptDetail.vue'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
export default {
    components: {
        SupplierInformationList,
        RecordList,
        BidManagerEdit,
        SignUpManagerEdit,
        BidManagerDetail,
        SignUpManagerDetail,
        ResponseDetail,
        InviteReceiptDetail
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage'
    ],
    computed: {
        subId () {
            // return '1525350539250196481'
            return this.subpackageId()
        },
        pageContentHeight () {
            let height = document.body.clientHeight -70
            return height + 'px'
        },
        subpackage (){
            return this.currentSubPackage()
        }
    },
    data () {
        return {
            show: false,
            showSignUpRecordEditPage: false,
            showBidManagerEditPage: false,
            showPreBidManagerEditPage: false,
            showSignUpRecordDetailPage: false,
            showBidManagerDetailPage: false,
            showResponseDetailPage: false,
            showInvitationDetailPage: false,
            currentEditRow: {},
            loading: false,
            formData: {},
            url: {
                getDataUrl: '/tender/purchase/supplierTenderProjectMasterInfo/queryBySubpackageId'
            }
        }
    },
    methods: {
        hidePage () {
            this.showSignUpRecordEditPage = false
            this.showBidManagerEditPage = false
            this.showPreBidManagerEditPage = false
            this.showSignUpRecordDetailPage = false
            this.showBidManagerDetailPage = false
            this.showResponseDetailPage = false
            this.showInvitationDetailPage = false
            this.refresh()
        },
        // 新增，不需要传row信息
        // 报名新增
        handleAddSignUp (row) {
            this.currentEditRow ={}
            this.propOfCheckType = this.checkType
            this.showSignUpRecordEditPage = true
        },
        // 购标新增
        handleAddBiddingItem ({checkType, index}) {
            this.currentEditRow = { checkType: this.checkType, type: index}
            this.propOfCheckType = checkType
            this.showBidManagerEditPage = true
        },
        // 购标记录查看
        handleBidManagerViewPage ({row, checkType}){
            console.log(row, checkType)

            this.currentEditRow = row
            this.propOfCheckType = checkType
            this.showBidManagerDetailPage = true
        },
        //报名记录查看
        handleSighUpViewPage (row){
            this.currentEditRow = row
            this.showSignUpRecordDetailPage = true
        },
        // 响应查看
        handleResponsesViewPage (row, checkType){
            console.log(row, checkType)
            this.currentEditRow = row
            this.propOfCheckType = checkType
            this.showResponseDetailPage = true
        },
        // 邀请回执查看
        handleInviteViewPage (row){
            this.currentEditRow = row
            this.currentEditRow.ifShow = true
            this.showInvitationDetailPage = true
        },
        // 邀请回执确认
        handleInviteConfirmPage (row){
            var that = this
            let url = '/tender/purchase/tenderInvitationSupplierReceipt/publish'
            this.loading = true
            getAction(url, {id: row.id }).then((res) => {
                if(res.success){
                    that.$message.success(res.message)
                    that.refresh()
                }else{
                    that.$message.warning(res.message)
                }
                this.loading = false
            })
        },
        // 邀请回执删除
        handleInviteDeletePage (row){
            var that = this
            let url = '/tender/purchase/tenderInvitationSupplierReceipt/delete'
            this.loading = true
            getAction(url, {id: row.id}).then((res) => {
                if(res.success){
                    that.$message.success(res.message)
                    that.refresh()
                }else{
                    that.$message.warning(res.message)
                }
                this.loading = false
            })
        },
        // 获取点击的tab是否属于预审
        activeTabsKey (key){
            if(key == '2'){
                this.confirmCheckType = 0
            }else{
                this.confirmCheckType = 1
            }
        },
        // 购标记录确认
        handleBidManagerConfirmPage (row){
            // this.currentEditRow = row
            var that = this
            let url = '/tender/purchase/supplierTenderProjectPurchaseBid/approved'
            this.loading = true
            getAction(url, {id: row.id}, {headers: {xNodeId: `${row.checkType}_0_0`}}).then((res) => {
                console.log('resres', res)
                if(res.success){
                    that.$message.success(res.message)
                    that.getData()
                }else{
                    that.$message.warning(res.message)
                    this.loading = false

                }
            })
            // that.getData()

        },

        // 报名记录编辑
        handleSighUpEditPage (row) {
            this.currentEditRow = row
            this.showSignUpRecordEditPage = true
        },
        // 购标记录编辑
        handleBidManagerEditPage (row, checkType){
            this.currentEditRow = row || {}
            this.propOfCheckType = checkType
            this.showBidManagerEditPage = true
        },
        // 页面上表的供应商信息列表list 没有该方法 先注释掉
        // handleSupplierInformationListViewPage (row){
        //     this.currentEditRow = row
        //     this.showSignUpRecordEditPage = true
        // },
        
        refresh () {
            this.getData()
        },
        getData () {
            this.loading = true
            getAction(this.url.getDataUrl, {subpackageId: this.subId}, {headers: {xNodeId: `${this.checkType}_0_0`}}).then(res => {
                if (res.success) {
                    this.formData = res.result || {}
                    console.log('this.formData', res.result)
                    this.show=true
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
                this.loading = false
            })
        },
        //获取报名购标状态
        getStatus (){
            let {signUp, bidding, signUpType, biddingType, checkType, status} = this.subpackage
            this.signUp = signUp
            this.bidding = bidding
            this.signUpType = signUpType
            this.biddingType = biddingType
            this.checkType = checkType
            this.status = status
            this.getData()
        }
    },
    mounted () {
        // this.getData()
        this.getStatus()
    }
}
</script>
<style lang="less" scoped>
.SupplierInformationListAndQuestions{
  height: 100%;
}
.page-content{
flex: 1;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    position: relative;
    overflow: auto;
    padding: 6px;
    background-color: #fff;
}
</style>


