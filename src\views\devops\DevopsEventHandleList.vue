<template>
    <div style="height:100%">
        <list-layout
            v-show="!showEditPage && !showDetailPage"
            ref="listPage"
            :pageData="pageData"
            :tabsList="tabsList"
            :current-edit-row="currentEditRow"
            :url="url"
            @afterChangeTab="handleAfterChangeTab"/>
        <EditDevopsEventHandle-modal
            v-if="showEditPage"
            :current-edit-row="currentEditRow"
            @hide="hideEditPage"/>
    </div>
</template>
<script>
import EditDevopsEventHandleModal from './modules/EditDevopsEventHandleModal'
import {ListMixin} from '@comp/template/list/ListMixin'
import {httpAction} from '@/api/manage'

export default {
    mixins: [ListMixin],
    components: {
        EditDevopsEventHandleModal
    },
    data() {
        return {
            showEditPage: false,
            pageData: {
                businessType: 'devops',
                formField: [
                    {
                        type: 'input',
                        label: '标题',
                        fieldName: 'title'
                    },
                    {
                        type: 'select',
                        label: '处理状态',
                        fieldName: 'handleStatus',
                        dictCode: 'devopsEventHandleStatus'
                    },
                ],
                form: {
                    title: '',
                    handleStatus: '',
                },
                button: [
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_export`, '下载'),
                        authorityCode: 'devops#devopsEventHandle:export',
                        icon: 'download',
                        clickFn: this.handleExportXls
                    },
                    {
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '自定义列'),
                        icon: 'setting',
                        clickFn: this.settingColumns
                    }
                ],
                optColumnList: [
                    {
                        type: 'edit',
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '处理'),
                        authorityCode: 'devops#devopsEventHandle:handle',
                        clickFn: this.handleHandle
                    }
                ],
                optColumnWidth: 300
            },
            url: {
                list: "/devops/eventHandle/list",
                add: "/devops/eventHandle/add",
                delete: "/devops/eventHandle/delete",
                deleteBatch: "/devops/eventHandle/deleteBatch",
                cancel: '/devops/eventHandle/cancel',
                exportXlsUrl: "devops/devopsEventHandle/exportXls",
                columns: 'devopsEventHandleList'
            },
        }
    },
    mounted() {
        this.serachCountTabs('/devops/eventHandle/counts')
    },
    methods: {
        handleHandle(row) {
            this.currentEditRow = row
            this.showEditPage = true
            // 编辑页面离开tab提示
            this.$store.dispatch('SetTabConfirm', true)
        },
        showCancelCondition(row) {
            return false
        },
        showEditCondition(row) {
            return false
        },
        showDelCondition(row) {
            return false
        },
        submitCallBack(row) {
            this.currentEditRow = row
            this.showEditPage = false
            this.showDetailPage = true
            this.searchEvent()
        },
        cancelAuditCallBack() {
            this.showDetailPage = false
            this.showEditPage = true
            this.searchEvent()
        },
        postUpdateData(url, row) {
            this.$refs.listPage.confirmLoading = true
            httpAction(url, row, 'post').then((res) => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.searchEvent()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.listPage.confirmLoading = false
            })
        },
        handleExportXls() {
            this.$refs.listPage.handleExportXls('系统事件处理')
        }
    }
}
</script>