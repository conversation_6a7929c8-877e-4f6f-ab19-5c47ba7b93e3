<template>
  <a-modal
    v-drag    
    :title="title"
    :width="800"
    :visible="visible"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    :cancel-text="$srmI18n(`${$getLangAccount()}#i18n_title_close`, '关闭')"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form">
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateCODE`, '模板CODE')"
        >
          <a-input
            :disabled="disable"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateCode`, '请输入模板编码')"
            v-decorator="['templateCode', validatorRules.templateCode ]"
          />
        </a-form-item>
        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateTitle`, '模板标题')"
        >
          <a-input
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateTitle`, '请输入模板标题')"
            v-decorator="['templateName', validatorRules.templateName]"
          />
        </a-form-item>

        <a-form-item
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateType`, '模板类型')"
        >
          <j-dict-select-tag
            @change="handleChangeTemplateType"
            :trigger-change="true"
            dict-code="msgType"
            v-decorator="['templateType', validatorRules.templateType ]"
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_selectTemplateType`, '请选择模板类型')"
          />
        </a-form-item>

        <a-form-item
          v-show="!useEditor"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateContent`, '模板内容')"
        >
          <a-textarea
            :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_pleaseInputTemplateContent`, '请输入模板内容')"
            v-decorator="['templateContent', validatorRules.templateContent ]"
            :autosize="{ minRows: 8, maxRows: 8 }"
          />
        </a-form-item>

        <a-form-item
          v-if="useEditor"
          :label-col="labelCol"
          :wrapper-col="wrapperCol"
          :label="$srmI18n(`${$getLangAccount()}#i18n_title_templateContent`, '模板内容')"
        >
          <j-editor v-model="templateEditorContent" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import {postAction} from '@/api/manage'
import pick from 'lodash.pick'
import { duplicateCheck } from '@/api/api'
import JEditor from '@/components/els/JEditor'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    name: 'SysMessageTemplateModal',
    components: {
        JEditor
    },
    data () {
        return {
            title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
            visible: false,
            disable: true,
            model: {},
            labelCol: {
                xs: {span: 24},
                sm: {span: 5}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            },
            confirmLoading: false,
            form: this.$form.createForm(this),
            validatorRules: {
                templateCode: {rules: [{required: true, message: srmI18n(`${getLangAccount()}#i18n_title_pleaseInputTemplateCode`, '请输入模板CODE!')}, {validator: this.validateTemplateCode}]},
                templateName: {rules: [{required: true, message: srmI18n(`${getLangAccount()}#i18n_title_pleaseInputTemplateTitle`, '请输入模板标题!')}]},
                templateContent: {rules: []},
                templateType: {rules: [{required: true, message: srmI18n(`${getLangAccount()}#i18n_title_inputTemplateType`, '请输入模板类型!')}]}
            },
            url: {
                add: '/message/sysMessageTemplate/add',
                edit: '/message/sysMessageTemplate/edit'
            },
            useEditor: false,
            templateEditorContent: ''
        }
    },
    created () {
    },
    methods: {
        add () {
            this.disable = false
            this.edit({})
        },
        edit (record) {
            this.form.resetFields()
            this.model = Object.assign({}, record)
            this.useEditor = (record.templateType==2)
            if(this.useEditor){
                this.templateEditorContent=record.templateContent
            }else{
                this.templateEditorContent=''
            }
            this.visible = true
            this.$nextTick(() => {
                if(this.useEditor){
                    this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateName', 'templateTestJson', 'templateType'))
                }else{
                    this.form.setFieldsValue(pick(this.model, 'templateCode', 'templateContent', 'templateName', 'templateTestJson', 'templateType'))
                }
            })
        },
        close () {
            this.$emit('close')
            this.visible = false
            this.disable = true
        },
        handleOk () {
            this.model.templateType = this.templateType
            const that = this
            // 触发表单验证
            this.form.validateFields((err, values) => {
                if (!err) {
                    that.confirmLoading = true
                    let httpurl = ''
                    if (!this.model.id) {
                        httpurl += this.url.add
                    } else {
                        httpurl += this.url.edit
                    }
                    let formData = Object.assign(this.model, values)
                    //时间格式化

                    if(this.useEditor){
                        formData.templateContent=this.templateEditorContent
                    }
                    console.log(formData)
                    postAction(httpurl, formData).then((res) => {
                        if (res.success) {
                            that.$message.success(res.message)
                            that.$emit('ok')
                        } else {
                            that.$message.warning(res.message)
                        }
                    }).finally(() => {
                        that.confirmLoading = false
                        that.close()
                    })


                }
            })
        },
        validateTemplateCode (rule, value, callback){
            var params = {
                tableName: 'sys_sms_template',
                fieldName: 'template_code',
                fieldVal: value,
                dataId: this.model.id
            }
            duplicateCheck(params).then((res)=>{
                if(res.success){
                    callback()
                }else{
                    callback(res.message)
                }
            })

        },
        handleCancel () {
            this.close()
        },
        handleChangeTemplateType (value){
        //如果是邮件类型那么则改变模板内容是富文本编辑器
            this.useEditor = value==2
        }

    }
}
</script>

<style scoped>

</style>