import { USER_INFO } from '@/store/mutation-types'
import {
    bindDefaultValue,
    getLangAccount,
    srmI18n,
    composePromise,
    getObjType,
    createPromise,
    isDef,
    downloadTemplate,
    handValidate
} from '@/utils/util.js'
import { PURCHASEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { ButtonComponent } from '@comp/template/business/class/ComponentFactory'
import { httpAction, postAction, getAction } from '@/api/manage'
import { cloneDeep } from 'lodash'

export const businessUtilMixin = {
    data () {
        return {
            refresh: true,
            confirmLoading: false,
            businessRefName: 'businessRef',
            curGroupCode: '',
            fieldSelectType: '',
            businessHandler: {
                handleFooterSave: this.composeBusinessSave,
                handleFooterPublish: this.composeBusinessPublish,
                handleFooterSubmit: this.composeBusinessSubmit,
                handleFooterAuditCancel: this.composeBusinessAuditCancel,
                handleFooterReject: this.composeBusinessReject,
                handleGoBack: this.businessHide,
                handleToolbarGridAdd: this.businessGridAdd, // 通用行添加
                handleToolbarGridDelete: this.businessGridDelete,
                handleToolbarGridExportExcel: this.businessGridExportExcel,
                customSelect: this.customSelect, // 模板自定义弹窗
                handleCheckProcess: this.checkProcessHanndle
            }
        }
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        },
        allowEdit: {
            type: Object,
            default: false,
        },
    },
    provide () {
        return {
            tplRootRef: this
        }
    },
    methods: {
        // 在自己的业务下重写此方法
        customSelect: () => { },
        handleBeforeRemoteConfigData: () => { },
        handleAfterRemoteConfigData: () => { },
        handleAfterDealSource: () => { },
        handleStepChange: () => { },
        // 表格向下填充
        fillDownGridItem (info, otherParams) {
            new ButtonComponent().gridFillDown(info, otherParams)
        },
        // 查看流程
        checkProcessHanndle: () => { },
        /**
         * 校验
         */
        handValidate,
        /**
         * 获取业务模板的所有数据和配置, 传入组件ref名称
         * @param {*} extendRef
         * @returns
         */
        getBusinessExtendData (extendRef) {
            if (this.$refs[extendRef]) {
                return this.$refs[this.businessRefName].extendAllData()
            } else {
                return {}
            }
        },
        handleHeaderFields ({pageData, flag, headGroupCode}) {
            const groupCode = headGroupCode || 'baseForm'
            let group = pageData.groups.find(rs => rs.groupType == 'head' && rs.groupCode == groupCode) || []
            let headerFields = group?.extend?.headerFields || []
            headerFields = headerFields.filter(rs => rs.type).map(rs => rs.fieldName)
            const formFields = group.formFields || []
            formFields.forEach(n => {
                if (headerFields.includes(n.fieldName)) {
                    n.disabled = flag
                }
            })
        },
        /**
         * 单个分组-隐藏与显示
         * @param {*} extendRef
         * @param {*} groupCode
         * @param {*} flag
         */
        hideSingleGroup (extendRef, groupCode, flag) {
            if (this.getBusinessExtendData(extendRef)) {
                let data = this.getBusinessExtendData(extendRef).pageConfig
                let groups = data.groups
                if (groups && groups.length) {
                    groups.forEach(item => {
                        if (item.groupCode === groupCode) {
                            item.show = !flag
                        }
                    })
                }
            }
        },
        /**
         * 设置表单单个field是否可编辑,可用于bindFuntion
         * @param {*} fieldName
         * @param {*} flag
         */
        setDisableByField (fieldName, formFields, flag) {
            for (let sub of formFields) {
                if (sub.fieldName === fieldName) {
                    sub.disabled = flag
                    break
                }
            }
        },
        /**
         * 设置所有的表单的字段不可编辑
         * @param {*} formFields
         */
        setAllFieldsDisable (formFields) {
            for (let sub of formFields) {
                sub.disabled = true
            }
        },
        /**
         * 设置表单单个field的值,可用于bindFuntion
         * @param {*} fieldName
         * @param {*} flag
         */
        setValueByField (groupCode, field, value) {
            let groups = this.$refs[this.businessRefName].pageConfig.groups
            for (let rs of groups) {
                if (rs.groupCode == groupCode && rs.formModel[field] != 'undefined') {
                    rs.formModel[field] = value
                    break
                }
            }
        },
        /**
         * 获取当前用户信息
         */
        getUserInfo () {
            return this.$ls.get(USER_INFO)
        },
        // 返回
        businessHide () {
            this.$emit('hide')
        },
        toEdit() {
            this.$emit('toEdit')
        },
        /**
         * @description: 规范编辑模板按钮配置
         * @param {*} key
         * @return {*}
         */
        normalizeBtnConfig (key) {
            let pageHeaderButtons = this.pageHeaderButtons || []
            let pageFooterButtons = this.pageFooterButtons || []
            let btns = [...pageHeaderButtons, ...pageFooterButtons]
            let {
                args = {},
                method = 'post',
                showMessage = true,
                title = '提示',
                content = '',
                config = null,
                isJudgePublish = true, // 发布操作前是否需要校验页面发布控制字段
                isJudgeSubmit = true, // 提交审批操作前是否需要校验页面提交审批控制字段
                isFlattenHeadTypeForm = false, // 是否展开 groupType 为 head 的分组合并至提交接口中
                isSave = true, // 发布、提交审批是否需要保存步骤
                handleBefore = (args) => Promise.resolve(args),
                handleAfter = (args) => Promise.resolve(args)
            } = btns.find(n => n.key === key) || {}

            const { url = '' } = args
            if (!url) {
                this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '须配置请求API'))
                return false
            }
            if (config) config = config()
            return {
                url,
                method,
                config,
                showMessage,
                title,
                content,
                isJudgePublish,
                isJudgeSubmit,
                isFlattenHeadTypeForm,
                isSave,
                handleBefore,
                handleAfter
            }
        },
        // 获取页面所有数据
        getAllData () {
            const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            return Object.assign({}, allData)
        },
        /**
         * @description: 规范编辑模板按钮异步组合操作步骤
         * 返回一个具有 handleBefore, requestAction, handleAfter 数组结构
         * 且因为加了“新增不保存需求逻辑” 不再返回异步组合后的方法
         * @param {String} key
         * @return {*}
         */
        normalizeStepPromiseMethod (key = 'save') {
            const {
                url,
                method,
                config,
                showMessage,
                handleBefore,
                handleAfter
            } = this.normalizeBtnConfig(key)

            if (!url) {
                this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '须配置请求API'))
                return false
            }

            let requestAction = (args) => {
                const fn = (resolve, reject) => {
                    let { allData = {} } = args || {}
                    if (key === 'publish') {
                        /**
                         * 采购订单模块需求(目前就这一个模块的需求如此)
                         * 这个模板的后端接口要求:
                         * 每次保存操作都会刷新 dataVersion 字段的值，并把它自增 1
                         * 因为移动端走的是模板配置，发布操作前会调用一次保存接口，然后再走发布接口
                         * 因此，此处的 dataVersion 须自增 1 来满足接口的需求
                         * 特此说明!!!
                         */
                        if (allData) {
                            if (getObjType(allData.dataVersion) === 'number') {
                                allData.dataVersion++
                            }
                        }
                    }

                    if(url == '/contract/purchaseContractHead/publish') allData = { id: allData.businessId}

                    httpAction(url, allData, method, config)
                        .then(res => {
                            if (res.success) {
                                if (showMessage) {
                                    this.$message[`${res.success ? 'success' : 'error'}`](res.message)
                                }
                                resolve(args)
                            } else {
                                this.$message.error(res.message)
                                reject(args)
                            }
                        })
                        .catch(() => {
                            reject(args)
                        })
                }

                return createPromise(fn)
            }

            return [handleBefore, requestAction, handleAfter]
        },
        handleValidateFail ({ message = '', currentStep = 0 }) {
            this.$message.error(message)
            this.$refs[this.businessRefName].currentStep = currentStep
        },
        // 编辑模板, 通用组合式驳回操作
        composeBusinessReject (args) {
            const allData = this.getAllData()

            let steps = [this.stepBusinessReject]
            const handleCompose = composePromise(...steps)

            this.confirmLoading = true
            handleCompose({ ...args, allData })
                .then(
                    res => {
                        console.log('all reject success', res)
                    },
                    err => {
                        console.log('all reject error', err)
                    }
                )
                .finally(() => {
                    this.confirmLoading = false
                    this.$refs[this.businessRefName] && this.$refs[this.businessRefName].queryDetail()
                })
        },
        // 编辑模板, 通用组合式保存操作
        // 条件判断 + 新增/编辑保存 + 刷新操作
        composeBusinessSave (args) {
            console.log('args :>> ', args)
            const allData = this.getAllData()

            // 需求: 新增不保存逻辑
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge
            let steps = [
                this.stepFlattenHeadTypeForm,
                flag ? this.stepBusinessAdd : this.stepBusinessSave
            ]
            const handleCompose = composePromise(...steps)

            this.$refs[this.businessRefName].confirmLoading = true
            handleCompose({ ...args, allData, _isAdd: flag })
                .then(res => {
                    console.log('all save success', res)
                    // 成功-刷新
                    if (flag || this.refresh) {
                        this.refreshPageData()
                    }
                }, err => {
                    console.log('all save error', err)
                })
                .finally(() => {
                    this.$refs[this.businessRefName].confirmLoading = false
                })
        },
        // 编辑模板, 通用组合式发布操作
        // 条件判断 + 新增/编辑/校验 + 单据校验是否允许发布 + 二次确认 + 发布 + 刷新操作
        composeBusinessPublish (args) {
            const allData = this.getAllData()
            // 需求: 新增不保存逻辑
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge

            /**
             * 是否需要保存步骤
             * 某些模块的保存接口需要做异步操作
             * 如返利管理-返利计算单
             * 组合式发布不需要调用保存步骤
             */
            let {
                isSave = true
            } = this.normalizeBtnConfig('publish') || {}

            let steps = [
                this.stepHideMessage, // 隐藏按钮配置的 showMessage
                this.stepFlattenHeadTypeForm,
                isSave ? this.stepSaveAndValidate : this.stepValidate,
                this.stepShowMessage, // 恢复按钮配置的 showMessage
                this.stepJudgeNeedPublish,
                this.stepJudgeIsExistFrozenData,
                this.stepConfirmPublish,
                this.stepBusinessPublish
            ]
            const handleCompose = composePromise(...steps)

            this.confirmLoading = true
            handleCompose({ ...args, allData, _isAdd: flag })
                .then(res => {
                    console.log('all publish success', res)
                    this.businessHide()
                }, err => {
                    console.log('all publish error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    if (flag || this.refresh) {
                        this.refreshPageData()
                    }
                })
        },
        // 编辑模板, 通用组合式提交审批操作
        // 条件判断 + 新增/编辑/校验 + 单据校验是否允许提交审批 + 二次确认 + 提交审批 + 回退列表页
        composeBusinessSubmit (args) {
            const allData = this.getAllData()
            // 需求: 新增不保存逻辑
            let hasId = !!allData.id
            let flag = !hasId && this.$refs[this.businessRefName].isNeedJudge

            /**
             * 是否需要保存步骤
             * 某些模块的保存接口需要做异步操作
             * 如返利管理-返利计算单
             * 组合式提交审批不需要调用保存步骤
             */
            let {
                isSave = true
            } = this.normalizeBtnConfig('submit') || {}

            let steps = [
                this.stepHideMessage, // 隐藏按钮配置的 showMessage
                this.stepFlattenHeadTypeForm,
                isSave ? this.stepSaveAndValidate : this.stepValidate,
                this.stepShowMessage, // 恢复按钮配置的 showMessage
                this.stepJudgeNeedSubmit,
                this.stepJudgeIsExistFrozenData,
                this.stepConfirmSubmit, // 二次确认
                this.stepBusinessSubmit // 提交审批操作
            ]
            const handleCompose = composePromise(...steps)
            this.confirmLoading = true
            handleCompose({ ...args, allData, _isAdd: flag })
                .then(res => {
                    console.log('all submit success', res)
                    this.businessHide()
                }, err => {
                    console.log('all submit error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    if (flag || this.refresh) {
                        this.refreshPageData()
                    }
                })
        },
        // 审批详情, 通用组合式提交审批操作
        // 二次确认 + 提交审批 + 回退列表页
        composeBusinessSubmitInAudit (args) {
            const allData = this.getAllData()

            let steps = [
                this.stepConfirmSubmit, // 二次确认
                this.stepBusinessSubmit // 提交审批操作
            ]

            const handleCompose = composePromise(...steps)
            this.confirmLoading = true
            handleCompose({ ...args, allData })
                .then(res => {
                    console.log('all submit success', res)
                    this.businessHide()
                }, err => {
                    console.log('all submit error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    if (this.refresh) {
                        this.refreshPageData()
                    }
                })
        },
        // 详情模板, 通用组合式撤消审批操作
        // 二次确认 + 提交审批 + 回退列表页
        composeBusinessAuditCancel (args) {
            const allData = this.getAllData()

            let steps = [
                this.stepConfirmSubmit, // 二次确认
                this.stepBusinessAuditCancel // 撤消审批操作
            ]

            const handleCompose = composePromise(...steps)
            this.confirmLoading = true
            handleCompose({ ...args, allData })
                .then(res => {
                    console.log('all submit success', res)
                    this.businessHide()
                }, err => {
                    console.log('all submit error', err)
                })
                .finally(() => {
                    this.confirmLoading = false
                    if (this.refresh) {
                        this.refreshPageData()
                    }
                })
        },
        // 编辑模板步骤: 发布操作条件判断
        stepJudgeNeedPublish (args) {
            let fn = (resolve, reject) => {
                let { allData = {}, btn = {} } = args || {}
                let { isJudgePublish = true } = btn

                // 发布前审批策略 publishAudit 为 “1” 时不需要发布, 直接提交审批
                if (isJudgePublish && isDef(allData.publishAudit)) {
                    if (allData.publishAudit === '1') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitForApprovalTips`, '不可直接发布，请提交审批'))
                        console.log('stepJudgeNeedPublish err publishAudit')
                        reject(args)
                    }
                }
                if (isJudgePublish && isDef(allData.isAudit)) {
                    if (allData.isAudit === '1') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_submitForApprovalTips`, '不可直接发布，请提交审批'))
                        console.log('stepJudgeNeedPublish err isAudit')
                        reject(args)
                    }
                }
                resolve(args)
            }
            return createPromise(fn)
        },
        // 编辑模板步骤: 提交审批条件判断
        stepJudgeNeedSubmit (args) {
            let fn = (resolve, reject) => {
                let { allData = {}, btn = {} } = args || {}
                let { isJudgeSubmit = true } = btn

                // 发布前审批策略 publishAudit 为 “0” 时不需要提交审批
                if (isJudgeSubmit && isDef(allData.publishAudit)) {
                    if (allData.publishAudit === '0') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                        console.log('stepJudgeNeedSubmit err publishAudit')
                        reject(args)
                    }
                }
                // 是否需要审批判断 isAudit 为 “0” 时不需要提交审批
                if (isJudgeSubmit && isDef(allData.isAudit)) {
                    if (allData.isAudit === '0') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                        console.log('stepJudgeNeedSubmit err isAudit')
                        reject(args)
                    }
                }
                // 审批状态
                // 0: 未审批, 1: 审批中, 2: 审批通过, 3: `审批拒绝`, 4: 无需审批
                // if (isDef(allData.auditStatus)) {
                //     let enableArr = ['0', '3']
                //     let falg = allData.auditStatus === '' || enableArr.includes(allData.auditStatus)
                //     if (!falg) {
                //         this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                //         console.log('stepJudgeNeedSubmit err auditStatus')
                //         reject(args)
                //     }
                // }
                // 是否需要审批
                if (isJudgeSubmit && isDef(allData.requiredAudit)) {
                    if (allData.requiredAudit === '0') {
                        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_noNeedSubmitApproval`, '无需提交审批！'))
                        console.log('stepJudgeNeedSubmit err requiredAudit')
                        reject(args)
                    }
                }
                resolve(args)
            }
            return createPromise(fn)
        },
        // 编辑模板步骤: 发布 或 提交审批前校验
        // 异步校验所选供应商 在 当前模板中 是否为冻结状态
        // 采购订单 测试单号 PO202204020021 供应商ELS账号选择 ********
        stepJudgeIsExistFrozenData (args) {
            let fn = (resolve, reject) => {
                let { allData = {}, btn = {} } = args || {}

                let { purchaseOrg = '', toElsAccount = '' } = allData
                let { isExistFrozen = false } = btn

                // 条件判断
                // V5环境
                // 按钮自定义配置 isExistFrozen + 采购组织 purchaseOrg + 对方ELS账号 toElsAccount
                if (isExistFrozen && purchaseOrg && toElsAccount) {
                    let frozenParams = {
                        toElsAccount,
                        frozenFunction: '0',
                        orgType: '0',
                        orgCode: purchaseOrg
                    }
                    let frozenUrl = '/supplier/supplierMaster/isExistFrozenStateData'
                    postAction(frozenUrl, frozenParams).then(res => {
                        if (!res.success) {
                            this.$message.success(res.message)
                        }
                        res.success ? resolve(args) : reject(args)
                    })
                } else {
                    resolve(args)
                }
            }
            return createPromise(fn)
        },
        // 校验步骤
        stepValidate (args) {
            const Ref = this.$refs[this.businessRefName]
            const tplRootRef = this
            // 校验
            let fn = (resolve, reject) => {
                this.handValidate(Ref, args.pageConfig, tplRootRef).then(
                    res => {
                        console.log('res', res)
                        if (res.validStatus) {
                            resolve(args)
                        } else {
                            this.handleValidateFail(res)
                            reject(args)
                        }
                    },
                    err => {
                        reject(err)
                    }
                )
            }

            return createPromise(fn)
        },
        // 校验步骤
        // 保存 + 校验步骤
        stepSaveAndValidate (args) {
            const { _isAdd = false } = args || {}
            const Ref = this.$refs[this.businessRefName]
            const tplRootRef = this
            let validatePromise = args => {
                const fn = (resolve, reject) => {
                    // 校验 + 保存操作
                    this.handValidate(Ref, args.pageConfig, tplRootRef).then(res => {
                        console.log('res', res)
                        if (res.validStatus) {
                            resolve(args)
                        } else {
                            this.handleValidateFail(res)
                            reject(args)
                        }
                    }, err => {
                        reject(err)
                    })
                }
                return createPromise(fn)
            }
            /**
             * 判断是 add 还是 edit
             * add 时：
             * add 的接口返回的数据不完整，后端默认只返回头分组的数据，缺少行分组的数据
             * 新增时，直接点提交或者发布，add 保存之后，需要 queryById 获取完整的数据
             * 将获取的完整数据传给 submit 或者 publish 的下一步逻辑
             * 
             * edit 时：
             * 直接把数据传下去，不需要请求 queryById
             */
            let toFixAddApiResponseData = args => {
                const fn = (resolve, reject) => {
                    if (_isAdd) {
                        if (this.requestData?.detail?.url) {
                            getAction(this.requestData.detail.url, { id: args.allData.id }).then(res => {
                                if (res.success) {
                                    args.allData = cloneDeep(res.result)
                                    resolve(args)
                                } else {
                                    this.$message.error(res.message)
                                    reject(args)
                                }
                            })
                        } else {
                            this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '须配置请求API'))
                            reject(args)
                        }
                    } else {
                        resolve(args)
                    }
                }
                return createPromise(fn)
            }
            let steps = [
                _isAdd ? this.stepBusinessAdd : this.stepBusinessSave,
                toFixAddApiResponseData,
                validatePromise
            ]
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        // 编辑模板步骤: 显示提示信息
        stepShowMessage (args) {
            let fn = resolve => {
                let pageHeaderButtons = this.pageHeaderButtons || []
                let pageFooterButtons = this.pageFooterButtons || []
                let btns = [...pageHeaderButtons, ...pageFooterButtons]
                let btn = btns.find(n => n.key === 'save')
                btn && (btn.showMessage = true)
                resolve(args)
            }

            return createPromise(fn)
        },
        // 编辑模板步骤: 隐藏提示信息
        stepHideMessage (args) {
            let fn = resolve => {
                let pageHeaderButtons = this.pageHeaderButtons || []
                let pageFooterButtons = this.pageFooterButtons || []
                let btns = [...pageHeaderButtons, ...pageFooterButtons]
                const btn = btns.find(n => n.key === 'save')
                btn && (btn.showMessage = false)
                resolve(args)
            }

            return createPromise(fn)
        },
        // 编辑模板组合式步骤: 编辑保存
        stepBusinessSave (args) {
            const steps = this.normalizeStepPromiseMethod('save') || []
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        /**
         * 格式化提交数据步骤
         * 如范式合同管理中，提交数据时，需要合并 groupType: 'head' 的 busRule、personFrom 表单分组
         */
        stepFlattenHeadTypeForm (args) {
            const fn = (resolve) => {

                let { allData = {}, pageConfig = {} } = args || {}
                let {
                    isFlattenHeadTypeForm = false
                } = this.normalizeBtnConfig('save') || {}

                if (!isFlattenHeadTypeForm) {
                    resolve(args)
                } else {
                    let formatAllData = Object.assign({}, allData)
                    const groups = pageConfig.groups || []
                    // 分组隐藏的不做处理
                    let headGroups = groups.filter(n => (n.groupCode !== 'baseForm' && n.groupType === 'head' && !!n.show))
                    for (const group of headGroups) {
                        let groupCode = group.groupCode
                        const formFields = group.formFields || []
                        formFields.forEach(n => {
                            let propery = n.fieldName
                            formatAllData[propery] = allData[groupCode][propery] || ''
                        })
                        delete formatAllData[groupCode]
                    }
                    resolve({
                        ...args,
                        allData: formatAllData
                    })
                }
            }
            return createPromise(fn)
        },
        /**
         * 编辑模板组合式步骤: 新增保存
         * 根据单据主键 id 值判断调用 add 还是 edit 接口
         * 逻辑复用，handleBefore + handleAfter 等配置与页面保存按钮配置一致
         */
        stepBusinessAdd (args) {
            let {
                url,
                method,
                config,
                showMessage
            } = this.normalizeBtnConfig('save') || {}
            let steps = this.normalizeStepPromiseMethod('save') || []

            url = url.replace('/edit', '/add')

            if (!url) {
                this.$message.error(srmI18n(`${getLangAccount()}#i18n_title_lineNotDownload`, '须配置请求API'))
                return false
            }

            let addRequestAction = args => {
                const fn = (resolve, reject) => {
                    httpAction(url, args.allData, method, config)
                        .then(res => {
                            if (res.success) {
                                if (showMessage) {
                                    this.$message[`${res.success ? 'success' : 'error'}`](res.message)
                                }

                                // 注意
                                // 这里需要重新刷新页面,并使用新增后接口返回的所有数据
                                let result = res.result || {}
                                // 更新父级行数据缓存 + 所有数据传参
                                this.$parent.currentEditRow = result
                                args.allData = cloneDeep(result)

                                resolve(args)
                            } else {
                                this.$message.error(res.message)
                                reject(args)
                            }
                        })
                        .catch(() => {
                            reject(args)
                        })
                }

                return createPromise(fn)
            }

            // 此处替换编辑保存步骤为新增保存步骤
            steps.splice(1, 1, addRequestAction)

            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        // 编辑模板步骤: 发布前二次确认
        stepConfirmPublish (args) {
            let { btn = {} } = args || {}
            let { title, content } = btn

            let fn = (resolve, reject) => {
                this.$confirm({
                    title: title || srmI18n(`${getLangAccount()}#i18n_title_release`, '发布'),
                    content: content || srmI18n(`${getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                    onOk () {
                        resolve(args)
                    },
                    onCancel () {
                        reject(args)
                    }
                })
            }

            return createPromise(fn)
        },
        // 编辑模板步骤: 提交审批前二次确认
        stepConfirmSubmit (args) {
            console.log('args', args)
            let { btn = {}, allData = {} } = args || {}
            let { title, content } = btn
            let baseTitle = srmI18n(`${getLangAccount()}#i18n_title_submitApproval`, '提交审批')
            let baseContent = srmI18n(`${getLangAccount()}#i18n_field_KQRLDJUz_8f71e759`, '是否确认提交审批')
            if (allData.auditStatus == '4') {
                baseTitle = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmToSubmit`, '确认提交')
                baseContent = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_i18n_title_confirmToSubmitTips`, '是否确认提交?')
            }
            let fn = (resolve, reject) => {
                this.$confirm({
                    title: title || baseTitle,
                    content: content || baseContent,
                    onOk () {
                        resolve(args)
                    },
                    onCancel () {
                        reject(args)
                    }
                })
            }

            return createPromise(fn)
        },
        // 编辑模板组合式步骤: 提交审批
        stepBusinessSubmit (args) {
            const steps = this.normalizeStepPromiseMethod('submit') || []
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        // 详情模板组合式步骤: 撤消审批
        stepBusinessAuditCancel (args) {
            const steps = this.normalizeStepPromiseMethod('auditCancel') || []
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        // 编辑模板组合式步骤: 发布
        stepBusinessPublish (args) {
            const steps = this.normalizeStepPromiseMethod('publish') || []
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        // 驳回步骤
        stepBusinessReject (args) {
            const steps = this.normalizeStepPromiseMethod('reject') || []
            const handleCompose = composePromise(...steps)
            return handleCompose(args)
        },
        getItemGridRef (groupCode) {
            let gridRef = `${groupCode}grid`
            return this.$refs[this.businessRefName].$refs[gridRef][0].$refs[groupCode]
        },
        getItemFormRef (groupCode) {
            let gridRef = `${groupCode}form`
            return this.$refs[this.businessRefName].$refs[gridRef][0].$refs[groupCode]
        },
        // 表格通用删除
        businessGridDelete ({ groupCode = '' }) {
            if (!groupCode) {
                return
            }
            let itemGrid = this.getItemGridRef(groupCode)
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(srmI18n(`${getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        // 表格通用 导出 excel
        // @example
        // {
        //     title: '导出 Excel',
        //     key: 'gridExportExcel',
        //     args: {
        //         url: '/base/excelByConfig/exportExcel', // 必传
        //         handlerName: 'saleEnquiryItemExcelHandler', // 必传
        //         roelCode: 'sale' // 必传
        //     }
        // }
        businessGridExportExcel ({ btn }) {
            const { allData = {} } = this.getBusinessExtendData(this.businessRefName)
            const { groupCode = '', handlerName = '', roelCode = '', url = '', id = '', data = '' } = btn.args || {}
            const options = {
                url,
                groupCode,
                handlerName,
                roelCode,
                id: id || allData.id,
                data // 成本报价this.currentEditRow.itemId + '_' + groupCode
            }
            return downloadTemplate(options)
        },
        // 表格通用新增
        businessGridAdd ({ pageConfig, groupCode }) {
            let itemGrid = this.getItemGridRef(groupCode)
            console.log('itemGrid :>> ', itemGrid)
            let { columns = [] } = pageConfig.groups.find(n => n.groupCode === groupCode)

            let row = columns
                .filter(n => n.field)
                .reduce((acc, obj) => {
                    if (obj.fieldType === 'computed') acc[obj.field] = bindDefaultValue(obj.defaultValue.trim()) || ''
                    else acc[obj.field] = bindDefaultValue(obj.defaultValue) || ''
                    return acc
                }, {})

            itemGrid.insertAt([row], -1)
        },
        uploadCallBack (result, ref) {
            let fileGrid = this.getItemGridRef(ref)
            fileGrid.insertAt(result, -1)
        },
        showAuditBtn ({ pageData }) {
            return pageData.auditStatus === '1'
        },
        // 手动请求时更新页面数据
        refreshPageData () {
            this.$refs[this.businessRefName] && this.$refs[this.businessRefName].queryDetail()
        },
        // 表格字段附件下载
        attachmentDownload (row) {
            this.$refs[this.businessRefName] && this.$refs[this.businessRefName].downloadEvent(row)
        },
        // 附件删除
        attachmentDelete (row = {}, config = {}) {
            let fileGrid = this.getItemGridRef(config.groupCode || '')
            const url = config.url || (this.url && this.url.attachmentDelete) || '/attachment/purchaseAttachment/delete'
            getAction(url, { id: row.id || '' }).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    // 执行删除行操作
                    fileGrid && fileGrid.remove(row)
                }
            })
        },
        // 附件预览
        attachmentPreview (preViewFile) {
            this.$previewFile.open({ params: preViewFile, path: preViewFile?.path || '' })
        },
        // 行附件下载
        attachmentDownloadEvent (row) {
            const { id, fileName } = row
            let downloadUrl = this.url?.download || PURCHASEATTACHMENTDOWNLOADAPI
            getAction(downloadUrl, { id }, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 附件删除
        attachmentDeleteEvent (opt, row, column, { groupCode, url = '/attachment/purchaseAttachment/delete' }) {
            let fileGrid = this.getItemGridRef(groupCode)
            getAction(url, { id: row.id }).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        // 附件预览
        attachmentPreviewEvent (opt, row, column) {
            console.log('[attachmentPreviewEvent]', opt, row, column)
            // 有本地路径打开本地路径 无本地路径params查询
            // 查询URL /attachment/purchaseAttachment/getSignature
            // this.$previewFile.open({params: preViewFile, path: preViewFile?.path || ""})
            this.$previewFile.open({ params: row })
        },
        /**
         * 手动回写单个分组的字段对应的值，方便不刷新更新数据
         * @param {*} groupCode
         * @param {*} ...fields
         */
        manualUpdateGroupFieldValue (groupCode, { ...fields }) {
            let extendAllData = this.getBusinessExtendData(this.businessRefName)
            if (extendAllData && extendAllData.pageConfig && extendAllData.pageConfig.groups && extendAllData.pageConfig.groups.length) {
                extendAllData.pageConfig.groups.forEach((group) => {
                    if (group.groupCode === groupCode) {
                        let formModel = group.formModel
                        Object.assign(formModel, fields)
                    }
                })
            }
        }
    }
}
