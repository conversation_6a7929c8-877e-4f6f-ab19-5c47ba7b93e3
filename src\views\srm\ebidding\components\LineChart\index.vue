<template>

  <div>
    <div
      :class="className"
      :style="{ height: height, width: width }"
      :ref="className"
    >
    
    </div>
    
    <a-modal
      v-model="dateZoom"
      :title="$srmI18n(`${$getLangAccount()}#i18n_field_Cf_c9ba9`, '放大')"
      :width="amodleWith"
      @ok="()=>this.dateZoom=false"
    >
      <div
        :class="className"
        :style="{ height: '500px', width: '780px' }"
        ref="echartDataZoom"
      >
    
      </div>
    </a-modal>
  </div>
</template>

<script>
import resizeMixin from '../mixin/resizeMixin.js'
import * as echarts from 'echarts'

export default {
    mixins: [
        resizeMixin
    ],
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '320px'
        },
        chartData: {
            type: Object,
            default () {
                return {
                    legend: {
                        data: []
                    },
                    xAxis: {},
                    series: []
                }
            }
        }
    },
    data () {
   

        return {
            chart: null,
            amodleWith: 800,
            option: {
                color: [ '#fda081', '#7ae5a5', '#1690ff', '#cc1b1b', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc' ],
                title: {
                    text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trendChartQuotation`, '报价趋势图')
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    left: '5%',
                    bottom: '1',
                    type: 'scroll',
                    width: '90%',
                    data: []
                },
                grid: {
                    left: '5%',
                    right: '2%',
                    bottom: '9%',
                    containLabel: true
                },
                toolbox: {
                    feature: {
                        saveAsImage: {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_sMLPO_902181c8`, '保存为图片')
                        },
                        myDataZoom: {}
                        // myDataZoom: !_this.dateZoom?myDataZoom:{}
                        // mycustomFangda: {
                        //     show: true,
                        //     title: '放大',
                        //     icon: 'icon-polymerization',
                        //     onclick: function (){
                        //         alert('myToolHandler1')
                        //     }
                        // }
                    }
         
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: this.chartData.xAxis
                },
                yAxis: {
                    type: 'value',
                    name: this.chartData.yAxis.name ? this.chartData.yAxis.name : this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价(元)')
                },
                series: this.chartData.series
            },
            dateZoom: false
        }
    },
    watch: {
        chartData: {
            deep: true,
            handler ({  xAxis, series, legend }) {
                this.option.legend.data = legend.data
                //this.option.legend.data =[]
                this.option.xAxis = xAxis
                this.option.series = series
                this.option.locale = 'EN'
                
                // this.chart.setOption(this.option)
                setTimeout(()=>{
                    this.initChart()
                }, 500)
            },
            immediate: true
        }
    },
    mounted () {
        this.chart = echarts.init(this.$refs[this.className], null, {
            width: 'auto',
            height: 'auto'
        })
    },
    methods: {
        //放大的图表
        dataZooMEcharts (){
            let dataZoomEchart= echarts.init(this.$refs.echartDataZoom)
            let optian={...this.option}
            optian.toolbox.feature.myDataZoom={}
            this.$nextTick(()=>{
                dataZoomEchart.setOption(this.option)
            })
        },
        initChart () {
            // console.log('this.$refs[this.className]', this.$refs[this.className])
            let _this=this
            // this.chart = echarts.init(this.$refs[this.className])
            let myDataZoom= {
                show: true,
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Cf_c9ba9`, '放大'),
                icon: 'path://M432.45,595.444c0,2.177-4.661,6.82-11.305,6.82c-6.475,0-11.306-4.567-11.306-6.82s4.852-6.812,11.306-6.812C427.841,588.632,432.452,593.191,432.45,595.444L432.45,595.444z M421.155,589.876c-3.009,0-5.448,2.495-5.448,5.572s2.439,5.572,5.448,5.572c3.01,0,5.449-2.495,5.449-5.572C426.604,592.371,424.165,589.876,421.155,589.876L421.155,589.876z M421.146,591.891c-1.916,0-3.47,1.589-3.47,3.549c0,1.959,1.554,3.548,3.47,3.548s3.469-1.589,3.469-3.548C424.614,593.479,423.062,591.891,421.146,591.891L421.146,591.891zM421.146,591.891',
                onclick: function (){
                    _this.dateZoom=true
                    setTimeout(()=>{
                        _this.dataZooMEcharts()
                    }, 500)
                        
                    // })
                               
                }          
            }
            this.option.toolbox.feature.myDataZoom=myDataZoom
            this.chart.setOption(this.option)
        },
        resizeChart (){
            this.$nextTick(() => {
                console.log(this.height)
                this.chart.resize()
            })
        },
        clear () {
            this.chart && this.chart.clear()
        }
    }
}
</script>


<style lang="less" scoped>


</style>
