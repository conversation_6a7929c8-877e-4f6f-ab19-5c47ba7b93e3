
@f1: 8px;
@f13: 13px;
@f2: 16px;
@f3: 24px;
@f4: 32px;

.ant-form-item {
  font-size: @f13;
}
.ant-form label {
  font-size: @f13;
}
.tab-content-box {
  height: calc(100% - 50px);
  overflow-y: auto;
  .main {
    height: 100%;
  }
}
.els-page-container {
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
        padding: 4px 16px
    }
    .ant-page-header {
      padding: 8px 16px;
      border: 1px solid #e8e8e8;
      border-bottom: none;
    }
    .ant-page-header-ghost {
        background-color: #fff;
    }
    .fixed-page-header {
        position: fixed;
        top: 0;
        right: 0;
        width: calc(100% - 200px);
        border-bottom: 1px solid #ccc;
        z-index: 2
    }
    .table-page-search-wrapper {
          background-color: #fff;
          border: 1px solid #e8e8e8;
          .ant-advanced-search-form {
            padding-top: 16px;
          }
          .ant-calendar-picker {
            width: 100%
          }
          .ant-form-item {
            display: flex;
            margin-bottom: 16px;
            margin-right: 0;
    
            .ant-form-item-control-wrapper {
              // flex: 1 1;
              display: inline-block;
              vertical-align: middle;
            }
    
            > .ant-form-item-label {
              line-height: 32px;
              // padding-right: 8px;
              // width: auto;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .ant-form-item-control {
              // height: 32px;
              line-height: 32px;
            }
          }
    
        .table-page-search-submitButtons {
          display: block;
          margin-bottom: 24px;
          white-space: nowrap;
        }
    
      }
      .els-table-box {
        background-color: #fff;
        .els-alert-info {
          display: flex;
          justify-content: space-between;
          background-color: #fafafa;
          padding: 4px;
          border-right: 1px solid;
          border-left: 1px solid;
          border-color: #e8e8e8;
        }
        .ant-table-pagination {
          padding-right: 16px;
        }
        .table-operator .ant-btn {
          margin-right: 6px
        }
      }
      .ant-collapse {
        border-radius: 0;
      }
      .ant-collapse-content > .ant-collapse-content-box {
        padding: 8px;
        border-radius: none;
      }
      .vxe-body--row.row--hover {
        background-color: #e6f7ff
      }
      // .vxe-table .vxe-body--row.row--checked, .vxe-table .vxe-body--row.row--radio {
      //   background-color: #b3e7ff
      // }
      .ant-form-item .is-validating {
        width: 100%;
      }
}
// .ant-table-tbody > tr.ant-table-row-selected td {
//   background-color: #b3e7ff
// }
// .ant-table-tbody > tr.ant-table-row-hover {
//   background-color: #b3e7ff
// }

.page-container {
  display: flex;
  flex-direction: column;
  position:relative;
  height: 100%;
  padding: 0px 0px 3px 0px;
  background-color: #f2f2f2;
  // 查询表单样式
  .ant-advanced-search-form {
      padding: 6px;
      background: #fff;
      border: 1px solid #e8eaec;
      .ant-form-item {
          display: flex;
          margin-bottom: 0;
          height: 42px;
          line-height: 42px;
          .ant-form-item-label {
              text-overflow: ellipsis;
          }
      }
      .expand-field {
          z-index: 9;
          background-color: #fff;
          box-shadow: 0px 7px 7px -5px #888888;
      }
      .ant-form-item-control-wrapper {
          flex: 1;
      }
      .search-btn-groups {
          height: 42px;
          line-height: 42px;
      }
  }
  //表格定位
  .grid-box {
      flex: 1; 
      margin-left: 6px;
      margin-right: 6px;
      min-height: 0;
      .vxe-toolbar {
          padding-left: 6px;
          padding-right: 6px;
          min-height: 46px;
          .ant-btn {
            & + .ant-btn {
              margin-left: 6px;
            }
            & + .CustomUpload {
              margin-left: 6px;
            }
          }
          .CustomUpload {
            & + .ant-btn {
              margin-left: 6px;
            }
          }
      }
  }
  //编辑界面
  .edit-page {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
        .ant-spin-container {
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
      .page-header {
          padding: 6px 14px;
          margin-bottom: 6px;
          background-color: #fff;
          .desc-col {
            text-align: left;
            line-height: 32px;
          }
          .btn-col {
            text-align: right;
          }
      }
      .page-content {
          flex: 1;
          justify-content: center;
          align-items: center;
          position: relative;
          overflow: auto;
          padding: 6px;
          margin: 0 6px;
          background-color: #fff;
          .ant-advanced-rule-form {
              padding: 12px;
              .ant-form-item {
                  display: flex;
                  margin-bottom: 0;
                  height: 55px;
                  line-height: 55px;
                  .ant-form-item-label {
                      text-overflow: ellipsis;
                  }
              }
          }
          .edit-grid-box {
              position: absolute;
              top: 0;
              bottom: 0;
              right: 0;
              left: 0;
              overflow: auto;
              padding: 8px;
              .vxe-toolbar {
                  padding-left: 6px;
                  padding-right: 6px;
                  min-height: 46px;
                  .tools-btn {
                    & + .tools-btn {
                      margin-left: 6px;
                    }
                  }
              }
              .ant-input {
                height: 28px;
              }
          }
      }
      .page-footer {
        border-top: 1px solid #e8eaec;
        margin-top: -1px;
        padding: 6px;
        text-align: center;
        .ant-btn:not(:first-child) {
            margin-left: 6px;
        }
      }
  }
  .detail-page {
      height: 100%;
      overflow: auto;
      .ant-page-header-ghost {
        padding: 6px 12px;
      }
      .ant-collapse-header {
        padding: 6px 12px;
        padding-left: 40px;
      }
      .ant-collapse-content-box {
          padding: 12px;
      }
      .page-header {
        padding: 6px 14px;
        margin-bottom: 6px;
        background-color: #fff;
        .desc-col {
          text-align: left;
          line-height: 32px;
        }
        .btn-col {
          text-align: right;
        }
      }
      .fixed-page-header {
          position: fixed;
          top: 0;
          right: 0;
          width: calc(100% - 200px);
          background: #fff;
          border-bottom: 1px solid #ccc;
          z-index: 2
      }
      .page-content {
          flex: 1;
          justify-content: center;
          align-items: center;
          position: relative;
          overflow: auto;
          padding-left: 6px;
          padding-top: 6px;
          padding-right: 6px;
          padding-bottom: 6px;
          background-color: #fff;
          margin-left: 6px;
          margin-right: 6px;
      }
      .detail-grid-box {
        .vxe-toolbar {
            padding: 0 6px;
            .tools-btn {
              & + .tools-btn {
                margin-left: 6px;
              }
            }
        }
      }
  }
  .sale-edit-page .content .ant-advanced-rule-form .ant-form-item .ant-form-item-label{
    text-overflow: ellipsis;
  }
}
.business-container{
    height: 100%;
    .ant-spin-nested-loading{
        height: 100%;
        .ant-spin-container{
            height: 100%;
        }
      }
}
// 阶梯价格json提示样式
.tip-overlay-class {
  .ant-tooltip-content {
    margin-left: -150px;
  }
  .ant-tooltip-inner {
      background-color: #eee;
      min-width: 518px;
  }
}
.workbench-message-content-wrap p {
  margin-bottom: 0px;
  overflow: hidden; 
  white-space: nowrap; 
  text-overflow: ellipsis;
} 
@basefs12: 12px;
@basefs13: 13px;
// 系统ui整体细节调整
// 菜单字体调整
@media screen and (max-width: 1200px) {
    .ant-menu-vertical .ant-menu-item, .ant-menu-vertical-left .ant-menu-item, .ant-menu-vertical-right .ant-menu-item, .ant-menu-inline .ant-menu-item, .ant-menu-vertical .ant-menu-submenu-title, .ant-menu-vertical-left .ant-menu-submenu-title, .ant-menu-vertical-right .ant-menu-submenu-title, .ant-menu-inline .ant-menu-submenu-title{
      font-size: @basefs13;
    }
}
// 按钮
.ant-btn{
  font-size: @basefs13;
}
// 下拉
.ant-dropdown-menu-item, .ant-dropdown-menu-submenu-title{
  font-size: @basefs13;
}
.ant-descriptions-item-label{
  font-size: @basefs13;
}
.ant-descriptions-item-content{
  font-size: @basefs13;
}

// im 样式调整
#layui-layim-close{
  padding: 2px 10px !important;
}
.layim-groupset-list{
  width: 128px;
    background: white;
    padding: 4px 0;
  div{
    width: 100%;
    cursor: pointer;
    height: 30px;
    padding: 0 20px;
    line-height: 30px;
    box-sizing: border-box;
    border-bottom: 1px solid #ccc;
    &:hover{
      background: #f7f7f7;
    }
  }
  div:last-child{
    border: none;
  }
}
.layim-chat-group .layim-chat-other .group-set{
  position: relative;
  cursor: pointer;
  top: 5px;
  left: 5px;
}
body .layui-layim-groupset{
  margin: 28px 0 0 0px;
    border: none;
    background: 0 0;
}

body {
  /* 覆盖弹窗右链菜单样式 */
  .message-tips.layui-layer-tips {
    .layui-layer-content {
      border-radius: 4px;
      padding: 8px 4px;
      .mousedownMenu {
        >ul {
          >li {
            padding: 0 15px;
            &:hover {
              background: #4E85FF;
            }
            &:not(:last-child) {
              border-bottom: 1px dashed #e6f7ff;
            }
          }
        }
      }
    }
    i.layui-layer-TipsT {
      display: none;
    }
  }
}

body .layui-layim-groupset .layui-layer-content{
  padding: 0;
  background: 0 0;
  color: #666;
  box-shadow: none;
  i.layui-layer-TipsB{
    display: none;
  }
}
.layui-layim-members .operation{
    width: 112px;
    margin: 10px 0;
    text-align: center;
    display: inline-block;
    i{ 
      width: 48px;
      height: 48px;
      background: #f1f1f1;
      display: inline-block;
      border-radius: 100%;
      font-size: 40px;
      line-height: 48px;
      cursor: pointer;
    }
}
.layim-list-group {
	.new_msg_point {
		&::after {
			position: absolute;
			right: 6px;
			top: 50%;
			border-radius: 50%;
			width: 8px;
			height: 8px;
			background-color: red;
			content: "";
			transform: translateY(-50%);
		}
	}
}

/**
|--------------------------------------------------
| 2022.09.11
| IM UI优化
|--------------------------------------------------
*/
body {
	.layui-box.layui-layim {
		border-radius: 15px;
	}
}