<template>
  <div>
    <tenderAdmin
      ref="tenderAdmin" 
      :fromSourceData="fromSourceData"
      :pageStatus="pageStatus"
    />
    <evaluationExpert
      ref="evaluationExpert"
      v-if="extractionType"
      :fromSourceData="fromSourceData"
      :pageStatus="pageStatus"
    />
    <extractionConditions
      v-else
      ref="extractionConditions"
      :fromSourceData="fromSourceData"
      :pageStatus="pageStatus"
      v-on="$listeners"
      :root="root"
    />
  </div>
</template>
<script>
import tenderAdmin from './tenderAdmin'
import evaluationExpert from './evaluationExpert'
import extractionConditions from './extractionConditions'
export default {
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        fromSourceData: {
            default: () => {
                return {}
            },
            type: Object
        },
        root: {
            default: () => {
                return {}
            },
            type: Object
        }
    },
    components: {
        tenderAdmin,
        evaluationExpert,
        extractionConditions
    },
    computed: {
        extractionType () {
            return this.fromSourceData.samplingWay == '0'
        }
    },
    data () {
        return {

        }
    },
    methods: {
        // 向外抛数据
        externalAllData () {
            let {fullData: tenderAdmin} = this.$refs.tenderAdmin.getTableData()
            let p = {
                tenderAdmin: tenderAdmin
            }
            console.log(this.extractionType)
            if (this.extractionType) {
                let {fullData: evaluationExpert} = this.$refs.evaluationExpert.getTableData()
                p['evaluationExpert'] = evaluationExpert
            }
            console.log('p', p)
            return p
        },
        // 校验
        getValidatePromise () {
            let refs = Object.values(this.$refs).filter((ref) => ref)
            // 获取当前ref子组件getValidate
            let refsValidate = refs.map(item => {
                return item.getValidate && item.getValidate()
            }).filter((promise) => promise)
            const handlePromise = (list = []) =>
                list.map((promise) =>
                    promise.then(
                        (res) => ({
                            status: 'success',
                            res
                        }),
                        (err) => ({
                            status: 'error',
                            err
                        })
                    )
                )
            return new Promise((resolve, reject) => {
                Promise.all(handlePromise(refsValidate)).then((result) => {
                    console.log(result)
                    let flag = true
                    for (let i = 0; i < result.length; i++) {
                        if (result[i].status === 'error') {
                            flag = false
                        }
                    }
                    if (flag) {
                        resolve(true)
                    } else {
                        reject(false)
                    }
                }).catch((err) => {
                    reject(err)
                })
            })
        }
    }
}
</script>

