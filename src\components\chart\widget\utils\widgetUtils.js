/*
 * @Author: fzb
 * @Date: 2022-03-01 14:51:57
 * @LastEditTime: 2022-03-09 11:28:33
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \portal-frontend\src\components\designer\widget\utils\widgetUtils.js
 */
import {
    chartsBarOpts,
    chartsGaugeOpts,
    chartsPieOpts,
    chartsRadarOpts,
    assistCustomTextOpts,
    assistDecorationOneOpts,
    assistDecorationTwoOpts,
    assistDecorationThreeOpts,
    assistDecorationTwoReverseOpts,
    assistDecorationFourOpts,
    assistDecorationFourReverseOpts,
    assistDecorationFiveOpts,
    assistDecorationSixOpts,
    assistDecorationSevenOpts,
    assistDecorationEightOpts,
    assistDecorationEightReverseOpts,
    assistDecorationNineOpts,
    assistDecorationZeroOpts,
    assistDecorationElevenOpts,
    assistDecorationTwelveOpts,
    chartsLineChartOpts,
    chartsSplashesOpts,
    chartsKLineOpts,
    businessByTableOpts,
    businessLeagueTableOpts,
    businessDigitalFlopOpts  
} from '@comp/chart/widget/options'
import {
    ASSIST_KEY,
    CHARTS_BAR,
    CHARTS_PIE,
    CHARTS_RADAR,
    CHARTS_GAUGE,
    ASSIST_CUSTOM_TEXT,
    STATIC_SERVICE_TYPE,
    HTTP_STATIC_SERVICE_TYPE,
    ASSIST_DECORATION_ONE,
    ASSIST_DECORATION_TWO,
    ASSIST_DECORATION_THREE,
    ASSIST_DECORATION_TWO_REVERSE,
    ASSIST_DECORATION_FOUR,
    ASSIST_DECORATION_FOUR_REVERSE,
    ASSIST_DECORATION_FIVE,
    ASSIST_DECORATION_SIX,
    ASSIST_DECORATION_SEVEN,
    ASSIST_DECORATION_EIGHT,
    ASSIST_DECORATION_EIGHT_REVERSE,
    ASSIST_DECORATION_NINE,
    ASSIST_DECORATION_ZERO,
    ASSIST_DECORATION_ELEVEN,
    ASSIST_DECORATION_TWELVE,
    ASSIST_BORDER_ONE,
    ASSIST_BORDER_TWO,
    ASSIST_BORDER_THREE,
    ASSIST_BORDER_FOUR,
    ASSIST_BORDER_FOUR_REVERSE,
    ASSIST_BORDER_FIVE,
    ASSIST_BORDER_FIVE_REVERSE,
    ASSIST_BORDER_SIX,
    ASSIST_BORDER_SEVEN,
    ASSIST_BORDER_EIGHT,
    ASSIST_BORDER_EIGHT_REVERSE,
    ASSIST_BORDER_NINE,
    ASSIST_BORDER_ZERO,
    ASSIST_BORDER_ELEVEN,
    ASSIST_BORDER_TWELVE,
    ASSIST_BORDER_THIRTEEN,
    CHARTS_LINE_CHART,
    CHARTS_SPLASHES,
    CHARTS_K_LINE,
    BUSINESS_BY_TABLE,
    BUSINESS_LEAGUE_TABLE,
    BUSINESS_DIGITAL_FLOP 
} from '@comp/chart/widget/const'
import { v4 as uuidv4 } from 'uuid'
import _ from 'lodash'
// 组件名
const WidgetEnumsMap = {
    // 业务类型
    [BUSINESS_BY_TABLE]: {
        layer: {
            name: '轮播表',
            icon: 'table'
        },
        entity: 'business-by-table',
        prop: {
            entity: 'prop-business'
        },
        defaultOption: businessByTableOpts,
        defaultData: {
            header: ['列1', '列2', '列3'],
            data: [
                ['行1列1', '行1列2', '行1列3'],
                ['行2列1', '行2列2', '行2列3'],
                ['行3列1', '行3列2', '行3列3'],
                ['行4列1', '行4列2', '行4列3'],
                ['行5列1', '行5列2', '行5列3'],
                ['行6列1', '行6列2', '行6列3'],
                ['行7列1', '行7列2', '行7列3'],
                ['行8列1', '行8列2', '行8列3'],
                ['行9列1', '行9列2', '行9列3'],
                ['行10列1', '行10列2', '行10列3']
            ]
        }
    },
    [BUSINESS_LEAGUE_TABLE]: {
        layer: {
            name: '排名轮播表',
            icon: 'table'
        },
        entity: 'business-league-table',
        prop: {
            entity: 'prop-business'
        },
        defaultOption: businessLeagueTableOpts,
        defaultData: [
            {
                name: '周口',
                value: 55
            },
            {
                name: '南阳',
                value: 120
            },
            {
                name: '西峡',
                value: 78
            },
            {
                name: '驻马店',
                value: 66
            },
            {
                name: '新乡',
                value: 80
            },
            {
                name: '信阳',
                value: 45
            },
            {
                name: '漯河',
                value: 29
            }
        ]
    },
    [BUSINESS_DIGITAL_FLOP]: {
        layer: {
            name: '数字文本',
            icon: 'number'
        },
        entity: 'business-digital-flop',
        prop: {
            entity: 'prop-business'
        },
        defaultOption: businessDigitalFlopOpts,
        defaultData: [
            {
                title: '管养里程',
                number: {
                    number: [23483],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#4d99fc',
                        fontWeight: 'bold'
                    }
                },
                unit: '公里'
            },
            {
                title: '桥梁',
                number: {
                    number: [28],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#f46827',
                        fontWeight: 'bold'
                    }
                },
                unit: '座'
            },
            {
                title: '涵洞隧道',
                number: {
                    number: [22],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#40faee',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '匝道',
                number: {
                    number: [10],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#4d99fc',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '隧道',
                number: {
                    number: [5],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#f46827',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '服务区',
                number: {
                    number: [6],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#40faee',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '收费站',
                number: {
                    number: [10],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#4d99fc',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '超限站',
                number: {
                    number: [10],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#f46827',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            },
            {
                title: '停车区',
                number: {
                    number: [6],
                    content: '{nt}',
                    textAlign: 'right',
                    style: {
                        fill: '#40faee',
                        fontWeight: 'bold'
                    }
                },
                unit: '个'
            }
        ]
    },  
    // 图表类型
    [CHARTS_BAR]: {
        layer: {
            name: '柱形图',
            icon: 'bar-chart'
        },
        entity: 'charts-bar',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'grid', 'tooltip', 'xAxis', 'yAxis', 'series', 'color']
        },
        defaultOption: chartsBarOpts,
        defaultData: {
            categories: ['苹果', '华为', '小米', 'oppo', 'vivo'],
            series: [
                {
                    name: '手机品牌',
                    data: [10999, 8688, 4899, 4799, 4588]
                },
                {
                    name: '手机品牌2',
                    data: [5099, 3688, 5899, 2799, 8588]
                }
            ]
        }
    },
    [CHARTS_PIE]: {
        layer: {
            name: '饼图',
            icon: 'pie-chart'
        },
        entity: 'charts-pie',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'tooltip', 'series', 'color']
        },
        defaultOption: chartsPieOpts,
        defaultData: [
            { value: 10999, name: '苹果' },
            { value: 8688, name: '华为' },
            { value: 4899, name: '小米' },
            { value: 4799, name: 'oppo' },
            { value: 4588, name: 'vivo' }
        ]
    },
    [CHARTS_GAUGE]: {
        layer: {
            name: '仪表盘',
            icon: 'heat-map'
        },
        entity: 'charts-gauge',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'tooltip']
        },
        defaultOption: chartsGaugeOpts,
        defaultData: {
            min: 0,
            max: 100,
            name: '及格率',
            value: 96,
            unit: '%'
        }
    },

    [CHARTS_RADAR]: {
        layer: {
            name: '雷达图',
            icon: 'radar-chart'
        },
        entity: 'charts-radar',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'tooltip', 'color']
        },
        defaultOption: chartsRadarOpts,
        defaultData: {
            indicator: [
                {
                    name: '销售',
                    max: 6500
                },
                {
                    name: '管理',
                    max: 16000
                },
                {
                    name: '信息技术',
                    max: 30000
                },
                {
                    name: '客服',
                    max: 38000
                },
                {
                    name: '研发',
                    max: 52000
                },
                {
                    name: '市场',
                    max: 25000
                }
            ],
            series: [
                {
                    data: [
                        {
                            value: [4300, 10000, 28000, 35000, 50000, 19000],
                            name: '预算分配'
                        },
                        {
                            value: [5000, 14000, 28000, 31000, 42000, 21000],
                            name: '实际开销'
                        }
                    ]
                }
            ]
        }
    },
    [CHARTS_LINE_CHART]: {
        layer: {
            name: '折线图',
            icon: 'line-chart'
        },
        entity: 'charts-line-chart',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'grid', 'tooltip', 'xAxis', 'yAxis', 'series']
        },
        defaultOption: chartsLineChartOpts,
        defaultData: {
            xAxisData: ['苹果', '华为', '小米', 'oppo', 'vivo'],
            seriesData: [10999, 8688, 4899, 4799, 20]
        }
    
    },
    [CHARTS_SPLASHES]: {
        layer: {
            name: '散点图',
            icon: 'dot-chart'
        },
        entity: 'charts-splashes',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'grid', 'tooltip', 'xAxis', 'yAxis', 'series', 'color']
        },
        defaultOption: chartsSplashesOpts,
        defaultData: [
            [10.0, 8.04],
            [8.07, 6.95],
            [13.0, 7.58],
            [9.05, 8.81],
            [11.0, 8.33],
            [14.0, 7.66],
            [13.4, 6.81],
            [10.0, 6.33],
            [14.0, 8.96],
            [12.5, 6.82],
            [9.15, 7.2],
            [11.5, 7.2],
            [3.03, 4.23],
            [12.2, 7.83],
            [2.02, 4.47],
            [1.05, 3.33],
            [4.05, 4.96],
            [6.03, 7.24],
            [12.0, 6.26],
            [12.0, 8.84],
            [7.08, 5.82],
            [5.02, 5.68]
        ]
    },
    [CHARTS_K_LINE]: {
        layer: {
            name: 'K线图',
            icon: 'fund'
        },
        entity: 'charts-k-line',
        prop: {
            entity: 'prop-charts',
            values: ['title', 'legend', 'grid', 'tooltip', 'xAxis', 'yAxis', 'series', 'color']
        },
        defaultOption: chartsKLineOpts,
        defaultData: [
            [20, 34, 10, 38],
            [40, 35, 30, 50],
            [31, 38, 33, 44],
            [38, 15, 5, 42]
        ]
    },
    // 辅助类型
    [ASSIST_CUSTOM_TEXT]: {
        layer: {
            name: '通用文本',
            icon: 'font-size'
        },
        entity: 'assist-custom-text',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistCustomTextOpts,
        defaultData: {
            value: '通用文本'
        }
    },
    [ASSIST_DECORATION_ONE]: {
        layer: {
            name: '装饰1',
            icon: 'font-size'
        },
        entity: 'assist-decoration-one',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationOneOpts,
        defaultData: {
            startColor: '#7ACAEC',
            endColor: '#2A3640'
        }
    },
    [ASSIST_DECORATION_TWO]: {
        layer: {
            name: '装饰2',
            icon: 'font-size'
        },
        entity: 'assist-decoration-two',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationTwoOpts,
        defaultData: {
            startColor: '#7ACAEC',
            endColor: '#2A3640',
            dur: 6
        }
    },
    [ASSIST_DECORATION_THREE]: {
        layer: {
            name: '装饰3',
            icon: 'font-size'
        },
        entity: 'assist-decoration-three',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationThreeOpts,
        defaultData: {
            startColor: '#7ACAEC',
            endColor: '#2A3640'
        }
    },
    [ASSIST_DECORATION_TWO_REVERSE]: {
        layer: {
            name: '装饰2(反转)',
            icon: 'font-size'
        },
        entity: 'assist-decoration-two-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationTwoReverseOpts,
        defaultData: {
            startColor: '#7ACAEC',
            endColor: '#2A3640',
            dur: 6
        }
    },
    [ASSIST_DECORATION_FOUR]: {
        layer: {
            name: '装饰4',
            icon: 'font-size'
        },
        entity: 'assist-decoration-four',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationFourOpts,
        defaultData: {
            startColor: '#4F5259',
            endColor: '#84868B',
            dur: 3
        }
    },
    [ASSIST_DECORATION_FOUR_REVERSE]: {
        layer: {
            name: '装饰4(反转)',
            icon: 'font-size'
        },
        entity: 'assist-decoration-four-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationFourReverseOpts,
        defaultData: {
            startColor: '#4F5259',
            endColor: '#84868B',
            dur: 3
        }
    },
    [ASSIST_DECORATION_FIVE]: {
        layer: {
            name: '装饰5',
            icon: 'font-size'
        },
        entity: 'assist-decoration-five',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationFiveOpts,
        defaultData: {
            startColor: '#3F96A5',
            endColor: '#3F96A5',
            dur: 1.2
        }
    },
    [ASSIST_DECORATION_SIX]: {
        layer: {
            name: '装饰6',
            icon: 'font-size'
        },
        entity: 'assist-decoration-six',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationSixOpts,
        defaultData: {
            startColor: '#7ACAEC',
            endColor: '#7ACAEC'
        }
    },
    [ASSIST_DECORATION_SEVEN]: {
        layer: {
            name: '装饰7',
            icon: 'font-size'
        },
        entity: 'assist-decoration-seven',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationSevenOpts,
        defaultData: {
            startColor: '#1DC1F5',
            endColor: '#1DC1F5',
            value: '装饰'
        }
    },
    [ASSIST_DECORATION_EIGHT]: {
        layer: {
            name: '装饰8',
            icon: 'font-size'
        },
        entity: 'assist-decoration-eight',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationEightOpts,
        defaultData: {
            startColor: '#3F96A5',
            endColor: '#3F96A5'
        }
    },
    [ASSIST_DECORATION_EIGHT_REVERSE]: {
        layer: {
            name: '装饰8(反转)',
            icon: 'font-size'
        },
        entity: 'assist-decoration-eight-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationEightReverseOpts,
        defaultData: {
            startColor: '#3F96A5',
            endColor: '#3F96A5'
        }
    },
    [ASSIST_DECORATION_NINE]: {
        layer: {
            name: '装饰9',
            icon: 'font-size'
        },
        entity: 'assist-decoration-nine',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationNineOpts,
        defaultData: {
            startColor: '#079ACF',
            endColor: '#15698A',
            value: '66%',
            dur: 3
        }
    },
    [ASSIST_DECORATION_ZERO]: {
        layer: {
            name: '装饰10',
            icon: 'font-size'
        },
        entity: 'assist-decoration-zero',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationZeroOpts,
        defaultData: {
            startColor: '#00C2FF',
            endColor: '#1C5A71',
            dur: 6
        }
    },
    [ASSIST_DECORATION_ELEVEN]: {
        layer: {
            name: '装饰11',
            icon: 'font-size'
        },
        entity: 'assist-decoration-eleven',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationElevenOpts,
        defaultData: {
            startColor: '#1A98FC',
            endColor: '#2BDDE4',
            value: '装饰'
        }
    },
    [ASSIST_DECORATION_TWELVE]: {
        layer: {
            name: '装饰12',
            icon: 'font-size'
        },
        entity: 'assist-decoration-twelve',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: assistDecorationTwelveOpts,
        defaultData: {
            startColor: '#2CF7FE',
            endColor: '#2CF7FE',
            scanDur: 3,
            haloDur: 2
        }
    },
    [ASSIST_BORDER_ONE]: {
        layer: {
            name: '边框1',
            icon: 'font-size'
        },
        entity: 'assist-border-one',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#358EBD',
            endColor: '#296091',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_TWO]: {
        layer: {
            name: '边框2',
            icon: 'font-size'
        },
        entity: 'assist-border-two',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#FFFFFF',
            endColor: '#A9ABAE',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_THREE]: {
        layer: {
            name: '边框3',
            icon: 'font-size'
        },
        entity: 'assist-border-three',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#2862B7',
            endColor: '#2862B7',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_FOUR]: {
        layer: {
            name: '边框4',
            icon: 'font-size'
        },
        entity: 'assist-border-four',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#FF0000',
            endColor: '#0809D6',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_FOUR_REVERSE]: {
        layer: {
            name: '边框4(反转)',
            icon: 'font-size'
        },
        entity: 'assist-border-four-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#FF0000',
            endColor: '#0809D6',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_FIVE]: {
        layer: {
            name: '边框5',
            icon: 'font-size'
        },
        entity: 'assist-border-five',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#53565D',
            endColor: '#73767B',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_FIVE_REVERSE]: {
        layer: {
            name: '边框5(反转)',
            icon: 'font-size'
        },
        entity: 'assist-border-five-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#53565D',
            endColor: '#73767B',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_SIX]: {
        layer: {
            name: '边框6',
            icon: 'font-size'
        },
        entity: 'assist-border-six',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#575A60',
            endColor: '#73767B',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_SEVEN]: {
        layer: {
            name: '边框7',
            icon: 'font-size'
        },
        entity: 'assist-border-seven',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#636568',
            endColor: '#636568',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_EIGHT]: {
        layer: {
            name: '边框8',
            icon: 'font-size'
        },
        entity: 'assist-border-eight',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#24528A',
            endColor: '#4ED1DC',
            backgroundColor: '#282C34',
            dur: 3
        }
    },
    [ASSIST_BORDER_EIGHT_REVERSE]: {
        layer: {
            name: '边框8(反转)',
            icon: 'font-size'
        },
        entity: 'assist-border-eight-reverse',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#24528A',
            endColor: '#4ED1DC',
            backgroundColor: '#282C34',
            dur: 3
        }
    },
    [ASSIST_BORDER_NINE]: {
        layer: {
            name: '边框9',
            icon: 'font-size'
        },
        entity: 'assist-border-nine',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#0ABBEA',
            endColor: '#0ABBEA',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_ZERO]: {
        layer: {
            name: '边框10',
            icon: 'font-size'
        },
        entity: 'assist-border-zero',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#223C85',
            endColor: '#D3E1F8',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_ELEVEN]: {
        layer: {
            name: '边框11',
            icon: 'font-size'
        },
        entity: 'assist-border-eleven',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#7592DA',
            endColor: '#586995',
            backgroundColor: '#282C34',
            title: '标题',
            titleWidth: 250
        }
    },
    [ASSIST_BORDER_TWELVE]: {
        layer: {
            name: '边框12',
            icon: 'font-size'
        },
        entity: 'assist-border-twelve',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#2E6099',
            endColor: '#7CE7FD',
            backgroundColor: '#282C34'
        }
    },
    [ASSIST_BORDER_THIRTEEN]: {
        layer: {
            name: '边框13',
            icon: 'font-size'
        },
        entity: 'assist-border-thirteen',
        prop: {
            entity: 'prop-assist',
            values: []
        },
        defaultOption: '',
        defaultData: {
            startColor: '#6586EC',
            endColor: '#2CF7FE',
            backgroundColor: '#282C34'
        }
    }
}

function getWidgetEnums (type) {
    if (typeof type === 'number') {
        type = type.toString()
    }
    if (Object.keys(WidgetEnumsMap).indexOf(type) !== -1) {
        return WidgetEnumsMap[type]
    }
    return null
}

export function initWidgetInfo (type) {
    let target = getWidgetEnums(type)
    if (target === null) {
        return null
    }
    let location = {
        width: '100%',
        height: 'auto',
        top: document.body.clientHeight / 3 - 140 * Math.random(),
        left: document.body.clientWidth / 3 - 225 * Math.random()
    }
    // 初始化位置微调
    if (target.entity.indexOf(ASSIST_KEY) !== -1) {
        location.width = 200
        location.height = 60
    }

    // 需要修改的属性，如果是对象类型的话，一定要进行深拷贝，否则就会改一变多
    return {
        id: uuidv4(),
        pageSnapshot: '',
        active: false,
        layer: _.cloneDeep(target.layer),
        location: location,
        type: type,
        entity: target.entity,
        prop: target.prop,
        option: _.cloneDeep(target.defaultOption),
        data: _.cloneDeep(target.defaultData),
        service: {
            type: HTTP_STATIC_SERVICE_TYPE, // '0'：静态数据，'1'：接口数据，'2':sql, '3': websocket '4': http接口返回结果
            timestamp: new Date().getTime(), // 主要用于属性面板通知组件刷新数据
            api: { // OpenAPI的属性
                url: '',
                method: 'GET', // 只支持GET、POST
                refresh: 10000 // 刷新频率：默认10秒
            },
            websocket: {
                url: ''
            },
            sql: {
                sql: '',
                cateProp: '',
                valueProp: '',
                seriesProp: '',
                currentSqlConfig: [],
                // [{"fieldName":"name","fieldText":"name","widgetType":"String","orderNum":1},{"fieldName":"type","fieldText":"type","widgetType":"String","orderNum":2},{"fieldName":"value","fieldText":"value","widgetType":"String","orderNum":3}],
                dbSource: '',
                tableName: '',
                pageNo: 1,
                pageSize: 10,
                refresh: 10000
            }
        }
    }
}