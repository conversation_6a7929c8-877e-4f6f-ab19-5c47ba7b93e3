<template>
  <div class="purchaseSupplierCapacityHead">
    <a-spin :spinning="confirmLoading">
      <business-layout
        v-if="flag"
        :ref="businessRefName"
        :currentEditRow="currentEditRow"
        :remoteJsFilePath="remoteJsFilePath"
        :requestData="requestData"
        :externalToolBar="externalToolBar"
        :pageHeaderButtons="pageHeaderButtons"
        :handleAfterDealSource="handleAfterDealSource"
        modelLayout="masterSlave"
        pageStatus="detail"
        
        v-on="businessHandler"
      >
      </business-layout>

      <field-select-modal
        ref="fieldSelectModal"
        @ok="fieldSelectOk"
        isEmit
      />
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"/>
    </a-spin>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {businessUtilMixin} from '@comp/template/business/businessUtilMixin.js'
import flowViewModal from '@comp/flowView/flowView'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'PurchaseSupplierCapacityHead',
    components: {
        BusinessLayout,
        fieldSelectModal,
        flowViewModal
    },
    mixins: [businessUtilMixin],
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            businessRefName: 'businessRefName',
            flag: true,
            flowView: false,
            flowId: 0,
            labelCol: { span: 4 },
            wrapperCol: { span: 15 },
            rejectVisible: false,
            refresh: true,
            rejectForm: {
                node: '',
                reject: ''
            },
            requestData: {
                detail: { url: '/tender/tenderProjectApprovalHead/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            externalToolBar: {
            },
            pageHeaderButtons: [
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_revokeApproval`, '撤销审批'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.cancelAudit,
                    show: this.cancelButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'),
                    attrs: {
                        type: 'primary'
                    },
                    click: this.showFlow,
                    show: this.auditButtonShow
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
                    key: 'goBack'
                }
            ],
            url: {
                save: '/supplierCapacity/purchaseSupplierCapacityHead/edit',
                cancelAudit: '/a1bpmn/audit/api/cancel'
            }
        }
    },
    computed: {
        remoteJsFilePath () {
            let templateNumber = this.currentEditRow.templateNumber
            let templateVersion = this.currentEditRow.templateVersion
            let account = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            return `${account}/purchase_projectApproval_${templateNumber}_${templateVersion}`
        }
    },
    methods: {
        cancelAudit ({ Vue, pageConfig, btn, groupCode }) {
            this.auditStatus = pageConfig.groups[0].formModel.auditStatus
            // if(this.auditStatus != '1'){
            //     this.$message.warning( this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_APxOqXUz_5fba973e`, '当前不能撤销审批'))
            //     return
            // }
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData(that.url.cancelAudit, pageConfig.groups[0].formModel)
                    // that.$refs.businessRefName.loadData()
                }
            })
            

        },
        auditButtonShow  ({ pageData }) {
            const {flowId, audit, status} = pageData
            console.log('pagedata', pageData)
            return (flowId != null && audit == '1' && status != '0')

            // return flowId ? true : false
        },
        cancelButtonShow  ({ pageData }) {
            const {auditStatus, audit} = pageData
            console.log('pagedata', pageData)
            return (auditStatus == '1' && audit == '1')

            // return flowId ? true : false
        },
        showFlow ({ Vue, pageConfig, btn, groupCode }) {
            this.flowId = pageConfig.groups[0].formModel.flowId
            // if(!this.flowId){
            //     this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_APUziNxOmAQLW_25fa4a00`, '当前审批策略不能查看流程！'))
            //     return
            // }
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['businessId'] = formData.id
            param['rootProcessInstanceId'] = formData.flowId
            param['businessType'] = 'barcodeInfoAudit'
            param['auditSubject'] = this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_TotUzWtyW_1dc23b59`, '条码单审批，单号：') + formData.trialNumber
            param['params'] = JSON.stringify(formData)
            this.confirmLoading=true
            this.flag=false
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$parent.submitCallBack(formData)
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
                this.flag=true
            })
        },
        handleAfterDealSource (pageConfig, resultData) {
            console.log('pageConfig', pageConfig)
            console.log('resultData', resultData)
            if (resultData['purchaseOrgType'] == '0') { // 招标方式为自行采购时进行操作
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'baseForm') {
                        group.formFields.forEach(formField => {
                            if (formField.fieldName == 'agencyLibraryAccount') {
                                formField.required = '0'
                            }
                        })
                    }
                })
            } else {
                pageConfig.groups.forEach(group => {
                    if (group.groupCode == 'baseForm') {
                        group.formFields.forEach(formField => {
                            if (formField.fieldName == 'purchasePrincipal') {
                                formField.required = '0'
                            }
                        })
                    }
                })
            }
        }
    }
}
</script>