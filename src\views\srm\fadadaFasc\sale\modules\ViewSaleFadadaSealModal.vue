<template>
  <div class="SaleFadadaSeal business-container">
    <business-layout
      :ref="businessRefName"
      pageStatus="detail"
      modelLayout="masterSlave"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      v-on="businessHandler" />

  </div>
</template>

<script lang="jsx">

import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import {  BUTTON_BACK} from '@/utils/constant.js'


export default {
    name: 'DetailSaleFadadaSealModal',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            required: true,
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            requestData: {
                detail: { url: '/electronsign/fadada/saleFadadaSeal/queryById', args: (that) => { return {id: that.currentEditRow.id}}}
            },
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            url: {
                detail: '/electronsign/fadada/saleFadadaSeal/queryById'
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_100100`, '基本信息'),
                        groupNameI18nKey: 'i18n_baseForm786',
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseEsNo`, '采购ELS号'),
                        fieldLabelI18nKey: '',
                        fieldName: 'busAccount'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRRL_4460e249`, '采购名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'companyName',
                        disabled: true
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'selectModal',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_1odNWi5aQ3dst0IU`, '机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'orgName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeAc_27ca63e0`, '印章类型'),
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaCategoryType',
                        fieldName: 'categoryType',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeRL_27c5a0f3`, '印章名称'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealName',
                        required: '1'     
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeBP_27c80dc7`, '印章标签'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealTag'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WecILcR_28cc60c5`, '印章创建人姓名'),
                        fieldLabelI18nKey: '',
                        fieldName: 'userName'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'select',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WezE_27c93d7b`, '印章状态'),
                        fieldLabelI18nKey: '',
                        dictCode: 'fadadaSealStatus',
                        fieldName: 'sealStatus'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iWrhtR_1256e73f`, '证书颁发机构'),
                        fieldLabelI18nKey: '',
                        fieldName: 'certcaOrg'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_iWuwdhAc_136aec39`, '证书加密算法类型'),
                        fieldLabelI18nKey: '',
                        fieldName: 'certEncryptType'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_LPlbjXA_2829e7d4`, '免签授权有效期'),
                        fieldLabelI18nKey: '',
                        fieldName: 'expiresTime'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'image',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'),
                        fieldLabelI18nKey: '',
                        fieldName: 'picFileUrl'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIWejELKyjXA10zs_8b70c130`, '创建印章的页面链接（有效期10分钟）'),
                        fieldLabelI18nKey: '',
                        fieldName: 'sealCreateUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_cIWejELKy_f440eca9`, '创建印章的页面链接'),
                                titleI18nKey: ''
                            },
                            exLink: true
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeGRLOiPKyjXA10zs_29a8ffd6`, '印章设置免验证签链接（有效期10分钟）'),
                        fieldLabelI18nKey: '',
                        fieldName: 'freeSignUrl',
                        extend: {
                            linkConfig: {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_WeGRLOiPKy_d6967b43`, '印章设置免验证签链接'),
                                titleI18nKey: ''
                            },
                            exLink: true
                        }
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQLP_2fb96de8`, '是否设置免签'),
                        fieldLabelI18nKey: '',
                        fieldName: 'visaFree_dictText'
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'date',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_HVKI_30576cfa`, '更新时间'),
                        fieldLabelI18nKey: '',
                        fieldName: 'updateTime'
                    }
                ]
            }
        }
    }
}
</script>
