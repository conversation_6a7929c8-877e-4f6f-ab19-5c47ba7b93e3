<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      modelLayout="collapse"
      :url="url"
      :pageData="pageData"
      isAudit
      :reloadData="handleReloadData"
      @loadSuccess="handleLoadSuccess"
    />
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
    <!-- 查看流程 -->
    <flowViewModal
      v-model="flowView"
      :flowId="flowId"
      :currentEditRow="currentEditRow"/>
    <!-- 审批意见 -->
    <a-modal
    v-drag    
      v-model="auditVisible"
      :title="$srmI18n(`${$getLangAccount()}#i18n_title_approvalComments`, ' 审批意见')"
      :okText="okText"
      @ok="handleOk">
      <a-textarea
        v-model="opinion"
        :placeholder="$srmI18n(`${$getLangAccount()}#i18n_title_enterApprovalCommentsTips`, ' 请输入审批意见')"
        :auto-size="{ minRows: 3, maxRows: 5 }"
      />
    </a-modal>
  </div>
</template>

<script lang="jsx">
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import { httpAction } from '@/api/manage'
import flowViewModal from '@comp/flowView/flowView'
import { removeRepet } from '@/utils/util'
import { ajaxFindDictItems } from '@/api/api'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'

export default {
    name: 'ResultEbiddingAudit',
    mixins: [DetailMixin],
    components: {
        flowViewModal
    },
    data () {
        return {
            stageTypeData: [],
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OuyRUz_3e08b33a`, '竞价结果审批'),
            confirmLoading: false,
            flowId: 0,
            showRemote: false,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            opinion: '',
            okText: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'),
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_ebiddingBankInfo`, '竞价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingItemList',
                        columns: []
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'purchaseEbiddingSupplierQuoteList',
                        expandColumnsMethod: this.expandColumnsMethod,
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead88b_supplierErpCode`, '供应商ERP编码'), field: 'supplierCode', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), field: 'needCoordination_dictText', width: 200, dictCode: 'srmSupplierCoordinationWay' },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_binddingStatus`, '应标状态'), field: 'replyStatus_dictText', width: 100}
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLd_1d59643`, '确认项'), groupCode: 'confirmItem', type: 'grid', custom: {
                        ref: 'purchaseEbiddingConfirmList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 200 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_binddingStatus`, '应标状态'), field: 'status_dictText', width: 100, dictCode: 'srmEbiddingConfirmStatus'},
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLPCMW_713efd4f`, '确认要点描述'), field: 'confirmDesc', width: 220 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), field: 'must_dictText', width: 120 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SMAc_2973057e`, '填写类型'), field: 'writeType_dictText', width: 120, dictCode: 'inspection_item_write_type' },
                            { field: 'confirmItemList', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_URid_469b0f42`, '预制选项'), width: 120,
                                slots: {
                                    default: ({row}) =>{
                                        if (row && ['0', '1'].includes(row.writeType) && row.confirmItemList) {
                                            if (row.writeType === '0' && row.content) {
                                                const item = row.confirmItemList.find(i => i.optionsCode === row.content)
                                                return [(<span>{ item.optionsName }</span>)]
                                            }
                                            if (row.content && row.writeType === '1') {
                                                if (row.content && typeof row.content === 'string') row.content = row.content.split(',')
                                                else if (!row.content) row.content = []
                                                const items = row.confirmItemList.filter(i => row.content.includes(i.optionsCode))
                                                const result = items.map(i => { return i.optionsName }).toString()
                                                return [(<span>{ result }</span>)]
                                            }
                                        } else {
                                            return [(<span>{ row.content }</span>)]
                                        }
                                    }
                                }
                            },
                            { field: 'purchaseRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_purchaseRemarks`, '采购备注') },
                            { field: 'supplierRemark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supRemark`, '供应商备注') }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_attachedDemandList`, '附件需求清单'), groupCode: 'fileDemandInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentDemandList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120},
                            { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 120},
                            { field: 'required_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_must`, '是否必填'), width: 120 },
                            { field: 'remark', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remark`, '备注'), width: 220 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseAttachmentList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 120 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.download},
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'), groupCode: 'awardOpinion', type: 'grid', custom: {
                        ref: 'purchaseAwardOpinionList',
                        columns: [
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'awardOpinion', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lBII_2ed15c51`, '授标意见'), width: 150 },
                            { field: 'createTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createTime`, '创建时间'), width: 150 },
                            { field: 'createBy', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_createBy`, '创建人'), width: 120 }
                        ],
                        showOptColumn: false
                    } }
                ],
                publicBtn: [
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过'), click: this.auditPass, showCondition: this.showAuditBtn},
                    {type: 'primary', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝'), click: this.auditReject, showCondition: this.showAuditBtn},
                    {type: '', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), click: this.showFlow},
                    {type: 'rollBack', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack}
                ]
            },
            url: {
                detail: '/ebidding/purchaseEbiddingHead/queryById'
            }
        }
    },
    async created () {
        await this.getStageTypeData()
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentRow.templateNumber
            let templateVersion = this.currentRow.templateVersion
            let elsAccount = this.currentRow.templateAccount || this.$ls.get('Login_elsAccount')
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/purchase_aduit_ebidding_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    methods: {
        async getStageTypeData () {
            let postData = {
                busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                dictCode: 'srmEbiddingStageType'
            }
            const res = await ajaxFindDictItems(postData)
            if (res.success) {
                this.stageTypeData = res.result
            }
        },
        handleReloadData (res) {
            res.result.ebiddingAmount = res.result.ebiddingAmount ? res.result.ebiddingAmount.toFixed(2) : ''
            res.result.savingAmount = res.result.savingAmount ? res.result.savingAmount.toFixed(2) : ''
            res.result.savingRate = res.result.savingRate ? res.result.savingRate.toFixed(2) : ''
            if (this.stageTypeData && this.stageTypeData.length > 0) {
                res.result.purchaseAttachmentDemandList = res.result.purchaseAttachmentDemandList.map(i => {
                    i.stageType_dictText = this.stageTypeData.find(item => item.value === i.stageType).text
                    return i
                })
            }
            return res
        },
        handleLoadSuccess (res) {
            this.currentRow = res.res.result
            this.showRemote = true
            this.flowId = this.currentRow.flowId
            this.currentEditRow.rootProcessInstanceId = this.currentRow.flowId
        },
        goBack () {
            this.$parent.hideController()
        },
        auditPass (){
            this.currentUrl = '/elsUflo/audit/complete'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approved`, '审批通过')
        },
        auditReject (){
            this.currentUrl = '/elsUflo/audit/reject'
            this.auditVisible = true
            this.okText = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_approvedRejection`, '审批拒绝')
        },
        showFlow (){
            this.flowId = this.currentEditRow.rootProcessInstanceId
            this.flowView = true
        },
        download ( row ) {
            this.$refs.detailPage.handleDownload(row)
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        closeFlowView (){
            this.flowView = false
        },
        handleOk (){
            let param = {}
            param['auditOpinion'] = this.opinion
            param['auditSubject'] = this.currentEditRow.subject
            param['businessType'] = this.currentEditRow.businessType
            param['businessId'] = this.currentEditRow.businessId
            param['rootProcessInstanceId'] = this.currentEditRow.rootProcessInstanceId
            param['taskId'] = this.currentEditRow.taskId
            this.$refs.detailPage.confirmLoading = true
            httpAction(this.currentUrl, param, 'post').then((res) => {
                if (res.success) {
                    this.auditVisible = false
                    this.$message.success(res.message)
                    this.$parent.reloadAuditList()
                    this.goBack()
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.$refs.detailPage.confirmLoading = false
            })
        },
        // 供应商列表扩展  定价
        expandColumnsMethod () {
            // 报价方式, prop: ebiddingWay, 0: 打包, 1: 逐条
            // 是否拆分数量, prop: quotaWay, 0: 否, 1: 是
            let { ebiddingWay = '0', quotaWay = '0', purchaseEbiddingItemList = [] } = this.$refs.detailPage && this.$refs.detailPage.form || {}
            const isEbiddingWay1 = ebiddingWay === '1'
            const title = 
                quotaWay === '0' ?
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_title_splitRatio`, '拆分比例%') :
                    this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vzWR_2e27225f`, '拆分数量')
            const wonTheBid = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_wonTheBid`, '已中标')
            const notWonTheBid = this.$srmI18n(`${this.$getLangAccount()}#i18n_title_notWonTheBid`, '未中标')
            purchaseEbiddingItemList = removeRepet(purchaseEbiddingItemList, 'itemNumber')
            if (isEbiddingWay1) {
                return purchaseEbiddingItemList.map((item, index) => {
                    return {
                        title: item.materialDesc,
                        children: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'),
                                field: `price${index}`,
                                width: 120,
                                slots: {
                                    default: ({ row }) => {
                                        const price = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].price
                                        const El = (<span>{ price }</span>)
                                        return [
                                            El
                                        ]
                                    }
                                }
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractAward`, '授标'),
                                field: `itemStatus${index}`,
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({ row }) => {
                                        const itemStatus = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].itemStatus
                                        const El = (<span>{ itemStatus === '9' ? wonTheBid : notWonTheBid }</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[index].price)
                                        const optsArr = [{value: '9', label: wonTheBid}, {value: '8', label: notWonTheBid}]
                                        const opts = optsArr.map(n => {
                                            return (<vxe-option value={ n.value } label={n.label}></vxe-option>)
                                        })
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({ value }) => {
                                                row[`itemStatus${index}`] = value
                                                row.purchaseEbiddingItemList[index].itemStatus = value
                                            }
                                        }
                                        const El = (
                                            <vxe-select {...{ props, on: on }}>
                                                { opts }
                                            </vxe-select>
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            },
                            {
                                title: title,
                                field: `quota${index}`,
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({ row }) => {
                                        const quota = row.purchaseEbiddingItemList[index] && row.purchaseEbiddingItemList[index].quota
                                        const El = (<span>{ quota }</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[index].price)
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({ value }) => {
                                                row[`quota${index}`] = value
                                                row.purchaseEbiddingItemList[index].quota = value
                                            }
                                        }
                                        const El = (
                                            <vxe-input { ...{ props, on: on } } />
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            }
                        ]
                    }
                })
            } else {
                return [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fs_c3b12`, '打包'),
                        children: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_offer`, '报价'),
                                field: 'totalAmount',
                                width: 120,
                                slots: {
                                    default: ({ row }) => {
                                        const totalAmount = row.purchaseEbiddingItemList[0].totalAmount
                                        const El = (<span>{ totalAmount }</span>)
                                        return [
                                            El
                                        ]
                                    }
                                }
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contractAward`, '授标'),
                                field: 'itemStatus',
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({ row }) => {
                                        const itemStatus = row.purchaseEbiddingItemList[0].itemStatus
                                        const El = (<span>{ itemStatus === '9' ? wonTheBid : notWonTheBid }</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[0].totalAmount)
                                        const optsArr = [{value: '9', label: wonTheBid}, {value: '8', label: notWonTheBid}]
                                        const opts = optsArr.map(n => {
                                            return (<vxe-option value={ n.value } label={n.label}></vxe-option>)
                                        })
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({ value }) => {
                                                row.itemStatus = value
                                                row.purchaseEbiddingItemList[0].itemStatus = value
                                            }
                                        }
                                        const El = (
                                            <vxe-select {...{ props, on: on }}>
                                                { opts }
                                            </vxe-select>
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            },
                            {
                                title: title,
                                field: 'quota',
                                width: 120,
                                editRender: {
                                    enabled: true
                                },
                                slots: {
                                    default: ({ row }) => {
                                        const quota = row.purchaseEbiddingItemList[0].quota
                                        const El = (<span>{ quota }</span>)
                                        return [
                                            El
                                        ]
                                    },
                                    edit: (({row, column}) => {
                                        let disabled = !(row.purchaseEbiddingItemList[0].totalAmount)
                                        const props = {
                                            value: row[column.property],
                                            disabled
                                        }
                                        const on = {
                                            change: ({ value }) => {
                                                row.quota = value
                                                row.purchaseEbiddingItemList[0].quota = value
                                            }
                                        }
                                        const El = (
                                            <vxe-input { ...{ props, on: on } } />
                                        )
                                        return [
                                            El
                                        ]
                                    })
                                }
                            }
                        ]
                    }
                ]
            }
        }
    }
}
</script>
