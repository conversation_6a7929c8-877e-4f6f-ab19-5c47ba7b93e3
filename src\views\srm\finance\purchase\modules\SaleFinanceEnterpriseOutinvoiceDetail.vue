<template>
  <div class="business-container">
    <business-layout
      :ref="businessRefName"
      :currentEditRow="currentEditRow"
      :requestData="requestData"
      :pageHeaderButtons="pageHeaderButtons"
      :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
      modelLayout="tab"
      pageStatus="detail"
      v-on="businessHandler"
    >
    </business-layout>
  </div>
</template>

<script>
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { BUTTON_BACK } from '@/utils/constant.js'
export default {
    name: 'SaleFinanceEnterpriseOutinvoiceDetail',
    mixins: [businessUtilMixin],
    components: {
        BusinessLayout
    },
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            pageHeaderButtons: [
                BUTTON_BACK
            ],
            requestData: {
                detail: {
                    url: '/finance/financeEnterpriseOutinvoice/saleQueryById', args: (that) => {
                        return { id: that.currentEditRow.id }
                    }
                }
            }
        }
    },
    methods: {
        handleBeforeRemoteConfigData () {
            return {
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_basicData`, '基础信息'),
                        groupCode: 'baseForm',
                        groupType: 'head',
                        sortOrder: '1'
                    }
                ],
                formFields: [
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'),
                        fieldName: 'elsAccount',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称'),
                        fieldName: 'enterpriseName',
                        disabled: true,
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供应商名称')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号'),
                        fieldName: 'taxpayerRegNumber',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_taxNumber`, '纳税人识别号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址'),
                        fieldName: 'registerAddress',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_regLocation`, '注册地址')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        fieldName: 'registerTelephone',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_diCE_32c265ec`, '注册电话'),
                        disabled: false
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行'),
                        fieldName: 'depositBank',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vDWE_2cb2838d`, '开户银行')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号'),
                        fieldName: 'bankAccount',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_bankAccount`, '银行账号')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BnL_15b374f`, '复核人'),
                        fieldName: 'reviewer',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_BnL_15b374f`, '复核人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVL_1893af2`, '收款人'),
                        fieldName: 'payee',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVL_1893af2`, '收款人')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vPj_173a6f0`, '开票员'),
                        fieldName: 'drawer',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vPj_173a6f0`, '开票员')
                    },
                    {
                        groupCode: 'baseForm',
                        fieldType: 'input',
                        fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱'),
                        fieldName: 'email',
                        required: '1',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_email`, '邮箱')
                    }
                ],
                itemColumns: [
                ]
            }
        }
    }
}
</script>