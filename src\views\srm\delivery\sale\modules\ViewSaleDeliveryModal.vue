<template>
  <div class="page-container">
    <detail-layout
      ref="detailPage"
      :current-edit-row="currentEditRow"
      :url="url"
      @loadSuccess="handleLoadSuccess"
      :pageData="pageData">

<!--      <template v-slot:customDetailContent>-->
<!--        <a-tabs>-->
<!--          <a-tab-pane forceRender tab="历史退回记录">-->
<!--            <vxe-table ref='historyRefundListGridRef' :data="currentEditRow.historyRefundList">-->
<!--              <vxe-column type="seq" width="70"></vxe-column>-->
<!--              <vxe-column field="bizNumber" title="业务单号"></vxe-column>-->
<!--              <vxe-column field="userNickName" title="退回人昵称"></vxe-column>-->
<!--              <vxe-column field="notes" title="退回原因"></vxe-column>-->
<!--              <vxe-column field="fbk1" title="供应商ElsAccount"></vxe-column>-->
<!--              <vxe-column field="fbk2" title="供应商名"></vxe-column>-->
<!--              <vxe-column field="createTime" title="退回时间"></vxe-column>-->
<!--            </vxe-table>-->
<!--          </a-tab-pane>-->
<!--        </a-tabs>-->
<!--      </template>-->

    </detail-layout>
    <!-- 加载配置文件 -->
    <remote-js
      v-if="showRemote"
      :src="fileSrc"
      @load="loadSuccess"
      @error="loadError" />
  </div>
</template>

<script>
import {DetailMixin} from '@comp/template/detailNew/DetailMixin'
import {getAction} from '@/api/manage'

export default {
    name: 'ViewSaleDeliveryModal',
    mixins: [DetailMixin],
    props: {
        currentEditRow: {
            type: Object,
            default: ()=> {
                return {}
            }
        }
    },
    data () {
        return {
            showRemote: false,
            confirmLoading: false,
            flowId: 0,
            currentBasePath: this.$variateConfig['domainURL'],
            flowView: false,
            auditVisible: false,
            opinion: '',
            pageData: {
                form: {},
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_shipmentLineInformation`, '发货单行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'saleDeliveryItemList',
                        columns: []
                    } },
                    {
                        groupName: '辅料发料',
                        groupCode: 'saleDeliverySubList',
                        type: 'grid',
                        needDynamicsHeight: true,
                        custom: {
                            ref: 'saleDeliverySubList',
                            columns: [
                                { type: 'checkbox', width: 40 },
                                { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                                { field: "arrivedTime", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_uSKI_277b0df5`, '到货时间'), width: 120 },
                                { field: "materialNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_PurchaseMassProdHeadList_materialCode`, '物料编码'), width: 120 },
                                { field: "materialName", title: this.$srmI18n(`${this.$getLangAccount()}i18n_massProdHead0e15_materialName`, '物料名称'), width: 120 },
                                { field: 'deliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_hSWR_28388855`, '发货数量'), width: 120 },
                                { field: "quantityUnit_dictText", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_dtL_1301213`, '主单位'), width: 120 },
                                { field: "conversionRate", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_conversionRate`, '换算率'), width: 120 },
                                { field: "secondaryQuantity", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_secondaryQuantity`, '辅数量'), width: 120 },
                                { field: "purchaseUnit_dictText", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_BtL_22528dd`, '辅单位'), width: 120 },
                                { field: "itemStatus_dictText", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_lineStatus`, '行状态'), width: 120 },
                                { field: "factory", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_GMVR_2c647268`, '库存组织'), width: 120, slots: {default: 'renderDictLabel'}, dictCode: 'purchase_organization_info#org_name#org_code#org_category_code="factory" && status="1"' },
                                { field: "materialSpec", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_materialSpec`, '物料规格'), width: 120 },
                                { field: "receiveQuantity", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '收货数量'), width: 120 },
                                { field: "remainQuantity", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_bUlSWR_2ed782e0`, '剩余收货数量'), width: 120 },
                                { field: "batchNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batchNumber`, '批次号'), width: 120 },
                                { field: "batch_dictText", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_batch`, '是否批次管理'), width: 120 },
                                { field: "ncQualityDayNum", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_ncQualityDayNum`, '保质天数'), width: 120 },
                                {  field: "productionDate", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_produceDate`, '生产日期'), width: 120 },
                                { field: "expiryDate", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_expiryDate`, '失效日期'), width: 120 },
                                { field: "taxCode", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxCode`, '税码'), width: 120 },
                                { field: "taxRate", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_taxRate`, '税率'), width: 120 },
                                { field: "price", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_price`, '含税价'), width: 120 },
                                { field: "netPrice", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_netPrice`, '净价'), width: 120 },
                                { field: "orderNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderNumber`, '订单号'), width: 120 },
                                { field: "orderItemNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_orderItemNumber`, '订单行号'), width: 120 },
                                { field: "returnQuantity", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_returnQuantity`, '退货数量'), width: 120 },
                                { field: "requireDate", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_requireDate`, '需求日期'), width: 120 },
                                { field: "overTolerance", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_overTolerance`, '超量容差率'), width: 120 },
                                { field: "purchaseRemark", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_purchaseRemark`, '需方备注'), width: 120 },
                                { field: "supplierRemark", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_supplierRemark`, '供方备注'), width: 120 },
                                { field: "sourceType_dictText", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceType`, '来源类型'), width: 120 },
                                { field: "sourceNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceNumber`, '来源单号'), width: 120 },
                                { field: "sourceItemNumber", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_sourceItemNumber`, '来源单行号'), width: 120 },
                                { field: 'quantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_orderQuantity`, '订单数量'), width: 120 },
                                { field: 'onWayQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_onWayQuantity`, '订单在途数量'), width: 120 },
                                { field: "orderReceiveQuantity", title: this.$srmI18n(`${this.$getLangAccount()}i18n_field_receiveQuantity`, '订单收货数量'), width: 120 },
                                { field: 'notDeliveryQuantity', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_notDeliveryQuantity`, '订单未交货数量'), width: 120 },
                            ],
                        }
                    },
                    { 
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_accessory`, '附件'), 
                        groupCode: 'fileInfo', 
                        type: 'grid', 
                        needDynamicsHeight: true,
                        custom: {
                        ref: 'saleAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'itemNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_rowIitem`, '行项目'), width: 120 },
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 120 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'uploadSubAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                        ]
                    } }

                ],
                formFields: [],
                publicBtn: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                detail: '/delivery/saleDeliveryHead/queryById',
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    computed: {
        fileSrc () {
            let templateNumber = this.currentEditRow ? this.currentEditRow.templateNumber : ''
            let elsAccount = this.currentEditRow.templateAccount || this.currentEditRow.busAccount
            let templateVersion = this.currentEditRow.templateVersion
            let time = new Date().getTime()
            return `${this.$variateConfig['configFiles']}/${elsAccount}/sale_delivery_${templateNumber}_${templateVersion}.js?t=`+time
        }
    },
    created () {
        // 如果是外部的参数，先请求获取模板js必须的参数
        if (this.$route.query && this.$route.query.open  && this.$route.query.id) {
            getAction(this.url.detail, {id: this.$route.query.id}).then((res) => {
                if (res && res.success) {
                    this.currentEditRow.templateNumber = res.result.templateNumber
                    this.currentEditRow.templateVersion = res.result.templateVersion
                    this.currentEditRow.templateAccount = res.result.templateAccount
                    this.currentEditRow.historyRefundList = res.result.historyRefundList
                    this.showRemote = true
                }
            })
        } else {
            // 没有就直接走原来的逻辑
            this.showRemote = true

            getAction(this.url.detail, {id: this.currentEditRow.id}).then((res) => {
              if (res && res.success) {
                this.currentEditRow.historyRefundList = res.result.historyRefundList
              }
            })
        }
    },
    methods: {
        // 請求接口後格式化列表數據
        async formatTableData(data) {
            this.setColumnData();
            console.log('請求接口後格式化列表數據', data)
            return new Promise((resolve, reject) => {
                data = data.map((item) => {
                if (item.price === 0 || Number(item.price) > 0) {
                    item.price = Number(item.price).toFixed(6)
                }
                if (item.netPrice === 0 || Number(item.netPrice) > 0) {
                    item.netPrice = Number(item.netPrice).toFixed(6)
                }
                return item
                })
                resolve(data)
            })
        },
        // 设置列显示
        setColumnData() {
            let st = setTimeout(() => {
                let itemGrid = this.$refs.detailPage.$refs.saleDeliveryItemList[0]
                let { fullData } = itemGrid.getTableData();
                if(fullData.length >= 1) {
                    let item = fullData[0];
                    if(item.price == item.netPrice && !item.taxRate) {
                        let columnsList = itemGrid.getColumns();
                        columnsList = columnsList.map((column) => {
                            if (column.field == 'taxCode' || column.field == 'taxRate') {
                                column.visible = false;
                            }
                            return column
                        })
                        itemGrid.loadColumn(columnsList);
                    }
                }
                clearTimeout(st);
                st = null;
            }, 100)
        },
        handleLoadSuccess (res) {
            this.currentEditRow = res.res.result
            this.showRemote = true
        },
        goBack () {
            this.$emit('hide')
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        }
    }
}
</script>
