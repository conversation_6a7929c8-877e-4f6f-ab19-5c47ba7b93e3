import { srmI18n, getLangAccount } from '@/utils/util'
export const tabs = [
    srmI18n(`${getLangAccount()}#i18n_title_operaRecord`, '操作记录')
    // srmI18n(`${getLangAccount()}#i18n_title_auditRecord`, '审批记录')  
]

export const operaUrl = '/log/queryOptHisList'
export const auditUrl = '/elsUflo/audit/auditHislist'

export const operaPagerConfig = {
    total: 0,
    currentPage: 1,
    pageSize: 500,
    align: 'right',
    pageSizes: [20, 50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
    perfect: true
}

export const auditPagerConfig = {
    total: 0,
    currentPage: 1,
    pageSize: 500,
    align: 'right',
    pageSizes: [20, 50, 100, 200, 500],
    layouts: ['PrevJump', 'PrevPage', 'JumpNumber', 'NextPage', 'NextJump', 'Sizes', 'FullJump', 'PageCount', 'Total'],
    perfect: true
}

export const operaColumns = [
    {
        'type': 'seq',
        'width': 50,
        'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_operatorPerson`, '操作人'),
        'field': 'username',
        'width': 200
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_operationTime`, '操作时间'),
        'field': 'createTime',
        'width': 150
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_action`, '动作'),
        'field': 'operateName',
        'width': 200
    },
    {
        'title': srmI18n(`${getLangAccount()}#i18n_title_elapsedTimeMS`, '耗时（毫秒）'),
        'field': 'costTime'
    }
]

export const auditColumns = [
    {
        'type': 'seq',
        'width': 50,
        'title': srmI18n(`${getLangAccount()}#i18n_title_seq`, '序号')
    },
    { field: 'subject', title: srmI18n(`${getLangAccount()}#i18n_title_processTheTheme`, '流程主题'), width: 220 },
    { field: 'nodeName', title: srmI18n(`${getLangAccount()}#i18n_title_node`, '节点'), width: 100 },
    { field: 'state_dictText', title: srmI18n(`${getLangAccount()}#i18n_baseForm01bf_ruleStatus`, '状态'), width: 100 },
    { field: 'owner', title: srmI18n(`${getLangAccount()}#i18n_field_userDeal`, '处理人'), width: 120 },
    { field: 'opinion', title: srmI18n(`${getLangAccount()}#i18n_field_opinion`, '处理意见'), width: 120 },
    { field: 'createDate', title: srmI18n(`${getLangAccount()}#i18n_field_createTime`, '创建时间'), width: 150 },
    { field: 'endDate', title: srmI18n(`${getLangAccount()}#i18n_field_completeTime`, '完成时间'), width: 150 }
]