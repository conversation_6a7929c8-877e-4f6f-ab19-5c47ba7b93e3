<template>
  <div style="font-size: 12px">
    <template v-for="column in columns">
      <a-row
        v-if="column.hidden === '0'"
        :key="column.columnCode">
        <a-col
          class="text-title"
          :span="10">
          {{ `${column.columnName}:` }}
        </a-col>
        <a-col
          class="text-content"
          :span="14">
          {{ row[column.columnCode] }}
        </a-col>
      </a-row>
    </template>
  </div>
</template>

<script lang="jsx">
export default {
    name: 'EnquiryMaterialInfo',
    props: {
        columns: {
            default: () => [],
            type: Array
        },
        row: {
            default: () => {},
            type: Object
        }
    }
}
</script>

<style lang="less" scoped>
.text-title {
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.text-content {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>