<template>
  <div class="page">
    <a-spin :spinning="confirmLoading">
      <content-header
        v-if="showHeader"
        :btns="btns"
      />
      <finalQuoteList 
        :pageStatus="'edit'"
        :canfinalQuoteList="pageStatus == 'edit'"
        determineType="sub"
        @back="back"
        :show="showfinalQuoteList"
        :currentEditRow="formData"
        v-if="showfinalQuoteList" />
      <div
        v-else
        class="container"
        :style="style">
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_sBLVH_7e1ecb2e`, '中标人信息') }}</span>
          </titleTrtl>
          <vxe-grid
            v-bind="gridConfig"
            :height="250"
            ref="table"
            :data="tableData"
            :columns="tableColumns"
            show-overflow="title" >
          </vxe-grid>
        </div>
        <div>
          <titleTrtl>
            <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjW_27a9ee3d`, '变更原因') }}</span>
          </titleTrtl>
          <div>
            <a-row class="margin-t-20">
              <a-col :span="3">
                <div class="label">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明:') }}
                </div>
              </a-col>
              <a-col :span="12">
                <a-textarea
                  :disabled="pageStatus == 'detail'"
                  v-model="formData.reason"
                  :auto-size="{ minRows: 2, maxRows: 6 }"></a-textarea>
              </a-col>
            </a-row>
            <a-row class="margin-t-20">
              <a-col :span="3">
                <div class="label">
                  {{ $srmI18n(`${$getLangAccount()}#i18n_field_BIW_23da548`, '附件:') }}
                </div>
              </a-col>
              <a-col :span="3">
                <a-upload
                  name="file"
                  :multiple="false"
                  :action="uploadUrl"
                  :headers="uploadHeader"
                  :accept="accept"
                  :data="{headId: formData.id, businessType: 'biddingPlatform', 'sourceNumber': tenderCurrentRow.tenderProjectNumber || tenderCurrentRow.id || '', 'actionRoutePath': '/srm/ebidding/PurchaseTenderProject,采购商与供应商的路径逗号隔开'}"
                  :beforeUpload="beforeUpload"
                  @change="handleUploadChange"
                >
                  <a-button 
                    @click="fileUp"
                    v-if="showHeader && pageStatus == 'edit'"> <a-icon type="upload" /> {{ $srmI18n(`${$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件') }}</a-button>
                </a-upload>
              </a-col>
              <a-col 
                :span="20"
                :offset="3">
                <div
                  v-for="fileItem in formData.attachmentList"
                  :key="fileItem.id">
                  <span>{{ fileItem.fileName }}</span>
                  <a-button 
                    @click="preViewEvent(fileItem)"
                    type="link">{{ $srmI18n(`${$getLangAccount()}#i18n_title_preview`, '预览') }}</a-button>
                  <a-button 
                    v-if="showHeader && pageStatus == 'edit'"
                    @click="deleteFile(fileItem)"
                    type="link" 
                    style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
                  <a-button 
                    v-if="pageStatus == 'detail'"
                    @click="downloadEvent(fileItem)"
                    type="link" 
                    style="padding-left: 0;">{{ $srmI18n(`${$getLangAccount()}#i18n_title_colunmDownload`, '下载') }}</a-button>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
      
      <flowViewModal
        v-model="flowView"
        :flowId="flowId"/>
    </a-spin>
  </div>
</template>
<script lang="jsx">
import ContentHeader from '@views/srm/bidding_new/BiddingHall/components/content-header'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl'
import { postAction, getAction } from '@views/srm/bidding_new/plugins/manage'
import { tableMixins } from '@/views/srm/bidding_new/plugins/tableMixins'
import flowViewModal from '@comp/flowView/flowView'
import { valiStringLength } from '@views/srm/bidding_new/utils/index'
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import { USER_INFO } from '@/store/mutation-types'
import finalQuoteList from './finalQuoteList'
import {getAttachmentUrl} from '@/views/srm/bidding_new/utils'
export default {
    name: 'ReDetermineTheWinningBidder',
    components: {
        titleTrtl,
        ContentHeader,
        flowViewModal,
        finalQuoteList
    },
    mixins: [baseMixins, tableMixins],
    props: {
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        },
        pageType: {
            type: Boolean,
            default () {
                return false
            }
        }
    },
    data () {
        return {
            flowView: false,
            flowId: 0,
            confirmLoading: false,
            showfinalQuoteList: false,
            tableColumns: [
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eBtLRL_1a33c72d`, '投标单位名称'),
                    'field': 'supplierName'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                    field: 'scopeSort',
                    width: '80'
                },
                // {
                //     'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'),
                //     'field': 'scopeSort'
                // },
                // {
                //     'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                //     'field': 'quote'
                // },
                // {
                //     'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sBHf_252229e6`, '中标金额'),
                //     'field': 'evaPrice'
                // },
                {
                    'title': this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQsBL_c7668749`, '是否中标人'),
                    'field': 'affirm',
                    'width': '120',
                    slots: {
                        default: ({row, column}) => {
                            return [
                                <a-checkbox disabled={this.pageStatus == 'detail'}v-model={row[column.property]} ></a-checkbox>
                            ]
                        }
                    }
                }
            ],
            tableData: [],
            formData: {},
            specialFields: [],
            showHeader: true,
            height: 0,
            accept: '.doc, .docx, .xls, .xlsx, .ppt, .png, .jpg, .jpeg, .gif, .pptx, .pdf',
            uploadHeader: {'X-Access-Token': this.$ls.get('Access-Token')},
            uploadUrl: `${this.$variateConfig['domainURL']}/attachment/purchaseAttachment/upload`,
            // btns: [
            //     { title: '保存', type: 'primary', click: this.save },
            //     { title: '发布', type: 'primary', click: this.submit }
            // ],
            url: {
                queryById: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryById',
                add: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/add',
                edit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/edit',
                submit: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/submit',
                queryPrice: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryFieldCategoryBySubpackageId',
                queryInfo: '/tender/calibration/purchaseTenderProjectBidWinningAffirmHead/queryBidWinningConfirmInitInfoBySubpackage'
            }
        }
    },
    inject: [
        'tenderCurrentRow',
        'subpackageId',
        'currentSubPackage', 
        'resetCurrentSubPackage'
    ],
    computed: {
        subId () {
            return this.subpackageId()
        },
        style () {
            const offset = this.showHeader ? 120 : 66
            return { minHeight: `${this.height - offset}px` }
        },
        subPackageRow () {
            return this.currentSubPackage()
        },
        btns () {
            let btn = []
            if (this.showfinalQuoteList) {
                btn = [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), type: 'primary', click: this.back }
                ] 
            } else {
                if ((this.formData.status == '0' || !this.formData.status) && this.pageStatus == 'edit') {
                    btn = [
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save },
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), type: 'primary', click: this.publish }
                    ]
                    let { evaluationType} = this.subPackageRow
                    if (evaluationType == '1') {
                        btn.push(
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAessu_119c780`, '发起最终报价'), attrs: {type: 'primary'}, click: this.finalQuote }
                        )
                    }
                }
                if (this.alreadyPublish && this.pageStatus == 'edit'){
                    btn = [
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.save }
                    ]
                    let { evaluationType} = this.subPackageRow
                    if (evaluationType == '1') {
                        btn.push(
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_hAessu_119c780`, '发起最终报价'), attrs: {type: 'primary'}, click: this.finalQuote }
                        )
                    }
                }
                //点击发布后已发布状态status == 1以及审批中状态aduitStatus == 1，
                if(this.formData.status == '1' && this.formData.auditStatus == '1'){
                    btn = [
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_withdraw`, '撤回'), type: 'primary', click: this.withdraw }
                    ]
                }
                if(this.formData.audit == '1' && this.formData.status != '0' && this.formData.flowId != null){
                    btn.push(
                        { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_viewProcess`, '查看流程'), type: 'primary', click: this.showFlow }
                    )
                }
                if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') btn = []
                btn.push({ title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBackPage })
            }
            return btn
        },
        pageStatus () {
            if (this.$ls.get('SET_TENDERCURRENTROW').applyRole == '0') {
                return 'detail'
            } else {
                if (this.pageType) {
                    return 'edit'
                } else {
                    return 'detail'
                }
            }
        }
    },
    methods: {
        goBackPage () {
            this.$emit('back')
        },
        async save (callback = null) {
            let params = Object.assign({}, this.formData)
            params['subpackageId'] = this.subId
            params['tenderProjectId'] = this.tenderCurrentRow.id
            // let tableData = JSON.parse(JSON.stringify(this.tableData))
            // params['bidWinningAffirmItemList'] = tableData.filter(item => {
            //     return item.affirm
            // }).map(item => {
            //     item.affirm = item.affirm ? '1' : '0'
            //     return item
            // })
            params['bidWinningAffirmPriceItemVoList'] = this.tableData.map(item => {
                item.affirm = item.affirm ? 1 : 0
                return item
            })
            // 拼接数据
            let url = params.id ? this.url.edit : this.url.add
            let saveFlag = '0'
            valiStringLength(params, [
                {field: 'reason', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(url, params).then(res => {
                let type = res.success ? 'success': 'error'
                this.$message[type](res.message)
                if (res.success) {
                    saveFlag = '1'
                    this.currentEditRow.id = res.result.id || ''
                    this.getData()
                }
            }).finally(() => {
                this.confirmLoading = false
                if (saveFlag == '1' && callback) {
                    callback()
                }
            })
        },
        publish () {
            let that = this
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_isConfirmPublishing`, '是否确认发布？'),
                onOk: function () {
                    that.submit()
                
                }
            })
        },
        async submit () {
            let params = Object.assign({}, this.formData)
            // params['bidWinningAffirmItemList'] = this.tableData.filter(item => {
            //     return item.affirm
            // })
            params['bidWinningAffirmPriceItemVoList'] = this.tableData.map(item => {
                item.affirm = item.affirm ? 1 : 0
                return item
            })
            valiStringLength(params, [
                {field: 'reason', label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_AHjWlRW_ba1cc783`, '变更原因说明'), maxLength: 100}
            ])
            this.confirmLoading = true
            postAction(this.url.submit, params).then(res => {
                
                if (res.success) {
                    this.alreadyPublish=true
                    this.goBackPage()
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async getData () {
            let params = {
                subpackageId: this.subId
            }
            try {
                this.confirmLoading = true
                let {evaluationType} = this.subPackageRow
                if (evaluationType == '1') {
                    const res = await getAction(this.url.queryPrice, params)
                    if (res.code == 200 && res.result) {
                        const resultData = res.result
                        let columns = []
                        resultData.forEach(data => {
                            let obj = {
                                title: data.title,
                                children: []
                            }
                            let columnChildren = []
                            data.quoteColumnList.forEach(column => {
                                column['field'] = `${column['field']}_${data['id']}`
                                columnChildren.push(column)
                            })
                            obj.children = columnChildren
                            columns.push(obj)
                        })
                    
                        this.tableColumns = this.tableColumns.filter(column => {
                            if (!column.hasOwnProperty('children')) {
                                return column
                            }
                        })
                        this.tableColumns.splice(2, 0, ...columns)
                    }
                } else {
                    let hasEvaPrice = false
                    this.tableColumns.forEach(item=>{
                        if(item.field == 'quote') hasEvaPrice = true
                    })
                    if(!hasEvaPrice){
                        this.tableColumns.splice(1, 0, {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_eBsuA_9df90613`, '投标报价'),
                            field: 'quote'
                        })
                    }
                }
                console.log('tableColumns', this.tableColumns)
                let res2 = {}
                if (this.currentEditRow.id) {
                    // 有ID
                    params['id'] = this.currentEditRow.id
                    res2 = await getAction(this.url.queryById, params)
                } else {
                    // 无ID
                    params['affirmType'] = '1'
                    res2 = await getAction(this.url.queryInfo, params)
                }
                
                if (res2.code == 200 && res2.result) {
                    let {bidWinningAffirmPriceItemVoList = [], ...others} = res2.result || {}
                    this.formData = others
                    bidWinningAffirmPriceItemVoList && bidWinningAffirmPriceItemVoList.forEach(item => {
                        item.saleQuoteColumnVOS.forEach(vos => {
                            item[`${vos['field']}_${vos['bidLetterId']}`] = vos['value'] || ''
                        })
                        item.affirm = item.affirm == '1' ? true : false
                    })
                    this.tableData = bidWinningAffirmPriceItemVoList
                }
            }catch (error) {
                console.log(error)
            }
            
            this.confirmLoading = false
            // this.$emit('resetCurrentSubPackage')
            this.resetCurrentSubPackage()
        },
        fileUp (event) {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                event.stopPropagation() // 只执行button的click，
            }
        },
        beforeUpload () {
            if (!this.formData.id) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_pleaseSaveDocumentFirst`, '请先保存单据！'))
                return false
            }
        },
        //附件上传
        handleUploadChange ({ file, fileList, event }) {
            // fxw：判断是否有id，有就执行上传方法，没有就直接结束这个函数的调用。
            if(this.formData.id){
                if (file.status === 'done') {
                    if (file.response.success) {
                        console.log('fileList', fileList)
                        // 每次取最后一个值
                        const FileList = fileList[fileList.length - 1]
                        const {result=null} = FileList.response
                        result && (() => {
                            const rt = {
                                uploadElsAccount: this.$ls.get(USER_INFO).elsAccount,
                                uploadSubAccount: this.$ls.get(USER_INFO).subAccount,
                                ...result
                            }
                            this.formData.attachmentList.splice(0, 0, rt)
                        })()
                    } else {
                        this.$message.error(`${file.name} ${file.response.message}.`)
                    }
                } else if (file.status === 'error') {
                    this.$message.error(`文件上传失败: ${file.msg} `)
                }
            }
        },
        init () {
            this.height = document.documentElement.clientHeight
            this.getData()
        },
        withdraw (){
            let that = this
            let params = this.formData
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApproval`, '确认撤销审批'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmRevokeApprovalTips`, '是否确认撤销审批?'),
                onOk: function () {
                    that.postAuditData('/a1bpmn/audit/api/cancel', params)
                }
            })
        },
        showFlow (){
            this.flowId = this.formData.flowId
            this.flowView = true
        },
        postAuditData (invokeUrl, formData) {
            let param = {}
            param['rootProcessInstanceId'] = formData.flowId
            this.confirmLoading = true
            postAction(invokeUrl, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.getData()
                    this.$parent.pageStatus = true
                } else {
                    this.$message.warning(res.message)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        preViewEvent (row){ // 文件预览
            row.subpackageId = this.subId
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        deleteFile (fileItem) { // 删除文件
            this.formData.attachmentList.forEach((item, index, array) => {
                if (item.id == fileItem.id) {
                    array.splice(index, 1)
                }
            })
        },
        async downloadEvent (row) {
            row.subpackageId = this.subId
            const fileName = row.fileName
            let {message: url} = await getAttachmentUrl(row)
            this.confirmLoading = true
            getAction(url, {}, {
                responseType: 'blob'
            }).then(res => {
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        async finalQuote () {
            await this.save()
            this.showfinalQuoteList = true
        },
        back () {
            this.showfinalQuoteList = false
            this.getData()
        }
    },
    mounted () {
        this.init()
    }
}
</script>
<style lang="less" scoped>
.container{

    background-color: #fff;
    margin-left: 6px;
    margin-right: 6px;
}
.margin-t-20{
  margin-top: 20px;
}
.label{
  text-align:right;
  padding-right: 10px;
}
:deep(.ant-upload-list){
    display: none;
}
</style>




