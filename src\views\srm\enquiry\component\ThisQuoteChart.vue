<template>
  <a-modal
    v-drag    
    v-if="showVisible"
    :visible="showVisible"
    :width="800"
    :title="$srmI18n(`${$getLangAccount()}#i18n_title_trendChartOfQuotation`, '本次报价趋势图')"
    :footer="null"
    @cancel="showVisible=false">
    <!-- <div class="chart">
      <vue-echarts
        class="thisTrendEchart"
        ref="thisTrendEchart"
        autoresize
        theme="light"
        :options="options"
        :auto-resize="true" />
    </div> -->
    <div
      v-if="showVisible"
      id="his-chart"
      style="height: 330px; width: 700px" />
    <div>
      <a-select
        v-show="showPrice"
        v-model="priceType"
        style="width: 120px"
        @change="getData">
        <a-select-option value="price">{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价') }}</a-select-option>
        <a-select-option value="netPrice">{{ this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价') }}</a-select-option>
      </a-select>

      <a-select
        v-show="showLadder"
        v-model="ladderQuantity"
        style="width: 120px"
        @change="getData">
        <a-select-option
          v-for="(item, index) in ladderOption"
          :value="item.ladderQuantity"
          :key="index">{{ item.ladder }}</a-select-option>
      </a-select>

      <a-select
        v-show="showCost"
        v-model="costGroup"
        style="width: 120px"
        @change="getData">
        <a-select-option
          v-for="(item, index) in costOption"
          :value="item.groupCode"
          :key="index">{{ item.groupName }}</a-select-option>
      </a-select>
    </div>
  </a-modal>
</template>
<script>
import { getAction } from '@/api/manage'
import * as echarts from 'echarts'

export default {
    name: 'ThisQuoteChart',
    data () {
        return {
            chart: null,
            showVisible: false,
            headId: null,
            itemNumber: null,
            priceType: 'price',
            ladderQuantity: null,
            costGroup: null,
            ladderOption: [],
            costOption: [],
            options: {
                // title: { text: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_trendChartOfQuotation`, '本次报价趋势图') },
                tooltip: { trigger: 'item' },
                legend: { type: 'scroll', top: '30', data: [] },
                grid: { top: '90', left: '5%', bottom: '5%', containLabel: true }, // , width: '100%'
                toolbox: {
                    feature: {
                        magicType: {
                            type: ['line', 'bar'],
                            title: {
                                line: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLNWP_96e1d8f8`, '切换为折线图'),
                                bar: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_FSLdzP_96f476fa`, '切换为柱状图')
                            }
                        },
                        restore: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_Sj_11bec7`, '还原') },
                        saveAsImage: { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_sMLPO_902181c8`, '保存为图片') }
                    }
                },
                xAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_frequencyn`, '次数'),
                    type: 'category',
                    boundaryGap: true,
                    data: []
                },
                yAxis: {
                    name: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_um_9f825`, '价格'),
                    type: 'value'
                },
                series: []
            },
            showPrice: false,
            showLadder: false,
            showCost: false
        }
    },
    methods: {
        initChart () {
            this.chart = echarts.init(document.getElementById('his-chart'))
            this.chart.setOption(this.options)
        },
        clear () {
            this.chart && this.chart.clear()
        },
        open (headId, records){
            let row = records[0]
            this.headId = headId
            this.itemNumber = row.itemNumber
            let quotePriceWay = row.quotePriceWay
            this.normalQuote()
            if(quotePriceWay === '1'){// 阶梯报价
                this.ladderOption = JSON.parse(row.ladderPriceJson)
                this.ladderQuantity = this.ladderOption ? this.ladderOption[0].ladderQuantity : ''
                this.ladderQuote()
            }else if(quotePriceWay === '2'){// 成本报价
                this.costOption = JSON.parse(row.costFormJson).groups
                this.costGroup = this.costOption ? this.costOption[0].groupCode : ''
                this.costQuote()
            }
            this.getData()
        },
        getData (){
            //查询本次报价趋势图
            getAction('/enquiry/purchaseEnquiryItemHis/queryQuoteHisTrend', {
                headId: this.headId,
                itemNumber: this.itemNumber,
                priceType: this.priceType,
                ladderQuantity: this.ladderQuantity,
                costGroup: this.costGroup
            }).then(res => {
                let {result} = res
                const { legendData = [], series = [], xAxisData = [], xaxisData = [] } = result || {}
                this.options.legend.data = legendData
                this.options.series = series
                this.options.xAxis.data = xAxisData.length > 0 ? xAxisData : xaxisData
                this.showVisible = true
                this.$nextTick(() => {
                    this.initChart()
                })
            })
        },
        normalQuote (){
            this.showPrice = true
            this.showLadder = false
            this.showCost = false
        },
        ladderQuote (){
            this.showPrice = true
            this.showLadder = true
            this.showCost = false
        },
        costQuote (){
            this.showPrice = false
            this.showLadder = false
            this.showCost = true
        }
    }
}
</script>