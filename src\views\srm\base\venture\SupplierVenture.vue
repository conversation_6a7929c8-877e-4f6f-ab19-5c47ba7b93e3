<template>
  <div class="supplierVenture">
    <!--返回-->
    <div class="venture-back">
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_suppliersRisk`, '供应商风险') }}</span>
      <a-button @click="goBack">{{ $srmI18n(`${$getLangAccount()}#i18n_title_back`, '返回') }}</a-button>
    </div>

    <div class="venture-cnt p-10">
      <!-- 顶部菜单 -->
    
      <menu-part
        v-bind="{menus:pageData.menus,mainSupplierInfo}"
        @content-header-click="contentHeaderClick"
        @content-reset-header-click="contentResetHeaderClick"
        ref="menuPart"
        v-if="Object.keys(tabData).length>0"
      >
      </menu-part>
      <!-- 展示部分 -->

      <content-tabs
        v-bind="{tabs:pageData.tabs,tabData,curK<PERSON>,curTabName}"
        ref="contentTab"
        class="m-t-8 p-13 p-t-n"
      ></content-tabs>
    </div>
  </div>
</template>

<script>
//组件
import MenuPart from './component/MenuPart'
import ContentTabs from './component/ContentTabs'

//接口
import { postAction } from '@/api/manage'
//vuex
import { mapMutations } from 'vuex'

//js
const LEGAL_RISK='0' //司法风险
const MANAGE_RISK='1' //经营风险
const RELATION_FIND='3' //关系发现
const INDUSTOR_POSTION='4' //行业地位

export default {
    name: 'Supplierventure',
    inject: ['closeCurrent'],
    components: {
        MenuPart,
        ContentTabs
    },
    data () {
        return {
            pageData: {
                menus: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_judicialRisk`, '司法风险'),
                        desc: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_OdWqRcWKV_b48c6f0b`, '诉讼/被执行/失信'),
                        listType: LEGAL_RISK,
                        number: 99,
                        menusTabs: [
                            { //二级tab
                            // type: 'form',
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_legalProceedings`, '法律诉讼'),
                                id: 'SupplierLawsuitList',
                                otherId: 'oldSupplierLawsuitList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_judicialHelp`, '司法协助'),
                                id: 'SupplierJudicialList',
                                otherId: 'oldSupplierJudicialList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_bankruptcReorganizationy`, '破产重整'),
                                id: 'SupplierBankruptcyList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_executee`, '被执行人'),
                                id: 'SupplierZhixinginfoList',
                                otherId: 'oldSupplierZhixinginfoList',
                                total: 0
                            },

                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_dishonestPerson`, '失信人'),
                                id: 'SupplierDishonestList',
                                otherId: 'oldSupplierDishonestList',
                                total: 0
                            }
                        ]
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_businessRisk`, '经营风险'),
                        listType: MANAGE_RISK,
                        desc: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ciGhWOdIH_33dc2af2`, '行政处罚/经营异常'),
                        number: '18',
                        menusTabs: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_punishmentForIndustry`, '行政处罚-工商局'),
                                id: 'SupplierPunishmentInfoList',
                                otherId: 'oldSupplierPunishmentInfoList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_punishmentForOther`, '行政处罚-其他来源'),
                                id: 'SupplierPunishmentList',
                                otherId: 'oldSupplierPunishmentList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operationAbnormal`, '经营异常'),
                                id: 'SupplierAbnormalList',
                                otherId: 'oldSupplierAbnormalList',
                                total: 0
                            }
                        ] 
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_relationshipDiscovery`, '关系发现'),
                        desc: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_eJRHWKVLWlIL_709139b1`, '投资关系/实控人/受益人'),
                        number: '18',
                        listType: RELATION_FIND,
                        menusTabs: [{
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_penetrationChart`, '股权穿透图'),
                            id: 'SupplierInvesttreeList',
                            total: 0
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_beneficiary`, '最终受益人'),
                            id: 'SupplierHumanHoldingList',
                            total: 0
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_actualControl`, '实际控制权'),
                            id: 'SupplierCompanyHolding',
                            total: 0
                        },
                        {
                            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_suspectedActualControl`, '疑似实际控制人'),
                            id: 'supplierActualControlList',
                            total: 0
                        }
                        ]
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_industryStatus`, '行业地位'),
                        desc: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XIjWONVH_66c67789`, '上下游/竞品信息'),
                        number: '3',
                        listType: INDUSTOR_POSTION,
                        menusTabs: [
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_competitiveInfo`, '竞品信息'),
                                id: 'SupplierJingpinList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_customer`, '客户'),
                                id: 'SupplierCustomerList',
                                total: 0
                            },
                            {
                                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_vendorName`, '供应商'),
                                id: 'SupplierSupplyList',
                                total: 0
                            }
                            
                        ]
                    }
                ],
                tabs: []
            },
            tabData: {}, //当前menu的数据
            mainSupplierInfo: {
                enterpriseLogo: '',
                name: ''
            }, //主体供应商信息
            isFrist: true,
            curKey: '',
            curTabName: ''
        }
    },

    created () {
        // 进来默认是司法风险
        // const elsAccount =  sessionStorage.getItem('cache_elsAccout')

        // this.$nextTick(()=>{
        //     this._initFun({
        //         riskType: '0',
        //         elsAccount
        //     })
        // })
    },
    activated () {
        //进来默认是司法风险
        const elsAccount =  sessionStorage.getItem('cache_elsAccout')
        this.$nextTick(()=>{
            this._initFun({
                riskType: '0',
                elsAccount
            })
        })
    },
    methods: {
        ...mapMutations(['SET_MULTI_PAGE']),
        //返回
        goBack (){
            if(this.$route.query.backToHistory){
                window.history.back()
            }else{
                this.closeCurrent()
            }

            // this['SET_MULTI_PAGE'](true)
        },
        //刷新
        contentResetHeaderClick (e){
            const elsAccount =  sessionStorage.getItem('cache_elsAccout')
            this.pageData.tabs=[]
            this.$nextTick(()=>{
                this.pageData.tabs=[...e.menusTabs]
            })
            this._initFun({
                riskType: e.listType,
                elsAccount,
                updated: '1'
            })
        },
        //menues切换
        contentHeaderClick (e){
            const elsAccount =  sessionStorage.getItem('cache_elsAccout')
            this.pageData.tabs=[]
            this.$nextTick(()=>{
                this.pageData.tabs=[...e.menusTabs]
            })
            this._initFun({
                riskType: e.listType,
                elsAccount
            })
        },
        //查看
        handleSee (parms){
            window.open(parms.lawsuitUrl)
        },
        //设置二级tab的数值
        _initSecTabNumber (params){
            for(let [index, vals] of this.pageData.menus.entries()){//根据对应menus设置mebusTab的数量
                if(vals.listType==params.riskType){

                    for(let val of vals.menusTabs){
                        let dataset=val.id.replace(val.id[0], val.id[0].toLowerCase())
                        let totals=0 //总数
                        let dataTemp=[] //临时变量保存
                        if(dataset=='supplierCompanyHolding') {dataset= dataset+'List'}//处理 实际控制权获取数据方法
                        if(val.id=='SupplierCustomerList'||val.id=='SupplierSupplyList'){//客户和供应商列表需要做处理
                            for(let i of this.tabData[dataset]){
                                let pageBean=i.pageBean?JSON.parse(i.pageBean).result:[]
                                dataTemp.push(...pageBean)
                            }
                            totals=dataTemp.length
                        }else{
                            totals=this.tabData[dataset]?this.tabData[dataset].length:0
                        }  
                        if(val.otherId){//如果有历史记录，
                            totals=totals+(this.tabData[val.otherId]?this.tabData[val.otherId].length:0)
                        }
                        val.total=totals
                    }
                    this.pageData.tabs=vals.menusTabs
                    this.$nextTick(()=>{
                        this.$refs.menuPart.curMenuIndex=index
                    })
                                       
                    break
                }

            }
        },
        //初始化函数
        _initFun (params){
            this.$refs.contentTab.tableConfig.loading=true
            postAction('/supplier/supplierMaster/queryRisk', {
                ...params
            }).then(res=>{
                const { success = false, message = '' } = res || {}
                const type = success ? 'success' : 'error'
                if (success) {
                    this.tabData=Object.assign({}, res.result) 
                    this.mainSupplierInfo.enterpriseLogo=this.tabData.enterpriseLogo
                    this.mainSupplierInfo.name=this.tabData.name
                    this._initSecTabNumber(params)
                    this.curKey= this.pageData.tabs[0].id
                    this.curTabName= this.pageData.tabs[0].title
                    
                    console.log(' this.pageData.tabs[0].id', this)
                    console.log(' this.pageData.tabs[0].id', this.pageData.tabs[0].id)
                    this.$nextTick(()=>{
                        this.$refs.contentTab.setCurMenu()
                        this.$refs.contentTab.getTableColunmData()
                        if(this.isFrist) this.isFrist=false
                        this.$forceUpdate()
                    })
            
                }else{
                    this.$refs.contentTab.tableConfig.loading=false
                    this.$message[type](message)
                }
            })
          
        }
    }
}
</script>

<style lang="less" scoped>
.margin-top-20{
    margin-top: 20px;
}
.margin-bottom-20 {
  margin-bottom: 20px;
}
.venture-back{
    background-color: #Fff;
    padding: 8px 14px;
    display: flex;
    justify-content: space-between;
    align-items:center;
}
.supplierVenture{
    // padding: 8px;
    // padding-top:0px ;
    height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}
.p-10{
    padding: 8px;
}
.p-13{
    padding: 13px;
}
.p-t-n{
    padding-top: 0px;
}
.m-t-8{
    margin-top: 8px;
}
</style>
