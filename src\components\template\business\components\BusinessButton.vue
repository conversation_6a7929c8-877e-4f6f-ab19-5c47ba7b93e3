<template>
  <div class="BusinessButton">
    <template v-for="(btn, i) in localButtons">
      <template v-if="btn.key === 'upload'">
        <custom-upload
          :key="'btn_' + i"
          class="btn"
          :single="(btn.args && btn.args.single) || false"
          :disabledItemNumber="(btn.args && btn.args.disabledItemNumber) || false"
          :requiredFileType="(btn.args && btn.args.requiredFileType) || false"
          :property="(btn.args && btn.args.property) || 'materialName'"
          :refName="(btn.args && btn.args.refName) ||''"
          :visible.sync="btn.args.modalVisible"
          :isGridUpload="(btn.args && btn.args.isGridUpload) || false"
          :multiple="(btn.args && btn.args.multiple) || true"
          :title="btn.title ? btn.title : $srmI18n(`${$getLangAccount()}#i18n_title_UpAtachments`, '附件上传')"
          :attrCheck="btn.attrCheck"
          :itemNumberDefaultValue="btn.itemNumberDefaultValue"
          :disabledHead="btn.disabledHead"
          :acceptDictCode="(btn.args && btn.args.attachmentExtensionDictCode) || attachmentExtensionDictCode"
          :dictCode="btn.dictCode || ''"
          :action="(btn.args && btn.args.action) || ACTION"
          :itemNumberKey="(btn.args && btn.args.itemNumberKey) || 'itemNumber'"
          :itemNumberLabel="(btn.args && btn.args.itemNumberLabel) || '行项目'"
          :itemNumbeValueProp="(btn.args && btn.args.itemNumbeValueProp) || ''"
          :itemInfo="(btn.args && btn.args.itemInfo) || []"
          :accept="(btn.args && btn.args.accept) || accept"
          :useLocalAccept="btn.args && btn.args.useLocalAccept"
          :headers="tokenHeader"
          :data="getRequiredData(btn)"
          @change="info => handleUploadChange(info, btn)"
        >
          <a-button
            v-if="btn.beforeChecked"
            type="primary"
            icon="cloud-upload"
            :disabled="(btn.disabled && btn.disabled(btn)) || false"
            v-bind="btn.attrs || {}"
            @click="checkedGridSelect(btn, btn.beforeCheckedCallBack)">
            {{ btn.title ? btn.title : $srmI18n(`${$getLangAccount()}#i18n_title_UpAtachments`, '附件上传') }}
          </a-button>
        </custom-upload>
      </template>
      <template v-else-if="btn.key === 'gridImportExcel'">
        <a-upload
          class="custom-mport"
          :key="'btn_' + i"
          :showUploadList="false"
          :multiple="false"
          :headers="tokenHeader"
          :data="
            () => {
              return handleBeforeUploadData(btn)
            }
          "
          :action="(btn.args && btn.args.url) || IMPORTEXCELACTION"
          @change="info => handleUploadChange(info, btn)"
        >
          <a-button
            class="btn"
            v-bind="btn.attrs || {}">
            <a-icon type="upload" />
            {{ btn.title }}
          </a-button>
        </a-upload>
      </template>
      <template v-else-if="btn.type == 'check'">
        <a-button
          v-if="btn._show"
          :key="'btn_' + i"
          class="btn"
          @click="checkedGridSelect(btn, btn.beforeCheckedCallBack)">
          {{ btn.title }}
        </a-button>
      </template>
      <!-- 向下填充 -->
      <template v-else-if="btn.type == 'tool-fill'">
        <a-button
          :type="btn.type"
          :key="'btn_' + i"
          class="btn"
          v-if="btn._show"
          :disabled="(btn.disabled && btn.disabled(btn)) || false"
          @click="toolButtonHandle(btn, groupCode, btn.beforeCheckedCallBack)">
          {{ btn.title }}
        </a-button>
      </template>
    <template v-else-if="btn.key === 'search'">
        <a-input-search
            class="search-btn"
            v-if="btn.searchParams"
            v-model="btn.searchParams.value"
            v-bind="btn.searchParams"
            @search="btn.searchParams.searchCallback"
        />
    </template>
      <template v-else>
        <a-button
          v-if="btn._show"
          :key="'btn_' + i"
          class="btn"
          v-show="btn.showCondition ? btn.showCondition() : true"
          v-debounce="{ method: 'handleClick', event: 'click', args: btn }"
          v-bind="btn.attrs || {}">
          {{ btn.title }}
        </a-button>
      </template>
    </template>
  </div>
</template>

<script>
const ACTION = '/attachment/purchaseAttachment/upload'
const IMPORTEXCELACTION = '/els/base/excelByConfig/importExcel'

import CustomUpload from '@comp/template/CustomUpload'
import {  ATTACHMENT_EXTENSION_DICT_CODE } from '@/utils/const'
import { ACCEPT } from '@/utils/constant'
import { isObject, isPromise } from '@/utils/util.js'
import { mapState } from 'vuex'

export default {
    name: 'BusinessButtons',
    inject: ['tplRootRef'],
    components: {
        CustomUpload
    },
    props: {
        buttons: {
            type: Array,
            default: () => []
        },
        pageConfig: {
            type: Object,
            default: () => {}
        },
        isToolbarButtons: {
            type: Boolean,
            default: false
        },
        groupCode: {
            type: String,
            default: ''
        },
        isAsync: {
            type: Boolean,
            default: true
        },
        currentEditRow: {
            type: Object,
            default () {
                return {}
            }
        }
    },
    data () {
        return {
            ACTION,
            IMPORTEXCELACTION,
            pageData: {},
            localButtons: [],
            attachmentExtensionDictCode: ATTACHMENT_EXTENSION_DICT_CODE,
            accept: ACCEPT,
            //附件上传配置
            tokenHeader: { 'X-Access-Token': this.$ls.get('Access-Token') }
        }
    },
    watch: {
        buttons: {
            immediate: true,
            handler (arr) {
                if (!Array.isArray(arr) || !arr.length) {
                    return
                }
                this.localButtons = arr.map(n => {
                    // 如果仅为本地配置按钮数据
                    // 无需异步接口返回数据作为判断条件时使用
                    if (!this.isAsync) {
                        this.handleButtonShow(n)
                    }
                    return { _show: true, ...n }
                })
                // 遍历权限码
                this.localButtons =  this.getAuthCodeBtns(this.localButtons)
            }
        },
        // resultData: {
        //     immediate: true,
        //     handler (obj) {
        //         if (!isObject(obj) || !Object.keys(obj).length) {
        //             return
        //         }
        //         debugger
        //         this.pageData = obj || {}
        //         this.localButtons.forEach(n => this.handleButtonShow(n))
        //     }
        // },
        '$attrs.resultData': {
            immediate: true,
            handler (obj) {
                if (!isObject(obj) || !Object.keys(obj).length) {
                    return
                }
                this.pageData = obj || {}
                this.localButtons.forEach(n => this.handleButtonShow(n))
                // 遍历权限码
                this.localButtons =  this.getAuthCodeBtns(this.localButtons)
            }
        }
    },
    methods: {
        getRequiredData (btn) {
            let data = {
                businessType: (btn.args && btn.args.businessType) || '',
                headId: (btn.args && btn.args.headId) || this.currentEditRow.id || ''
            }
            if (btn.attr && typeof btn.attr === 'function') {
                data = Object.assign(data, btn.attr())
            }
            return  data
        },
        toolButtonHandle (btn, group, cb) {
            const otherParams = {
                buttonInfo: btn,
                flag: 'business',
                self: this,
                tplRootRef: this.tplRootRef,
                group
            }
            cb(this.cache_vuex_editActivedInfo, otherParams)
        },
        // 获取非权限码按钮组
        getAuthCodeBtns (btns) {
            let authBtns = []
            if (btns && btns.length) {
                btns.forEach((item)=> {
                    // 配置authorityCode做权限控制
                    if (item && item.authorityCode) {
                        // 有权限
                        if (this.$hasOptAuth(item.authorityCode)) {
                            authBtns.push(item)
                        }
                    } else {
                        // 不配置authorityCode就不做权限控制
                        authBtns.push(item)
                    }
                })
            }
            return authBtns
        },
        handleButtonShow (btn = {}) {
            if (!btn._show) {
                btn._show = undefined
            }
            const show = btn.show
            let payload = { Vue: this, pageConfig: this.pageConfig, pageData: this.pageData, btn }
            if (this.isToolbarButtons) {
                payload.groupCode = this.groupCode
            }
            if (show && typeof show === 'function') {
                let response = show(payload)
                if (isPromise(response)) {
                    response.then(
                        () => {
                            btn._show = true
                        },
                        () => {
                            btn._show = false
                        }
                    )
                } else {
                    btn._show = !!response
                }
            } else if (typeof btn.show !== 'undefined') {
                btn._show = !!btn.show
            } else {
                btn._show = true
            }
        },
        // 允许上传数据前数据组装
        handleBeforeUploadData (btn) {
            let data = {
                groupCode: btn.args && btn.args.groupCode,
                handlerName: btn.args && btn.args.handlerName,
                roelCode: btn.args && btn.args.roelCode,
                id: btn.args && btn.args.id
            }
            // 特殊判断：询价 供应商报价 成本报价方式，单导入按钮导入多表格数据，与公用区分接口、参数
            if (btn.args.type === 'enquiry-sale-cost') {
                data = {
                    busAccount: btn.args.busAccount,
                    account: btn.args.account,
                    templateNumber: btn.args.templateNumber,
                    templateVersion: btn.args.templateVersion
                }
            }
            if (btn && btn.args && typeof btn.args === 'function') {
                let data = { Vue: this, pageConfig: this.pageConfig, btn }
                return btn.args && btn.args(data)
            }
            return data
        },
        //附件上传
        handleUploadChange (info, btn) {
            let groupCode = this.groupCode
            if (btn.callBack) {
                btn.callBack(info, groupCode, this.pageConfig)
            } else {
                // 附件上传通用行添加方法
                this.tplRootRef.uploadCallBack(info, groupCode)
            }
        },
        checkedGridSelect (btn, cb) {
            let parent = this.$parent
            let selectData = null
            if (parent) {
                if (parent.getCheckboxRecords || parent.getRadioRecord) {
                    selectData = parent.getCheckboxRecords() || parent.getRadioRecord()
                } else if (parent.$parent.getCheckboxRecords || parent.$parent.getRadioRecord) {
                    selectData = parent.$parent.getCheckboxRecords() || parent.$parent.getRadioRecord()
                }
            }
            if (selectData && selectData.length) {
                if (cb && typeof cb === 'function') {
                    if (btn.key !== 'batchDownload') {
                        cb(selectData).then(res => {
                            if (res) {
                                btn.args.modalVisible = true
                            }
                        })
                    } else {
                        cb(selectData)
                    }
                } else {
                    btn.args.modalVisible = true
                }
            } else {
                if (btn.msgType === 'batchDownload') {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))
                } else if (btn.key && btn.key === 'upload' && btn.args.isBeforeCustomMethod) {
                    cb(selectData).then(res => {
                        if (res) {
                            btn.args.modalVisible = true
                        }
                    })
                } else {
                    this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))
                }
            }
        },
        beforeClick () {
            return new Promise(resolve => {
                resolve(true)
            })
        },
        afterClick () {
            return new Promise(resolve => {
                resolve(true)
            })
        },
        handleClick (btn) {
            let key = btn.key || ''

            key = key.charAt(0).toUpperCase() + key.slice(1)
            // 本地方法
            let fn = this[`handle${key}`]
            if (btn.click && typeof btn.click === 'function') {
                fn = btn.click
            }
            // 载荷
            let payload = { Vue: this, pageConfig: this.pageConfig, btn }
            if (this.isToolbarButtons) {
                payload.groupCode = this.groupCode
            }
            fn && fn(payload)
        },
        //查看流程
        handleCheckProcess ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterCheckProcess', { Vue, pageConfig, btn })
        },
        // 返回
        handleGoBack ({ Vue, pageConfig, btn }) {
            this.$emit('handleGoBack', { Vue, pageConfig, btn })
        },
        // 发布
        handlePublish ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterPublish', { Vue, pageConfig, btn })
        },
        // 提交审批
        handleSubmit ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterSubmit', { Vue, pageConfig, btn })
        },
        // 撤销审批
        handleAuditCancel ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterAuditCancel', { Vue, pageConfig, btn })
        },
        // 保存
        handleSave ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterSave', { Vue, pageConfig, btn })
        },
        // 拒绝
        handleReject ({ Vue, pageConfig, btn }) {
            this.$emit('handleFooterReject', { Vue, pageConfig, btn })
        },
        // 表行通用新增
        handleGridAdd ({ Vue, pageConfig, btn }) {
            this.$emit('handleToolbarGridAdd', { Vue, pageConfig, btn, groupCode: this.groupCode })
        },
        // 表行通用删除
        handleGridDelete ({ Vue, pageConfig, btn }) {
            this.$emit('handleToolbarGridDelete', { Vue, pageConfig, btn, groupCode: this.groupCode })
        },
        // 导出 Excel
        handleGridExportExcel ({ Vue, pageConfig, btn }) {
            this.$emit('handleToolbarGridExportExcel', { Vue, pageConfig, btn, groupCode: this.groupCode })
        }
    },
    computed: {
        ...mapState({
            cache_vuex_editActivedInfo: state => state.app.cache_vuex_editActivedInfo
        })
    }
}
</script>

<style lang="scss" scoped>

.search-btn {
    margin-left: 6px;
}

.BusinessButton {
    display: inline-block;
    margin-left: 10px;

    .custom-mport,
    .CustomUpload {
        & + .btn {
            margin-left: 6px;
        }
    }

    .btn {
        & + .btn {
            margin-left: 6px;
        }
    }
}
</style>
