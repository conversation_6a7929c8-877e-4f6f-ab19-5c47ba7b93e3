/**

 @Name：layim v3.9.6
 @License：LGPL 
    
 */
const globalSrmI18n = window.globalSrmI18n ? window.globalSrmI18n : function () { }
const globalGetLangAccount = window.globalGetLangAccount ? window.globalGetLangAccount : function () { }
layui
  .define(['layer', 'laytpl', 'upload'], function (exports) {
    var v = '3.9.6';
    var $ = layui.$;
    var layer = layui.layer;
    var laytpl = layui.laytpl;
    var device = layui.device();

    var SHOW = 'layui-show';
    var THIS = 'layim-this';
    var MAX_ITEM = 20;
    var MARGIN_LEFT = 210;


    // 回调
    var call = {};
    /** 触发回调事件 */
    function $emit(eventName, event) {
      layui.each(call[eventName], function (index, item) {
        item && item(event);
      });
    }

    // 鼠标右键菜单
    var MENU = [
      { label: '撤回', event: 'unDoMsg' },
    ]
    // 消息缓存
    var globalMessage = {};

    // 对外API
    var LAYIM = function () {
      this.v = v;
      this.expandEvents = {};
      var self = this
      $('body').on('click', '*[layim-event]', function (e) {
        var othis = $(this);
        var methid = othis.attr('layim-event');
        events[methid] ? events[methid].call(this, othis, e) : '';
        self.expandEvents[methid] ? self.expandEvents[methid].call(this, othis, e) : '';
      });
    };

    // 基础配置
    LAYIM.prototype.config = function (options) {
      var skin = [];
      layui.each(Array(5), function (index) {
        skin.push(layui.cache.dir + 'css/modules/layim/skin/' + (index + 1) + '.jpg');
      });
      options = options || {};
      options.skin = options.skin || [];
      layui.each(options.skin, function (index, item) {
        skin.unshift(item);
      });
      options.skin = skin;
      options = $.extend(
        {
          isfriend: !0,
          isgroup: !0,
          voice: 'default.mp3'
        },
        options
      );
      if (!window.JSON || !window.JSON.parse) {
        return;
      }
      init(options);
      return this;
    };

    // 获取主面板
    LAYIM.prototype.getLayimMain = function () {
      return layimMain
    };
    // 获取主面板
    LAYIM.prototype.closeCurrentChat = function (othis, num) {
      return changeChat(othis, num)
    };
    // 监听事件
    LAYIM.prototype.on = function (events, callback) {
      if (typeof callback === 'function') {
        call[events] ? call[events].push(callback) : (call[events] = [callback]);
      }
      return this;
    };

    // 获取所有缓存数据
    LAYIM.prototype.cache = function () {
      return cache;
    };

    // 打开一个自定义的会话界面
    LAYIM.prototype.chat = function (data) {
      if (!window.JSON || !window.JSON.parse) {
        return;
      }
      return popchat(data), this;
    };

    // 设置聊天界面最小化
    LAYIM.prototype.setChatMin = function () {
      return setChatMin(), this;
    };

    // 设置当前会话状态
    LAYIM.prototype.setChatStatus = function (str) {
      var thatChat = thisChat();
      if (!thatChat) {
        return;
      }
      var status = thatChat.elem.find('.layim-chat-status');
      return status.html(str), this;
    };

    // 接受消息
    LAYIM.prototype.getMessage = function (data) {
      return getMessage(data), this;
    };

    // 桌面消息通知
    LAYIM.prototype.notice = function (data) {
      return notice(data), this;
    };

    // 打开添加好友/群组面板
    LAYIM.prototype.add = function (data) {
      return popAdd(data), this;
    };

    // 好友分组面板
    LAYIM.prototype.setFriendGroup = function (data) {
      return popAdd(data, 'setGroup'), this;
    };

    // 消息盒子的提醒
    LAYIM.prototype.msgbox = function (nums) {
      return msgbox(nums), this;
    };

    // 添加好友/群
    LAYIM.prototype.addList = function (data) {
      return addList(data), this;
    };

    // 删除好友/群
    LAYIM.prototype.removeList = function (data) {
      return removeList(data), this;
    };
    // 删除临时会话列表
    LAYIM.prototype.removeHistoryList = function (data) {
      return removeHistoryList(data), this;
    };

    // 撤回消息成功后替换原 DOM 节点内容
    LAYIM.prototype.undoMessageCallback = function (data) {
      return undoMessageCallback(data), this;
    }

    // 获取会话消息 cid
    LAYIM.prototype.setMessageIdReturn = function (data) {
      return setMessageIdReturn(data), this;
    }

    // 设置好友在线/离线状态
    LAYIM.prototype.setFriendStatus = function (id, type) {
      var list = $('.layim-friend' + id);
      list[type === 'online' ? 'removeClass' : 'addClass']('layim-list-gray');
    };

    // 解析聊天内容
    LAYIM.prototype.content = function (content) {
      return layui.data.content(content);
    };
    // 主面板大分组tab选择 
    LAYIM.prototype.mainTabDeaultOpen = function (index) {
      return events.tab(index | 0);
    };

    // 主面板大分组tab选择 
    LAYIM.prototype.getMembersData = function (bool) {
      return getMembersData(bool), this;
    };

    // 主模板
    // 当前 DOM 节点存放明细数据在 data-info 上
    var listTpl = function (options) {
      var nodata = {
        friend: globalSrmI18n(globalGetLangAccount() + '#i18n_field_rzVIPSyj_f9256454', '该分组下暂无好友'),
        group: globalSrmI18n(globalGetLangAccount() + '#i18n_field_PSaV_302753be', '暂无群组'),
        history: globalSrmI18n(globalGetLangAccount() + '#i18n_field_PSvKME_aefcc6cd', '暂无历史会话')
      };
      options = options || {};
      options.item = options.item || ('d.' + options.type) || [];

      var onlineI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_field_KW_b0c97', '在线');
      var offlineI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_field_vW_f3c64', '离线');

      // 针对friend 修改源码
      return [
        '{{# var length = 0, info; layui.each(' + options.item + ', function(i, data) { length++; info = encodeURIComponent(JSON.stringify(data)); }}',
        '<li layim-event="chat" data-info={{ info }} ',
        'data-type="' + options.type + '" data-index="{{ ' + (options.index || 'i') + ' }}"',
        'class="layim-' + (options.type === 'history' ? '{{i}}' : options.type + '{{data.id}}'),
        '{{ data.id !== d.mine.id && data.status === "offline" ? " layim-list-gray" : "" }}">',
        '<img src="{{ data.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />',
        '<span class="username">{{ data.username || data.groupname || data.name||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}</span>',
        '<p>{{ data.enterpriseName || "" }}</p>',
        '{{# if ("' + options.type + '" === "friend" || data.type === "friend") { }}',
        '<span class="{{ data.id !== d.mine.id && data.status === "offline" ? "status offline" : "status online" }}">{{ data.id !== d.mine.id && data.status === "offline" ? "' + offlineI18n + '" : "' + onlineI18n + '" }}</span>',
        '{{# } else if (data.newMsgCount && data.newMsgCount > 0) { var count = data.newMsgCount; var multiple = count > 10; if (count > 99) { count = "99+"; } }}',
        '<span class="{{ multiple ? "layui-badge badge multipleWords" : "layui-badge badge" }}">{{ count }}</span>',
        '{{# } }}',
        '</li>',
        '{{# }); if (length === 0) { }}',
        '<li class="layim-null">' + (nodata[options.type] || globalSrmI18n(globalGetLangAccount() + '#i18n_field_PSWF_30240c1c', '暂无数据')) + '</li>',
        '{{# } }}'
      ].join('');
    };

    // 聊天信息右键扩展功能面板
    var chatMousedownMenuTpl = [
      '<div class="mousedownMenu" id="mousedownMenu">',
      '<ul style="margin: 0;">',
      '{{# layui.each(d.menu, function (index, item) { }}',
      '<li layim-event="{{ item.event }}"><span>{{ item.label }}</span></li>',
      '{{# }) }}',
      '</ul>',
      '</div>',
    ].join('');

    var elemTpl = [
      '<div class="layui-layim-main">',
      '<div class="layui-layim-info">',
      '<div class="layui-layim-user">',
      '<img src="{{ d.mine.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />',
      '<span class="enterpriseName">{{ d.mine.enterpriseName || "' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}</span>',
      '<span class="username">{{ d.mine.username + "" + d.mine.elsAccount }}</span>',
      '<div class="layui-layim-status"></div>',
      '</div>',
      // '<input class="layui-layim-remark" placeholder="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_AtPR_3bef58ca", "编辑签名") + '" value="{{ d.mine.remark||d.mine.sign||"" }}">',
      '</div>',
      '<ul class="layui-unselect layui-layim-tab ',
      '{{# if(!d.base.isfriend || !d.base.isgroup){ }}',
      'layim-tab-two',
      '{{# } }}',
      '">',
      '<li class="',
      '{{# if(!d.base.isfriend){ }}',
      ' layim-hide',
      '{{# } else { }}',
      ' layim-this',
      '{{# } }}',
      '" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_contact", "联系人") + '" layim-event="tab" lay-type="friend"><i class="layui-icon layui-icon-friends"></i></li>',
      '<li class="',
      '{{# if(!d.base.isgroup){ }}',
      ' layim-hide',
      '{{# } else if(!d.base.isfriend) { }}',
      ' layim-this',
      '{{# } }}',
      '" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_aV_ff3a0", "群组") + '" layim-event="tab" lay-type="group"><i class="layui-icon layui-icon-group"></i></li>',
      '<li class="',
      '" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_vKME_273cf62f", "历史会话") + '" layim-event="tab" lay-type="history"><i class="layui-icon layui-icon-reply-fill"></i></li>',
      '</ul>',
      '<ul class="layui-unselect layim-tab-content {{# if(d.base.isfriend){ }}layui-show{{# } }} layim-list-friend">',
      '{{# layui.each(d.friend, function(index, item){ var spread = d.local["spread"+index]; }}',
      '<li data-groupid="{{item.id}}">',
      '<h5 data-groupid="{{item.id}}" data-groupname="{{item.groupname}}" layim-event="spread" lay-type="{{ spread }}"><i class="layui-icon">{{# if(spread === "true"){ }}&#xe61a;{{# } else {  }}&#xe602;{{# } }}</i><span>{{ item.groupname||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_LRRzV_d572b2d8", "未命名分组") + '"+index }}</span><em>(<cite class="layim-count"> {{ (item.list||[]).length }}</cite>)</em></h5>',
      '<ul class="layui-layim-list {{# if(spread === "true"){ }}',
      ' layui-show',
      '{{# } }}">',
      listTpl({
        type: 'friend',
        item: 'item.list',
        index: 'index'
      }),
      '</ul>',
      '</li>',
      '{{# }); if(d.friend.length === 0){ }}',
      '<li><ul class="layui-layim-list layui-show"><li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_PSKHL_d4c5d015", "暂无联系人") + '</li></ul>',
      '{{# } }}',
      '</ul>',
      '<ul class="layui-unselect layim-tab-content {{# if(!d.base.isfriend && d.base.isgroup){ }}layui-show{{# } }}">',
      '<li>',
      '<ul class="layui-layim-list layui-show layim-list-group">',
      listTpl({
        type: 'group'
      }),
      '</ul>',
      '</li>',
      '</ul>',
      '<ul class="layui-unselect layim-tab-content  {{# if(!d.base.isfriend && !d.base.isgroup){ }}layui-show{{# } }}">',
      '<li>',
      '<ul class="layui-layim-list layui-show layim-list-history">',
      listTpl({
        type: 'history'
      }),
      '</ul>',
      '</li>',
      '</ul>',
      '<ul class="layui-unselect layim-tab-content">',
      '<li>',
      '<ul class="layui-layim-list layui-show" id="layui-layim-search"></ul>',
      '</li>',
      '</ul>',
      '<ul class="layui-unselect layui-layim-tool">',
      '<li class="layui-icon layim-tool-search" layim-event="search" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_title_search", "搜索") + '">&#xe615;</li>',
      '{{# if(d.base.msgbox){ }}',
      '<li class="layui-icon layim-tool-msgbox" layim-event="msgbox" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_XHnJ_334304e5", "消息盒子") + '">&#xe645;<span class="layui-anim"></span></li>',
      '{{# } }}',
      '{{# if(d.base.find){ }}',
      '<li class="layui-icon layim-tool-find" layim-event="find" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_mY_cf739", "查找") + '">&#xe608;</li>',
      '{{# } }}',
      // '<li class="layui-icon layim-tool-skin" layim-event="skin" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_HSqO_3051d691", "更换背景") + '">&#xe61b;</li>',
      '{{# if(!d.base.copyright){ }}',
      '<li class="layui-icon layim-tool-about" layim-event="about" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_RU_a2b7b", "关于") + '">&#xe60b;</li>',
      '{{# } }}',
      '</ul>',
      '<div class="layui-layim-search"><input><label class="layui-icon" layim-event="closeSearch">&#x1007;</label></div>',
      '</div>'
    ].join('');

    // 换肤模版
    var elemSkinTpl = [
      '<ul class="layui-layim-skin">',
      '{{# layui.each(d.skin, function(index, item){ }}',
      '<li><img layim-event="setSkin" src="{{ item }}"></li>',
      '{{# }); }}',
      '<li layim-event="setSkin"><cite>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IZ_f7326", "简约") + '</cite></li>',
      '</ul>'
    ].join('');

    // 聊天主模板
    var elemChatTpl = [
      '<div class="layim-chat layim-chat-{{d.data.type}}{{d.first ? " layui-show" : ""}}">',
      '<div class="layui-unselect layim-chat-title">',
      '<div class="layim-chat-other">',
      '<img class="layim-{{ d.data.type }}{{ d.data.id }}" src="{{ d.data.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />',
      // '<span class="layim-chat-username" layim-event="{{ d.data.type==="group" ? "groupMembers" : "" }}">',
      '<span layim-event="jumpDetail" class="jumpDetail">',
      '{{ d.data.name||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}',
      '{{d.data.temporary ? "<cite>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_JKME_251575c5", "临时会话") + '</cite>" : ""}}',
      '{{# if(d.data.type==="group"){ }}',
      '</span>',
      '<em class="number" style="vertical-align: top;"></em>',
      '{{# } }}',
      '</span>',
      '<p class="layim-chat-status"></p>',
      '</div>',
      '</div>',
      '<div class="layim-chat-main">',
      '<ul></ul>',
      '<div class="member-setting"><ul class="member-list"></ul><div class="menu-wrapper"><ul class="menu-list"></ul></div></div>',
      '{{# if(d.data.type==="group"){ }}',
      '<div class="toggle"><i layim-event="toggleMember" class="layui-icon layui-icon-more"></i></div>',
      '{{# } }}',
      '</div>',
      '<div class="layim-chat-footer layim-chat-footer-border">',
      '<div class="layui-unselect layim-chat-tool" data-json="{{encodeURIComponent(JSON.stringify(d.data))}}">',
      '<span class="layui-icon layim-tool-face" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_iFBV_42fdb95d", "选择表情") + '" layim-event="face">&#xe60c;</span>',
      '{{# if(d.base && d.base.uploadImage){ }}',
      '<span class="layui-icon layim-tool-image" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_XVPO_24ad7fdf", "上传图片") + '" layim-event="image">&#xe60d;<input type="file" name="file"></span>',
      '{{# }; }}',
      '{{# if(d.base && d.base.uploadFile){ }}',
      '<span class="layui-icon layim-tool-image" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_hdQI_2842fc9f", "发送文件") + '" layim-event="image" data-type="file">&#xe61d;<input type="file" name="file"></span>',
      '{{# }; }}',
      '{{# if(d.base && d.base.isAudio){ }}',
      '<span class="layui-icon layim-tool-audio" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_hdTWWN_2ff63839", "发送网络音频") + '" layim-event="media" data-type="audio">&#xe6fc;</span>',
      '{{# }; }}',
      '{{# if(d.base && d.base.isVideo){ }}',
      '<span class="layui-icon layim-tool-video" title="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_hdTWKN_2ff480c6", "发送网络视频") + '" layim-event="media" data-type="video">&#xe6ed;</span>',
      '{{# }; }}',
      '{{# layui.each(d.base.tool, function(index, item){ }}',
      '<span class="layui-icon layim-tool-{{item.alias}}" title="{{item.title}}" layim-event="extend" lay-filter="{{ item.alias }}">{{item.icon}}</span>',
      '{{# }); }}',
      '{{# if(d.base && d.base.chatLog){ }}',
      '<span class="layim-tool-log" layim-event="chatLog"><i class="layui-icon">&#xe60e;</i>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_LStH_3bb12004", "聊天记录") + '</span>',
      '{{# }; }}',
      '</div>',
      '<div class="layim-chat-textarea"><textarea></textarea></div>',
      '<div class="layim-chat-bottom">',
      '<div class="layim-chat-send">',
      // '{{# if(!d.base.brief){ }}',
      // '<span class="layim-send-close" layim-event="closeThisChat">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_Rl_a72da", "关闭") + '</span>',
      // '{{# } }}',
      '<span class="layim-send-btn" layim-event="send">' + globalSrmI18n(globalGetLangAccount() + "#i18n_title_send", "发送") + '</span>',
      '<span class="layim-send-set" layim-event="setSend" lay-type="show"><em class="layui-edge"></em></span>',
      '<ul class="layui-anim layim-menu-box">',
      '<li {{d.local.sendHotKey !== "Ctrl+Enter" ? "class=layim-this" : ""}} layim-event="setSend" lay-type="Enter"><i class="layui-icon">&#xe605;</i>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_pWWWWWIhdXH_4c3e9c76", "按Enter键发送消息") + '</li>',
      '<li {{d.local.sendHotKey === "Ctrl+Enter" ? "class=layim-this" : ""}} layim-event="setSend"  lay-type="Ctrl+Enter"><i class="layui-icon">&#xe605;</i>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_pWWWWWWWWWWIhdXH_4aa29584", "按Ctrl+Enter键发送消息") + '</li>',
      '</ul>',
      '</div>',
      '</div>',
      '</div>',
      '</div>'
    ].join('');

    // 添加好友群组模版
    var elemAddTpl = [
      '<div class="layim-add-box">',
      '<div class="layim-add-img">',
      '<img class="layui-circle" src="{{ d.data.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />',
      '<p class="ellipsis">{{ d.data.name||"" }}</p></div>',
      '<div class="layim-add-remark">',
      '{{# if(d.data.type === "friend" && d.type === "setGroup"){ }}',
      '<p>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_iFzV_42f7417e", "选择分组") + '</p>',
      '{{# } if(d.data.type === "friend"){ }}',
      '<select class="layui-select" id="LAY_layimGroup">',
      '{{# layui.each(d.data.group, function(index, item){ }}',
      '<option value="{{ item.id }}">{{ item.groupname }}</option>',
      '{{# }); }}',
      '</select>',
      '{{# } }}',
      '{{# if(d.data.type === "group"){ }}',
      '<p>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_VWNOiVH_cf7f380c", "请输入验证信息") + '</p>',
      '{{# } if(d.type !== "setGroup"){ }}',
      '<textarea id="LAY_layimRemark" placeholder="' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_OiVH_48577623", "验证信息") + '" class="layui-textarea"></textarea>',
      '{{# } }}',
      '</div>',
      '</div>'
    ].join('');

    // 聊天内容列表模版
    var elemChatMain = [
      '<li {{ d.mine ? "class=layim-chat-mine" : "" }} {{# if(d.cid){ }} data-cid="{{d.cid}}"{{# } }} data-msg="{{encodeURIComponent(JSON.stringify(d))}}">',
      '<div class="layim-chat-user">',
      '<img src="{{ d.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />', ,
      '<cite>',
      '{{# if(d.mine){ }}',
      '<i>{{ layui.data.date(d.timestamp) }}</i>{{ d.username||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}',
      '{{# } else { }}',
      '{{ d.username||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}<i>{{ layui.data.date(d.timestamp) }}</i>',
      '{{# } }}',
      '</cite></div>',
      '<div class="layim-chat-text">{{ layui.data.content(d.content||"&nbsp") }}</div>',
      '</li>'
    ].join('');

    // 聊天内容列表模版
    var elemChatUndo = [
      '<li class="layim-chat-undo" {{# if(d.cid){ }} data-cid="{{d.cid}}"{{# } }} data-msg="{{encodeURIComponent(JSON.stringify(d))}}">',
      '<span>{{ layui.data.content(d.content||"&nbsp") }}</span>',
      '</li>'
    ].join('');

    var onlineI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_field_KW_b0c97', '在线');
    var offlineI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_field_vW_f3c64', '离线');

    var elemChatList = [
      '<li class="layim-{{ d.data.type }}{{ d.data.id }} layim-chatlist-{{ d.data.type }}{{ d.data.id }} layim-this" layim-event="tabChat">',
      '<img src="{{ d.data.avatar }}" onerror="javascript:this.src=\'{{ d.base.defaultAvatar }}\',this.onerror=null" />',
      '<span class="username">{{ d.data.name||"' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名") + '" }}</span>',
      '<p>{{ d.data.enterpriseName || "" }}</p>',
      '{{# if (d.data.type === "friend") { }}',
      '<span class="{{ d.data.id !== d.mine.id && d.data.status === "offline" ? "status offline" : "status online" }}">{{ d.data.id !== d.mine.id && d.data.status === "offline" ? "' + offlineI18n + '" : "' + onlineI18n + '" }}</span>',
      // '{{# } else if (d.data.newMsgCount && d.data.newMsgCount > 0) { var count = d.data.newMsgCount; var multiple = count > 10; if (count > 99) { count = "99+"; } }}',
      // '<span class="{{ multiple ? "layui-badge badge multipleWords" : "layui-badge badge" }}">{{ count }}</span>',
      '{{# } }}',
      '{{# if(!d.base.brief){ }}<i class="layui-icon" layim-event="closeChat">&#x1007;</i>',
      '{{# } }}</li>'
    ].join('');

    // 群组成员Item
    var elemMemberList = [
      '<li data-uid="{{ d.id }}" layim-event="chatFromMember" data-type="friend" data-info={{ encodeURIComponent(JSON.stringify(d)) }}>',
      '<a href="javascript:void(0);">',
      '<img src="{{ d.avatar }}" onerror="javascript:this.src=\'{{ d.defaultAvatar }}\', this.onerror=null;" />',
      '<cite class="memberName">{{ d.username }}{{ d.isMaster ? "【群主】" : "" }}</cite>',
      '</a>',
      '</li>'
    ].join('');

    // 补齐数位
    var digit = function (num) {
      return num < 10 ? '0' + (num | 0) : num;
    };

    // 转换时间
    layui.data.date = function (timestamp) {
      var d = new Date(timestamp || new Date());
      return (
        d.getFullYear() +
        '-' +
        digit(d.getMonth() + 1) +
        '-' +
        digit(d.getDate()) +
        ' ' +
        digit(d.getHours()) +
        ':' +
        digit(d.getMinutes()) +
        ':' +
        digit(d.getSeconds())
      );
    };

    // 转换内容
    layui.data.content = function (content) {
      // 支持的html标签
      var html = function (end) {
        return new RegExp(
          '\\n*\\[' +
          (end || '') +
          '(code|pre|div|span|p|table|thead|th|tbody|tr|td|ul|li|ol|li|dl|dt|dd|h2|h3|h4|h5)([\\s\\S]*?)\\]\\n*',
          'g'
        );
      };
      content = (content || '')
        .replace(/&(?!#?[a-zA-Z0-9]+;)/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/'/g, '&#39;')
        .replace(/"/g, '&quot;') // XSS
        .replace(/@(\S+)(\s+?|$)/g, '@<a href="javascript:;">$1</a>$2') // 转义@

        .replace(/face\[([^\s\[\]]+?)\]/g, function (face) {
          // 转义表情
          var alt = face.replace(/^face/g, '');
          return '<img alt="' + alt + '" title="' + alt + '" src="' + faces[alt] + '">';
        })
        .replace(/img\[([^\s]+?)\]/g, function (img) {
          // 转义图片
          return (
            '<img class="layui-layim-photos" src="' + img.replace(/(^img\[)|(\]$)/g, '') + '">'
          );
        })
        .replace(/file\([\s\S]+?\)\[[\s\S]*?\]/g, function (str) {
          // 转义文件
          var href = (str.match(/file\(([\s\S]+?)\)\[/) || [])[1];
          var text = (str.match(/\)\[([\s\S]*?)\]/) || [])[1];
          if (!href) {
            return str;
          }
          return (
            '<a class="layui-layim-file" href="' +
            href +
            '" download target="_blank"><i class="layui-icon">&#xe61e;</i><cite>' +
            (text || href) +
            '</cite></a>'
          );
        })
        .replace(/audio\[([^\s]+?)\]/g, function (audio) {
          // 转义音频
          return (
            '<div class="layui-unselect layui-layim-audio" layim-event="playAudio" data-src="' +
            audio.replace(/(^audio\[)|(\]$)/g, '') +
            '"><i class="layui-icon">&#xe652;</i><p>' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_WNXH_475cdb65", "音频消息") + '</p></div>'
          );
        })
        .replace(/video\[([^\s]+?)\]/g, function (video) {
          // 转义音频
          return (
            '<div class="layui-unselect layui-layim-video" layim-event="playVideo" data-src="' +
            video.replace(/(^video\[)|(\]$)/g, '') +
            '"><i class="layui-icon">&#xe652;</i></div>'
          );
        })

        .replace(/a\([\s\S]+?\)\[[\s\S]*?\]/g, function (str) {
          // 转义链接
          var href = (str.match(/a\(([\s\S]+?)\)\[/) || [])[1];
          var text = (str.match(/\)\[([\s\S]*?)\]/) || [])[1];
          if (!href) {
            return str;
          }
          return '<a href="' + href + '" target="_blank">' + (text || href) + '</a>';
        })
        .replace(html(), '<$1 $2>')
        .replace(html('/'), '</$1>') // 转移HTML代码
        .replace(/\n/g, '<br>'); // 转义换行
      return content;
    };

    // Ajax
    var post = function (options, callback, tips) {
      options = options || {};
      return $.ajax({
        url: options.url,
        type: options.type || 'get',
        data: options.data,
        dataType: options.dataType || 'json',
        cache: false,
        success: function (res) {
          res.code == 0
            ? callback && callback(res.data || {})
            : layer.msg(res.msg || (tips || 'Error') + ': LAYIM_NOT_GET_DATA', {
              time: 5000
            });
        },
        error: function (err, msg) {
          window.console && console.log && console.error('LAYIM_DATE_ERROR：' + msg);
        }
      });
    };

    // 处理初始化信息
    var cache = { message: {}, chat: [] };
    var init = function (options) {
      var init = options.init || {};
      (mine = init.mine || {}),
        (local = layui.data('layim')[mine.id] || {}),
        (obj = {
          base: options,
          local: local,
          mine: mine,
          history: local.history || {}
        }),
        (create = function (data) {
          var mine = data.mine || {};
          var local = layui.data('layim')[mine.id] || {};
          var obj = {
            base: options, // 基础配置信息
            local: local, // 本地数据
            mine: mine, // 我的用户信息
            friend: data.friend || [], // 联系人信息
            group: data.group || [], // 群组信息
            history: local.history || {}, // 历史会话信息
            srmParams: {
              frendsRequest: data.frendsRequest || []
            } // 源码添加新字段处理当前
          };
          cache = $.extend(cache, obj);

          console.log('cache :>> ', cache);

          popim(laytpl(elemTpl).render(obj));
          if (local.close || options.min) {
            popmin();
          }
          layui.each(call.ready, function (index, item) {
            item && item(obj);
          });
        });
      cache = $.extend(cache, obj);
      if (options.brief) {
        return layui.each(call.ready, function (index, item) {
          item && item(obj);
        });
      }
      init.url ? post(init, create, 'INIT') : create(init);
    };

    // 显示主面板
    var layimMain;
    var popim = function (content) {
      var left = $(window).width() - 32 - 260;
      left = left + 'px';
      var top = $(window).height() - 34 - 520;
      top = top + 'px';

      return layer.open({
        type: 1,
        area: ['280px', '520px'],
        skin: 'layui-box layui-layim',
        title: '&#8203;',
        offset: [top, left],
        // offset: ['30px', '30px'],
        id: 'layui-layim',
        shade: false,
        anim: 2,
        resize: false,
        content: content,
        success: function (layero) {
          layimMain = layero;

          // setSkin(layero);

          if (cache.base.right) {
            layero.css('margin-left', '-' + cache.base.right);
          }
          if (layimClose) {
            layer.close(layimClose.attr('times'));
          }

          // 按最新会话重新排列
          var arr = [];
          var historyElem = layero.find('.layim-list-history');
          historyElem.find('li').each(function () {
            arr.push($(this).prop('outerHTML'));
          });
          if (arr.length > 0) {
            arr.reverse();
            historyElem.html(arr.join(''));
          }

          banRightMenu();
          events.sign();
        },
        cancel: function (index) {
          popmin();
          var local = layui.data('layim')[cache.mine.id] || {};
          local.close = true;
          layui.data('layim', {
            key: cache.mine.id,
            value: local
          });
          return false;
        }
      });
    };

    // 屏蔽主面板右键菜单
    var banRightMenu = function () {
      layimMain.on('contextmenu', function (event) {
        event.cancelBubble = true;
        event.returnValue = false;
        return false;
      });

      var hide = function () {
        layer.closeAll('tips');
      };

      // 自定义历史会话右键菜单
      layimMain.find('.layim-list-history').on('contextmenu', 'li', function (event) {

        var eventWhich = event.which
        // 1: 鼠标左键, 2: 鼠标中键, 3: 鼠标右键
        if (eventWhich !== 3) {
          return
        }

        var othis = $(this);
        var index = othis.data('index')

        var html =
          '<ul data-index="' + index + '"><li layim-event="menuHistory" data-type="one">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IGrME_fd616a3f", "移除该会话") + '</li><li layim-event="menuHistory" data-type="all">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_VVbxMEAB_c0afba49", "清空全部会话列表") + '</li></ul>';

        if (othis.hasClass('layim-null')) {
          return;
        }

        var liH = othis.outerHeight()

        var imW = layimMain.width()

        layer.tips(html, this, {
          tips: 1,
          time: 0,
          anim: 5,
          fixed: true,
          skin: 'layui-box layui-layim-qqtmenu',
          success: function (layero) {
            var stopmp = function (e) {
              stope(e);
            };
            layero.off('mousedown', stopmp).on('mousedown', stopmp);

            // 弹窗css居中
            var layeroW = layero.width()
            var layeroH = layero.height()

            var left = layero.css('left')
            var top = layero.css('top')
            left = ~~left.replace('px', '')
            top = ~~top.replace('px', '')

            left += (imW - layeroW) / 2
            top += layeroH + liH / 2

            layero.css({ 'left': left + 'px', 'top': top + 'px' })
          }
        });
        $(document).off('mousedown', hide).on('mousedown', hide);
        $(window).off('resize', hide).on('resize', hide);
      });
    };

    // 主面板最小化状态
    var layimClose;
    var popmin = function (content) {
      // 主面板最小化事件
      $emit('master-mini');
      if (layimClose) {
        layer.close(layimClose.attr('times'));
      }
      if (layimMain) {
        layimMain.hide();
      }
      cache.mine = cache.mine || {};
      return layer.open({
        type: 1,
        title: false,
        id: 'layui-layim-close',
        skin: 'layui-box layui-layim-min layui-layim-close',
        shade: false,
        closeBtn: false,
        anim: 1,
        offset: cache.base.setMinWinOffset && cache.base.setMinWinOffset() || 'rb',
        resize: false,
        content:
          '<img src="' +
          (cache.mine.avatar || layui.cache.dir + 'css/modules/layim/skin/default.png') +
          '"><span>' +
          (content || cache.base.title || globalSrmI18n(globalGetLangAccount() + "#i18n_field_MjWWWWW_e76eaef5", "我的LayIM")) +
          '</span>',
        move: '#layui-layim-close img',
        success: function (layero, index) {
          layimClose = layero;
          if (cache.base.right) {
            layero.css('margin-left', '-' + cache.base.right);
          }
          layero.on('click', function () {
            // 主面板展开事件
            $emit('master-open');

            layer.close(index);
            layimMain.show();
            var local = layui.data('layim')[cache.mine.id] || {};
            delete local.close;
            layui.data('layim', {
              key: cache.mine.id,
              value: local
            });
          });
        }
      });
    };

    // 显示聊天面板
    var layimChat;
    var layimMin;
    var chatIndex;
    var To = {};
    var popchat = function (data) {
      data = data || {};

      var type = data.type || ''
      var isGroup = type == 'group'

      var chat = $('#layui-layim-chat');
      var render = {
        data: data,
        base: cache.base || {},
        local: cache.local || {},
        mine: cache.mine || {},
      };

      if (!data.id) {
        return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_uhjD_4675e186", "非法用户"));
      }

      if (chat[0]) {
        var list = layimChat.find('.layim-chat-list');

        var listThat = list.find('.layim-chatlist-' + data.type + data.id);
        var chatBox = chat.children('.layim-chat-box');

        // 如果是最小化，则还原窗口
        if (layimChat.css('display') === 'none') {
          // 聊天窗口由最小化复原
          $emit('chat-reset', thisChat());
          layimChat.show();
        }

        if (layimMin) {
          layer.close(layimMin.attr('times'));
        }

        // 打开的是非当前聊天面板，则新增面板
        if (!listThat[0]) {
          list.append(laytpl(elemChatList).render(render));
          chatBox.append(laytpl(elemChatTpl).render(render));
          syncGray(data);
          resizeChat();
        }

        changeChat(list.find('.layim-chatlist-' + data.type + data.id));
        listThat[0] || viewChatlog();
        isGroup && getMembersData({bool:true});
        setHistory(data);
        hotkeySend();

        offMessageRightMenu()
        setTimeout(function () {
          bindMessageRightMenu()
        }, 0)

        // 显示删除Icon
        list.find('li').addClass(SHOW)

        return chatIndex;
      }

      render.first = !0;

      var index = (chatIndex = layer.open({
        type: 1,
        area: '600px',
        skin: 'layui-box layui-layim-chat',
        id: 'layui-layim-chat',
        title: '&#8203;',
        shade: false,
        maxmin: true,
        offset: data.offset || 'auto',
        anim: data.anim || 0,
        closeBtn: cache.base.brief ? false : 1,
        content: laytpl(
          '<ul class="layui-unselect layim-chat-list">' +
          elemChatList +
          '</ul><div class="layim-chat-box">' +
          elemChatTpl +
          '</div>'
        ).render(render),
        success: function (layero) {
          layimChat = layero;

          var list = layimChat.find('.layim-chat-list');
          list.before('<div class="qqtLogo"></div>');

          var hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin');
          var chatTitle = layimChat.find('.layui-layer-title');
          var chatBox = layimChat.find('.layim-chat-box');

          layimChat.css({
            'min-width': '500px',
            'min-height': '420px',
            'padding': '10px 10px 10px 0',
            'background': '#4290F7',
            'border-radius': '24px'
          });

          // 设置聊天面板
          // 如果出现多个聊天面板
          hasFull || layimChat.css('width', 800);
          var maxHeight = layimChat.height() - 72
          console.log('maxHeight :>> ', maxHeight);
          list
            .css({ height: maxHeight, 'max-height': maxHeight })
            .show();

          var marginLeft = MARGIN_LEFT + 'px'
          chatTitle.css('margin-left', marginLeft);
          chatBox.css('margin-left', marginLeft);

          syncGray(data);

          typeof data.success === 'function' && data.success(layero);

          hotkeySend();
          // setSkin(layero);
          setHistory(data);

          viewChatlog();

          isGroup && getMembersData();
          showOffMessage();

          // 聊天窗口打开的监听
          $emit('chat-open', thisChat());
          // 聊天窗口的切换监听
          layui.each(call.chatChange, function (index, item) {
            item && item(thisChat());
          });

          // 查看大图
          layero.on('dblclick', '.layui-layim-photos', function () {
            var src = this.src;
            layer.close(popchat.photosIndex);
            layer.photos({
              photos: {
                data: [
                  {
                    alt: globalSrmI18n(globalGetLangAccount() + "i18n_field_fPCK_29da8a05", "大图模式"),
                    src: src
                  }
                ]
              },
              shade: 0.01,
              closeBtn: 2,
              anim: 0,
              resize: false,
              success: function (layero, index) {
                popchat.photosIndex = index;
              }
            });
          });

          // 对话消息右键菜单
          bindMessageRightMenu()
        },
        full: function (layero) {
          layer.style(
            index,
            {
              width: '100%',
              height: '100%'
            },
            true
          );
          resizeChat();
        },
        resizing: resizeChat,
        restore: resizeChat,
        min: function () {
          // 聊天窗口最小化的监听
          $emit('chat-mini', thisChat());
          setChatMin();
          return false;
        },
        end: function () {
          // 聊天窗口关闭的监听
          $emit('chat-close');
          layer.closeAll('tips');
          layimChat = null;
        }
      }));
      return index;
    };

    var offMessageRightMenu = function () {
      console.log('contextmenu off :>> ');
      var chatMain = layimChat.find('.layim-chat-main');
      chatMain.off('contextmenu', 'ul li*[data-cid]')
    };

    // 聊天内容右键功能菜单
    // 绘制右键菜单
    // 撤回功能
    var globalIndex
    var bindMessageRightMenu = function () {
      var hide = function () {
        layer.closeAll('tips');
      };

      var chatMain = layimChat.find('.layim-chat-main');
      // 只允许撤回自己发出的消息
      chatMain.on('contextmenu', 'ul li.layim-chat-mine', function (event) {

        var index = $(this).index()
        globalIndex = index

        var clientX = event.clientX || event.pageX
        var clientY = event.clientY || event.pageY

        clientX = clientX - 35
        clientY = clientY - 4

        var eventWhich = event.which
        // 1: 鼠标左键, 2: 鼠标中键, 3: 鼠标右键
        if (eventWhich !== 3) {
          return
        }
        var othis = $(this);
        var chatText = othis.find('.layim-chat-text')

        var message = othis.attr('data-msg');
        message = JSON.parse(decodeURIComponent(message))

        // 已撤回消息不作处理
        if (message.undoStatus === 1) {
          return
        }
        // 缓存当前对话明细
        globalMessage = message

        var html = laytpl(chatMousedownMenuTpl).render({ menu: MENU })

        layer.tips(html, chatText, {
          tips: 1,
          time: 0,
          anim: 5,
          fixed: true,
          skin: 'message-tips',
          success: function (layero, index) {
            var stopmp = function (e) {
              stope(e);
            };
            layero.off('mousedown', stopmp).on('mousedown', stopmp);
            // 移动弹框位置
            layero.css({ 'left': clientX + 'px', 'top': clientY + 'px' })
          }
        });
        $(document).off('mousedown', hide).on('mousedown', hide);
        $(window).off('resize', hide).on('resize', hide);
      });
    }

    // 同步置灰状态
    var syncGray = function (data) {
      $('.layim-' + data.type + data.id).each(function () {
        if ($(this).hasClass('layim-list-gray')) {
          layui.layim.setFriendStatus(data.id, 'offline');
        }
      });
    };

    // 重置聊天窗口大小
    var resizeChat = function () {
      var list = layimChat.find('.layim-chat-list');
      var chatMain = layimChat.find('.layim-chat-main');
      var chatHeight = layimChat.height();
      list.css({
        height: chatHeight
      });
      chatMain.css({
        height: chatHeight - 20 - 60 - 158
      });
    };

    // 设置聊天窗口最小化 & 新消息提醒
    var setChatMin = function (newMsg) {

      var top = $(window).height() - 16 - 40;
      top = top + 'px';

      var thatChat = newMsg || thisChat().data;
      var base = layui.layim.cache().base;

      var content = [
        '<div class="wrapper">',
        '<img class="layim-min-img" src="' + thatChat.avatar + '"',
        'onerror="javascript:this.src=\'' + cache.base.defaultAvatar + '\', this.onerror=null;" />',
        '<span class="ellipsis">' + thatChat.name + '</span>',
        '<i class="layui-icon layui-icon-next" />',
        '</div>'
      ].join('')

      if (layimChat && !newMsg) {
        layimChat.hide();
      }
      layer.close(setChatMin.index);
      setChatMin.index = layer.open({
        type: 1,
        title: false,
        skin: 'layui-box layui-layim-min',
        shade: false,
        closeBtn: false,
        anim: thatChat.anim || 2,
        offset: top,
        move: '.layim-min-img',
        resize: false,
        area: ['196px', '50px'],
        content: content,
        success: function (layero, index) {
          if (!newMsg) {
            layimMin = layero;
          }

          if (base.minRight) {
            layer.style(index, {
              left: $(window).width() - layero.outerWidth() - parseFloat(base.minRight)
            });
          }

          layero.find('.layui-layer-content').on('click', function () {
            layer.close(index);
            newMsg
              ? layui.each(cache.chat, function (i, item) {
                popchat(item);
              })
              : layimChat.show();
            if (newMsg) {
              cache.chat = [];
              chatListMore();

              // 关闭新消息提醒事件
              $emit('new-message-close');
            } else {
              // 聊天窗口由最小化复原
              $emit('chat-reset', thisChat());
            }
          });
          // layero.find('.layui-layer-content img').on('click', function (e) {
          //   stope(e);
          // });
        },
        end: function () {
          $emit('system-chat-close');
        }
      });
    };

    // 打开添加好友、群组面板、好友分组面板
    var popAdd = function (data, type) {
      data = data || {};
      layer.close(popAdd.index);
      return (popAdd.index = layer.open({
        type: 1,
        area: '430px',
        title:
          {
            friend: globalSrmI18n(globalGetLangAccount() + "#i18n_field_Suyj_333fe0b3", "添加好友"),
            group: globalSrmI18n(globalGetLangAccount() + "#i18n_field_uNaV_26d0a925", "加入群组")
          }[data.type] || '',
        shade: false,
        resize: false,
        btn: type ? [globalSrmI18n(globalGetLangAccount() + "#i18n_field_RL_f20f6", "确认"), globalSrmI18n(globalGetLangAccount() + "#i18n_title_cancle", "取消")] : [globalSrmI18n(globalGetLangAccount() + "#i18n_field_hdUV_28451f74", "发送申请"), globalSrmI18n(globalGetLangAccount() + "#i18n_field_Rl_a72da", "关闭")],
        content: laytpl(elemAddTpl).render({
          data: {
            name: data.username || data.groupname,
            avatar: data.avatar,
            group: data.group || parent.layui.layim.cache().friend || [],
            type: data.type,
          },
          type: type,
          base: { defaultAvatar: cache.base.defaultAvatar || '' }
        }),
        yes: function (index, layero) {
          var groupElem = layero.find('#LAY_layimGroup');
          var remarkElem = layero.find('#LAY_layimRemark');
          if (type) {
            data.submit && data.submit(groupElem.val(), index);
          } else {
            data.submit && data.submit(groupElem.val(), remarkElem.val(), index);
          }
        }
      }));
    };

    // 切换聊天
    var changeChat = function (elem, del) {
      elem = elem || $('.layim-chat-list .' + THIS);
      var index = elem.index() === -1 ? 0 : elem.index();
      var str = '.layim-chat';
      var cont = layimChat.find(str).eq(index);
      var hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin');

      if (del) {
        // 如果关闭的是当前聊天，则切换聊天焦点
        if (elem.hasClass(THIS)) {
          changeChat(index === 0 ? elem.next() : elem.prev());
        }

        var length = layimChat.find(str).length;

        // 关闭聊天界面
        if (length === 1) {
          return layer.close(chatIndex);
        }

        elem.remove();
        cont.remove();

        // 只剩下1个列表，隐藏左侧区块
        if (length === 2) {
          layimChat.find('.layim-chat-list li').removeClass(SHOW);
          // layimChat.find('.layim-chat-list').hide();
          // if (!hasFull) {
          //   layimChat.css('width', '600px');
          // }
          // layimChat.find('.layim-chat-box').css('margin-left', 0);
        }

        return false;
      }

      elem.addClass(THIS).siblings().removeClass(THIS);
      cont.addClass(SHOW).siblings(str).removeClass(SHOW);
      cont.find('textarea').focus();

      // 聊天窗口的切换监听
      layui.each(call.chatChange, function (index, item) {
        item && item(thisChat());
      });
      showOffMessage();
    };

    // 展示存在队列中的消息
    var showOffMessage = function () {
      var thatChat = thisChat();
      var message = cache.message[thatChat.data.type + thatChat.data.id];
      if (message) {
        // 展现后，删除队列中消息
        delete cache.message[thatChat.data.type + thatChat.data.id];
      }
    };

    // 获取当前聊天面板
    var thisChat = (LAYIM.prototype.thisChat = function () {
      if (!layimChat) {
        return;
      }
      var index = $('.layim-chat-list .' + THIS).index();
      var cont = layimChat.find('.layim-chat').eq(index);
      var to = JSON.parse(decodeURIComponent(cont.find('.layim-chat-tool').data('json')));
      return {
        elem: cont,
        data: to,
        textarea: cont.find('textarea')
      };
    });

    // 记录初始背景
    var setSkin = function (layero) {
      var local = layui.data('layim')[cache.mine.id] || {};
      var skin = local.skin;
      layero.css({
        'background-image': skin
          ? 'url(' + skin + ')'
          : (function () {
            return cache.base.initSkin
              ? 'url(' + (layui.cache.dir + 'css/modules/layim/skin/' + cache.base.initSkin) + ')'
              : 'none';
          })()
      });
    };

    // 记录历史会话
    var setHistory = function (data) {
      var local = layui.data('layim')[cache.mine.id] || {};
      var obj = {};
      var history = local.history || {};
      var is = history[data.type + data.id];

      if (!layimMain) {
        return;
      }

      var historyElem = layimMain.find('.layim-list-history');

      data.historyTime = new Date().getTime();
      history[data.type + data.id] = data;

      local.history = history;

      layui.data('layim', {
        key: cache.mine.id,
        value: local
      });

      if (is) {
        return;
      }

      obj[data.type + data.id] = data;

      var historyList = laytpl(
        listTpl({
          type: 'history',
          item: 'd.data'
        })
      ).render({ data: obj, base: { defaultAvatar: cache.base.defaultAvatar || '' }, mine: cache.mine || {} });

      historyElem.prepend(historyList);
      historyElem.find('.layim-null').remove();
    };

    // 发送消息
    var sendMessage = function () {
      // 断网
      if (!navigator.onLine) {
        return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_alert_APTWIOvW_4f7ec141", "当前网络已断开！"))
      }
      var data = {
        username: cache.mine ? cache.mine.username : globalSrmI18n(globalGetLangAccount() + "#i18n_field_Cq_1147c3", "访客"),
        avatar: cache.mine ? cache.mine.avatar : layui.cache.dir + 'css/modules/layim/skin/+',
        id: cache.mine ? cache.mine.id : null,
        mine: true,
        base: { defaultAvatar: cache.base.defaultAvatar || '' }
      };
      var thatChat = thisChat();
      var ul = thatChat.elem.find('.layim-chat-main >ul');
      var maxLength = cache.base.maxLength || 3000;
      data.content = thatChat.textarea.val();

      // 手动添加 cid 标识
      if (!data.cid) {
        data.cid = +new Date() + ''
      }

      if (data.content.replace(/\s/g, '') !== '') {
        if (data.content.length > maxLength) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_CceHxOBR_6be44645", "内容最长不能超过") + maxLength + globalSrmI18n(globalGetLangAccount() + "#i18n_dict_JB_b8aaf", "个字符"));
        }

        ul.append(laytpl(elemChatMain).render(data));

        var param = {
          mine: data,
          to: thatChat.data
        };
        var message = {
          username: param.mine.username,
          avatar: param.mine.avatar,
          id: param.to.id,
          type: param.to.type,
          content: param.mine.content,
          timestamp: new Date().getTime(),
          mine: true
        };
        pushChatlog(message);

        // 发送消息事件
        $emit('sendMessage', param);
      }
      chatListMore();
      thatChat.textarea.val('').focus();
    };

    // 撤回消息成功后替换原 DOM 节点内容
    var undoMessageCallback = function (mydata) {
      // 断网
      if (!navigator.onLine) {
        return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_alert_APTWIOvW_4f7ec141", "当前网络已断开！"))
      }
      var data = mydata.data || {}

      var thatChat = thisChat()
      var ul = thatChat.elem.find('.layim-chat-main >ul')
      var li = ul.find('li*[data-cid]').eq(globalIndex)
      li.removeClass()
      li.addClass('layim-chat-undo')

      li.html('<span>' + data.content + '</span>')
    };

    // 获取会话消息 cid
    var setMessageIdReturn = function (mydata) {
      // 断网
      if (!navigator.onLine) {
        return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_alert_APTWIOvW_4f7ec141", "当前网络已断开！"))
      }

      var data = mydata.data || {}
      var undoMessageId = data.undoMessageId
      var unSaveMessageId = data.unSaveMessageId

      var thatChat = thisChat()
      var ul = thatChat.elem.find('.layim-chat-main >ul')
      var li = ul.find('li*[data-cid]').last()
      var cid = li.data('cid');

      function setItemDataMsg(elem) {
        // 设置消息 cid
        elem.attr('data-cid', undoMessageId)

        var message = elem.attr('data-msg');
        message = JSON.parse(decodeURIComponent(message))
        message.cid = undoMessageId
        // 重新 data-msg 明细缓存
        elem.attr('data-msg', encodeURIComponent(JSON.stringify(message)))
      }

      // TODO
      // 此处调用jQ data方法无法设置消息返回值
      // 获取当前会话 undoMessageId, 覆盖原消息 cid值
      if (cid == unSaveMessageId) {
        setItemDataMsg(li)
      } else {
        layui.each(ul.find('li*[data-cid]'), function (idx, item) {
          var othis = $(this)
          cid = othis.data('cid');
          if (cid == unSaveMessageId) {
            setItemDataMsg(othis)
          }
        })
      }
    }

    // 桌面消息提醒
    var notice = function (data) {
      data = data || {};
      if (window.Notification) {
        if (Notification.permission === 'granted') {
          var notification = new Notification(data.title || '', {
            body: data.content || '',
            icon: data.avatar || 'http://tp2.sinaimg.cn/5488749285/50/5719808192/1'
          });
        } else {
          Notification.requestPermission();
        }
      }
    };

    // 消息声音提醒
    var voice = function () {
      if (device.ie && device.ie < 9) {
        return;
      }
      var audio = document.createElement('audio');
      audio.src = layui.cache.dir + 'css/modules/layim/voice/' + cache.base.voice;
      audio.play();
    };

    // 接受消息
    var messageNew = {};
    var getMessage = function (data) {
      data = data || {};
      data.base = { defaultAvatar: cache.base.defaultAvatar || '' }

      var elem = $('.layim-chatlist-' + data.type + data.id);
      var group = {};
      var index = elem.index();

      data.timestamp = data.timestamp || new Date().getTime();
      if (data.fromid == cache.mine.id) {
        data.mine = true;
      }
      data.system || pushChatlog(data);
      messageNew = JSON.parse(JSON.stringify(data));

      if (cache.base.voice) {
        voice();
      }

      if ((!layimChat && data.content) || index === -1) {
        if (cache.message[data.type + data.id]) {
          cache.message[data.type + data.id].push(data);
        } else {
          cache.message[data.type + data.id] = [data];
          // 记录聊天面板队列
          if (data.type === 'friend') {
            var friend;
            layui.each(cache.friend, function (index1, item1) {
              layui.each(item1.list, function (index, item) {
                if (item.id == data.fromid) {
                  item.type = 'friend';
                  item.name = item.username;
                  cache.chat.push(item);
                  return (friend = true);
                }
              });
              if (friend) {
                return true;
              }
            });
            if (!friend) {
              data.name = data.username;
              data.temporary = true; // 临时会话
              cache.chat.push(data);
            }
          } else if (data.type === 'group') {
            var isgroup;
            layui.each(cache.group, function (index, item) {
              if (item.id == data.id) {
                item.type = 'group';
                item.name = item.groupname;
                cache.chat.push(item);
                return (isgroup = true);
              }
            });
            if (!isgroup) {
              data.name = data.groupname;
              cache.chat.push(data);
            }
          } else {
            data.name = data.name || data.username || data.groupname;
            cache.chat.push(data);
          }
        }
        if (data.type === 'group') {
          layui.each(cache.group, function (index, item) {
            if (item.id == data.id) {
              group.avatar = item.avatar;
              return true;
            }
          });
        }
        if (!data.system) {
          if (cache.base.notice) {
            notice({
              title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_wJW_193dd9b", "来自") + data.username + globalSrmI18n(globalGetLangAccount() + "i18n_title_news", "的消息"),
              content: data.content,
              avatar: group.avatar || data.avatar
            });
          }
          // 收到新消息提醒事件
          $emit('new-message-open');

          return setChatMin({
            name: globalSrmI18n(globalGetLangAccount() + "#i18n_field_luVXH_b926779d", "收到新消息"),
            avatar: group.avatar || data.avatar,
            anim: 6
          });
        }
      }

      if (!layimChat) {
        return;
      }

      // 接受到的消息不在当前Tab
      var thatChat = thisChat();
      if (thatChat.data.type + thatChat.data.id !== data.type + data.id) {
        elem.addClass('layui-anim layer-anim-06');
        setTimeout(function () {
          elem.removeClass('layui-anim layer-anim-06');
        }, 300);
      }

      var cont = layimChat.find('.layim-chat').eq(index);
      var ul = cont.find('.layim-chat-main >ul');

      // 系统消息
      if (data.system) {
        if (index !== -1) {
          ul.append('<li class="layim-chat-system"><span>' + data.content + '</span></li>');
        }
      } else if (data.content.replace(/\s/g, '') !== '') {
        ul.append(laytpl(elemChatMain).render(data));
      }

      chatListMore();
    };

    // 消息盒子的提醒
    var ANIM_MSG = 'layui-anim-loop layer-anim-05';
    var msgbox = function (num) {
      var msgboxElem = layimMain.find('.layim-tool-msgbox');
      msgboxElem.find('span').addClass(ANIM_MSG).html(num);
    };

    // 存储最近MAX_ITEM条聊天记录到本地
    var pushChatlog = function (message) {
      console.log('message :>> ', message);
      var local = layui.data('layim')[cache.mine.id] || {};
      local.chatlog = local.chatlog || {};
      var thisChatlog = local.chatlog[message.type + message.id];
      if (thisChatlog) {
        // 避免浏览器多窗口时聊天记录重复保存
        var nosame;
        layui.each(thisChatlog, function (index, item) {
          if (
            item.timestamp === message.timestamp &&
            item.type === message.type &&
            item.id === message.id &&
            item.content === message.content
          ) {
            nosame = true;
          }
        });
        if (!(nosame || message.fromid == cache.mine.id)) {
          thisChatlog.push(message);
        }
        if (thisChatlog.length > MAX_ITEM) {
          thisChatlog.shift();
        }
      } else {
        local.chatlog[message.type + message.id] = [message];
      }
      layui.data('layim', {
        key: cache.mine.id,
        value: local
      });
    };

    // 渲染本地最新聊天记录到相应面板
    var viewChatlog = function () {
      const token = localStorage.getItem('t_token');
      const fromId = sessionStorage.getItem('currentUserId');
      //开始请求聊天记录
      const api = '/message/list';
      var BASE_URL = localStorage.getItem('IM_BASE_URL') || '//v5sit.51qqt.com/els/im'
      if (location.hostname.includes('localhost')) {
        // BASE_URL = 'http://localhost:8888/els/im'
        BASE_URL = 'https://v5sit-micro.51qqt.com/els/im'
      }
      var thatChat = thisChat();

      $.ajax({
        headers: {
          'X-Access-Token': token
        },
        url: `${BASE_URL}${api}?chatId=${thatChat.data.id}&chatType=${thatChat.data.type}&fromId=${fromId}&pageNo=1`,
        type: 'get',
        contentType: 'application/json',
        dataType: 'json',
        async: true,
        success: function (res) {
          console.log('ajax success', res);
          // 渲染聊天记录到相应面板
          var ul = thatChat.elem.find('.layim-chat-main >ul');
          layui.each(res.messageList.reverse(), function (index, item) {
            item.base = { defaultAvatar: cache.base.defaultAvatar || '' }
            var undoStatus = item.undoStatus || 0;

            // 渲染模板判断
            var template = elemChatMain;
            if (undoStatus === 1) {
              // 已撤回模板
              template = elemChatUndo;

              var content = '撤回了一条消息'
              var pre = cache.mine.id === item.fromid ? '您' : thatChat.data.name;
              if (pre && pre.length > 25) {
                pre = pre.slice(0, 25) + '...';
              }
              content = pre + content;
              item.content = content;
            }
            ul.append(laytpl(template).render(item));
          });
          chatListMore();
        },
        error: function () {
          console.log('ajax error');
        }
      });
    };

    // 查询群分组成员
    var getMembersData = function (bool = false) {
      // 组装请求传参
      var members = cache.base.members || {};
      var thatChat = thisChat();
      var isMaster = false;

      if (thatChat.data.isMaster) {
        isMaster = !!thatChat.data.isMaster
      } else if (thatChat.data.id.indexOf('_') == -1) {
        // 手动创建群聊群组
        isMaster = thatChat.data.master == cache.mine.id
      } else {
        isMaster = thatChat.data.id.split('_').indexOf(cache.mine.id) != -1
      }

      console.log('isMaster :>> ', isMaster);

      var chatMain = thatChat.elem.find('.layim-chat-main');
      var memberList = chatMain.find('.member-setting >ul.member-list');
      // 清空列表
      if (bool) {
        memberList && memberList.empty()
      }
      // var children = memberList.children()
      // // 考虑同一个分组多次点击操作
      // if (children && children.length) {
      //   return
      // }
      members.data = $.extend(members.data, {
        id: thatChat.data.id
      });

      post(members, function (res) {
        $emit('fliter-list', res.list);
        layui.each(res.list, function (index, item) {
          // item.isMaster = isMaster;
          if(thatChat.data.id.indexOf('_') == -1 && !isMaster && cache.mine.id == item.id){
            isMaster = item.isMaster
          }
          item.defaultAvatar = cache.base.defaultAvatar || '';
          memberList.append(laytpl(elemMemberList).render(item));
        });

        // 采购群主才能踢好友或加好友
        if (isMaster) {
          var addI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_title_add', '添加');
          var delI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_title_delete', '删除');

          var delGroupI18n = globalSrmI18n(globalGetLangAccount() + '#i18n_alert_yIaV_403a9380', '解散群组')

          var addDom = [
            '<li layim-event="inviteFriends">',
            '<a href="javascript:void(0);">',
            '<i class="layui-icon layui-icon-addition" />',
            '<cite>' + addI18n + '</cite>',
            '</a>',
            '</li>'
          ].join('');

          var delDom = [
            '<li layim-event="deleteFriends">',
            '<a href="javascript:void(0);">',
            '<i class="layui-icon layui-icon-subtraction" />',
            '<cite>' + delI18n + '</cite>',
            '</a>',
            '</li>'
          ].join('');

          memberList.append(addDom);
          memberList.append(delDom);

          // 群主拥有解散群能力
          var menuList = chatMain.find('.member-setting .menu-wrapper >ul.menu-list');
          // 清空列表
          if (bool) {
            menuList && menuList.empty()
          }

          menuList.append('<li layim-event="ungroup"><span>' + delGroupI18n + '</span></li>');
        }
        // 获取群员
        var number = res.list.length || 0
        console.log('number :>> ', number);
        thatChat.elem
          .find('.layui-unselect.layim-chat-title em.number')
          .text('(' + number + ')');

        $emit('members', res)
      });
    }

    // 添加好友或群
    var addList = function (data) {
      var obj = {};
      var has;
      var listElem = layimMain.find('.layim-list-' + data.type);
      if (cache[data.type]) {
        if (data.type === 'friend') {
          layui.each(cache.friend, function (index, item) {
            if (data.groupid == item.id) {
              // 检查好友是否已经在列表中
              layui.each(cache.friend[index].list, function (idx, itm) {
                if (itm.id == data.id) {
                  return (has = true);
                }
              });
              if (has) {
                return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_yj_b29ee", "好友") + '[' + (data.username || '') + ']' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_IOMKABs_391a6d4f", "已经存在列表中"), {
                  anim: 6
                });
              }
              cache.friend[index].list = cache.friend[index].list || [];
              obj[cache.friend[index].list.length] = data;
              data.groupIndex = index;
              cache.friend[index].list.push(data); // 在cache的friend里面也增加好友
              return true;
            }
          });
        } else if (data.type === 'group') {
          // 检查群组是否已经在列表中
          layui.each(cache.group, function (idx, itm) {
            if (itm.id == data.id) {
              return (has = true);
            }
          });
          if (has) {
            return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_LIKWW_7e43ec20", "您已是 [") + (data.groupname || '') + globalSrmI18n(globalGetLangAccount() + "i18n_field_WWjaLj_d840c7cb", "] 的群成员"), { anim: 6 });
          }
          obj[cache.group.length] = data;
          cache.group.push(data);
        }
      }

      if (has) {
        return;
      }

      var list = laytpl(
        listTpl({
          type: data.type,
          item: 'd.data',
          index: data.type === 'friend' ? 'data.groupIndex' : null
        })
      ).render({ data: obj, base: { defaultAvatar: cache.base.defaultAvatar || '' }, mine: cache.mine || {} });


      if (data.type === 'friend') {
        var li = listElem.find('>li').eq(data.groupIndex);
        li.find('.layui-layim-list').append(list);
        li.find('.layim-count').html(cache.friend[data.groupIndex].list.length); // 刷新好友数量
        // 如果初始没有好友
        if (li.find('.layim-null')[0]) {
          li.find('.layim-null').remove();
        }
      } else if (data.type === 'group') {
        listElem.append(list);
        // 如果初始没有群组
        if (listElem.find('.layim-null')[0]) {
          listElem.find('.layim-null').remove();
        }
      }
    };
    var removeHistoryList = function (data) {
      var local = layui.data('layim')[cache.mine.id] || {};
      var hisElem = layimMain.find('.layim-list-history');
      let index = data.type + data.id
      var none = '<li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_PSvKME_aefcc6cd", "暂无历史会话") + '</li>';
      if (data.type === 'group') {
        var history = local.history;
        delete history[index]

        local.history = history;

        layui.data('layim', {
          key: cache.mine.id,
          value: local
        });

        // // 删除 DOM
        $('.layim-list-history li.layim-' + index).remove();

        if (hisElem.find('li').length === 0) {
          hisElem.html(none);
        }
      }
      // 关闭当前聊天窗
      if (layimChat) {
        var list = layimChat.find('.layim-chat-list');
        changeChat(list.find('.layim-chatlist-' + data.type + data.id), 1);
      }
    }

    // 移出好友或群
    var removeList = function (data) {
      var listElem = layimMain.find('.layim-list-' + data.type);
      var obj = {};
      if (cache[data.type]) {
        if (data.type === 'friend') {
          layui.each(cache.friend, function (index1, item1) {
            layui.each(item1.list, function (index, item) {
              if (data.id == item.id) {
                var li = listElem.find('>li').eq(index1);
                var list = li.find('.layui-layim-list>li');
                li.find('.layui-layim-list>li').eq(index).remove();
                cache.friend[index1].list.splice(index, 1); // 从cache的friend里面也删除掉好友
                li.find('.layim-count').html(cache.friend[index1].list.length); // 刷新好友数量
                // 如果一个好友都没了
                if (cache.friend[index1].list.length === 0) {
                  li.find('.layui-layim-list').html(
                    '<li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_rzVIISyjr_b2de3422", "该分组下已无好友了") + '</li>'
                  );
                }
                return true;
              }
            });
          });
        } else if (data.type === 'group') {
          layui.each(cache.group, function (index, item) {
            if (data.id == item.id) {
              listElem.find('>li').eq(index).remove();
              cache.group.splice(index, 1); // 从cache的group里面也删除掉数据
              // 如果一个群组都没了
              if (cache.group.length === 0) {
                listElem.html('<li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_PSaV_302753be", "暂无群组") + '</li>');
              }
              return true;
            }
          });
        }
      }
    };

    // 查看更多记录
    var chatListMore = function () {
      var thatChat = thisChat();
      var chatMain = thatChat.elem.find('.layim-chat-main');
      var ul = chatMain.find('>ul');
      var length = ul.find('>li').length;

      if (length >= MAX_ITEM) {
        var first = ul.find('>li').eq(0);
        if (!ul.prev().hasClass('layim-chat-system')) {
          ul.before(
            '<div class="layim-chat-system"><span layim-event="chatLog">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_mAHOtH_7a24191", "查看更多记录") + '</span></div>'
          );
        }
        if (length > MAX_ITEM) {
          first.remove();
        }
      }
      chatMain.scrollTop(chatMain[0].scrollHeight + 1000);
      chatMain
        .find('ul li:last')
        .find('img')
        .load(function () {
          chatMain.scrollTop(chatMain[0].scrollHeight + 1000);
        });
    };

    // 快捷键发送
    var hotkeySend = function () {
      var thatChat = thisChat();
      var textarea = thatChat.textarea;
      textarea.focus();
      textarea.off('keydown').on('keydown', function (e) {
        var local = layui.data('layim')[cache.mine.id] || {};
        var keyCode = e.keyCode;
        if (local.sendHotKey === 'Ctrl+Enter') {
          if (e.ctrlKey && keyCode === 13) {
            sendMessage();
          }
          return;
        }
        if (keyCode === 13) {
          if (e.ctrlKey) {
            return textarea.val(textarea.val() + '\n');
          }
          if (e.shiftKey) {
            return;
          }
          e.preventDefault();
          sendMessage();
        }
      });
    };

    // 表情库
    var faces = (function () {
      var alt = [
        '[微笑]',
        '[嘻嘻]',
        '[哈哈]',
        '[可爱]',
        '[可怜]',
        '[挖鼻]',
        '[吃惊]',
        '[害羞]',
        '[挤眼]',
        '[闭嘴]',
        '[鄙视]',
        '[爱你]',
        '[泪]',
        '[偷笑]',
        '[亲亲]',
        '[生病]',
        '[太开心]',
        '[白眼]',
        '[右哼哼]',
        '[左哼哼]',
        '[嘘]',
        '[衰]',
        '[委屈]',
        '[吐]',
        '[哈欠]',
        '[抱抱]',
        '[怒]',
        '[疑问]',
        '[馋嘴]',
        '[拜拜]',
        '[思考]',
        '[汗]',
        '[困]',
        '[睡]',
        '[钱]',
        '[失望]',
        '[酷]',
        '[色]',
        '[哼]',
        '[鼓掌]',
        '[晕]',
        '[悲伤]',
        '[抓狂]',
        '[黑线]',
        '[阴险]',
        '[怒骂]',
        '[互粉]',
        '[心]',
        '[伤心]',
        '[猪头]',
        '[熊猫]',
        '[兔子]',
        '[ok]',
        '[耶]',
        '[good]',
        '[NO]',
        '[赞]',
        '[来]',
        '[弱]',
        '[草泥马]',
        '[神马]',
        '[囧]',
        '[浮云]',
        '[给力]',
        '[围观]',
        '[威武]',
        '[奥特曼]',
        '[礼物]',
        '[钟]',
        '[话筒]',
        '[蜡烛]',
        '[蛋糕]'
      ];
      var arr = {};
      layui.each(alt, function (index, item) {
        arr[item] = layui.cache.dir + 'images/face/' + index + '.gif';
      });
      return arr;
    })();

    var stope = layui.stope; // 组件事件冒泡

    // 在焦点处插入内容
    var focusInsert = function (obj, str) {
      var result;
      var val = obj.value;
      obj.focus();
      if (document.selection) {
        // ie
        result = document.selection.createRange();
        document.selection.empty();
        result.text = str;
      } else {
        result = [val.substring(0, obj.selectionStart), str, val.substr(obj.selectionEnd)];
        obj.focus();
        obj.value = result.join('');
      }
    };

    // 事件
    var anim = 'layui-anim-upbit';
    var events = {
      // 在线状态
      status: function (othis, e) {
        var hide = function () {
          othis.next().hide().removeClass(anim);
        };
        var type = othis.attr('lay-type');
        if (type === 'show') {
          stope(e);
          othis.next().show().addClass(anim);
          $(document).off('click', hide).on('click', hide);
        } else {
          var prev = othis.parent().prev();
          othis.addClass(THIS).siblings().removeClass(THIS);
          prev.html(othis.find('cite').html());
          prev
            .removeClass('layim-status-' + (type === 'online' ? 'hide' : 'online'))
            .addClass('layim-status-' + type);
          layui.each(call.online, function (index, item) {
            item && item(type);
          });
        }
      },

      // 编辑签名
      sign: function () {
        var input = layimMain.find('.layui-layim-remark');
        input.on('change', function () {
          var value = this.value;
          layui.each(call.sign, function (index, item) {
            item && item(value);
          });
        });
        input.on('keyup', function (e) {
          var keyCode = e.keyCode;
          if (keyCode === 13) {
            this.blur();
          }
        });
      },

      // 大分组切换
      tab: function (othis) {
        var index;
        var main = '.layim-tab-content';
        var tabs = layimMain.find('.layui-layim-tab>li');
        typeof othis === 'number'
          ? ((index = othis), (othis = tabs.eq(index)))
          : (index = othis.index());
        index > 2
          ? tabs.removeClass(THIS)
          : ((events.tab.index = index), othis.addClass(THIS).siblings().removeClass(THIS));
        layimMain.find(main).eq(index).addClass(SHOW).siblings(main).removeClass(SHOW);
      },

      // 展开联系人分组
      spread: function (othis) {
        var type = othis.attr('lay-type');
        var spread = type === 'true' ? 'false' : 'true';
        var local = layui.data('layim')[cache.mine.id] || {};
        othis.next()[type === 'true' ? 'removeClass' : 'addClass'](SHOW);
        local['spread' + othis.parent().index()] = spread;
        layui.data('layim', {
          key: cache.mine.id,
          value: local
        });
        othis.attr('lay-type', spread);
        othis.find('.layui-icon').html(spread === 'true' ? '&#xe61a;' : '&#xe602;');
      },

      // 搜索
      search: function (othis) {
        var search = layimMain.find('.layui-layim-search');
        var main = layimMain.find('#layui-layim-search');
        var input = search.find('input');
        var find = function (e) {
          var val = input.val().replace(/\s/);
          if (val === '') {
            events.tab(events.tab.index | 0);
          } else {
            var data = [];
            var friend = cache.friend || [];
            var group = cache.group || [];
            var html = '';
            for (var i = 0; i < friend.length; i++) {
              for (var k = 0; k < (friend[i].list || []).length; k++) {
                if (friend[i].list[k].username.indexOf(val) !== -1) {
                  friend[i].list[k].type = 'friend';
                  friend[i].list[k].index = i;
                  friend[i].list[k].list = k;
                  data.push(friend[i].list[k]);
                }
              }
            }
            for (var j = 0; j < group.length; j++) {
              if (group[j].groupname.indexOf(val) !== -1) {
                group[j].type = 'group';
                group[j].index = j;
                group[j].list = j;
                data.push(group[j]);
              }
            }
            if (data.length > 0) {
              for (var l = 0; l < data.length; l++) {
                html +=
                  '<li layim-event="chat" data-type="' +
                  data[l].type +
                  '" data-index="' +
                  data[l].index +
                  '" data-list="' +
                  data[l].list +
                  '"><img src="' +
                  data[l].avatar +
                  '"><span>' +
                  (data[l].username || data[l].groupname || globalSrmI18n(globalGetLangAccount() + "#i18n_field_IR_9eff3", "佚名")) +
                  '</span><p>' +
                  (data[l].remark || data[l].sign || '') +
                  '</p></li>';
              }
            } else {
              html = '<li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_SddyR_cb01cc0f", "无搜索结果") + '</li>';
            }
            main.html(html);
            events.tab(3);
          }
        };
        if (!cache.base.isfriend && cache.base.isgroup) {
          events.tab.index = 1;
        } else if (!cache.base.isfriend && !cache.base.isgroup) {
          events.tab.index = 2;
        }
        search.show();
        input.focus();
        input.off('keyup', find).on('keyup', find);
      },

      // 关闭搜索
      closeSearch: function (othis) {
        othis.parent().hide();
        events.tab(events.tab.index | 0);
      },

      // 消息盒子
      msgbox: function () {
        var msgboxElem = layimMain.find('.layim-tool-msgbox');
        layer.close(events.msgbox.index);
        msgboxElem.find('span').removeClass(ANIM_MSG).html('');
        return (events.msgbox.index = layer.open({
          type: 2,
          title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_XHnJ_334304e5", "消息盒子"),
          shade: false,
          maxmin: true,
          area: ['600px', '520px'],
          skin: 'layui-box layui-layer-border',
          resize: false,
          content: cache.base.msgbox
        }));
      },

      // 弹出查找页面
      // find: function () {
        
      //   layer.close(events.find.index);
      //   return (events.find.index = layer.open({
      //     type: 2,
      //     title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_Suyj_333fe0b3", "添加好友"),
      //     shade: false,
      //     maxmin: true,
      //     area: ['798px', '530px'],
      //     skin: 'layui-box layui-layer-border layui-find',
      //     resize: false,
      //     content: cache.base.find
      //   }));
      // },

      // 弹出更换背景
      skin: function () {
        layer.open({
          type: 1,
          title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_HSqO_3051d691", "更换背景"),
          shade: false,
          area: '300px',
          skin: 'layui-box layui-layer-border',
          id: 'layui-layim-skin',
          zIndex: ********,
          resize: false,
          content: laytpl(elemSkinTpl).render({
            skin: cache.base.skin
          }),
          end: function () {
            $emit('skin-close');
          }
        });
        $emit('skin-open', events.find);
      },

      // 关于
      about: function () {
        // layer.alert('版本： ' + v + '<br>版权所有：© LayIM', {
        //   title: '关于 LayIM',
        //   shade: false
        // });
        layer.open({
          type: 0,
          title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_RUHe_263cd9ff", "关于系统"),
          shade: false,
          area: '300px',
          skin: 'layui-box layui-layer-border',
          id: 'layui-layim-skin',
          zIndex: ********,
          resize: false,
          btn: [globalSrmI18n(globalGetLangAccount() + "#i18n_title_define", "确定")],
          content: globalSrmI18n(globalGetLangAccount() + "#i18n_field_rv_e3de4", "版本") + '：v5.0 <br>' + globalSrmI18n(globalGetLangAccount() + "#i18n_alert_rbdjWUbKAAeqtjWRC_85802632", "版权所有：深圳市企企通科技有限公司")
        });
      },

      // 生成换肤
      setSkin: function (othis) {
        var src = othis.attr('src');
        var local = layui.data('layim')[cache.mine.id] || {};
        local.skin = src;
        if (!src) {
          delete local.skin;
        }
        layui.data('layim', {
          key: cache.mine.id,
          value: local
        });
        try {
          layimMain.css({
            'background-image': src ? 'url(' + src + ')' : 'none'
          });
          layimChat.css({
            'background-image': src ? 'url(' + src + ')' : 'none'
          });
        } catch (e) { }
        layui.each(call.setSkin, function (index, item) {
          var filename = (src || '').replace(layui.cache.dir + 'css/modules/layim/skin/', '');
          item && item(filename, src);
        });
      },

      // 弹出聊天面板
      chat: function (othis) {
        var local = layui.data('layim')[cache.mine.id] || {};
        var type = othis.data('type');
        var index = othis.data('index');
        var list = othis.attr('data-list') || othis.index();
        var data = {};
        if (type === 'friend') {
          data = cache[type][index].list[list];
        } else if (type === 'group') {
          data = cache[type][list];
        } else if (type === 'history') {
          data = (local.history || {})[index] || {};
        }
        data.name = data.name || data.username || data.groupname;
        if (type !== 'history') {
          data.type = type;
        }
        popchat(data);
        // 回调
        cache.base.handleChatListCallback && cache.base.handleChatListCallback(othis, data)
      },

      chatFromMember: function () {
        var othis = $(this);
        var info = othis.data('info')
        info = JSON.parse(decodeURIComponent(info))
        info.name = info.username || '';
        info.type = 'friend';
        popchat(info)
      },

      // 切换聊天
      tabChat: function (othis) {
        changeChat(othis);
      },

      // 关闭聊天列表
      closeChat: function (othis, e) {
        changeChat(othis.parent(), 1);
        stope(e);
      },
      closeThisChat: function () {
        changeChat(null, 1);
      },

      // 展开群组成员
      groupMembers: function (othis, e) {
        var icon = othis.find('.layui-icon');
        var hide = function () {
          icon.html('&#xe61a;');
          othis.data('down', null);
          layer.close(events.groupMembers.index);
        };
        var stopmp = function (e) {
          stope(e);
        };

        if (othis.data('down')) {
          hide();
        } else {
          icon.html('&#xe619;');
          othis.data('down', true);
          events.groupMembers.index = layer.tips('<ul class="layim-members-list"></ul>', othis, {
            tips: 3,
            time: 0,
            anim: 5,
            fixed: true,
            skin: 'layui-box layui-layim-members',
            success: function (layero) {
              var members = cache.base.members || {};
              var thatChat = thisChat();
              var ul = layero.find('.layim-members-list');
              var li = '';
              var opt = ''
              var membersCache = {};
              var hasFull = layimChat.find('.layui-layer-max').hasClass('layui-layer-maxmin');
              var listNone = layimChat.find('.layim-chat-list').css('display') === 'none';
              if (hasFull) {
                ul.css({
                  width: $(window).width() - 22 - (listNone || 200)
                });
              }
              members.data = $.extend(members.data, {
                id: thatChat.data.id
              });
              post(members, function (res) {
                var thatChatInfo = thisChat()
                var isMaster = ''
                $emit('fliter-list', res.list);
                layui.each(res.list, function (index, item) {
                  li +=
                    '<li data-uid="' +
                    item.id +
                    '"><a href="javascript:;"><img src="' +
                    item.avatar +
                    '"><cite>' +
                    item.username + (item.isMaster ? '【群主】' : '') +
                    '</cite></a></li>';
                  membersCache[item.id] = item;
                });
                if (thatChatInfo.data.isMaster) {
                  isMaster = thatChatInfo.data.isMaster
                } else {
                  isMaster = thatChatInfo.data.id.split('_').indexOf(cache.mine.id) > -1 ? true : false
                }
                // 采购群主才能踢好友或加好友
                opt = isMaster ? '<span class="operation add"><i layim-event="inviteFriends" title="加好友" class="layui-icon layui-icon-addition"></i></span ><span class="operation delete"><i layim-event="deleteFriends" title="删好友" class="layui-icon layui-icon-subtraction"></i></span>' : ''
                li = li + opt
                ul.html(li);

                // 获取群员
                othis
                  .find('.layim-chat-members')
                  .html(res.members || (res.list || []).length + globalSrmI18n(globalGetLangAccount() + "#i18n_field_L_4eba", "人"));

                // 私聊
                ul.find('li').on('click', function () {
                  var uid = $(this).data('uid');
                  var info = membersCache[uid];
                  popchat({
                    name: info.username,
                    type: 'friend',
                    avatar: info.avatar,
                    id: info.id
                  });
                  hide();
                });

                layui.each(call.members, function (index, item) {
                  item && item(res);
                });
              });
              layero.on('mousedown', function (e) {
                stope(e);
              });
            }
          });
          $(document).off('mousedown', hide).on('mousedown', hide);
          $(window).off('resize', hide).on('resize', hide);
          othis.off('mousedown', stopmp).on('mousedown', stopmp);
        }
      },

      // 发送聊天内容
      send: function () {
        sendMessage();
      },

      // 设置发送聊天快捷键
      setSend: function (othis, e) {
        var box = (events.setSend.box = othis.siblings('.layim-menu-box'));
        var type = othis.attr('lay-type');

        if (type === 'show') {
          stope(e);
          box.show().addClass(anim);
          $(document).off('click', events.setSendHide).on('click', events.setSendHide);
        } else {
          othis.addClass(THIS).siblings().removeClass(THIS);
          var local = layui.data('layim')[cache.mine.id] || {};
          local.sendHotKey = type;
          layui.data('layim', {
            key: cache.mine.id,
            value: local
          });
          events.setSendHide(e, othis.parent());
        }
      },
      setSendHide: function (e, box) {
        (box || events.setSend.box).hide().removeClass(anim);
      },

      toggleMember: function (othis, e) {
        var thatChat = thisChat();
        var chatMain = thatChat.elem.find('.layim-chat-main');
        var setting = chatMain.find('.member-setting')
        setting.toggleClass(SHOW)
      },

      // 表情
      face: function (othis, e) {
        var content = '';
        var thatChat = thisChat();

        for (var key in faces) {
          content += '<li title="' + key + '"><img src="' + faces[key] + '"></li>';
        }
        content = '<ul class="layui-clear layim-face-list">' + content + '</ul>';

        events.face.index = layer.tips(content, othis, {
          tips: 1,
          time: 0,
          fixed: true,
          skin: 'layui-box layui-layim-face',
          success: function (layero) {
            layero
              .find('.layim-face-list>li')
              .on('mousedown', function (e) {
                stope(e);
              })
              .on('click', function () {
                focusInsert(thatChat.textarea[0], 'face' + this.title + ' ');
                layer.close(events.face.index);
              });
          }
        });

        $(document).off('mousedown', events.faceHide).on('mousedown', events.faceHide);
        $(window).off('resize', events.faceHide).on('resize', events.faceHide);
        stope(e);
      },
      faceHide: function () {
        layer.close(events.face.index);
      },

      // 图片或一般文件
      image: function (othis) {
        var type = othis.data('type') || 'images';
        var api = {
          images: 'uploadImage',
          file: 'uploadFile'
        };
        var thatChat = thisChat();
        var conf = cache.base[api[type]] || {};

        layui.upload.render({
          url: conf.url || '',
          method: conf.type,
          elem: othis.find('input')[0],
          accept: type,
          done: function (res) {
            if (res.code == 0) {
              res.data = res.data || {};
              if (type === 'images') {
                focusInsert(thatChat.textarea[0], 'img[' + (res.data.src || '') + ']');
              } else if (type === 'file') {
                focusInsert(
                  thatChat.textarea[0],
                  'file(' + (res.data.src || '') + ')[' + (res.data.name || globalSrmI18n(globalGetLangAccount() + "#i18n_field_IKQI_25a130a1", "下载文件")) + ']'
                );
              }
              sendMessage();
            } else {
              layer.msg(res.msg || globalSrmI18n(globalGetLangAccount() + "#i18n_field_IKQI_25a130a1", "上传失败"));
            }
          }
        });
      },

      // 音频和视频
      media: function (othis) {
        var type = othis.data('type');
        var text = {
          audio: globalSrmI18n(globalGetLangAccount() + "#i18n_field_WN_12fefe", "音频"),
          video: globalSrmI18n(globalGetLangAccount() + "#i18n_field_KN_11478b", "视频")
        };
        var thatChat = thisChat();

        layer.prompt(
          {
            title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_VWNTW_f6e111d4", "请输入网络") + text[type] + globalSrmI18n(globalGetLangAccount() + "#i18n_field_nR_ae610", "地址"),
            shade: false,
            offset: [
              othis.offset().top - $(window).scrollTop() - 158 + 'px',
              othis.offset().left + 'px'
            ]
          },
          function (src, index) {
            focusInsert(thatChat.textarea[0], type + '[' + src + ']');
            sendMessage();
            layer.close(index);
          }
        );
      },

      // 扩展工具栏
      extend: function (othis) {
        var filter = othis.attr('lay-filter');
        var thatChat = thisChat();

        layui.each(call['tool(' + filter + ')'], function (index, item) {
          item &&
            item.call(
              othis,
              function (content) {
                focusInsert(thatChat.textarea[0], content);
              },
              sendMessage,
              thatChat
            );
        });
      },

      // 播放音频
      playAudio: function (othis) {
        var audioData = othis.data('audio');
        var audio = audioData || document.createElement('audio');
        var pause = function () {
          audio.pause();
          othis.removeAttr('status');
          othis.find('i').html('&#xe652;');
        };
        if (othis.data('error')) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_rCWNjIH_164142b7", "播放音频源异常"));
        }
        if (!audio.play) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_rCWNjIH_164142b7", "您的浏览器不支持audio"));
        }
        if (othis.attr('status')) {
          pause();
        } else {
          audioData || (audio.src = othis.data('src'));
          audio.play();
          othis.attr('status', 'pause');
          othis.data('audio', audio);
          othis.find('i').html('&#xe651;');
          // 播放结束
          audio.onended = function () {
            pause();
          };
          // 播放异常
          audio.onerror = function () {
            layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_rCWNjIH_164142b7", "播放音频源异常"));
            othis.data('error', true);
            pause();
          };
        }
      },

      // 播放视频
      playVideo: function (othis) {
        var videoData = othis.data('src');
        var video = document.createElement('video');
        if (!video.play) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_LjQBAxRuWWWWW_9adb992f", "您的浏览器不支持video"));
        }
        layer.close(events.playVideo.index);
        events.playVideo.index = layer.open({
          type: 1,
          title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_rCKN_2f51157c", "播放视频"),
          area: ['460px', '300px'],
          maxmin: true,
          shade: false,
          content:
            '<div style="background-color: #000; height: 100%;"><video style="position: absolute; width: 100%; height: 100%;" src="' +
            videoData +
            '" loop="loop" autoplay="autoplay"></video></div>'
        });
      },

      // 聊天记录
      chatLog: function (othis) {
        var thatChat = thisChat();
        if (!cache.base.chatLog) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_field_LvAHOLStH_7f39c0c3", "未开启更多聊天记录"));
        }
        layer.close(events.chatLog.index);
        return (events.chatLog.index = layer.open({
          type: 2,
          maxmin: true,
          title: globalSrmI18n(globalGetLangAccount() + "#i18n_field_U_4e0e", "与") + thatChat.data.name + globalSrmI18n(globalGetLangAccount() + "#i18n_field_WjLStH_f865c868", " 的聊天记录"),
          area: ['500px', '100%'],
          shade: false,
          offset: 'rb',
          skin: 'layui-box',
          anim: 2,
          id: 'layui-layim-chatlog',
          content: cache.base.chatLog + '?id=' + thatChat.data.id + '&type=' + thatChat.data.type
        }));
      },

      // 历史会话右键菜单操作
      menuHistory: function (othis, e) {
        var local = layui.data('layim')[cache.mine.id] || {};
        var parent = othis.parent();
        var index = parent.data('index')

        var type = othis.data('type');
        var hisElem = layimMain.find('.layim-list-history');
        var none = '<li class="layim-null">' + globalSrmI18n(globalGetLangAccount() + "#i18n_field_PSvKME_aefcc6cd", "暂无历史会话") + '</li>';

        if (type === 'one') {
          var history = local.history;
          delete history[parent.data('index')];

          local.history = history;

          layui.data('layim', {
            key: cache.mine.id,
            value: local
          });

          // 删除 DOM
          $('.layim-list-history li.layim-' + index).remove();

          if (hisElem.find('li').length === 0) {
            hisElem.html(none);
          }
        } else if (type === 'all') {
          delete local.history;
          layui.data('layim', {
            key: cache.mine.id,
            value: local
          });
          hisElem.html(none);
        }

        layer.closeAll('tips');
      },

      // 标题跳转对应详情页
      jumpDetail: function (othis, e) {
        $emit('jump-detail', othis.context.innerText);
        e.stopPropagation()
      },

      unDoMsg: function (othis, e) {
        // 断网
        if (!navigator.onLine) {
          return layer.msg(globalSrmI18n(globalGetLangAccount() + "#i18n_alert_APTWIOvW_4f7ec141", "当前网络已断开！"))
        }

        var thatChat = thisChat();

        var param = {
          mine: globalMessage,
          to: thatChat.data
        };

        // 发送消息事件
        $emit('unDoMsg', param);
      },
    };

    // 暴露接口
    exports('layim', new LAYIM());
  })
  .addcss('modules/layim/layim.css?v=3.9.6', 'skinlayimcss');
