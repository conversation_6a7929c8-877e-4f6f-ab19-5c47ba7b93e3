<template>
  <div class="SupplierInformationList">
    <div v-if="show">
      <a-tabs
        default-active-key="1"
        v-model="activeIndex"
        @change="changeTabsEvent">

        <a-tab-pane
          v-if="tenderType == '0' || checkType == '0'"
          key="1"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_PVMR_43c7f100`, '邀请回执')">
          <InviteReceiptList
            ref="supplierInviteReceiptList"
            :fromSourceData="formData.supplierInviteReceiptList"
            @handleInviteViewPage="handleInviteViewPage"
            @handleInviteConfirmPage="handleInviteConfirmPage"
            @handleInviteDeletePage="handleInviteDeletePage"></InviteReceiptList>
        </a-tab-pane>

        <a-tab-pane
          v-if="signUp == '1'"
          key="2"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_sRtH_2e242dad`, '报名记录')">
          <SignUpManagerList
            :fromSourceData="formData.supplierSignUpList"
            :checkType="checkType"
            @handleSighUpViewPage="handleSighUpViewPage"
            @handleSighUpEditPage="handleSighUpEditPage"></SignUpManagerList>
        </a-tab-pane>
        <a-tab-pane
          v-if="bidding == '1' && checkType == 0"
          key="3"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_UURBtH_8eda50bc`, '预审购标记录')">
          <BidManagerList
            :type="'prejudication'"
            :fromSourceData="formData.supplierPrePurchaseBidList"
            :checkType="checkType"
            @handleBidManagerViewPage="(row) => { this.handleBidManagerViewPage(row, '0')}"
            @handleBidManagerConfirmPage="handleBidManagerConfirmPage"
            @handleBidManagerEditPage="(row) => { this.handleBidManagerEditPage(row, '0')}"></BidManagerList>
        </a-tab-pane>
        <a-tab-pane
          v-if="checkType == 0"
          key="4"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_UUddtH_75166c69`, '预审响应记录')">
          <ResponsesList
            :type="'prejudication'"
            :fromSourceData="formData.supplierPreResponseList"
            :checkType="checkType"
            @handleResponsesViewPage="(row) => { this.handleResponsesViewPage(row, '0')}"></ResponsesList>
        </a-tab-pane>
        <a-tab-pane
          v-if="bidding == '1'"
          key="5"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_RBtH_41c4939f`, '购标记录')">
          <BidManagerList
            :fromSourceData="formData.supplierPurchaseBidList"
            :checkType="checkType"
            @handleBidManagerViewPage="(row) => { this.handleBidManagerViewPage(row, '1')}"
            @handleBidManagerConfirmPage="handleBidManagerConfirmPage"
            @handleBidManagerEditPage="(row) => { this.handleBidManagerEditPage(row, '1')}"></BidManagerList>
        </a-tab-pane>
        <a-tab-pane
          key="6"
          v-if="subpackage.processType == '1'"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_ddtH_2800af4c`, '响应记录')">
          <ResponsesList
            :fromSourceData="formData.supplierResponseList"
            :checkType="checkType"
            @handleResponsesViewPage="(row) => { this.handleResponsesViewPage(row, '1')}"></ResponsesList>
        </a-tab-pane>
        <a-tab-pane
          key="6"
          v-if="subpackage.processType == '0'"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_ddtH_2800af4c`, '响应记录')">
          <ResponsesList
            :fromSourceData="formData.supplierResultResponseList"
            :checkType="checkType"
            @handleResponsesViewPage="(row) => { this.handleResponsesViewPage(row, '1')}"></ResponsesList>
        </a-tab-pane>
        <a-tab-pane
          key="7"
          v-if="tenderCurrentRow.showProbeList == '1'"
          :tab="$srmI18n(`${this.$getLangAccount()}#i18n_field_LBJitH_4a08df61`, '围标探测记录')">
          <probeResultList
            :fromSourceData="formData.probeResultList">
          </probeResultList>
        </a-tab-pane>


        <a-button
          slot="tabBarExtraContent"
          v-if="allowSignUp"
          @click="addSignUp">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VasRtH_94b6af9b`, '新增报名记录') }}</a-button>
        <a-button
          slot="tabBarExtraContent"
          v-if="allowPreBid"
          @click="addBiddingItem(activeIndex, '0')">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaUURBtH_9ad40f2a`, '新增预审购标记录') }}</a-button>
        <a-button
          slot="tabBarExtraContent"
          v-if="allowBid"
          @click="addBiddingItem(activeIndex, '1')">{{ $srmI18n(`${$getLangAccount()}#i18n_field_VaRBtH_a857158d`, '新增购标记录') }}</a-button>
        <a-button
          slot="tabBarExtraContent"
          v-if="allowSupply"
          @click="addInviteItem">{{ $srmI18n(`${$getLangAccount()}#i18n_field_xVPVRdX_11b365b6`, '补充邀请供应商') }}</a-button>
      </a-tabs>
      <field-select-modal
        ref="fieldSelectModal"
        isEmit
        @ok="fieldSelectOk" />
    </div>

  </div>
</template>
<script>
import titleTrtl from '../../components/title-crtl'
import listTable from '../../components/listTable'
import ResponsesList from './ResponsesList'
import probeResultList from './probeResultList'
import BidManagerList from './BidManagerList'
import SignUpManagerList from './SignUpManagerList'
import InviteReceiptList from './InviteReceiptList'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import {getAction, postAction} from '@/api/manage'


export default {
    props: {
        formData: {
            default: () => {
                return {}
            },
            type: Object
        },
        status: {
            default: () => {
                return 0
            },
            type: Number
        }
    },
    inject: ['tenderCurrentRow', 'currentSubPackage', 'subpackageId'],
    components: {
        titleTrtl,
        listTable,
        ResponsesList,
        probeResultList,
        BidManagerList,
        SignUpManagerList,
        InviteReceiptList,
        fieldSelectModal
    },
    data () {
        return {
            ifPre: false,
            signUpFlag: false,
            biddingFlag: false,
            activeIndex: '6',
            show: false,
            url: {
                add: '/tender/purchase/tenderInvitationSupplierReceipt/addBatch'
            }
        }
    },
    computed: {
        applyRoleCanEdit () {
            return this.$ls.get('SET_TENDERCURRENTROW').applyRole == '1'
        },
        subpackage (){
            return this.currentSubPackage()
        },
        subId () {
            return this.subpackageId()
        },
        allowSignUp (){
            if(this.activeIndex == '2' && this.signUpType != '1' && this.applyRoleCanEdit && this.signUpFlag){
                console.log('yunxubaoming')
                return true
            }else{
                return false
            }
        },
        allowPreBid () {
            console.log(this.biddingType != '1', this.applyRoleCanEdit, this.ifPre, this.biddingFlag)
            if(this.activeIndex == '3' && this.ifPre && this.biddingType != '1' && this.applyRoleCanEdit && this.biddingFlag && this.status == 2150){
                return true
            }else{
                return false
            }
        },
        allowBid (){
            if(this.activeIndex == '5' && this.biddingType != '1' && this.applyRoleCanEdit && this.status >= '3150' && this.biddingFlag){
                console.log('yunxugoubiao')
                return true
            }else{
                return false
            }
        },
        allowSupply () {
            if(this.activeIndex == '1' && this.applyRoleCanEdit && this.status >= '3150' && this.status < '4010' && !this.ifPre){
                console.log('yunxubuchongyaoqing')
                return true
            }else{
                return false
            }
        }
    },
    methods: {
        handleView (row) {
            this.$emit('handleSupplierInformationListViewPage', row)
        },
        // ifPre () {
        //     return this.checkType == 0
        // },
        changeTabsEvent (key) {
            this.activeIndex = key
            this.$emit('activeTabsKey', key)
            console.log('判断值:', this.activeIndex, this.biddingType, this.applyRoleCanEdit, this.biddingFlag)

        },
        // 新增报名
        addSignUp () {
            this.$emit('handleAddSignUp', {})
        },
        // 新增购标
        addBiddingItem (index, checkType) {
            this.$emit('handleAddBiddingItem', {index, checkType})
        },
        // 报名查看
        handleSighUpViewPage (row){
            this.$emit('handleSighUpViewPage', row)
        },
        // 报名编辑
        handleSighUpEditPage (row){
            this.$emit('handleSighUpEditPage', row)
        },
        // 购标查看
        handleBidManagerViewPage (row, checkType){
            this.$emit('handleBidManagerViewPage', {row, checkType})
        },
        // 购标确认
        handleBidManagerConfirmPage (row){
            this.$emit('handleBidManagerConfirmPage', row)
        },
        // 购标编辑
        handleBidManagerEditPage (row, checkType){
            this.$emit('handleBidManagerEditPage', row, checkType)
        },
        // 响应查看
        handleResponsesViewPage (row, checkType){
            this.$emit('handleResponsesViewPage', row, checkType)
        },
        //邀请回执查看
        handleInviteViewPage (row){
            this.$emit('handleInviteViewPage', row)
        },
        //邀请回执确认
        handleInviteConfirmPage (row){
            this.$emit('handleInviteConfirmPage', row)
        },
        //邀请回执删除
        handleInviteDeletePage (row){
            this.$emit('handleInviteDeletePage', row)
        },
        // 邀请回执新增
        addInviteItem (){
            let param = encodeURI('[{"logicSymbol":"in","fieldCode":"supplierStatus","fieldType":"dict","dictCode":"srmSupplierStatus","fieldValue":"1,2","joiner":"AND"}]')
            let url = '/supplier/supplierMaster/contactList?functionName='+encodeURI('报价')+'&superQueryParams='+param
            let columns = [
                { field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierName`, '供应商名称') },
                { field: 'functionName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contact`, '联系人') },
                { field: 'functionTelphone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_contactPhone`, '联系电话') }

            ]
            this.$refs.fieldSelectModal.open(url, { headId: this.tenderCurrentRow.id}, columns, 'multiple')
        },
        fieldSelectOk (data) {
            // let itemGrid = this.getItemGridRef('supplierInviteReceiptList')
            var subpackageList = {}
            let param = []
            var subpackage = {}
            let {supplierAccount, supplierName, tenderProjectName, subpackageName} = this.subpackage
            let subpackageMsg = {supplierAccount, supplierName, tenderProjectName, subpackageName}
            subpackageMsg.tenderProjectId = this.subpackage.headId
            subpackageMsg.subpackageId = this.subId
            data.forEach(item => {
                subpackage['supplierName'] = item.supplierName
                subpackage['contacts'] = item.functionName
                subpackage['contactsPhone'] = item.functionTelphone
                subpackage['supplierAccount'] = item.toElsAccount
                subpackageList={...subpackageMsg, ...subpackage}
                param.push({...subpackageList})

            })
            // itemGrid.insertAt([...subpackageList], -1)
            postAction(this.url.add, param).then(res => {
                if (res.success) {
                    this.$message.success(res.message)
                    this.$refs.supplierInviteReceiptList.insertAt(data, -1)
                    this.$emit('refresh')
                } else {
                    this.$message.error(res.message)
                }
            }).finally(() => {
            })
        }
    },
    mounted () {
        let {signUp, bidding, signUpType, biddingType, checkType, status, biddingEndTime, preBiddingEndTime, signUpEndTime, tenderType} = this.subpackage
        console.log('this.subpackage', this.subpackage)
        var date = new Date
        let time = date.getTime()
        let signUpEndding = new Date(signUpEndTime)
        let biddingEndding = new Date(biddingEndTime)
        let preBidding = new Date(preBiddingEndTime)
        let biddingEnddingTime = biddingEndding.getTime()
        let preBiddingEnddingTime = preBidding.getTime()
        let signUpEnddingTime = signUpEndding.getTime()
        // 预审情况购标新增按钮控制
        if(status >= '2150' && status < '2210'){
            this.biddingFlag = time < preBiddingEnddingTime
        }else if(status >= '3150' && status < '4010'){
        // 非预审情况购标新增按钮控制
            this.biddingFlag = time < biddingEnddingTime
        }
        this.ifPre = (checkType == '0')
        // 报名新增按钮控制
        if((status >= 3150 && status < 4010 && checkType == '1') || (status >= 2150 && status < 2210 & checkType == '0')){
        // 非预审情况购标新增按钮控制
            console.log('time', time)
            console.log('signUpEnddingTime', signUpEnddingTime)
            this.signUpFlag = time < signUpEnddingTime
            console.log('this.signUpFlag', this.signUpFlag)

        }
        this.signUp = signUp
        this.bidding = bidding
        this.signUpType = signUpType
        this.biddingType = biddingType
        this.checkType = checkType
        this.tenderType = tenderType
        this.show = true
    },
    created (){
        console.log('showProbeList', this.tenderCurrentRow.showProbeList)
        console.log('subpackage', this.subpackage)
    }
}
</script>
<style lang="less" scoped>
.SupplierInformationList{
  margin-top: 20px;
}
.margin-b-10{
  margin-bottom: 5px;
}
.display_inline_block{
  display: inline-block;
}
</style>