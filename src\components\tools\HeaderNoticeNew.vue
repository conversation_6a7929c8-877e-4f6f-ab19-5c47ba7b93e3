<template>
  <a-badge
    :count="msgTotal"
  >
    <div
      class="header-notice"
      @click="toElsMsgRecord"
    >
    
      <icon-font
        style="font-size: 16px; padding: 4px"
        class="icon"
        :class="{change:changeBg}"
        :style="{color:currentActive === 5 && changeBg ?defaultColor:'',}"
        type="icon-xiaoxi">
      </icon-font>
      <span v-if="currentActive === 5">{{ $srmI18n(`${$getLangAccount()}#i18n_title_news`, '消息') }}</span>
      <a-popover
        v-if="false"
        trigger="click"
        placement="bottomRight"
        :auto-adjust-overflow="true"
        :arrow-point-at-center="true"
        overlay-class-name="header-notice-wrapper"
        @visibleChange="handleHoverChange"
        :overlay-style="{ width: '300px', top: '50px' }"
      >
        <template slot="content">
          <a-spin :spinning="loadding">
            <a-list>
              <template v-for="el in elsMsg">
                <a-list-item :key="el.id">
                  <a-list-item-meta
                    :title="el.msgTitle"
                    :description="el.sendTime">
                    <a-avatar
                      slot="avatar"
                      icon="message" />
                  </a-list-item-meta>
                </a-list-item>
              </template>
            </a-list>
            <div style="margin-top: 5px;text-align: center">
              <a-button
                @click="toElsMsgRecord"
                type="dashed"
                block
              >
                查看更多
              </a-button>
            </div>
          </a-spin>
        </template>
        <span
          @click="fetchNotice"
          class="header-notice"
        >
          <a-badge :count="msgTotal">
            <!-- <a-icon
          style="font-size: 16px; padding: 4px"
          type="icon-Icon"
        /> -->
            <icon-font
              style="font-size: 16px; padding: 4px"
              class="icon"
              type="icon-Icon">
            </icon-font>
          </a-badge>
        </span>
        <show-announcement
          ref="ShowAnnouncement"
          @ok="modalFormOk"
        />
      </a-popover>
   
    </div>
  </a-badge>
</template>

<script>
import { mapState } from 'vuex'
import { getAction, putAction } from '@/api/manage'
import ShowAnnouncement from './ShowAnnouncement'
import store from '@/store/'
import { List, Badge, Popover, Tag } from 'ant-design-vue'

export default {
    name: 'HeaderNotice',
    components: {
        ShowAnnouncement,
        AList: List,
        AListItem: List.Item,
        AListItemMeta: List.Item.Meta,
        ABadge: Badge,
        APopover: Popover,
        ATag: Tag
    },
    props: {
        currentActive: {
            type: Number,
            default: 0
        },
        changeBg: {
            type: Boolean,
            default: false
        },
        defaultColor: {
            type: String,
            default: ''
        }
    },
    data () {
        return {
            loadding: false,
            url: {
                listCementByUser: '/annountCement/listByUser',
                editCementSend: '/announcementSend/editByAnntIdAndUserId',
                queryById: '/annountCement/queryById'
            },
            hovered: false,
            announcement1: [],
            announcement2: [],
            msg1Count: '0',
            msg2Count: '0',
            msg1Title: '通知(0)',
            msg2Title: '',
            stopTimer: false,
            websock: null,
            lockReconnect: false,
            heartCheck: null,
            totalCount: 0,
            elsMsg: []
        }
    },
    computed: {
        ...mapState({
            msgTotal: (state) => state.message.msgTotal
        })
    },
    created () {
        // this.getELsMsgData()
        this.$store.dispatch('updateMatTotal')
    },
    mounted () {
        this.loadData()
        //this.timerFun();
        //this.initWebSocket()
        console.log(this)
        //this.heartCheckFun()
    },
    destroyed: function () { // 离开页面生命周期函数
        this.websocketclose()
    },
    methods: {
        // getELsMsgData () {
        //     const url = 'message/elsMsgRecord/list'
        //     const params = {
        //         pageNo: 1,
        //         pageSize: 5,
        //         handleFlag: '0',
        //         column: 'id',
        //         order: 'desc'
        //     }
        //     getAction(url, params).then(res => {
        //         this.elsMsg = res.result.records || []
        //         this.totalCount = res.result.total
        //     })
        // },
        toElsMsgRecord () {
            this.$router.push({ path: '/sys/message/ElsMsgRecordList' })
        },
        timerFun () {
            this.stopTimer = false
            let myTimer = setInterval(()=>{
                // 停止定时器
                if (this.stopTimer == true) {
                    clearInterval(myTimer)
                    return
                }
                this.loadData()
            }, 6000)
        },
        loadData (){
            try {
                // 获取系统消息
                // getAction(this.url.listCementByUser).then((res) => {
                //     if (res.success) {
                //         this.announcement1 = res.result.anntMsgList
                //         this.msg1Count = res.result.anntMsgTotal
                //         this.msg1Title = '通知(' + res.result.anntMsgTotal + ')'
                //         this.announcement2 = res.result.sysMsgList
                //         this.msg2Count = res.result.sysMsgTotal
                //         this.msg2Title = '系统消息(' + res.result.sysMsgTotal + ')'
                //     }
                // }).catch(error => {
                //     console.log('系统消息通知异常', error)//这行打印permissionName is undefined
                //     this.stopTimer = true
                //     console.log('清理timer')
                // })
            } catch (err) {
                this.stopTimer = true
                console.log('通知异常', err)
            }
        },
        fetchNotice () {
            if (this.loadding) {
                this.loadding = false
                return
            }
            this.loadding = true
            setTimeout(() => {
                this.loadding = false
            }, 200)
        },
        showAnnouncement (record){
            putAction(this.url.editCementSend, {anntId: record.id}).then((res)=>{
                if(res.success){
                    this.loadData()
                }
            })
            this.hovered = false
            this.$refs.ShowAnnouncement.detail(record)
        },
        toMyAnnouncement (){

            this.$router.push({
                path: '/isps/userAnnouncement',
                name: 'isps-userAnnouncement'
            })
        },
        modalFormOk (){
        },
        handleHoverChange (visible) {
            this.hovered = visible
        },

        initWebSocket: function () {
        // WebSocket与普通的请求所用协议有所不同，ws等同于http，wss等同于https
            var userId = store.getters.userInfo.id
            var url = this.$variateConfig['domainURL'].replace('https://', 'wss://').replace('http://', 'ws://')+'/websocket/'+userId
            this.websock = new WebSocket(url)
            this.websock.onopen = this.websocketonopen
            this.websock.onerror = this.websocketonerror
            this.websock.onmessage = this.websocketonmessage
            this.websock.onclose = this.websocketclose
        },
        websocketonopen: function () {
        //    console.log('WebSocket连接成功')
            //心跳检测重置
            this.heartCheck.reset().start()
        },
        websocketonerror: function () {
            console.log('WebSocket连接发生错误')
            // this.reconnect()
        },
        websocketonmessage: function (e) {
            //console.log("-----接收消息-------",e.data);
            var data = eval('(' + e.data + ')') //解析对象
            if(data.cmd == 'topic'){
            //系统通知
                this.loadData()
            }else if(data.cmd == 'user'){
            //用户消息
                this.loadData()
            }

            //心跳检测重置
            this.heartCheck.reset().start()

        },
        websocketsend (text) { // 数据发送
            try {
                this.websock.send(text)
            } catch (err) {
                console.log('send failed (' + err + ')')
            }
        },
        websocketclose: function (e) {
            console.log('connection closed (' + e + ')')
            this.reconnect()
        },

        openNotification (data) {
            var text = data.msgTxt
            const key = `open${Date.now()}`
            this.$notification.open({
                message: '消息提醒',
                placement: 'bottomRight',
                description: text,
                key,
                btn: (h)=>{
                    return h('a-button', {
                        props: {
                            type: 'primary',
                            size: 'small'
                        },
                        on: {
                            click: () => this.showDetail(key, data)
                        }
                    }, '查看详情')
                }
            })
        },

        reconnect () {
            var that = this
            if(that.lockReconnect) return
            that.lockReconnect = true
            //没连接上会一直重连，设置延迟避免请求过多
            // setTimeout(function () {
            //     console.info('尝试重连...')
            //     that.initWebSocket()
            //     that.lockReconnect = false
            // }, 5000)
        },
        heartCheckFun (){
            var that = this
            //心跳检测,每20s心跳一次
            that.heartCheck = {
                timeout: 20000,
                timeoutObj: null,
                serverTimeoutObj: null,
                reset: function (){
                    clearTimeout(this.timeoutObj)
                    //clearTimeout(this.serverTimeoutObj);
                    return this
                },
                start: function (){
                    // var self = this
                    this.timeoutObj = setTimeout(function (){
                        //这里发送一个心跳，后端收到后，返回一个心跳消息，
                        //onmessage拿到返回的心跳就说明连接正常
                        that.websocketsend('HeartBeat')
                        //  console.info('客户端发送心跳')
                        //self.serverTimeoutObj = setTimeout(function(){//如果超过一定时间还没重置，说明后端主动断开了
                        //  that.websock.close();//如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
                        //}, self.timeout)
                    }, this.timeout)
                }
            }
        },


        showDetail (key, data){
            this.$notification.close(key)
            var id = data.msgId
            getAction(this.url.queryById, {id: id}).then((res) => {
                if (res.success) {
                    var record = res.result
                    this.showAnnouncement(record)
                }
            })

        }
    }
}
</script>

<style lang="css">
  .header-notice-wrapper {
    top: 50px !important;
  }
</style>
<style lang="scss" scoped>
  .header-notice{
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    // height: 100%;
    // display: inline-block;
    // transition: all 0.3s;
    
    span {
      vertical-align: initial;
    }
  }
  .active{
    width: unset;
    padding: 0 12px;
    border-radius: 42px;
    transition: padding 0.5s;
  }
  .change{
    color:#AEB1BB;
  }
  :deep(.ant-badge-count){
    top: 4px;
    box-shadow: none;
}
</style>