import { srmI18n, getLangAccount } from '@/utils/util'

export const materialGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    loading: false,
    headerAlign: 'center',
    columns: [
        { field: 'materialNumber', title: srmI18n(`${getLangAccount()}#i18n_title_materialCode`, '物料编码'), width: 120, align: 'center', showOverflow: true },
        { field: 'materialName', title: srmI18n(`${getLangAccount()}#i18n_title_materialName`, '物料名称'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialDesc', title: srmI18n(`${getLangAccount()}#i18n_title_materialDesc`, '物料描述'), width: 200, align: 'center', showOverflow: true },
        { field: 'materialSpec', title: srmI18n(`${getLangAccount()}#i18n_title_materialSpec`, '物料规格'), width: 120, align: 'center', showOverflow: true },
        { field: 'factory_dictText', title: srmI18n(`${getLangAccount()}#i18n_massProdHead4e3_factoryCode`, '工厂'), width: 120, align: 'center', showOverflow: true },
        { field: 'storageLocation_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_storageLocation`, '库存地点'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireQuantity', title: srmI18n(`${getLangAccount()}#i18n_field_TVWR_46474721`, '需求数量'), width: 120, align: 'center', showOverflow: true },
        { field: 'purchaseUnit_dictText', title: srmI18n(`${getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), width: 120, align: 'center', showOverflow: true },
        { field: 'requireDate', title: srmI18n(`${getLangAccount()}#i18n_field_TVBA_46472a9c`, '需求日期'), width: 120, align: 'center', showOverflow: true },
        { field: 'startPrice', title: srmI18n(`${getLangAccount()}#i18n_field_AAu_21f5181`, '起拍价'), width: 120, showOverflow: true }
    ],
    data: []
}

export const chartGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    columns: [
        {
            field: 'supplierName',
            title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'),
            width: 84,
            align: 'center',
            slots: { default: 'supplierName_default' }
        },
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_offer`, '报价'),
            width: 84,
            align: 'center',
            slots: { default: 'price_default' }
        },
        {
            field: 'quoteTime',
            title: srmI18n(`${getLangAccount()}#i18n_title_quotationTime`, '报价时间'),
            width: 92,
            align: 'center',
            slots: { default: 'quoteTime_default' }
        }
    ],
    data: []
}
// 英式 供应商列表
export const rankGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 396,
    loading: false,
    headerAlign: 'center',
    columns: [
        // { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_title_rank`, '排名'), width: 60, align: 'center', showOverflow: true },
        { field: 'online', title: srmI18n(`${getLangAccount()}#i18n_field_KWzE_298886e2`, '在线状态'), width: 70, align: 'center', showOverflow: true, slots: { default: 'online' } },
        // { field: 'toElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), width: 120, align: 'center', showOverflow: true },
        { field: 'supplierName', title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), width: 120, align: 'center', showOverflow: true },
        { field: 'netPrice', title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), width: 90, visible: true, align: 'center', showOverflow: true },
        { field: 'price', title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), width: 80, visible: true, align: 'center', showOverflow: true },
        { field: 'totalAmount', title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'), width: 100, align: 'center', showOverflow: true },
        { field: 'netTotalAmount', title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'), width: 120, align: 'center', showOverflow: true },
        {
            field: 'confirmQuantity',
            title: srmI18n(`${getLangAccount()}#i18n_field_RLWR_38d795d5`, '确认数量'),
            width: 120
        },
        {
            field: 'supplierRemark',
            title: srmI18n(`${getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
            width: 120
        },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_title_quotationTimes`, '报价次数'), width: 80, align: 'center', showOverflow: true },
        { field: 'quoteTime', title: srmI18n(`${getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), width: 120, align: 'center', showOverflow: true },
        { field: 'quoteIp', title: srmI18n(`${getLangAccount()}#i18n_title_quotationIp`, 'IP地址'), width: 120, align: 'center', showOverflow: true }
    ],
    data: []
}

// 日荷式 供应商列表
export const rankJapGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 396,
    rowConfig: {
        isCurrent: true
    },
    loading: false,
    headerAlign: 'center',
    columns: [
        { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_field_quoteRank`, '排名'), width: 60, align: 'center', showOverflow: true },
        { field: 'online', title: srmI18n(`${getLangAccount()}#i18n_field_KWzE_298886e2`, '在线状态'), align: 'center', showOverflow: true, slots: { default: 'online' } },
        // { field: 'toElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), align: 'center', showOverflow: true },
        { field: 'supplierName', title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), align: 'center', showOverflow: true },
        { field: 'netPrice', title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), visible: true, align: 'center', showOverflow: true },
        { field: 'price', title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), visible: true, align: 'center', showOverflow: true },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_field_quoteCount`, '报价次数'), align: 'center', showOverflow: true },
        { field: 'quoteTime', title: srmI18n(`${getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), align: 'center', showOverflow: true },
        { field: 'quoteIp', title: srmI18n(`${getLangAccount()}#i18n_field_WWnR_2d4177`, 'IP地址'), align: 'center', showOverflow: true }
    ],
    data: []
}

// 荷式 供应商列表
export const rankDucGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 396,
    rowConfig: {
        isCurrent: true
    },
    loading: false,
    headerAlign: 'center',
    columns: [
        { field: 'quoteRank', title: srmI18n(`${getLangAccount()}#i18n_field_quoteRank`, '排名'), width: 60, align: 'center', showOverflow: true, slots: { default: 'quoteRank' } },
        { field: 'online', title: srmI18n(`${getLangAccount()}#i18n_field_KWzE_298886e2`, '在线状态'), align: 'center', showOverflow: true, slots: { default: 'online' } },
        // { field: 'toElsAccount', title: srmI18n(`${getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), align: 'center', showOverflow: true },
        { field: 'supplierName', title: srmI18n(`${getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), align: 'center', showOverflow: true },
        { field: 'netPrice', title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'), visible: true, align: 'center', showOverflow: true },
        { field: 'price', title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'), visible: true, align: 'center', showOverflow: true },
        { field: 'quoteCount', title: srmI18n(`${getLangAccount()}#i18n_field_quoteCount`, '报价次数'), align: 'center', showOverflow: true },
        { field: 'quoteTime', title: srmI18n(`${getLangAccount()}#i18n_title_lastQuotationTime`, '最后报价时间'), align: 'center', showOverflow: true },
        { field: 'quoteIp', title: srmI18n(`${getLangAccount()}#i18n_field_WWnR_2d4177`, 'IP地址'), align: 'center', showOverflow: true }
    ],
    data: []
}
// 打包
export const quotaPackGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    addBtn: true,
    columns: [
        {
            field: 'netTotalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_field_fsLfHf_8b362222`, '打包未税金额'),
            slots: {
                default: 'netTotalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'totalAmount',
            title: srmI18n(`${getLangAccount()}#i18n_title_packageTaxInclusiveAmount`, '打包含税金额'),
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: {
                default: 'totalAmount_default',
                header: 'price_header'
            }
        },
        {
            field: 'accept',
            title: srmI18n(`${getLangAccount()}#i18n_title_status`, '状态'),
            align: 'center',
            slots: { default: 'accept_default' }
        },
        {
            field: 'operation',
            title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
            width: 80,
            align: 'center',
            slots: { default: 'operation' }
        }
    ],
    toolbarConfig: {
        slots: {
            buttons: 'toolbar_buttons'
        }
    },
    data: []
}
// 逐条
export const quotaGridOptions = {
    size: 'mini',
    border: true,
    resizable: true,
    keepSource: true,
    showOverflow: true,
    showHeaderOverflow: true,
    height: 320,
    loading: false,
    headerAlign: 'center',
    addBtn: true,
    columns: [
        {
            field: 'price',
            title: srmI18n(`${getLangAccount()}#i18n_title_unitPriceIncludTax`, '含税单价'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'price_default', header: 'price_header' }
        },
        {
            field: 'netPrice',
            title: srmI18n(`${getLangAccount()}#i18n_field_Lftu_30b9e566`, '未税单价'),
            align: 'center',
            require: true,
            className: 'bg-color-1',
            headerClassName: 'bg-color-1',
            slots: { default: 'netPrice_default', header: 'price_header' }
        },
        
        {
            field: 'accept',
            title: srmI18n(`${getLangAccount()}#i18n_title_status`, '状态'),
            align: 'center',
            slots: { default: 'accept_default' }
        },
        {
            field: 'operation',
            title: srmI18n(`${getLangAccount()}#i18n_title_operation`, '操作'),
            align: 'center',
            slots: { default: 'operation' }
        }
    ],
    toolbarConfig: {
        slots: {
            buttons: 'toolbar_buttons'
        }
    },
    data: []
}