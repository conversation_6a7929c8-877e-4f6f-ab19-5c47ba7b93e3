/*
 * @Author: your name
 * @Date: 2021-06-03 17:38:10
 * @LastEditTime: 2021-06-03 17:43:51
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \srm-frontend_v4.5\src\store\modules\purchaseEnquiry.js
 */
const purchaseEnquiry = {
    state: {
        refleshInterFaceData: false
    },
    mutations: {
        TOGGLE_REFLESHINTERFACE_DATA: (state, payLoad)=> {
            state.refleshInterFaceData = payLoad
        }
    }
}
export default purchaseEnquiry