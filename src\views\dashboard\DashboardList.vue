<template>
  <div class="els-page-container">
    <els-list-page
      ref="listPage"
      v-show="!showEditPage"
      :pageData="pageData"
      :url="url"
      @handleEdit="handleEditInner"></els-list-page>
    <!-- 表单区域 -->
    <dashboard-modal
      v-if="showEditPage"
      :current-edit-row="currentEditRow"
      ref="modalForm"
      @ok="modalFormOk"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import DashboardModal from './modules/DashboardModal'
import {listPageMixin} from '@comp/template/listPageMixin'
import { srmI18n, getLangAccount } from '@/utils/util'

export default {
    mixins: [listPageMixin],
    components: {
        DashboardModal
    },
    data () {
        return {
            showEditPage: false,
            pageData: {
                formField: [
                    {
                        type: 'input',
                        label: srmI18n(`${getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: srmI18n(`${getLangAccount()}#i18n_title_pleaseEnterCodeDesc`, '请输入编码/名称/描述')
                    }
                ],
                button: [
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'), icon: 'plus', clickFn: this.handleAdd, primary: true},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), icon: 'delete', clickFn: this.handleDel},
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                form: {
                    keyWord: ''
                }
            },
            url: {
                list: '/report/dashboard/dashboard/list',
                delete: '/report/dashboard/dashboard/delete',
                deleteBatch: '/report/dashboard/dashboard/deleteBatch',
                exportXlsUrl: 'dashboard/dashboard/exportXls',
                importExcelUrl: 'dashboard/dashboard/importExcel',
                columns: 'dashboardList'
            }
        }
    },
    methods: {
        handleExportXls () {
            this.$refs.listPage.handleExportXls(srmI18n(`${getLangAccount()}#i18n_title_kanbang`, '看板'))
        }
    }
}
</script>