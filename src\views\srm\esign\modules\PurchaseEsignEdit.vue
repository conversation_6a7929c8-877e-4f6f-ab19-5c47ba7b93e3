<template>
  <div class="page-container">
    <edit-layout
      ref="editPage"
      :page-data="pageData"
      :url="url" />
    <!-- 加载配置文件 -->
    <field-select-modal
      ref="fieldSelectModal" />
    <a-modal
      v-drag
      v-model="visible"
      :title="this.$srmI18n(`${this.$getLangAccount()}#i18n_title_score`, '评分')"
      @ok="handleOk">
      <a-form-model
        :model="form"
        :label-col="labelCol"
        :wrapper-col="wrapperCol">
        <a-form-model-item :label="this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sULW_2741ed82`, '参与人数')">
          <a-input
            v-model="form.number"
            type="number" />
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { EditMixin } from '@comp/template/edit/EditMixin'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { getAction, postAction } from '@/api/manage'
export default {
    name: 'EsignFlowAdd',
    mixins: [EditMixin],
    components: {
        fieldSelectModal
    },
    data () {
        return {
            rowIndex: -1,
            rowSignerIndex: -1,
            selectType: 'esignFlow',
            labelCol: { span: 4 },
            wrapperCol: { span: 14 },
            visible: false,
            peopleNumber: 1,
            form: {
                number: 1
            },
            layout: {
                labelCol: { span: 4 },
                wrapperCol: { span: 15 }
            },
            pageData: {
                form: {
                    toElsAccount: '',
                    supplierName: '',
                    esignNumber: null,
                    businessScene: null,
                    filesName: null,
                    filesId: null,
                    uploaded: null,
                    autoArchiving: null,
                    autoInitiate: null,
                    remark: null,
                    cutOffTime: null,
                    contractRemind: null,
                    noticeType: null,
                    effectiveTime: null
                },
                groups: [
                    {
                        groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'),
                        groupCode: 'baseForm',
                        sortOrder: '1',
                        custom: {
                            formFields: [
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ESAy_24c6a9a8`, '业务编号'),
                                    fieldName: 'esignNumber',
                                    disabled: true
                                },
                                {
                                    fieldType: 'selectModal',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供方ELS账号'),
                                    fieldName: 'toElsAccount',
                                    bindFunction: function (Vue, data){
                                        Vue.$set(Vue.form, 'toElsAccount', data[0].toElsAccount)
                                        Vue.$set(Vue.form, 'supplierName', data[0].supplierName)
                                        // Vue.form.toElsAccount = data[0].toElsAccount
                                        // Vue.form.supplierName = data[0].supplierName
                                    },
                                    extend: {
                                        modalColumns: [
                                            {field: 'toElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_toCompanyCode`, '供应商编码'), fieldLabelI18nKey: 'i18n_field_toCompanyCode', with: 150},
                                            {field: 'supplierName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), fieldLabelI18nKey: 'i18n_massProdHeade95_supplierName', with: 150}
                                        ],
                                        modalUrl: '/supplier/supplierMaster/list',
                                        modalParams: {}
                                    },
                                    required: '1'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierName`, '供方名称'),
                                    fieldName: 'supplierName',
                                    disabled: true
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_findbyTitle`, '文件主题'),
                                    fieldName: 'businessScene',
                                    required: '1'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_p9Gt6s92wep4uCmw`, '主体文件是否已上传e签宝'),
                                    fieldName: 'uploaded',
                                    disabled: true,
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationDateSigning`, '签署有效截止时间'),
                                    fieldName: 'cutOffTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_contractRemind`, '文件到期前多少时间提醒(小时)'),
                                    fieldName: 'contractRemind'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_remindWay`, '提醒方式'),
                                    fieldName: 'noticeType',
                                    dictCode: 'srmFlowNoticType'
                                },
                                {
                                    fieldType: 'date',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_expirationdateDocument`, '文件有效截止时间'),
                                    fieldName: 'effectiveTime',
                                    dataFormat: 'YYYY-MM-DD'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isFile`, '是否已归档'),
                                    fieldName: 'archiving',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_isOpen`, '是否已开启'),
                                    fieldName: 'initiate',
                                    dictCode: 'yn',
                                    disabled: true
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_gwarjw3QgSxzQUiQ`, '签署完成是否自动发送'),
                                    fieldName: 'autoSend',
                                    dictCode: 'yn'
                                },
                                {
                                    fieldType: 'select',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_publishStatus`, '发布状态'),
                                    fieldName: 'sendStatus',
                                    disabled: true,
                                    dictCode: 'srmSendStatus'
                                },
                                {
                                    fieldType: 'input',
                                    fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注'),
                                    fieldName: 'remark',
                                    placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_saleBarcodeInfoHeadList_remark`, '备注')
                                }
                            ],
                            validateRules: {
                                toElsAccount: [{required: true, message: '供应商ELS账号不能为空'}],
                                businessScene: [{required: true, message: '文件主题不能为空'}]
                            }
                        }
                    },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Q6FOl1RUIuSOfH3w`, '签署人'), groupCode: 'purchaseSignersList', type: 'grid', custom: {
                        ref: 'purchaseEsignSignersList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 170, align: 'center', slots: { default: 'grid_opration' } },
                            { field: 'sealIds', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_stamp`, '印章'), width: 200 },
                            { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100, visible: false },
                            { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180, visible: false},
                            { field: 'loadingCompany', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), visible: false},
                            { field: 'loadingCompany_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_whetherLoadCompany`, '是否加载公司列表'), width: 120, visible: false},
                            { field: 'companyCode', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_companyCode`, '公司代码'), width: 120, visible: false},
                            { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureCompany`, '签署公司'), width: 200 },
                            { field: 'personType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_Jw4cGjnuDCPCSekJ`, '签署人类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dPL_134ee77`, '主签人'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Bd_c7dfd`, '抄送'), value: '1'}
                            ]}  },
                            { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ROSkyKENR2a4MA2u`, '签署/抄送人'), width: 120 },
                            { field: 'accountId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatureAccount`, '签署用户E签宝账号'), width: 120, visible: false },
                            { field: 'idType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170, visible: false },
                            { field: 'idType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'), width: 170 },
                            { field: 'idNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'), width: 170 },
                            { field: 'orgLegalIdNumber', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateNumber`, '法人证件号'), width: 170, visible: false },
                            { field: 'orgLegalName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_corporateName`, '法人名称'), width: 120, visible: false},
                            { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120, visible: false },
                            { field: 'filesId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileID`, '文件id'), width: 120, visible: false },
                            { field: 'filesName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_documentName`, '文件名'), width: 120, visible: false },
                            { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), visible: false},
                            { field: 'certificationStatus_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120},
                            { field: 'actorIndentityType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_ZyXhfnn5f0HojJzG`, '签署主体'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_8dg3QCTz`,  '个人'), value: '1'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead9f6_companyCode`, '公司'), value: '2'}
                            ]}  }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_menu_Su_da405`, '添加'),  type: 'primary', click: this.addPurchaseSignEvent},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), click: this.deletePurchaseSignEvent}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRWe_416ee4e0`, '设置印章'), clickFn: this.getSeal },
                            { type: 'add', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_GRPWL_ed04f016`, '设置签署人'), clickFn: this.addPurchaseSignerEvent}
                        ],
                        rules: {
                            // sealIds: [{required: true, message: '印章不能为空'}],
                            subAccount: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLWWWeyWxOLV_18bfbcf9`, '[签署人SRM账号]不能为空')}],
                            personType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWLAcWxOLV_fe45bfd2`, '[签署人类型]不能为空')}],
                            actorIndentityType: [{required: true, message: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_WPWdDWxOLV_86c84046`, '[签署主体]不能为空')}]
                        }
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_clNKYkONzEKOQpi9`, '签署文件'), groupCode: 'fileInfo', type: 'grid', custom: {
                        ref: 'purchaseEsignAttachmentList',
                        columns: [
                            { type: 'checkbox', width: 40 },
                            { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                            { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                            { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 180 },
                            { field: 'uploadElsAccount_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploader`, '上传方'), width: 120 },
                            { field: 'createBy_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCJey_b5b4ba34`, '上传方子帐号'), width: 120 },
                            { field: 'esignType', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signedTypes`, '签署类型'), width: 120, editRender: {name: '$select', options: [
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_Attachment`, '附件'), value: '0'},
                                {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_dD_9c878`, '主体'), value: '1'}
                            ]} },
                            { field: 'uploaded', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 180, visible: false },
                            { field: 'uploaded_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_column_LaUawliDMFVcRPYm`, '是否上传e签宝'), width: 120 },
                            { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                        ],
                        buttons: [
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadAttachment`, '上传附件'),
                                type: 'upload', businessType: 'esign',
                                attr: this.attrHandle,
                                callBack: this.uploadCallBack},
                            {title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_deleteInBatches`, '批量删除'), click: this.deleteBatch}
                        ],
                        showOptColumn: true,
                        optColumnList: [
                            { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.downloadEvent },
                            { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.deleteFilesEvent }
                        ]
                    } }
                ],
                formFields: [],
                footerButtons: [
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_save`, '保存'), type: 'primary', click: this.saveEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_QIXVWPs_e850e7df`, '文件上传E签宝'), type: 'primary', click: this.fileUpload },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_btn_hAIxPW_f576ec9f`, '发起一步签署'), type: 'primary', click: this.launchOneStepEsignEvent },
                    { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), click: this.goBack }
                ]
            },
            url: {
                add: '/esign/purchaseEsign/add',
                edit: '/esign/purchaseEsign/edit',
                detail: '/esign/purchaseEsign/queryById',
                uploadFile: '/esign/purchaseEsign/uploadToEsign',
                keyWordToAera: '/esign/elsEsign/keyWordToAera',
                viewEsignFile: '/esign/elsEsign/viewEsignFile',
                upload: '/attachment/purchaseAttachment/upload',
                uploadLogUrl: '/attachment/purchaseAttachment/uploadLog',
                download: '/attachment/purchaseAttachment/download',
                launchOneStepEsign: '/esign/purchaseEsign/launchOneStepEsign',
                getSignature: '/attachment/purchaseAttachment/getSignature'
            }
        }
    },
    mounted () {
        this.init()
    },
    methods: {
        attrHandle () {
            return {
                sourceNumber: this.currentEditRow.esignNumber,
                actionRoutePath: '/srm/esign/PurchaseEsignList,/srm/esign/sale/SaleEsignList'
            }
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({params: preViewFile })
        },
        init () {
            if(this.currentEditRow && this.currentEditRow.id) {
                this.$refs.editPage.queryDetail(this.currentEditRow.id)
            }else{
                this.$refs.editPage.queryDetail('')
            }
        },
        deleteBatch (){
            const fileGrid = this.$refs.editPage.$refs.purchaseEsignAttachmentList[0]
            const checkboxRecords = fileGrid.getCheckboxRecords()
            if (!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据'))
                return
            }
            const ids = checkboxRecords.map(n => (n.id)).join(',')
            const params = {
                ids
            }
            getAction('/esign/purchaseEsignAttachment/deleteBatch', params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.removeCheckboxRow()
            })
        },
        deleteFilesEvent (row) {
            let fileGrid = this.$refs.editPage.$refs.purchaseEsignAttachmentList[0]
            getAction('/attachment/purchaseAttachment/delete', {id: row.id}).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if (res.success) fileGrid.remove(row)
            })
        },
        getSeal (row, column, $rowIndex){
            if(row.personType!=='0'){
                this.$message.warning('抄送不需要设置印章')
                return
            }else{
                if(!row.accountId){
                    this.$message.warning('先设置签署/抄送人')
                    return
                }
                if(!row.actorIndentityType){
                    this.$message.warning('先设置签署主体')
                    return
                }
            }
            this.selectType = 'addSigner'
            this.rowIndex = $rowIndex
            let url = '/esign/purchaseEsignSeals/list'
            let columns = [
                { field: 'orgName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_nameOrganization`, '机构名称'), with: 150 },
                { field: 'sealId', title: '签章id', width: 180 },
                { field: 'alias', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
            ]
            if(row.actorIndentityType==='1'){
                columns = [
                    { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_personalAccount`, '个人账号'), width: 200 },
                    { field: 'sealId', title: '签章id', width: 180 },
                    { field: 'alias', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_SealtheAlias`, '印章别名'), width: 180 }
                ]
            }
            let params = {orgId: row.orgId, sealType: '1'}
            if(row.actorIndentityType==='1'){
                params = {companyName: row.companyName, accountId: row.accountId, sealType: '0'}
            }
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignEvent () {
            this.visible = true
        },
        handleOk () {
            this.peopleNumber = this.form.number
            this.selectType = 'purchaseEsign'
            const form = this.$refs.editPage.getPageData()
            let url = '/esign/elsEnterpriseCertificationInfo/list'
            let columns = [
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_signatory`, '签署方'), width: 100 },
                { field: 'enterpriseName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_theNameFirm`, '企业名称'), width: 180 },
                { field: 'companyName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead1ec_companyCode_dictText`, '公司名称'), width: 180 },
                { field: 'orgId', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_companySignatureAccount`, '公司E签宝账号'), width: 120 },
                { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120, editRender: {name: '$select', options: [
                    {label: '未认证', value: '0'},
                    {label: '已认证', value: '1'}
                ], disabled: true} }
            ]
            let params = {elsAccount: form.elsAccount}
            this.visible = false
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        addPurchaseSignerEvent (row, column, $rowIndex){
            this.selectType = 'purchaseEsigner'
            this.rowSignerIndex = $rowIndex
            let url = '/esign/elsSubaccountCertificationInfo/list'
            let columns = [
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'), width: 100 },
                { field: 'accountId', title: 'e签宝账户号', width: 180 },
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_name`, '姓名'), width: 180 },
                { field: 'certificationStatus', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_certificateStatus`, '认证状态'), width: 120, editRender: {name: '$select', options: [
                    {label: '未认证', value: '0'},
                    {label: '已认证', value: '1'}
                ], disabled: true} }
            ]
            let params = {orgCreateFlag: '1'}
            this.$refs.fieldSelectModal.open(url, params, columns, 'single')
        },
        fieldSelectOk (data) {
            const form = this.$refs.editPage.getPageData()
            if(this.selectType == 'purchaseEsign'){
                if(data[0].certificationStatus!=='1'){
                    this.$message.warning('该签署公司需要先进行实名认证，可前往认证[电子签章->企业认证->找到对应的公司提交认证并根据返回的链接进行认证]')
                    return
                }
                let arr = data.map(({ id, ...others }) => ({ relationId: id, signType: '0', autoSign: '0', ...others, subAccount: '', accountId: '', personType: '1', actorIndentityType: '2'}))
                let itemGrid = this.$refs.editPage.$refs.purchaseEsignSignersList[0]
                itemGrid.remove()
                for(let i=0;i<this.peopleNumber;i++){
                    itemGrid.insertAt(arr)
                }
            }else if(this.selectType === 'addSigner'){
                let ids = data.map(n => n.sealId).join(',')
                form.purchaseEsignSignersList[this.rowIndex].sealIds = ids
            }else if(this.selectType === 'purchaseEsigner'){
                if(data[0].certificationStatus!=='1'){
                    this.$message.warning('该签署公司需要先进行实名认证，可前往认证[电子签章->个人认证->找到对应的公司提交认证并根据返回的链接进行认证]')
                    return
                }
                form.purchaseEsignSignersList[this.rowSignerIndex].subAccount = data[0].subAccount
                form.purchaseEsignSignersList[this.rowSignerIndex].accountId = data[0].accountId
            }
        },
        uploadCallBack (result) {
            let itemGrid = this.$refs.editPage.$refs.purchaseEsignAttachmentList[0]
            itemGrid.insertAt(result, -1)
        },
        deletePurchaseSignEvent (){
            let itemGrid = this.$refs.editPage.$refs.purchaseEsignSignersList[0]
            let checkboxRecords = itemGrid.getCheckboxRecords()
            if(!checkboxRecords.length) {
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_selectDataMsg`, '请选择数据！'))
                return
            }
            itemGrid.removeCheckboxRow()
        },
        saveEvent () {
            const params = this.$refs.editPage.getPageData()
            if(typeof params.id == 'undefined'){
                postAction(this.url.add, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.currentEditRow = res.result
                        this.init()
                    }
                })
            }else {
                postAction(this.url.edit, params).then(res => {
                    const type = res.success ? 'success' : 'error'
                    this.$message[type](res.message)
                    if(res.success){
                        this.init()
                    }
                })
            }
        },
        launchOneStepEsignEvent (){
            const params = this.$refs.editPage.getPageData()
            if(!params.purchaseEsignSignersList || params.purchaseEsignSignersList.length<1){
                this.$message.warning('签署人不能为空')
                return
            }else{
                let mainSigner = 0
                for(let item of params.purchaseEsignSignersList){
                    if(item.personType==='0'){
                        if(!item.sealIds && item.actorIndentityType==='2'){
                            this.$message.warning('签署主体为公司时，主签人对应行印章不能为空')
                            return
                        }
                        mainSigner++
                    }
                }
                if(mainSigner===0){
                    this.$message.warning('签署人列表中全是抄送，没有主签人')
                    return
                }else if(mainSigner>1){
                    this.$message.warning('签署人列表中主签人存在多位')
                    return
                }
            }
            if(params.uploaded!=='1'){
                this.$message.warning('主体文件还未上传e签宝')
                return
            }
            if(!params.purchaseEsignAttachmentList || params.purchaseEsignAttachmentList.length<1){
                this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_PWQIxOLV_d540bb93`, '签署文件不能为空'))
                return
            }
            let mainFileFlag = false
            let mainFileUploadFlag = false
            for(let file of params.purchaseEsignAttachmentList){
                if(file.esignType==='1'){
                    mainFileFlag = true
                }
                if(file.fileId && file.esignType==='1'){
                    mainFileUploadFlag = true
                }
            }
            if(!mainFileFlag){
                this.$message.warning('签署文件列表中缺少“主体”文件')
                return
            }
            // if(!mainFileUploadFlag){
            //     this.$message.warning('"主体"文件没有上传e签宝')
            //     return
            // }
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.$refs.editPage.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.$refs.editPage.currentStep = i
                        return
                    }
                }
                if (flag) {
                    let url = this.url.launchOneStepEsign
                    postAction(url, params).then(res => {
                        const type = res.success ? 'success' : 'error'
                        this.$message[type](res.message)
                        if(res.success){
                            this.goBack()
                        }
                    })
                }
            })
        },
        fileUpload (){
            const params = this.$refs.editPage.getPageData()
            // if(params.uploaded==='1'){
            //     this.$message.warning('文件已上传')
            //     return
            // }
            if(!params.purchaseEsignAttachmentList || params.purchaseEsignAttachmentList.length<1){
                this.$message.warning('上传文件不能为空')
                return
            }
            let url = this.url.uploadFile
            postAction(url, params).then(res => {
                const type = res.success ? 'success' : 'error'
                this.$message[type](res.message)
                if(res.success){
                    this.init()
                }
            })

        },
        esignFileDown (){
            const params = this.$refs.editPage.getPageData()
            getAction(this.url.viewEsignFile, {id: params.id}).then(res => {
                if(res.success){
                    window.open(res.result.downloadUrl)
                }else{
                    this.$message.warning(res.message)
                }
            })
        }
    }
}
</script>