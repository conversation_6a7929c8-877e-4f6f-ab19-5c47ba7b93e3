<template>
  <!-- 需求池管理 - 转订单 -->
  <div class="business-container">
    <a-spin :spinning="confirmLoading">
      <business-layout
        :ref="businessRefName"
        :gridCustomConfig="{
          treeConfig: { transform: true }
        }"
        v-if="businessShow"
        :currentEditRow="currentEditRow"
        :fromSourceData="responseDataForFilter"
        :pageHeaderButtons="pageHeaderButtons"
        :handleBeforeRemoteConfigData="handleBeforeRemoteConfigData"
        modelLayout="collapse"
        pageStatus="edit"
        :setGridHeight="setGridHeight"
        v-on="businessHandler"
      >
        <template #purchaseDeliveryNoticeToDeliveryItemVOListTab="{ slotProps }">
          <!-- 新增筛选区域 -->
          <div class="tableFilter">
            <a-form
              class="tableFilterForm"
              :form="filterForm"
              :label-col="{ span: 8 }"
              :wrapper-col="{ span: 14 }"
            >
              <a-form-item
                class="filterFormItem"
                label="物料编码"
              >
                <a-input
                  v-model="filterForm.materialNumber"
                  placeholder="请输入"
                />
              </a-form-item>
              <a-form-item
                class="filterFormItem"
                label="物料名称"
              >
                <a-input
                  v-model="filterForm.materialName"
                  placeholder="请输入"
                />
              </a-form-item>
              <a-form-item
                class="filterFormItem"
                label="供应商编码"
              >
                <a-input
                  v-model="filterForm.supplierCode"
                  placeholder="请输入"
                />
              </a-form-item>
              <a-form-item
                class="filterFormItem"
                label="供应商名称"
              >
                <a-input
                  v-model="filterForm.supplierName"
                  placeholder="请输入"
                />
              </a-form-item>

              <div class="filterFormFooter">
                <a-button
                  type="primary"
                  icon="search"
                  @click="handleSearch"
                  >{{ $srmI18n(`${$getLangAccount()}#i18n_title_Query`, '查询') }}
                </a-button>

                <a-button
                  type="default"
                  icon="reload"
                  style="margin-right: 8px"
                  @click="handleReset"
                  >{{ $srmI18n(`${$getLangAccount()}#i18n_title_reset`, '重置') }}
                </a-button>
              </div>
            </a-form>
          </div>
          <!-- {{ slotProps.group }} -->
          <edit-grid-layout
            v-if="slotProps.group.groupType === 'item' && slotProps.group.groupCode === 'purchaseDeliveryNoticeToDeliveryItemVOList'"
            :ref="slotProps.group.groupCode + 'grid'"
            :busAccount="slotProps.busAccount"
            :resultData="slotProps.resultData"
            :currentEditRow="currentEditRow"
            :gridLoading="false"
            :group="slotProps.group"
            :gridConfig="$refs[businessRefName].getGridConfig(slotProps.group)"
            :loadData="slotProps.group.loadData"
            :pageConfig="slotProps.pageConfig"
            v-on="$listeners"
          >
          </edit-grid-layout>
        </template>
      </business-layout>
    </a-spin>
    <column-set
      @selectedOk="getColumnsListNew"
      ref="columnSet"
    />
  </div>
</template>

<script lang="jsx">
const ROWDEMO = {
  groupCode: '',
  title: '',
  fieldLabelI18nKey: '',
  field: '',
  fieldType: '',
  align: '',
  headerAlign: '',
  defaultValue: '',
  width: '',
  dictCode: '',
  alertMsg: '',
  mobile: 1,
  helpText: ''
}

import { cloneDeep, throttle } from 'lodash'
import BusinessLayout from '@comp/template/business/BusinessLayout'
import { businessUtilMixin } from '@comp/template/business/businessUtilMixin.js'
import { postAction } from '@/api/manage'
import REGEXP from '@/utils/regexp.js'
import columnSet from './columnSet'
import { ajaxGetAllColumns } from '@/api/api'
import EditGridLayout from '@comp/template/business/EditGridLayout.vue'

export default {
  name: 'DemandToOrderModal',
  mixins: [businessUtilMixin],
  components: {
    BusinessLayout,
    EditGridLayout,
    columnSet
  },
  props: {
    responseData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      columnsCode: 'DemondToOrderCode',
      businessShow: false,
      pageHeaderButtons: [
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'),
          key: 'gridColSet',
          click: this.handleColumnSet,
          attrs: {
            type: 'primary'
          }
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_creatOrder`, '生成订单'),
          key: '_creatOrder',
          attrs: {
            type: 'primary'
          },
          authorityCode: 'purchaseRequest#purchaseRequestHead:requestToCreateOrder',
          click: this.generateOrder
        },
        {
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'),
          key: 'goBack'
        }
      ],
      url: {
        demandMatchPrice: '/demand/purchaseRequestHead/requestMatchInfomationRecord',
        requestToOrder: '/demand/purchaseRequestHead/requestToCreateOrder'
      },
      purchaseDeliveryNoticeToDeliveryItemVOListColumns: [
        // {
        //   type: 'expand',
        //   width: 50,
        //   fieldType: 'contentIndex',
        //   align: 'center',
        //   headerAlign: 'left',
        //   slots: {
        //     content: ({ row, rowIndex, column, columnIndex }, h) => {
        //       const props = {
        //         columns: [
        //           { type: 'seq', title: '序号', width: 80 },
        //           { field: 'requestNumber', title: '采购申请单号', width: 100 },
        //           { field: 'materialNumber', title: '物料编码', width: 100 },
        //           { field: 'materialName', title: '物料名称', width: 100 }
        //         ],
        //         data: row.requestItemChildList
        //       }
        //       let tpl = (
        //         <div class='expand-wrapper'>
        //           <vxe-grid {...{ props }}></vxe-grid>
        //         </div>
        //       )
        //       return tpl
        //     }
        //   }
        // },
        {
          title: '物料编码',
          fieldLabelI18nKey: 'i18n_title_materialCode',
          field: 'materialNumber',
          width: 200,
          sortable: true
        },
        {
          title: '物料描述',
          fieldLabelI18nKey: 'i18n_title_materialDesc',
          field: 'materialDesc',
          width: 160
        },
        {
          title: '物料名称',
          fieldLabelI18nKey: 'i18n_title_materialName',
          field: 'materialName',
          width: 160
        },
        {
          title: '可转订单数量',
          fieldLabelI18nKey: 'i18n_title_transferredOrderNum',
          field: 'quantity',
          width: 180,
          sortable: true
        },
        {
          title: '价格主数据记录号',
          fieldLabelI18nKey: 'i18n_field_infoRecordNumber',
          field: 'infoRecordNumber',
          width: 180
        },
        {
          title: '价格剩余可执行数量',
          fieldLabelI18nKey: 'i18n_field_remainExecutableQuantity',
          field: 'remainExecutableQuantity',
          width: 180
        },

        {
          title: '建议分配数量',
          fieldLabelI18nKey: '',
          field: 'suggestAllocQuantity',
          width: 180
        },
        {
          title: '转订单数量',
          fieldLabelI18nKey: 'i18n_title_toOrderQuantity',
          field: 'toOrderQuantity',
          width: 180,
          fieldType: 'number',
          alertMsg: '只允许输入正整数或正小数',
          alertMsgI18nKey: 'i18n_field_RiTWNiiWSiXW_6c064a5a',
          regex: REGEXP.interger,
          bindFunction: function bindFunction(row, column, value) {
            let getQuantityPrice = (row) => {
              const json = JSON.parse(row.ladderPriceJson)
              const quantityList = json.map((item) => Number(item.ladderQuantity))
              const toOrderQuantity = Number(value) || quantityList[quantityList.length - 1]
              quantityList.push(toOrderQuantity)
              quantityList.sort((a, b) => a - b)
              const indexs = []
              quantityList.forEach((i, index) => {
                if (i === Number(toOrderQuantity)) indexs.push(index)
              })
              const index = indexs.pop()
              return json[index === 0 ? index : index - 1]
            }
            if (row.ladderPriceJson) {
              const current = getQuantityPrice(row)
              const { price, netPrice } = current
              // let toOrderQuantity = row.toOrderQuantity || 1
              row['price'] = Number(price) // 含税价
              row['netPrice'] = Number(netPrice) // 未税价
              // row['taxAmount'] = (price * toOrderQuantity).toFixed(6)
              // row['netAmount'] = (netPrice * toOrderQuantity).toFixed(6)
            }
            console.log('转订单数量编辑', value)
            if (!!value) {
              row.secondaryQuantity = value / (row.conversionRate || 1)
            } else {
              row.secondaryQuantity = 0
            }
            row.secondaryQuantity = row.secondaryQuantity.toFixed(6)
          },
          extend: {
            min: 0
          },
          required: '1'
        },
        {
          title: '备注',
          fieldLabelI18nKey: 'i18n_field_remark',
          field: 'remark',
          fieldType: 'input',
          width: 180
        },
        {
          title: '是否启用货源清单',
          fieldLabelI18nKey: 'i18n_field_source',
          field: 'source_dictText',
          width: 180
        },
        {
          title: '首选供应商',
          fieldLabelI18nKey: 'i18n_title_isPreferredSupplier',
          field: 'preferredSupplier_dictText',
          width: 180
        },
        {
          title: '含税单价',
          fieldLabelI18nKey: 'i18n_title_taxPrice',
          field: 'price',
          width: 200,
          fieldType: 'number',
          alertMsg: '只允许输入正整数或正小数',
          alertMsgI18nKey: 'i18n_field_RiTWNiiWSiXW_6c064a5a',
          regex: REGEXP.interger,
          bindFunction: this.bindFunction,
          extend: {
            min: 0
          },
          required: '1',
          sortable: true
        },
        {
          title: '未税单价',
          fieldLabelI18nKey: 'i18n_field_netPrice',
          field: 'netPrice',
          width: 200,
          sortable: true
        },
        {
          title: '税码',
          fieldLabelI18nKey: 'i18n_title_enterTaxCode',
          field: 'taxCode',
          fieldType: 'selectModal',
          width: 200,
          required: '1',
          bindFunction: this.bindFunction,
          extend: {
            modalColumns: [
              {
                field: 'taxCode',
                title: '税码',
                fieldLabelI18nKey: 'i18n_field_taxCode',
                with: 150
              },
              {
                field: 'taxRate',
                title: '税率（%）',
                fieldLabelI18nKey: 'i18n_field_fIW_1d82c0c',
                with: 150
              },
              {
                field: 'taxName',
                title: '税码名称',
                fieldLabelI18nKey: 'i18n_field_taxName',
                with: 150
              },
              {
                field: 'remark',
                title: '备注',
                fieldLabelI18nKey: 'i18n_field_remark',
                with: 150
              }
            ],
            modalUrl: '/base/tax/list',
            modalParams: {},
            beforeCheckedCallBack(Vue, row, column) {
              return new Promise((resolve, reject) => {
                return row.freePrice !== '0' ? reject('不允许编辑') : resolve('success')
              })
            },
            afterRowClearCallBack: function (Vue, row) {
              row.taxCode = ''
              row.taxRate = ''
            }
          }
        },
        {
          title: '税率',
          fieldLabelI18nKey: 'i18n_title_taxRate',
          field: 'taxRate',
          width: 200
        },
        {
          title: '临时定价',
          field: 'estimate',
          fieldLabelI18nKey: 'i18n_field_temporaryPrice',
          width: 200,
          fieldType: 'select',
          dictCode: 'yn'
        },
        {
          title: '对方ELS账号',
          fieldLabelI18nKey: 'i18n_title_supplierAccount',
          field: 'toElsAccount',
          fieldType: 'selectModal',
          width: 160,
          required: '1',
          bindFunction: function bindFunction(row, data) {
            row.toElsAccount = data[0].toElsAccount || ''
            row.supplierName = data[0].supplierName || ''
            row.needCoordination_dictText = data[0].needCoordination_dictText || ''
            row.needCoordination = data[0].needCoordination || ''
          },
          extend: {
            modalColumns: [
              {
                field: 'toElsAccount',
                fieldLabelI18nKey: 'i18n_title_supplierAccount',
                title: '供应商ELS账号',
                with: 150
              },
              {
                field: 'supplierCode',
                fieldLabelI18nKey: 'i18n_title_supplierCode',
                title: '供应商编码',
                with: 150
              },
              {
                field: 'supplierName',
                fieldLabelI18nKey: 'i18n_title_supplierName',
                title: '供应商名称',
                with: 150
              },
              {
                field: 'needCoordination_dictText',
                fieldLabelI18nKey: 'i18n_field_MeCK_2726e7d3',
                title: '协同方式',
                with: 150
              }
            ],
            modalUrl: '/supplier/supplierMaster/list',
            modalParams: {
              frozenFunctionValue: '1'
            },
            beforeCheckedCallBack(Vue, row, column) {
              console.log('row', row)
              return new Promise((resolve, reject) => {
                return row.freePrice !== '0' ? reject('不允许编辑') : resolve('success')
              })
            }
          }
        },
        {
          title: '供应商名称',
          fieldLabelI18nKey: 'i18n_title_supplierName',
          field: 'supplierName',
          width: 200,
          sortable: true
        },
        {
          title: '价格开始时间',
          fieldLabelI18nKey: 'i18n_title_priceEffectiveDate',
          field: 'effectiveDate',
          width: 200
        },
        {
          title: '价格结束时间',
          fieldLabelI18nKey: 'i18n_title_priceExpiryDate',
          field: 'expiryDate',
          width: 200
        },
        {
          title: '阶梯价格',
          fieldLabelI18nKey: 'i18n_title_ladderPrice',
          field: 'ladderPriceJson',
          width: 200,
          slots: this.ladderPriceJsonSlots(),
          showOverflow: 'ellipsis'
        },
        {
          title: '协同方式',
          fieldLabelI18nKey: 'i18n_field_MeCK_2726e7d3',
          field: 'needCoordination_dictText',
          width: 200
        },
        {
          title: '付款方式',
          fieldLabelI18nKey: 'i18n_field_paymentWay',
          field: 'payWay_dictText',
          width: 200
        },
        {
          title: '付款条件',
          fieldLabelI18nKey: 'i18n_field_paymentClause',
          field: 'paymentClause_dictText',
          width: 200
        },
        {
          title: '工厂',
          fieldLabelI18nKey: 'i18n_field_factory',
          field: 'factory_dictText',
          width: 200
        },
        {
          title: '采购组织',
          fieldLabelI18nKey: 'i18n_field_purchaseOrg',
          field: 'purchaseOrg_dictText',
          width: 200
        },
        {
          title: '公司',
          field: 'company_dictText',
          fieldLabelI18nKey: 'i18n_field_company',
          width: 200
        },
        {
          title: '采购组',
          fieldLabelI18nKey: 'i18n_field_purchaseGroup',
          field: 'purchaseGroup_dictText',
          width: 200
        },
        {
          title: '送货安排',
          fieldLabelI18nKey: 'i18n_field_dSpA_43933cef',
          field: 'jit_dictText',
          width: 200
        },
        {
          title: '是否价格控制',
          fieldLabelI18nKey: 'i18n_field_KQumVR_25c9d6ab',
          field: 'freePrice',
          width: 200,
          fieldType: 'select',
          dictCode: 'yn',
          disabled: true
        },
        {
          title: '配额',
          fieldLabelI18nKey: '',
          field: 'quota',
          width: 200
        },
        {
          title: '累计建议数量',
          fieldLabelI18nKey: '',
          field: 'sumSuggestQuantity',
          width: 200
        },
        {
          title: '最小订单量',
          fieldLabelI18nKey: '',
          field: 'minOrderQuantity',
          width: 200
        },
        {
          title: '最小包装量',
          fieldLabelI18nKey: '',
          field: 'minPackageQuantity',
          width: 200
        },
        {
          title: '最大分配数量',
          fieldLabelI18nKey: '',
          field: 'maxAllocQuantity',
          width: 200
        },
        {
          title: '模板名称',
          fieldLabelI18nKey: 'i18n_field_templateName',
          field: 'templateName',
          width: 200
        },
        {
          title: '模板编号',
          fieldLabelI18nKey: 'i18n_title_templateNumber',
          field: 'templateNumber',
          width: 200
        },
        {
          title: '订单类型',
          fieldLabelI18nKey: 'i18n_field_orderType',
          field: 'orderType_dictText',
          width: 200
        },
        {
          title: '采购类型',
          fieldLabelI18nKey: 'i18n_field_purchaseType',
          field: 'purchaseType_dictText',
          width: 200
        },
        {
          groupCode: '',
          title: '进度模板编码',
          fieldLabelI18nKey: 'i18n_field_HzIrAo_e24d7254',
          field: 'orderProgressTemplateNumber',
          align: 'center',
          headerAlign: 'center',
          dataFormat: '',
          defaultValue: '',
          width: '150',
          dictCode: '',
          alertMsg: '',
          helpText: ''
        },
        {
          groupCode: '',
          title: '进度模板ID',
          fieldLabelI18nKey: 'i18n_field_HzIrWW_e23d9fc4',
          field: 'orderProgressTemplateId',
          align: 'center',
          headerAlign: 'center',
          dataFormat: '',
          defaultValue: '',
          width: '150',
          dictCode: '',
          alertMsg: '',
          helpText: ''
        },
        {
          groupCode: '',
          title: '进度模板名称',
          fieldLabelI18nKey: 'i18n_field_HzIrRL_e2483e2c',
          field: 'orderProgressTemplateName',
          align: 'center',
          headerAlign: 'center',
          dataFormat: '',
          defaultValue: '',
          width: '150',
          dictCode: '',
          alertMsg: '',
          bindFunction: function bindFunction(row, data, _self) {
            row.orderProgressTemplateNumber = data[0].progressTemplateNumber
            row.orderProgressTemplateName = data[0].progressTemplateName
            row.orderProgressTemplateId = data[0].id
          },
          extend: {
            modalColumns: [
              {
                field: 'progressTemplateNumber',
                title: '进度模板编码',
                fieldLabelI18nKey: 'i18n_field_HzIrAo_e24d7254',
                with: 150
              },
              {
                field: 'progressTemplateName',
                title: '进度模板名称',
                fieldLabelI18nKey: 'i18n_field_HzIrRL_e2483e2c',
                with: 150
              },
              {
                field: 'applicableScope',
                title: '适用范围',
                fieldLabelI18nKey: 'i18n_field_KjvL_433e5837',
                with: 150
              }
            ],
            modalUrl: '/progressTemplate/purchaseOrderProgressHead/list',
            selectModel: 'single',
            modalParams(Vue, form, row) {},
            mobileModalParams(Vue, { _pageData, _cacheAllData }) {}
          },
          fieldType: 'selectModal',
          required: '0',
          helpText: ''
        }
      ],

      filterForm: {
        materialName: '', // 物料名称
        materialNumber: '', // 物料编码
        supplierName: '', // 供应商名称
        supplierCode: '' // 供应商编码
      },
      responseDataForFilter: []
    }
  },
  created() {
    console.log('responseData', this.responseData)
    let purchaseDeliveryNoticeToDeliveryItemVOList = this.responseData.purchaseDeliveryNoticeToDeliveryItemVOList || []
    purchaseDeliveryNoticeToDeliveryItemVOList = purchaseDeliveryNoticeToDeliveryItemVOList.map((item) => {
      if (!!item.toOrderQuantity) {
        item.secondaryQuantity = item.toOrderQuantity / (item.conversionRate || 1)
      } else {
        item.secondaryQuantity = 0
      }
      item.secondaryQuantity = item.secondaryQuantity.toFixed(6)
      return item
    })
    this.responseData.purchaseDeliveryNoticeToDeliveryItemVOList = purchaseDeliveryNoticeToDeliveryItemVOList
    this.getColumnsListNew()
  },
  computed: {
    setGridHeight() {
      let height = document.documentElement.clientHeight
      let num = height - 250
      num = num < 500 ? 500 : num
      return num + ''
    }
  },
  methods: {
    formatTableData(data) {
      console.log('請求接口後格式化列表數據', data)
      let purchaseDeliveryNoticeToDeliveryItemVOList = data.purchaseDeliveryNoticeToDeliveryItemVOList
      if (!!purchaseDeliveryNoticeToDeliveryItemVOList && purchaseDeliveryNoticeToDeliveryItemVOList.length > 0) {
        purchaseDeliveryNoticeToDeliveryItemVOList = purchaseDeliveryNoticeToDeliveryItemVOList.map((item) => {
          if (item.price === 0 || Math.abs(item.price) > 0) {
            item.price = Number(item.price).toFixed(6)
          }
          if (item.ladderPriceJson === 0 || Math.abs(item.ladderPriceJson) > 0) {
            item.ladderPriceJson = Number(item.ladderPriceJson).toFixed(6)
          }
          if (item.estimate === 0 || Math.abs(item.estimate) > 0) {
            item.estimate = Number(item.estimate).toFixed(6)
          }

          if (item.netPrice === 0 || Math.abs(item.netPrice) > 0) {
            item.netPrice = Number(item.netPrice).toFixed(6)
          }
          //需求池转订单，单条数据备注自动赋值，同物料多条数据合并备注置空
          if(item.requestItemChildList.length>1){
              item.remark = ''
          }else{
              item.remark = item.requestItemChildList[0].remark
          }
          item.fbk2= item.requestItemChildList[0].fbk2


          return item
        })
        data.purchaseDeliveryNoticeToDeliveryItemVOList = purchaseDeliveryNoticeToDeliveryItemVOList
      }

      return data
    },
    getColumnsListNew() {
      this.confirmLoading = true
      ajaxGetAllColumns(this.columnsCode, null)
        .then((res) => {
          // this.visible =false
          if (res.success) {
            this.responseDataForFilter = this.responseData
            this.businessShow = true
            const data = res.result
            this.$nextTick(() => {
              const $grid = this.$refs.purchaseDeliveryNoticeToDeliveryItemVOListgrid.$refs.purchaseDeliveryNoticeToDeliveryItemVOList
              // const hideMap = data.filter((rs) => rs.hidden == '1').map((rs) => rs.columnCode)
              this.autoHideColumns($grid, data)
            })
          }
        })
        .finally(() => {
          this.confirmLoading = false
          // this.loading = false
        })
    },
    // autoHideColumns(grid, hideColumn) {
    autoHideColumns(grid, dataColumn) {
      let { fullColumn, tableColumn, collectColumn } = grid.getTableColumn()
      console.log('allColumns', grid.getTableColumn())

      // let pageColumns = cloneDeep(this.purchaseDeliveryNoticeToDeliveryItemVOListColumns)
      let pageColumns = tableColumn
      // listColumns.unshift(tableColumn[0], tableColumn[1])
      let newColumn = dataColumn.map((column) => {
        column = {
          ...column,
          visible: true,
          title: column.columnName,
          fieldLabelI18nKey: column.columnNameI18nKey,
          field: column.columnCode,
          width: column.width || 200
        }
        pageColumns.forEach((pageColumn) => {
          if (pageColumn.field === column.columnCode) {
            column = {
              ...column,
              ...pageColumn,
              title: column.columnName
            }
          }
        })
        if (column.hidden == '1') column.visible = false
        return column
      })
      newColumn.unshift(tableColumn[0], tableColumn[1])

      let responseData = cloneDeep(this.responseData)
      console.log('responseData', responseData)
      let item = (responseData.purchaseDeliveryNoticeToDeliveryItemVOList || [])[0] || {}
      let quoteType = item.quoteType || null
      if (quoteType == '1') {
        newColumn = newColumn.filter((col) => {
          if (col.field == 'taxCode' || col.field == 'taxRate') return false
          return true
        })
        item.taxCode = null
        item.taxRate = null
      }

      console.log('newColumn', newColumn)
      grid.loadColumn(newColumn)

      // if (allColumns.fullColumn) {
      //   allColumns.fullColumn.forEach((col) => {
      //     col.visible = true
      //     if (hideColumn && hideColumn.length) {
      //       hideColumn.forEach((hideCol) => {
      //         if (hideCol === col.property) {
      //           col.visible = false
      //         }
      //       })
      //     }
      //   })
      // }
      // grid.refreshColumn()
    },
    handleColumnSet() {
      this.$refs.columnSet.open(this.columnsCode)
    },
    bindFunction(row, data = []) {
      //是否价格控制
      if (!row.freePrice || row.freePrice != 0) return

      function add(a, b) {
        var c, d, e
        try {
          c = a.toString().split('.')[1].length
        } catch (f) {
          c = 0
        }
        try {
          d = b.toString().split('.')[1].length
        } catch (f) {
          d = 0
        }
        e = Math.pow(10, Math.max(c, d))
        return (mul(a, e) + mul(b, e)) / e
      }

      function sub(a, b) {
        var c, d, e
        try {
          c = a.toString().split('.')[1].length
        } catch (f) {
          c = 0
        }
        try {
          d = b.toString().split('.')[1].length
        } catch (f) {
          d = 0
        }
        e = Math.pow(10, Math.max(c, d))
        return (mul(a, e) - mul(b, e)) / e
      }

      function mul(a, b) {
        var c = 0
        var d = a.toString()
        var e = b.toString()
        try {
          c += d.split('.')[1].length
        } catch (f) {}
        try {
          c += e.split('.')[1].length
        } catch (f) {}
        return (Number(d.replace('.', '')) * Number(e.replace('.', ''))) / Math.pow(10, c)
      }

      function div(a, b) {
        var c = 0
        var d = 0
        var e = 0
        var f = 0
        try {
          e = a.toString().split('.')[1].length
        } catch (g) {}
        try {
          f = b.toString().split('.')[1].length
        } catch (g) {}
        c = Number(a.toString().replace('.', ''))
        d = Number(b.toString().replace('.', ''))
        return mul(c / d, Math.pow(10, f - e))
      }

      if (Array.isArray(data) && data.length) {
        row.taxCode = data[0]?.taxCode.toString() || ''
        row.taxRate = data[0]?.taxRate.toString() || ''
      }
      if ((row.price || row.price == 0) && (row.taxRate || row.taxRate == 0)) {
        let taxRate = row.taxRate
        let price = row.price
        let tax = add(1, div(taxRate, 100))
        // console.log('tax', tax)
        let netPrice = div(price, tax)
        row.netPrice = netPrice.toFixed(6)
      }
    },
    ladderPriceJsonSlots() {
      let _that = this
      // 阶梯报价json数据组装
      let initRowLadderJson = (jsonData) => {
        let arr = []
        if (jsonData) {
          arr = JSON.parse(jsonData)
        }
        return arr
      }
      // 阶梯报价默认显示
      let defaultRowLadderJson = (jsonData) => {
        let arrString = ''
        if (jsonData) {
          let arr = JSON.parse(jsonData)
          arr.forEach((item, index) => {
            let ladderQuantity = item.ladderQuantity
            let price = item.price
            let netPrice = item.netPrice
            let str = `${ladderQuantity} ${price} ${netPrice} `
            let separator = index === arr.length - 1 ? '' : ','
            arrString += str + separator
          })
        }
        return arrString
      }
      return {
        default: ({ row, rowIndex, column, columnIndex }, h) => {
          return row[column.property]
            ? [
                <a-tooltip
                  placement='top'
                  overlayClassName='tip-overlay-class'
                >
                  <template slot='title'>
                    <vxe-table
                      auto-resize
                      border
                      row-id='id'
                      size='mini'
                      data={initRowLadderJson(row[column.property])}
                    >
                      <vxe-table-column
                        type='seq'
                        title={`${_that.$srmI18n(`${_that.$getLangAccount()}#i18n_title_details`, '序号')}`}
                        width='80'
                      />
                      <vxe-table-column
                        field='ladderQuantity'
                        title={`${_that.$srmI18n(`${_that.$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`}
                        width='140'
                      />
                      <vxe-table-column
                        field='price'
                        title={`${_that.$srmI18n(`${_that.$getLangAccount()}#i18n_title_price`, '含税价')}`}
                        width='140'
                      />
                      <vxe-table-column
                        field='netPrice'
                        title={`${_that.$srmI18n(`${_that.$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`}
                        width='140'
                      />
                    </vxe-table>
                  </template>
                  <div className='json-box'>
                    <a href='javascript: void(0)'>{defaultRowLadderJson(row[column.property])}</a>
                  </div>
                </a-tooltip>
              ]
            : []
        }
      }
    },
    handleBeforeRemoteConfigData() {
      let itemColumns = this.purchaseDeliveryNoticeToDeliveryItemVOListColumns.map((n) => {
        let cloneDemo = cloneDeep(ROWDEMO)
        return { ...cloneDemo, ...n, groupCode: 'purchaseDeliveryNoticeToDeliveryItemVOList' }
      })

      return {
        groups: [
          {
            groupName: '需求行',
            groupNameI18nKey: 'i18n_title_demandLine',
            groupCode: 'purchaseDeliveryNoticeToDeliveryItemVOList',
            groupType: 'item',
            sortOrder: '1',
            extend: {
              editConfig: {
                showStatus: true,
                mode: 'cell',
                activeMethod(gridData, row, _rowIndex, column, _columnIndex) {
                  let arr = [
                    // 'toOrderQuantity', // 转订单数量
                    'taxCode', // 税码
                    'toElsAccount', // 对方ELS账号
                    'price', // 含税单价
                    'estimate'// 临时定价
                  ]
                  // 转订单数量 允许输入
                  if (column.property === 'toOrderQuantity'||column.property==='remark') {
                    return true
                  }
                  if (row.freePrice && row.freePrice === '0') {
                    if (arr.includes(column.property)) {
                      return true
                    }
                  }
                  return false
                }
              }
            }
          }
        ],
        formFields: [],
        itemColumns,
        sortConfig: {
          multiple: true
        }
      }
    },
    async a(itemGrid) {
      let checkboxRecords = itemGrid.getCheckboxRecords() || []
      const errMap = await itemGrid.$refs.xTable.validate(checkboxRecords)
      console.log(errMap)
      return errMap
    },
    //生成订单处理逻辑
    async createOrderControll() {
      let self = this
      const groupCode = 'purchaseDeliveryNoticeToDeliveryItemVOList'
      const gridRef = `${groupCode}grid`
      let itemGrid = this.$refs[gridRef].$refs[groupCode]
      let checkboxRecords = itemGrid.getCheckboxRecords() || []

      console.log('生成订单', checkboxRecords)
      let url = '/demand/purchaseRequestHead/overQualityNotice'
      let params = checkboxRecords.map((i) => {
        let rowIndex = itemGrid.getRowSeq(i)
        return {
          materialCode: i.materialNumber || '', //物料编码
          requestItemNumber: rowIndex || '', //需求行号
          quantity: i.toOrderQuantity, //转订单数量
          infoRecordNumber: i.infoRecordNumber || '' //价格记录编号
        }
      })
      let res = await postAction(url, params)
      if (!!res.result && res.result.length) {
        for (const msg of res.result) {
          this.$message.warning(msg, 10)
        }
      }

      itemGrid.$refs.xTable
        .validate(checkboxRecords)
        .then((result) => {
          this.$confirm({
            title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
            content: this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_KQRLbLIt_979ec911`, '是否确认生成订单'),
            onOk: () => {
              self.confirmLoading = true
              postAction(self.url.requestToOrder, checkboxRecords).then((res) => {
                if (res.success) {
                  if (res?.result?.length == 1) {
                    self.jumpTargetPage({ result: res.result })
                  } else if (res?.result?.length > 1) {
                    let orders = res.result.map((rs) => rs.orderNumber)
                    orders = orders.join(',')
                    const message = `转订单成功，已转 ${res.result.length} 单，单号为${orders}`
                    self.$message.success(message)
                  } else {
                    self.$message.success(res.message)
                  }
                  self.businessHide()
                } else {
                  self.$message.warn(res.message)
                  self.confirmLoading = false
                }
              })
            }
          })
        })
        .catch((err) => {
          self.$message.error('验证失败，请处理')
          self.confirmLoading = false
        })
    },
    jumpTargetPage(data) {
      const { result } = data
      const query = {
        source: 'demand-pool',
        result
      }
      this.$router.push({ path: '/srm/order/purchase/PurchaseOrderHeadList', query })
    },
    // 生成订单
    generateOrder: throttle(function (args) {
      const groupCode = 'purchaseDeliveryNoticeToDeliveryItemVOList'
      const gridRef = `${groupCode}grid`
      let itemGrid = this.$refs[gridRef].$refs[groupCode]
      let checkboxRecords = itemGrid.getCheckboxRecords() || []

      if (!checkboxRecords || !checkboxRecords.length) {
        this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ViFTPbLItjTVc_32c227c2`, '请选择需要生成订单的需求行'))
        return
      }
      let isPuotaProcotolNoValid = []
      checkboxRecords.forEach((v, i) => {
        if (v.quotaProcotolId && v.quantity != v.suggestAllocQuantity) {
          isPuotaProcotolNoValid.push(i + 1)
        }
      })
      if (isPuotaProcotolNoValid.length > 0) {
        this.$confirm({
          title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_tip`, '提示'),
          content: `${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_isE_226b188`, '选中行')}  {${isPuotaProcotolNoValid.join(',')}} ，${this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_qsItWRxEUIIzEWR_865958bd`, '可转订单数量不等于建议分配数量')}`,
          onOk: () => {
            this.createOrderControll()
          }
        })
      } else {
        this.createOrderControll()
      }
    }, 500),

    handleSearch() {
      this.confirmLoading = true
      // this.businessShow = false

      setTimeout(() => {
        try {
          let responseData = cloneDeep(this.responseData)
          let keys = Object.keys(this.filterForm)
          keys.forEach((key) => {
            let formValue = this.filterForm[key]
            if (!!formValue) {
              responseData.purchaseDeliveryNoticeToDeliveryItemVOList = responseData.purchaseDeliveryNoticeToDeliveryItemVOList.filter((item) => {
                return !!item[key] && String(item[key]).includes(formValue)
              })
            }
          })
          this.responseDataForFilter = responseData
        } catch (error) {
        } finally {
          // this.businessShow = true
          this.confirmLoading = false
        }
      }, 100)
    },

    handleReset() {
      this.filterForm = {
        materialName: '', // 物料名称
        materialNumber: '', // 物料编码
        supplierName: '', // 供应商名称
        supplierCode: '' // 供应商编码
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.tableFilter {
  .tableFilterForm {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  .filterFormItem {
    display: inline-block;
  }
  .filterFormFooter {
    // float: right;
    // margin-top: 5px;
    flex: 1;
    text-align: right;
    margin-bottom: 24px;
    min-width: 185px;

    :deep(.ant-btn) {
      margin-left: 6px;
    }
  }
}
</style>
