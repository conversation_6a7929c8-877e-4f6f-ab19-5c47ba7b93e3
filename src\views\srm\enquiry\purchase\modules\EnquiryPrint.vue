<template>
  <div class="page-container">
    <a-spin :spinning="confirmLoading">
      <!-- 头部页面 -->
      <div class="page-header">
        <!-- 页面标题 -->
        <div class="breadcrumb">
          <span>{{ $srmI18n(`${$getLangAccount()}#i18n_field_husBfW_3aeb35f5`, '询价报表打印') }}</span>
        </div>
        <!-- 头部按钮 -->
        <div class="btnGroups">
          <a-button
            v-for="(btn, index) in btns"
            :type="btn.type"
            @click="btn.clickFn"
            :key="'pub_btn_' + index">
            {{ btn.title }}
          </a-button>
        </div>
      </div>
      <div
        class="page-main"
        ref="printPage">
        <div class="print-title">{{ $srmI18n(`${$getLangAccount()}#i18n_field_hutHB_d93177ee`, '询价记录表') }}</div>
        <!-- 表单 -->
        <div class="form">
          <a-descriptions
            size="small"
            :column="{ xxl: 4, xl: 3, lg: 3, md: 3, sm: 2, xs: 1 }"
            bordered>
            <a-descriptions-item 
              v-for="item in formData.custom.formFields"
              :key="item.fieldName"
              :label="item.fieldLabel">
              {{ form[item.fieldName] }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 表格 -->
        <div
          class="grid"
          v-for="grid in pageData.groups"
          :key="grid.groupCode">
          <vxe-grid
            :ref="grid.custom.ref"
            v-bind="defaultGridOption"
            :scroll-y="{enabled: false}"
            :columns="grid.custom.columns">
            <template #grid_opration="{ row, column }">
              <a
                v-for="(item, i) in grid.custom.optColumnList"
                :key="'opt_'+ row.id + '_' + i"
                :title="item.title"
                style="margin:0 4px"
                :disabled="item.allow ? item.allow(row) : false"
                v-show="item.showCondition ? item.showCondition(row) : true"
                @click="item.clickFn(row, column)">{{ item.title }}</a>
            </template>
          </vxe-grid>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
export default {
    name: 'EnquiryPrint',
    props: {
        currentEditRow: {
            type: Object,
            default: () =>{
                return {}
            }
        }
    },
    data (){
        return {
            confirmLoading: false,
            form: {},
            defaultGridOption: {
                border: true,
                resizable: true,
                autoResize: true,
                showOverflow: false,
                showHeaderOverflow: false,
                columnKey: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: []
            },
            // 表单
            formData: {
                groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_tvVH_298b46a0`, '基础信息'),
                groupCode: 'baseForm',
                custom: {
                    formFields: [
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryNumber`, '询价单号'),
                            fieldName: 'enquiryNumber'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_sender`, '发布人'),
                            fieldName: 'publishUser'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_submitTime`, '发布日期'),
                            fieldName: 'publishTime'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_company`, '公司代码'),
                            fieldName: 'company_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_nRVR_44661349`, '采购组织'),
                            fieldName: 'purchaseOrg_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryScope`, '询价范围'),
                            fieldName: 'enquiryScope_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_zECK_27774abd`, '分配方式'),
                            fieldName: 'quotaWay_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_yRIW_3cdd292b`, '节支率'),
                            fieldName: 'savingRate'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_publishNewSupplier`, '是否允许发布新供应商'),
                            fieldName: 'publishNewSupplier_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_openBidBefore`, '是否可提前开标'),
                            fieldName: 'openBidBefore_dictText'
                        },
                        {
                            fieldType: 'input',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_seePrice`, '报价截前是否可查看价格'),
                            fieldName: 'seePrice_dictText'
                        },
                        {
                            fieldType: 'switch',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierScope`, '供应商范围'),
                            fieldName: 'supplierScope_dictText'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXWR_8e14292c`, '供应商数量'),
                            fieldName: 'supplierCounts'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_budgetAmount`, '预算金额'),
                            fieldName: 'projectBudget'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_economyAmount`, '节支金额'),
                            fieldName: 'savingAmount'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_enquiryDesc`, '询价描述'),
                            fieldName: 'enquiryDesc'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRUzzEW_d49bd4e8`, '询价结果审批状态'),
                            fieldName: 'resultAuditStatus_dictText'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRII_473f7450`, '询价结果意见'),
                            fieldName: 'resultOpinion'
                        },
                        {
                            fieldType: 'select',
                            fieldLabel: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_huyRUzL_a09640c4`, '询价结果审批人'),
                            fieldName: 'resultOpinionPeople'
                        }
                    ]
                }

            },
            // 表格
            pageData: {
                groups: [
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierInfo`, '供应商信息'), groupCode: 'supplierInfo', type: 'grid', custom: {
                        ref: 'enquirySupplierListList',
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_supplierELSAccount`, '供应商ELS账号'), field: 'toElsAccount', width: 170 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHeade95_supplierName`, '供应商名称'), field: 'supplierName', width: 300 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_dict_RdXMeCK_5095b760`, '供应商协同方式'), field: 'needCoordination_dictText', width: 160 }
                        ]
                    } },
                    { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_RFQLineInformation`, '询价行信息'),  groupType: 'item', groupCode: 'itemInfo', type: 'grid', custom: {
                        ref: 'purchaseEnquiryItemList',
                        columns: [
                            { type: 'seq', width: 50, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号') },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_SLzAAo_fa81e5f0`, '物料分类编码'), field: 'cateCode', width: 160 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_materialClassificationName`, '物料分类名称'), field: 'cateName', width: 110 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_massProdHead9ab6_materialCode`, '物料编码'), field: 'materialNumber', width: 170 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_materialDesc`, '物料描述'), field: 'materialDesc', width: 127 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_purchaseUnit`, '采购单位'), field: 'purchaseUnit_dictText', width: 127 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_TVWR_46474721`, '需求数量'), field: 'requireQuantity', width: 75 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fIWWW_f03b2eb3`, '税率(%)'), field: 'taxRate', width: 65 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_currency`, '币别'), field: 'currency_dictText', width: 62 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RdXRL_8e11f650`, '供应商名称'), field: 'supplierName', width: 140 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RCSRH_a3092b47`, '公司或工厂'), field: 'factory_dictText', width: 190 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteRank`, '排名'), field: 'quoteRank', width: 50 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_lVsuWLftuW_667d4185`, '首轮报价（未税单价)'), field: 'firstQuotePrice', width: 88 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_CVsuWLftuW_f3b2eb90`, '末轮报价（未税单价)'), field: 'netPrice', width: 88 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quota`, '配额'), field: 'quotaQuantity', width: 62 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteEndTime`, '报价截止时间'), field: 'quoteEndTime', width: 102 },
                            { title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_quoteCount`, '报价次数'), field: 'quoteCount', width: 70 }
                        ]
                    } }
                    // { groupName: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVBI_24b50608`, '上传附件'), groupCode: 'fileInfo', type: 'grid', custom: {
                    //     ref: 'purchaseAttachmentExtendList',
                    //     columns: [
                    //         { type: 'checkbox', width: 40 },
                    //         { type: 'seq', width: 60, title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')},
                    //         { field: 'fileType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_fileType`, '文件类型'), width: 150 },
                    //         { field: 'stageType_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phaseType`, '阶段类型'), width: 150 },
                    //         { field: 'fileName', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_fileName`, '文件名称'), width: 200 },
                    //         { field: 'fbk2', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCRL_713698e6`, '上传方名称'), width: 200 },
                    //         { field: 'uploadElsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_XVCey_7137a94a`, '上传方帐号'), width: 160 },
                    //         { field: 'uploadTime', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_uploadTime`, '上传时间'), width: 200 },
                    //         { field: 'grid_opration', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_operation`, '操作'), width: 120, align: 'center', slots: { default: 'grid_opration' } }
                    //     ],
                    //     showOptColumn: true,
                    //     optColumnList: [
                    //         { type: 'download', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_colunmDownload`, '下载'), clickFn: this.handleDownload },
                    //         { type: 'preView', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_preview`, '预览'), clickFn: this.preViewEvent }
                    //     ]
                    // } }
                ]
            },
            btns: [
                {
                    type: 'primary', 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_Print`, '打印'), 
                    clickFn: this.goPrintpage
                },
                {
                    type: 'defult', 
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_back`, '返回'), 
                    clickFn: this.back
                }
            ],
            url: {
                download: '/attachment/saleAttachment/download'
            }
        }
    },
    methods: {
        back (){
            this.$emit('hide')
        },
        goPrintpage () {
            let url = this.$router.resolve({
                path: '/print/enquiryPrint',
                query: { id: this.currentEditRow.id }
            })
            window.open(url.href, '_blank')
        },
        preViewEvent (row){
            let preViewFile = row
            this.$previewFile.open({ params: preViewFile })
        },
        getData (){
            this.confirmLoading = true
            getAction('/enquiry/purchaseEnquiryHead/print', {id: this.currentEditRow.id}).then(res=>{
                if(res.success) {
                    this.form = res.result
                    this.$nextTick(()=>{
                        this.pageData.groups.forEach(group=>{
                            if(group.type == 'grid') {
                                let ref = group.custom.ref
                                this.$refs[ref][0].loadData(res.result[ref])
                            }
                        })  
                    })
                }
            }).finally(()=>{
                this.confirmLoading = false
            })
        },
        // 文件下载
        handleDownload ({ id, fileName }, url = '') {
            const params = {
                id
            }
            let downloadUrl = url 
            if(this.url.download){
                downloadUrl = this.url.download
            }
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        }
    },
    created (){
        this.getData()
    }
}
</script>

<style lang="less" scoped>
@primary-color: #1890ff;
.page-container {
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 14px;
        background: #fff;
        border-bottom: 1px solid #e8eaec;
        .btnGroups {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 10px;
                }
            }
        }
    }
    .page-main {
        position: relative;
    }
}


  :deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
    background-color: #f8feff;
  }
  :deep(.ant-descriptions-item-content) {
    width: 16.66%;
    max-width: 16.66%;
    background-color: #fff;
  }
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title),
  :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description) {
    color: @primary-color;
  }
  :deep(.rich-editor-display-box) p {
      margin-bottom: 0px;
  }

  :deep(.vTable-table .vxe-table .vxe-table--header .vxe-header--row  .vxe-header--column  .vxe-cell--title) {
	    white-space:pre-wrap;
	    word-wrap: break-word;
  }

</style>