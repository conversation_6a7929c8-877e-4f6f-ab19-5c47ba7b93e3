<template>
  <div>
    <titleTrtl>
      <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_evaluationExpert`, '评标专家') }}</span>
      <template
        slot="right"
        v-if="pageStatus == 'edit'">
        <a-button
          type="primary"
          style="margin-right: 10px"
          size="small"
          @click="gridAdd">{{ $srmI18n(`${$getLangAccount()}#i18n_field_SuUBsu_79fb198b`, '添加评标专家') }}</a-button>
        <a-button
          type="primary"
          size="small"
          @click="del">{{ $srmI18n(`${$getLangAccount()}#i18n_title_delete`, '删除') }}</a-button>
      </template>
    </titleTrtl>
    <listTable
      ref="listTable"
      :pageStatus="pageStatus"
      :fromSourceData="fromSourceData.evaluationExperts"
      :statictableColumns="statictableColumns"
      :editRulesProps="editRulesProps"
      :showTablePage="false" />
    <field-select-modal
      ref="fieldSelectModal"
      isEmit
      @ok="fieldSelectOk" />
  </div>
</template>

<script>
import { baseMixins } from '@views/srm/bidding_new/plugins/baseMixins'
import fieldSelectModal from '@comp/template/fieldSelectModal'
import listTable from '@views/srm/bidding_new/BiddingHall/components/listTable'
import titleTrtl from '@views/srm/bidding_new/BiddingHall/components/title-crtl.vue'
import REGEXP from '@/utils/regexp'

export default {
    mixins: [baseMixins],
    props: {
        pageStatus: {
            type: String,
            default: 'edit'
        },
        currentRow: {
            type: Object,
            default: () => {
                return {}
            }
        },
        fromSourceData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    computed: {
    },
    components: {
        fieldSelectModal,
        listTable,
        titleTrtl
    },
    watch: {
      
    },
    data () {
        return {
            statictableColumns: [
                {
                    type: 'checkbox',
                    width: 50
                },
                {
                    type: 'seq',
                    width: 50,
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_seq`, '序号')
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, '主账号'),
                    field: 'elsAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号'),
                    field: 'subAccount',
                    headerAlign: 'center',
                    required: '1',
                    width: 150
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'),
                    field: 'elsRealname',
                    headerAlign: 'center',
                    width: 150,
                    fieldType: 'input',
                    required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'),
                    field: 'phone',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateType`, '证件类型'),
                    field: 'certificateType',
                    headerAlign: 'center',
                    defaultValue: '',
                    dictCode: 'srmCertificateType',
                    width: 150,
                    fieldType: 'select',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_certificateNumber`, '证件号'),
                    field: 'certificateNumber',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input',
			        required: '1'
                },
                {
                    title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位'),
                    field: 'workUnit',
                    headerAlign: 'center',
                    defaultValue: '',
                    width: 150,
                    fieldType: 'input'
                }
            ],
            show: false,
            editRulesProps: {
                phone: [
                    { 
                        validator: ({ cellValue}) => {
                            if (cellValue) {
                                let reg = REGEXP.mobile
                                if (!reg.test(cellValue)) {
                                    return new Error(this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNiRyo_b9c72f7e`, '请输入正确号码'))
                                }
                            }
                        }
                    }
                ]
            }
        }
    },
    methods: {
        getValidate () {
            return this.$refs['listTable'].getValidate()
        },
        getTableData () {
            return this.$refs['listTable'].getTableData()
        },
        del () {
            this.$refs['listTable'].businessGridDelete()
        },
        gridAdd () {
            let url = 'specialist/specialistInfo/list'
            let columns = [
                { field: 'name', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '名称'), width: 70 },
                { field: 'mobileTelephone', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_ltyo_2e3c9979`, '手机号'), width: 120 },
                { field: 'workUnit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_workUnit`, '工作单位') },
                { field: 'elsAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n__WWWey_401851d`, 'ELS账号') },
                { field: 'subAccount', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_subAccount`, '子账号') },
                { field: 'specialistClasses_dictText', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_suAq_24e54053`, '专家类别'), width: 180 }
            ]
            this.$refs.fieldSelectModal.open(url, { headId: this.fromSourceData.id }, columns, 'multiple')
        },
        fieldSelectOk (data) {
            let userList = data.map((item) => {
                delete item.id
                return {
                    ...item,
                    memberType: '1',
                    elsAccount: item.elsAccount,
                    elsSubAccount: item.subAccount,
                    elsRealname: item.name,
                    certificateType: item.certificateType,
                    phone: item.mobileTelephone
                }
            })
            console.log()
            this.$refs.listTable.insertAt(userList, -1)
        }
    },
    created () {
    }
}
</script>
