import {
    getNextNode
} from '../../api/analy'
import { ajaxFindDictItems } from '@/api/api'
import fieldSelectModal from '../fieldSelectModal'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
export default {
    props: {
        id: [String, Number],
        task: {
            type: Object,
            default: () => {
                return {}
            }
        },
        taskInfo: {
            type: Object,
            default: () => {
                return {}
            }
        },
        taskId: {
            type: [String, Number],
            default: ''
        },
        processInstanceId: {
            type: String,
            default: ''
        },
        resultData: {
            type: Object,
            default: () => {
                return {}
            }
        },
        currentEditRow: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    components: {
        fieldSelectModal
    },
    data () {
        return {
            labelCol: { span: 6 },
            wrapperCol: { span: 14 },
            form: {},
            formName: 'form',
            rules: {},
            existData: null,
            nextNode: {},
            agreeAdHocSubProcess: {},
            usersInfo: [],
            scopeType: null,
            loading: false,
            priorityMap: [
                // { lable: '低', value: '0' },
                // { lable: '中', value: '50' },
                // { lable: '高', value: '70' },
                // { lable: '紧急', value: '100' }
            ],
            signType: [
                { lable: '10', value: '10%' },
                { lable: '20', value: '20%' },
                { lable: '30', value: '30%' },
                { lable: '40', value: '40%' },
                { lable: '50', value: '50%' },
                { lable: '60', value: '60%' },
                { lable: '70', value: '70%' },
                { lable: '80', value: '80%' },
                { lable: '90', value: '90%' },
                { lable: '100', value: '100%' }
            ]
        }
    },
    methods: {
        async getData () {
            let data = await this.cheackValidate().then(res => {
                if (res) {
                    return this.form
                } else {
                    console.log('err: 校验失败')
                    return {}
                }
            })
            return data
        },
        cheackValidate () {
            return new Promise((resolve, reject) => {
                this.$refs[this.formName].validate(valid => {
                    if (valid) {
                        resolve(true)
                    } else {
                        console.log('error submit!!')
                        this.$emit('closeLoading')
                        reject(false)
                    }
                })
            })
        },
        clearValidate () {
            this.$refs[this.formName].clearValidate()
        },
        getNextNode () {
            return getNextNode(this.taskInfo.taskId).then((response) => {
                if (response.code == 0) {
                    let resData = response.data
                    resData.map(item => {
                        item['extObjDemo'] = item.extObj || []
                    })
                    this.nextNode = resData
                    this.form.nextNode = resData
                    this.existData = []
                    this.existData = this.form.nextNode
                    this.agreeAdHocSubProcess.nextNode = resData
                    let nextNodeName = []
                    let nextNodeNameOptions = []
                    for (let i = 0; i < resData.length; i++) {
                        let d = resData[i]
                        nextNodeName.push(d.taskName)
                        nextNodeNameOptions.push(d.taskName)
                    }
                    this.agreeAdHocSubProcess.nextNodeName = nextNodeName
                    this.agreeAdHocSubProcess.nextNodeNameOptions = nextNodeNameOptions
                    if (this.nextNode.length == 0) {
                        this.nextNode.push({
                            taskId: '',
                            taskName: '等待其他任务汇总',
                            nodeType: 'userTask'
                        })
                    }
                } else {
                    this.$message.error(response.message)
                    // 关闭弹窗
                    this.$emit('success')
                }
            })
        },
        handleConfirm () {
            this.getTask()
        },
        async initData () {
            this.loading = true
            if (['agree', 'pass', 'unPass', 'agreeAdHocSubProcess'].includes(this.task.action)) {
                await this.getNextNode()
            }
            this.loading = false
        },
        showUserSelectModal (p) {
            let { selectModel = 'multiple' } = p
            let item = {
                selectModel,
                sourceUrl: '/uc/api/user/page/listAll',
                params: {},
                columns: [
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_name`, '姓名'), field: 'fullName'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_account`, '账户'), field: 'account'
                    },
                    {
                        title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_phone`, '手机号'), field: 'photo'
                    }
                ]
            }
            let resInit = res => {
                let { aaData, total } = res.page
                let params = {
                    success: res.success,
                    result: {
                        records: aaData,
                        total: Number(total)
                    }
                }
                return params
            }
            let pageInit = res => {
                let { pageSize, pageNo, ...other } = res
                let params = {
                    page: pageNo,
                    length: pageSize,
                    ...other
                }
                return params
            }
            let reset = {
                pageInit,
                resInit,
                keyWordCode: 'fullName',
                actionType: 'get'
            }
            // this.$refs.editPage.currentSelectModal.selectCallBack = this.fieldSelectOk
            this.$refs.fieldSelectModal.open(item.sourceUrl, item.params, item.columns, item.selectModel, {}, reset)
        },
        getDictData (dictCode, mapCode) {
            if (dictCode) {
                //根据字典Code, 初始化字典数组
                let postData = {
                    busAccount: this.busAccount || this.$ls.get(USER_ELS_ACCOUNT),
                    dictCode: dictCode
                }
                ajaxFindDictItems(postData).then((res) => {
                    if (res.success) {
                        this[mapCode] = res.result
                    }
                })
            }
        }
    },
    created () {
        this.form.taskId = this.taskId
        this.$set(this.form, 'taskTitle', this.task.taskTitle)
        this.initData()
    }
}
