<template>
  <div style="height: 100%">
    <list-layout
      ref="listPage"
      v-show="!showEditPage && !showDetailPage"
      :pageData="pageData"
      :current-edit-row="currentEditRow"
      :url="url"
      refresh
      :tabsList="tabsList"
      @afterChangeTab="handleAfterChangeTab" />
    <project-building-edit
      v-if="showEditPage"
      ref="supplierCapacity"
      :current-edit-row="currentEditRow"
      @hide="hideEditPageNew"
    />
    <project-building-detail
      v-if="showDetailPage"
      ref="detailPage"
      :current-edit-row="currentEditRow"
      @hide="hideEditPage"
    />
  </div>
</template>
<script>
import fieldSelectModal from '@comp/template/fieldSelectModal'
import { ListMixin } from '@comp/template/list/ListMixin'
import ProjectBuildingEdit from './modules/ProjectBuildingEdit'
import ProjectBuildingDetail from './modules/ProjectBuildingDetail'
import { USER_ELS_ACCOUNT} from '@/store/mutation-types'
import { postAction, getAction } from '@/api/manage'
export default {
    mixins: [ListMixin],
    components: {
        fieldSelectModal,
        ProjectBuildingEdit,
        ProjectBuildingDetail
    },
    data () {
        return {
            ready: false,
            showEditPage: false,
            pageData: {
                businessType: 'projectApproval',
                form: {
                },
                formField: [
                    {
                        type: 'input',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_keyword`, '关键字'),
                        fieldName: 'keyWord',
                        placeholder: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_VWNIrRLWdIRLSdIAy_1659e63d`, '请输入模板名称、项目名称或项目编号')
                    },
                    {
                        type: 'select',
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_PurchaseMassProdHeadList_documentsStatus`, '单据状态'),
                        fieldName: 'sourceStatus',
                        dictCode: 'approvalSourceStatus'
                    }
                ],
                
                button: [
                    {
                        allow: ()=> {
                        },
                        label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_add`, '新增'),
                        authorityCode: 'tender#tenderProjectApprovalHead:add',
                        key: 'add',
                        icon: 'plus',
                        clickFn: this.handleAdd,
                        type: 'primary'
                    },
                    {label: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_listCustom`, '列自定义'), icon: 'setting', clickFn: this.settingColumns}
                ],
                optColumnList: [
                    { type: 'view', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_see`, '查看'), clickFn: this.handleView, authorityCode: 'tender#tenderProjectApprovalHead:queryById'},
                    { type: 'edit', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_edit`, '编辑'), clickFn: this.handleEdit, allow: this.allowEdit, key: 'edit', authorityCode: 'tender#tenderProjectApprovalHead:edit'},
                    // {type: 'release', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_release`, '发布'), clickFn: this.handleRelease, allow: this.allowReleaseBtn, authorityCode: 'tender#tenderProjectApprovalHead:publish'},
                    { type: 'delete', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_delete`, '删除'), clickFn: this.handleDelete, allow: this.allowDel, key: 'delete', authorityCode: 'tender#tenderProjectApprovalHead:delete'},
                    {type: 'cancel', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_void`, '作废'), clickFn: this.handleCancel, allow: this.showCancelCondition, authorityCode: 'tender#tenderProjectApprovalHead:cancel'},
                    {type: 'backToPool', title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_MLTVu_f5b69eaa`, '回归需求池'), clickFn: this.backToPool, allow: this.showBTPoolCondition, authorityCode: 'tender#tenderProjectApprovalHead:goBackDemandPool'}
                ],
                optColumnWidth: 270,
                tabsList: []
            }, 
            url: {
                // 列表数据展示
                list: '/tender/tenderProjectApprovalHead/list',
                columns: 'purchaseTenderProjectApprovalList',
                add: '/tender/tenderProjectApprovalHead/add',
                edit: '/tender/tenderProjectApprovalHead/edit',
                delete: '/tender/tenderProjectApprovalHead/delete',
                // 作废sourceStatus为3；
                cancel: '/tender/tenderProjectApprovalHead/abandonStatus',
                // 发布sourceStatus为2；
                release: '/tender/tenderProjectApprovalHead/publishStatus',
                backToPool: '/tender/tenderProjectApprovalHead/goBackDemandPool'
            }
        }
    },
    beforeRouteEnter (to, from, next) {
        if (Object.keys(to.query).length) {
            next(vm => vm.handleDemandPool())
        } else {
            next()
        }
    },
    methods: {
        // 返回按钮
        hideEditPageNew () {
            let query = this.$route.query || {}
            if (query.linkFilter || query.open) { 
                // 关闭超链接页面
                this.closeCurrent()
            }
            if (query.source == 'demand-pool') {
                this.$router.replace({path: this.$route.path})
            }
            this.showEditPage = false
            this.showDetailPage = false
            this.$store.dispatch('SetTabConfirm', false)
            this.searchEvent(false)
            // 更新list页面tab count
            this.refreshCountTabs()
        },
        handleDemandPool () {
            this.showEditPage = false
            const query = this.$route.query
            if (query.source == 'demand-pool') { // 需求调整过来
                if (query?.result?.length == 1) { // 只有一条
                    this.currentEditRow = query.result[0]
                    this.showEditPage = true
                }
            }
        },
        handleCancel (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_confirmVoid`, '确认作废'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_title_voidSelectedData`, '是否作废选中数据?'),
                onOk: () =>  {
                    this.loading = true
                    //postAction, 
                    postAction(this.url.cancel, {id: row.id, sourceStatus: 3, sourceStatus_dictText: row.sourceStatus_dictText}).then(res => {
                        if(res.success) {
                            this.$message.success(res.message)
                            this.searchEvent()
                        }else {
                            this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        backToPool (row) {
            this.$confirm({
                title: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_RLMLTVu_e5e3a774`, '确认回归需求池'),
                content: this.$srmI18n(`${this.$getLangAccount()}#i18n_field_KQRLMLTVu_69b43422`, '是否确认回归需求池?'),
                onOk: () =>  {
                    this.loading = true
                    //postAction, 
                    postAction(this.url.backToPool, {id: row.id, sourceStatus: 3, sourceStatus_dictText: row.sourceStatus_dictText}).then(res => {
                        if(res.success) {
                            this.$message.success(res.message)
                            this.searchEvent()
                        }else {
                            this.$message.warning(res.message)
                        }
                    })
                }
            })
        },
        showBTPoolCondition (row) {
            if (row.sourceStatus == '3' || row.auditStatus == '1' || row.sourceType != 'request'){ //废弃、审批中、sourceType不为request不可回退需求池
                return true
            }
            return false
        },
        showCancelCondition (row) {
            if ('2' == row.sourceStatus) { //已发布
                return false
            }else {
                return true
            }
        },
        allowDel (row){
            if (row.sourceType == 'request') return true
            if (row.sourceStatus != '0'){   //新建
                return true
            }
            return false
        },
        allowEdit (row){
            if (row.sourceStatus != '0' && row.auditStatus !='3'){ //新建
                return true
            }
            return false
        },
        handleDeleteSingle (row) {
            this.handleDelete(row)
        }
        
    },
    mounted () {
        // this.serachTabs('srmReconciliationStatus', 'reconciliationStatus')
        this.serachCountTabs('/tender/tenderProjectApprovalHead/counts')
    }
    
}
</script>