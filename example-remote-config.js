// 示例：远程配置文件中启用根据数据条数渲染高度
function getPageConfig() {
    return {
        groups: [
            {
                groupName: '联系人信息',
                groupCode: 'supplierContactsInfoList',
                groupType: 'item',
                sortOrder: '1',
                show: true,
                extend: {
                    // 启用根据数据条数自动设置高度
                    autoHeightByData: true,
                    
                    // 高度计算参数（可选）
                    rowHeight: 36,        // 每行高度，默认36px
                    headerHeight: 50,     // 表头高度，默认50px
                    maxHeight: 600,       // 最大高度限制，默认600px
                    minHeight: 150,       // 最小高度限制，默认100px
                    
                    // 其他配置...
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    }
                },
                columns: [
                    {
                        type: 'seq',
                        width: 50,
                        title: '序号'
                    },
                    {
                        field: 'elsAccount',
                        title: 'ELS账号',
                        width: 150
                    },
                    {
                        field: 'contactName',
                        title: '联系人姓名',
                        width: 120
                    },
                    {
                        field: 'phone',
                        title: '联系电话',
                        width: 120
                    },
                    {
                        field: 'email',
                        title: '邮箱',
                        width: 150
                    }
                ]
            }
        ]
    }
}
