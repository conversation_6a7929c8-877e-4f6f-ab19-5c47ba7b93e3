// 示例：供应商信息变更详情 - 联系人信息页签配置
// 文件路径：{account}/purchase_supplierMasterData_{templateNumber}_{templateVersion}.js

function getPageConfig() {
    return {
        groups: [
            {
                groupName: '联系人信息',
                groupCode: 'supplierContactsInfoList',
                groupType: 'item',
                sortOrder: '1',
                show: true,
                extend: {
                    // ===== 启用根据数据条数自动设置高度 =====
                    autoHeightByData: true,

                    // ===== 高度计算参数（可选，有默认值）=====
                    rowHeight: 36,        // 每行高度，默认36px
                    headerHeight: 50,     // 表头高度，默认50px
                    paddingHeight: 20,    // 内边距，默认20px
                    maxHeight: 600,       // 最大高度限制，默认600px
                    minHeight: 150,       // 最小高度限制，默认150px

                    // ===== 其他现有配置保持不变 =====
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    }
                },
                columns: [
                    {
                        type: 'seq',
                        width: 50,
                        title: '序号'
                    },
                    {
                        field: 'elsAccount',
                        title: 'ELS账号',
                        width: 150
                    },
                    {
                        field: 'contactName',
                        title: '联系人姓名',
                        width: 120
                    },
                    {
                        field: 'phone',
                        title: '联系电话',
                        width: 120
                    },
                    {
                        field: 'email',
                        title: '邮箱',
                        width: 150
                    },
                    {
                        field: 'updateType_dictText',
                        title: '修改类型',
                        width: 150,
                        align: 'center'
                    }
                ]
            }
        ]
    }
}

// ===== 使用场景示例 =====

// 场景1：少量数据（1-3条）
// 计算高度 = 3 × 36 + 50 + 20 = 178px
// 最终高度 = max(150, min(178, 600)) = 178px

// 场景2：中等数据（10条）
// 计算高度 = 10 × 36 + 50 + 20 = 430px
// 最终高度 = max(150, min(430, 600)) = 430px

// 场景3：大量数据（20条）
// 计算高度 = 20 × 36 + 50 + 20 = 790px
// 最终高度 = max(150, min(790, 600)) = 600px（受最大高度限制）

// 场景4：无数据
// 最终高度 = 150px（最小高度）
