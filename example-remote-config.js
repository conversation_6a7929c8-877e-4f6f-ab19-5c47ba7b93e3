/**
 * 供应商信息变更详情 - 联系人信息页签自动高度配置示例
 *
 * 文件路径：{account}/purchase_supplierMasterData_{templateNumber}_{templateVersion}.js
 *
 * 功能说明：
 * - 启用 autoHeightByData 后，表格高度将根据数据条数自动计算
 * - 计算公式：数据条数 × 行高 + 表头高度 + 内边距
 * - 支持最大最小高度限制，避免极端情况
 *
 * 更新时间：2025-06-23
 * 作者：Augment Agent
 */

function getPageConfig() {
    return {
        groups: [
            {
                groupName: '联系人信息',
                groupCode: 'supplierContactsInfoList',
                groupType: 'item',
                sortOrder: '1',
                show: true,
                extend: {
                    // ===== 🎯 启用根据数据条数自动设置高度 =====
                    autoHeightByData: true,

                    // ===== ⚙️ 高度计算参数（可选配置，都有合理默认值）=====
                    rowHeight: 36,        // 每行数据高度（20-100px，默认36px）
                    headerHeight: 50,     // 表头高度（30-100px，默认50px）
                    paddingHeight: 20,    // 内边距高度（默认20px）
                    footerHeight: 0,      // 页脚高度（默认0px）
                    maxHeight: 600,       // 最大高度限制（最小200px，默认600px）
                    minHeight: 150,       // 最小高度限制（100-maxHeight，默认150px）

                    // ===== 📋 其他现有配置保持不变 =====
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    },

                    // 操作列配置示例
                    optColumnList: [
                        {
                            title: '查看',
                            click: function(vm, row, col, tplRootRef) {
                                console.log('查看联系人:', row)
                            }
                        }
                    ]
                },
                columns: [
                    {
                        type: 'seq',
                        width: 50,
                        title: '序号',
                        align: 'center'
                    },
                    {
                        field: 'elsAccount',
                        title: 'ELS账号',
                        width: 150,
                        align: 'left'
                    },
                    {
                        field: 'contactName',
                        title: '联系人姓名',
                        width: 120,
                        align: 'left'
                    },
                    {
                        field: 'phone',
                        title: '联系电话',
                        width: 120,
                        align: 'center'
                    },
                    {
                        field: 'email',
                        title: '邮箱',
                        width: 180,
                        align: 'left'
                    },
                    {
                        field: 'updateType_dictText',
                        title: '修改类型',
                        width: 120,
                        align: 'center'
                    },
                    {
                        field: 'operation',
                        title: '操作',
                        width: 100,
                        align: 'center',
                        slots: { default: 'grid_opration' }
                    }
                ]
            }
        ]
    }
}

// ===== 📊 高度计算示例 =====

/**
 * 计算公式：
 * 最终高度 = Math.max(minHeight, Math.min(计算高度, maxHeight))
 * 计算高度 = 数据条数 × rowHeight + headerHeight + footerHeight + paddingHeight
 */

// 🔹 场景1：少量数据（3条记录）
// 计算高度 = 3 × 36 + 50 + 0 + 20 = 178px
// 最终高度 = max(150, min(178, 600)) = 178px
// 效果：紧凑显示，无多余空白

// 🔹 场景2：中等数据（10条记录）
// 计算高度 = 10 × 36 + 50 + 0 + 20 = 430px
// 最终高度 = max(150, min(430, 600)) = 430px
// 效果：适中高度，良好的视觉体验

// 🔹 场景3：大量数据（20条记录）
// 计算高度 = 20 × 36 + 50 + 0 + 20 = 790px
// 最终高度 = max(150, min(790, 600)) = 600px（受最大高度限制）
// 效果：达到最大高度，表格内部出现滚动条

// 🔹 场景4：无数据（0条记录）
// 最终高度 = 150px（直接使用最小高度）
// 效果：显示"暂无数据"，保持最小可视区域

// ===== 🎛️ 不同配置方案对比 =====

// 方案A：紧凑型（适合数据较少的场景）
const compactConfig = {
    autoHeightByData: true,
    rowHeight: 32,
    headerHeight: 40,
    paddingHeight: 15,
    maxHeight: 400,
    minHeight: 120
}

// 方案B：标准型（推荐的默认配置）
const standardConfig = {
    autoHeightByData: true,
    rowHeight: 36,
    headerHeight: 50,
    paddingHeight: 20,
    maxHeight: 600,
    minHeight: 150
}

// 方案C：宽松型（适合数据较多的场景）
const spaciousConfig = {
    autoHeightByData: true,
    rowHeight: 40,
    headerHeight: 60,
    paddingHeight: 25,
    maxHeight: 800,
    minHeight: 200
}

// ===== 🚀 快速启用指南 =====

/**
 * 1. 找到对应的远程配置文件
 *    路径：{account}/purchase_supplierMasterData_{templateNumber}_{templateVersion}.js
 *
 * 2. 在目标页签的 extend 配置中添加：
 *    extend: {
 *        autoHeightByData: true  // 仅此一行即可启用！
 *    }
 *
 * 3. 可选：根据需要调整高度参数
 *    extend: {
 *        autoHeightByData: true,
 *        maxHeight: 500,         // 自定义最大高度
 *        minHeight: 120          // 自定义最小高度
 *    }
 *
 * 4. 保存配置文件，刷新页面即可看到效果
 */

// ===== 🔧 调试和验证 =====

/**
 * 开发环境下，启用自动高度的表格会在控制台输出调试信息：
 *
 * [supplierContactsInfoList] 自动高度计算: {
 *   dataLength: 5,
 *   calculatedHeight: 250,
 *   config: { rowHeight: 36, headerHeight: 50, ... },
 *   formula: "5 × 36 + 50 + 0 + 20 = 250px"
 * }
 *
 * 通过这些信息可以验证高度计算是否符合预期
 */
