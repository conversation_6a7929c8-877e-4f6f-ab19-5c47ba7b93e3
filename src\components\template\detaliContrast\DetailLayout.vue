<template>
  <div class="detail-page">
    <a-spin :spinning="confirmLoading">
      <div class="container">
        <div class="top">
          <div class="breadcrumb">
            <span>{{ currentPageName }}{{ $srmI18n(`${$getLangAccount()}#i18n_title_details`, '详情') }}</span>
            <!-- <breadcrumb></breadcrumb> -->
          </div>
          <div class="btnGroups">
            <template v-if="taskInfo.taskId">
              <taskBtn
                :currentEditRow="currentEditRow"
                :pageHeaderButtons="pageData.publicBtn"/>
            </template>
            <template v-else>
              <a-button
                v-for="(btn,index) in pageData.publicBtn"
                :key="'pub_btn_' + index"
                :type="btn.type"
                v-show="btn.showCondition ? btn.showCondition() : true"
                @click="btn.click"
              >
                {{ btn.title }}
              </a-button>
            </template>
          </div>
        </div>
        <div
          class="sub-top"
          v-if="elsStatusLog && elsStatusLog.list && elsStatusLog.list.length">
          <div class="step-status">
            <a-steps :current="elsStatusLog.currentStatusIndex">
              <a-popover
                slot="progressDot"
                slot-scope="{ status, prefixCls }">
                <template slot="content">
                  <span v-if="status==='wait'">{{ $srmI18n(`${busAccount}#i18n_field_EoGv_397ca1de`, '等待处理') }}</span>
                  <span v-if="status==='process'">{{ $srmI18n(`${busAccount}#i18n_field_Gvs_15c82eb`, '处理中') }}</span>
                  <span v-if="status==='finish'">{{ $srmI18n(`${busAccount}#i18n_field_IML_16c2176`, '已完成') }}</span>
                </template>
                <span :class="`${prefixCls}-icon-dot`" />
              </a-popover>
              <a-step
                v-for="(value, index ) in elsStatusLog.list"
                :key="index"
                :title="value.businessStatus_dictText"
                :description="value.statusDate"/>
            </a-steps>
          </div>
        </div>
        <div class="content">
          <template
            v-if="displayModel==='masterSlave'">
            <div class="tabs">
              <a-tabs v-model="activeKey">
                <a-tab-pane
                  v-for="tab in pageData.groups"
                  :key="tab.groupCode"
                  forceRender
                  :tab="$srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName)">
                  <span slot="tab">
                    <a-badge :dot="tab.showDadgeDot">
                      {{ $srmI18n(`${busAccount}#${tab.groupNameI18nKey}`, tab.groupName) }}
                    </a-badge>
                  </span>
                  <div
                    v-if="tab.type && tab.type === 'grid'"
                    class="table"
                    :style="{ minHeight: `${minHeight}px` }">
                    <vxe-grid
                      :ref="tab.custom.ref"
                      v-bind="defaultGridOption"
                      header-align="center"
                      :columns="resetCustomCols(tab.custom.columns)"
                      :row-config="{isCurrent: true, isHover: true}">
                      <template #toolbar_buttons>
                        <span
                          v-for="(btn, i) in tab.custom.buttons"
                          class="tools-btn"
                          :key="'btn_' + i">
                          <a-button
                            v-if="btn.type != 'upload' && btn.type !== 'import' && btn.type !== 'check'"
                            :type="btn.type"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="btn.click">
                            {{ btn.title }}
                          </a-button>
                          <a-button
                            v-else-if="btn.type == 'check'"
                            :type="btn.type"
                            v-show="btn.showCondition ? btn.showCondition() : true"
                            @click="checkedGridSelect(btn, tab.custom.ref, btn.beforeCheckedCallBack)">
                            <!--                              @click="btn.beforeChecked ?  : btn.click"-->
                            {{ btn.title }}
                          </a-button>
                        </span>
                      </template>
                      <template #grid_opration="{ row, column }">
                        <a
                          v-for="(item, i) in tab.custom.optColumnList"
                          :key="'opt_'+ row.id + '_' + i"
                          :title="item.title"
                          style="margin:0 4px"
                          :disabled="item.allow ? item.allow(row) : false"
                          v-show="item.showCondition ? item.showCondition(row) : true"
                          @click="item.clickFn(row, column)">{{ item.title }}</a>
                      </template>
                    </vxe-grid>
                  </div>
                  <div
                    v-else
                    bordered
                    class="description"
                    :style="{ minHeight: `${minHeight}px` }">
                    <template v-if="tab.custom.formFields && tab.custom.formFields.length">
                      <a-descriptions
                        bordered
                        size="small">
                        <a-descriptions-item
                          v-for="field in tab.custom.formFields"
                          :key="field.fieldName">
                          <span slot="label">
                            {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
                            <a-tooltip
                              v-if="field.helpText"
                              :title="field.helpText">
                              <a-icon type="question-circle-o" />
                            </a-tooltip>
                          </span>
                          <template>
                            <span v-if="!tab.custom.parentObj">
                              <span v-if="field.fieldType == 'switch'">
                                {{
                                  ['1', 'Y'].includes(form[field.fieldName])
                                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                }}
                                <span v-if="form[field.fieldName + '_new']">
                                  ➔
                                  <span style="color: #1890ff">
                                    {{
                                      ['1', 'Y'].includes(form[field.fieldName + '_new'])
                                        ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                        : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                    }}
                                  </span>
                                </span>
                              </span>
                              <a
                                :href="form[field.fieldName]"
                                target="_blank"
                                v-else-if="field.fieldType == 'link'">
                                <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_authenticationLink`, '认证链接') }}</span>
                              </a>
                              <div v-else-if="field.fieldType == 'image'">
                                <span
                                  class="preview"
                                  @click.stop="previewPicture(form[field.fieldName])">{{ form[field.fieldName]?'点击查看图片':'' }}</span>
                              </div>
                              <!-- 阶梯价格 -->
                              <template v-else-if="field.fieldName === 'ladderPriceJson'">
                                <a-tooltip
                                  placement="top"
                                  v-if="form['ladderPriceJson']"
                                  overlayClassName="tip-overlay-class">
                                  <template slot="title">
                                    <vxe-table
                                      auto-resize
                                      border
                                      size="mini"
                                      :data="initRowLadderJson(form['ladderPriceJson'])">
                                      <vxe-table-column
                                        type="seq"
                                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details`, '序号')}`"
                                        width="80"></vxe-table-column>
                                      <vxe-table-column
                                        field="ladderQuantity"
                                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`"
                                        width="140"></vxe-table-column>
                                      <vxe-table-column
                                        field="price"
                                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                                        width="140"></vxe-table-column>
                                      <vxe-table-column
                                        field="netPrice"
                                        :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                                        width="140"></vxe-table-column>
                                    </vxe-table>
                                  </template>
                                  <div class="json-box"><a href="javascript: void(0)">{{ defaultRowLadderJson(form['ladderPriceJson']) }}</a></div>
                                </a-tooltip>
                              </template>
                              <template v-else-if="field.fieldName === 'frozenFunction' && field.fieldType === 'customSelectModal'">
                                <span
                                  @click="$emit('customSelect', form)"
                                  style="color: blue; cursor:pointer;">{{ `${$srmI18n(`${$getLangAccount()}#i18n_field_mARH_31060cbe`, '查看明细')}` }}</span>
                              </template>
                              <span v-else>
                                <template v-if="field.fieldName === 'clarificationMatter'">
                                  <Popover
                                    :content="form[field.fieldName]"
                                    class="rich-editor-display-box p" />
                                </template>
                                <template v-else>
                                  {{ field.fieldType == 'select' || field.fieldType == 'multiple' ? form[field.fieldName + '_dictText'] : form[field.fieldName] }}
                                  <span v-if="form[field.fieldName + '_new']">
                                    ➔ <span style="color: #1890ff;">{{ field.fieldType == 'select' || field.fieldType == 'multiple' ? form[field.fieldName + '_new_dictText'] : form[field.fieldName + '_new'] }}</span>
                                  </span>
                                </template>
                              </span>
                            </span>
                            <span v-else-if="tab.custom.parentObj && form[tab.custom.parentObj]">
                              <span v-if="field.fieldType == 'switch'">
                                {{
                                  ['1', 'Y'].includes(form[panel.custom.parentObj][field.fieldName])
                                    ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                    : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                }}
                                <span v-if="form[panel.custom.parentObj][field.fieldName + '_new']">
                                  ➔
                                  <span style="color: #1890ff">
                                    {{
                                      ['1', 'Y'].includes(form[panel.custom.parentObj][field.fieldName + '_new'])
                                        ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                        : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                    }}
                                  </span>
                                </span>
                              </span>
                              <a
                                :href="form[field.fieldName]"
                                target="_blank"
                                v-else-if="field.fieldType == 'link'">
                                <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_authenticationLink`, '认证链接') }}</span>
                              </a>
                              <!-- <div v-else-if="field.fieldType == 'image'">1111</div> -->
                              <span v-else>
                                {{ field.fieldType == 'select' || field.fieldType == 'multiple' ? form[panel.custom.parentObj][field.fieldName + '_dictText'] : form[panel.custom.parentObj][field.fieldName] }}
                              </span>
                            </span>
                          </template>
                        </a-descriptions-item>
                      </a-descriptions>
                    </template>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </template>
          <template v-else-if="displayModel==='collapse'">
            <a-collapse v-model="activeKey">
              <a-collapse-panel
                forceRender
                v-for="tab in pageData.groups"
                :key="tab.groupCode"
                :header="tab.groupName">
                <div
                  v-if="tab.type && tab.type === 'grid'"
                  class="table"
                  :style="{ minHeight: `${minHeight}px` }">
                  <vxe-grid
                    :ref="tab.custom.ref"
                    v-bind="defaultGridOption"
                    header-align="center"
                    :columns="resetCustomCols(tab.custom.columns)">
                    <template #toolbar_buttons>
                      <span
                        v-for="(btn, i) in tab.custom.buttons"
                        class="tools-btn"
                        :key="'btn_' + i">
                        <a-button
                          v-if="btn.type != 'upload' && btn.type !== 'import' && btn.type !== 'check'"
                          :type="btn.type"
                          v-show="btn.showCondition ? btn.showCondition() : true"
                          @click="btn.click">
                          {{ btn.title }}
                        </a-button>
                        <a-button
                          v-else-if="btn.type == 'check'"
                          :type="btn.type"
                          v-show="btn.showCondition ? btn.showCondition() : true"
                          @click="checkedGridSelect(btn, tab.custom.ref, btn.beforeCheckedCallBack)">
                          {{ btn.title }}
                        </a-button>
                      </span>
                    </template>
                    <template #grid_opration="{ row, column }">
                      <a
                        v-for="(item, i) in tab.custom.optColumnList"
                        :key="'opt_'+ row.id + '_' + i"
                        :title="item.title"
                        style="margin:0 4px"
                        :disabled="item.allow ? item.allow(row) : false"
                        v-show="item.showCondition ? item.showCondition(row) : true"
                        @click="item.clickFn(row, column)">{{ item.title }}</a>
                    </template>
                  </vxe-grid>
                </div>
                <div
                  v-else
                  bordered
                  class="description"
                  :style="{ minHeight: `${minHeight}px` }">
                  <template v-if="tab.custom.formFields && tab.custom.formFields.length">
                    <a-descriptions
                      bordered
                      size="small">
                      <a-descriptions-item
                        v-for="field in tab.custom.formFields"
                        :key="field.fieldName">
                        <span slot="label">
                          {{ $srmI18n(`${busAccount}#${field.fieldLabelI18nKey}`, field.fieldLabel) }}
                          <a-tooltip
                            v-if="field.helpText"
                            :title="field.helpText">
                            <a-icon type="question-circle-o" />
                          </a-tooltip>
                        </span>
                        <template>
                          <span v-if="!tab.custom.parentObj">
                            <span v-if="field.fieldType == 'switch'">
                              {{
                                ['1', 'Y'].includes(form[field.fieldName])
                                  ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                  : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                              }}
                              <span v-if="form[field.fieldName + '_new']">
                                ➔
                                <span style="color: #1890ff">
                                  {{
                                    ['1', 'Y'].includes(form[field.fieldName + '_new'])
                                      ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                      : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                  }}
                                </span>
                              </span>
                            </span>
                            <a
                              :href="form[field.fieldName]"
                              target="_blank"
                              v-else-if="field.fieldType == 'link'">
                              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_authenticationLink`, '认证链接') }}</span>
                            </a>
                            <div v-else-if="field.fieldType == 'image'">
                              <span
                                class="preview"
                                @click.stop="previewPicture(form[field.fieldName])">{{ form[field.fieldName]?'点击查看图片':'' }}</span>
                            </div>
                            <!-- 阶梯价格 -->
                            <template v-else-if="field.fieldName === 'ladderPriceJson'">
                              <a-tooltip
                                placement="top"
                                v-if="form['ladderPriceJson']"
                                overlayClassName="tip-overlay-class">
                                <template slot="title">
                                  <vxe-table
                                    auto-resize
                                    border
                                    size="mini"
                                    :data="initRowLadderJson(form['ladderPriceJson'])">
                                    <vxe-table-column
                                      type="seq"
                                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_details`, '序号')}`"
                                      width="80"></vxe-table-column>
                                    <vxe-table-column
                                      field="ladderQuantity"
                                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_ladderQuantity`, '阶梯数量')}`"
                                      width="140"></vxe-table-column>
                                    <vxe-table-column
                                      field="price"
                                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_price`, '含税价')}`"
                                      width="140"></vxe-table-column>
                                    <vxe-table-column
                                      field="netPrice"
                                      :title="`${$srmI18n(`${$getLangAccount()}#i18n_title_netPrice`, '不含税价')}`"
                                      width="140"></vxe-table-column>
                                  </vxe-table>
                                </template>
                                <div class="json-box"><a href="javascript: void(0)">{{ defaultRowLadderJson(form['ladderPriceJson']) }}</a></div>
                              </a-tooltip>
                            </template>
                            <span v-else>
                              <template v-if="field.fieldName === 'clarificationMatter'">
                                <Popover
                                  :content="form[field.fieldName]"
                                  class="rich-editor-display-box p" />
                              </template>
                              <template v-else>
                                {{ field.fieldType == 'select' || field.fieldType == 'multiple' ? form[field.fieldName + '_dictText'] : form[field.fieldName] }}
                              </template>
                            </span>
                          </span>
                          <span v-else-if="tab.custom.parentObj && form[tab.custom.parentObj]">
                            <span v-if="field.fieldType == 'switch'">
                              {{
                                ['1', 'Y'].includes(form[panel.custom.parentObj][field.fieldName])
                                  ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                  : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                              }}
                              <span v-if="form[panel.custom.parentObj][field.fieldName + '_new']">
                                ➔
                                <span style="color: #1890ff">
                                  {{
                                    ['1', 'Y'].includes(form[panel.custom.parentObj][field.fieldName + '_new'])
                                      ? `${$srmI18n(`${$getLangAccount()}#i18n_title_yes`, '是')}`
                                      : `${$srmI18n(`${$getLangAccount()}#i18n_title_no`, '否')}`
                                  }}
                                </span>
                              </span>
                            </span>
                            <a
                              :href="form[field.fieldName]"
                              target="_blank"
                              v-else-if="field.fieldType == 'link'">
                              <span>{{ $srmI18n(`${$getLangAccount()}#i18n_title_authenticationLink`, '认证链接') }}</span>
                            </a>
                            <!-- <div v-else-if="field.fieldType == 'image'">1111</div> -->
                            <span v-else>
                              {{ field.fieldType == 'select' || field.fieldType == 'multiple' ? form[panel.custom.parentObj][field.fieldName + '_dictText'] : form[panel.custom.parentObj][field.fieldName] }}
                            </span>
                          </span>
                        </template>
                      </a-descriptions-item>
                    </a-descriptions>
                  </template>
                </div>
              </a-collapse-panel>
            </a-collapse>
          </template>
        </div>
      </div>
    </a-spin>
    <!-- 图片预览弹窗 -->
    <a-modal
      v-drag    
      :visible="previewVisible"
      :footer="null"
      @cancel="handleCancel">
      <img
        alt="example"
        style="width: 100%"
        :src="previewImage" />
    </a-modal>
  </div>
</template>

<script>
import { PURCHASEATTACHMENTDOWNLOADAPI } from '@/utils/const'
import { getAction, postAction } from '@/api/manage'
import { USER_ELS_ACCOUNT } from '@/store/mutation-types'
import taskBtn from '@/views/srm/bpm/components/taskBtn'
import { mapGetters } from 'vuex'

export default {
    name: 'DetailLayout',
    components: {
        Popover: ()=>import('@comp/Popover/index.vue'),
        taskBtn
        // Breadcrumb
    },
    props: {
        title: {
            type: String,
            default: '详情'
        },
        pageData: {
            type: Object,
            default: () => {}
        },
        url: {
            type: Object,
            default: () => {}
        },
        modelLayout: {
            type: String,
            default: 'masterSlave'
        }
    },
    data () {
        return {
            previewImage: '',
            previewVisible: false,
            minHeight: 0,
            activeKey: '',
            confirmLoading: false,
            text: 'text',
            form: {},
            //默认表格配置
            defaultGridOption: {
                border: true,
                toolbarConfig: {
                    slots: {buttons: 'toolbar_buttons'},
                    print: true,
                    zoom: true,
                    perfect: true
                },
                // resizable: true,
                autoResize: true,
                height: 'auto',
                showOverflow: true,
                columnKey: true,
                showHeaderOverflow: true,
                size: 'mini',
                align: 'center',
                headerAlign: 'center',
                columns: [],
                data: [],
                // rowConfig: '{isCurrent: true, isHover: true}',
                // checkboxConfig: { highlight: true, trigger: 'row' },
                // editConfig: { trigger: 'dblclick', mode: 'cell' },
                // toolbarConfig: { slots: {buttons: 'toolbar_buttons'} },
                rowClassName: this.rowClassName,
                cellClassName: this.cellClassName
            },
            elsStatusLog: {
                currentStatusIndex: 0,
                list: []
            },
            elsStatusLogList: {
                currentStatusIndex: '',
                list: []
            }
        }
    },
    computed: {
        ...mapGetters([
            'taskInfo'
        ]),
        imageUrl () {
            const { protocol = '', hostname = '' } = window.location || {}
            return `${protocol}//${hostname}/opt/upFiles`
        },
        displayModel () {
            return this.modelLayout
        },
        panels () {
            let panels = this.pageData.groups.filter(group => {
                return group.type != 'grid'
            })
            return panels
        },
        tabs () {
            let tabs = this.pageData.groups.filter(group => {
                return group.type == 'grid'
            })
            return tabs
        },
        busAccount () {
            let account = this.$ls.get(USER_ELS_ACCOUNT)
            if (this.currentEditRow && this.currentEditRow.busAccount) {
                account = this.currentEditRow.busAccount || this.currentEditRow.elsAccount || this.$ls.get(USER_ELS_ACCOUNT)
            }
            return account
        },
        currentPageName () {
            // 当前页面的名称
            let currentPageName = this.$srmI18n(`${this.$getLangAccount()}#i18n_field_supplierInfoChangeInfo`, '供应商信息变更')
            return currentPageName
        }
    },
    methods: {
        // 添加必填列附加样式
        resetCustomCols (cols) {
            cols.forEach((col)=> {
                if (col.required && col.required === '1') {
                    col.className = 'required-col'
                    col.headerClassName = 'required-col'
                }
                
            })
            return cols
        },
        checkedGridSelect (btn, refName, cb) {
            let selectData = null
            let that = this
            if (this.$refs[refName] ) {
                if (this.$refs[refName][0]) {
                    selectData = this.$refs[refName][0].getCheckboxRecords() || this.$refs[refName][0].getRadioRecord()
                }
            }
            if (selectData && selectData.length) {
                if (cb && typeof cb === 'function') {
                    if (btn.key !== 'batchDownload') {
                        cb(selectData, that).then(res=> {
                            if (res) {
                                btn.modalVisible = true
                            }
                        })}else{
                        cb(selectData, that)
                    }
                } else {
                    this.modalVisible = true
                }

            } else {
                console.log(btn)
                if(btn.msgType ==='batchDownload')  {this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_alert_VRiTIKBIcW_10289077`, '请勾选需下载附件行！'))}
                else { this.$message.warning(this.$srmI18n(`${this.$getLangAccount()}#i18n_title_afterChoose`, '请先选择'))}
            }
        },
        handleCancel () {
            this.previewVisible = false
        },
        // 图片预览
        previewPicture (data) {
            if(data){
                this.previewImage = this.imageUrl + data
                this.previewVisible = true
            }
        },
        // 阶梯报价json数据组装
        initRowLadderJson (jsonData) {
            let arr = []
            if (jsonData) {
                arr = JSON.parse(jsonData)
            }
            return arr
        },
        // 阶梯报价默认显示
        defaultRowLadderJson (jsonData) {
            let arrString = ''
            if (jsonData) {
                let arr = JSON.parse(jsonData)
                arr.forEach((item, index)=> {
                    let ladderQuantity = item.ladderQuantity
                    let price = item.price
                    let netPrice= item.netPrice
                    let str = `${ladderQuantity} ${price} ${netPrice} `
                    let separator = index===arr.length-1? '': ','
                    arrString +=str+ separator
                })
            }
            return arrString
        },
        // 文件下载
        handleDownload ({ id, fileName }, url = '') {
            const params = {
                id
            }
            let downloadUrl = url || PURCHASEATTACHMENTDOWNLOADAPI
            if(this.url.download){
                downloadUrl = this.url.download
            }
            getAction(downloadUrl, params, {
                responseType: 'blob'
            }).then(res => {
                console.log(res)
                let url = window.URL.createObjectURL(new Blob([res]))
                let link = document.createElement('a')
                link.style.display = 'none'
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link) //下载完成移除元素
                window.URL.revokeObjectURL(url) //释放掉blob对象
            })
        },
        // 处理头部状态流程节点
        dealStatusLog (data) {
            if (data && data.elsStatusLogList && data.elsStatusLogList.length) {
                data.elsStatusLogList.forEach((log, index)=> {
                    if (log.businessStatus=== data.reconciliationStatus) {
                        this.elsStatusLogList.currentStatusIndex = index
                    }
                })
                this.elsStatusLog.list= data.elsStatusLogList
            }
        },
        queryDetail (id, cb) {
            let that = this
            this.confirmLoading = true
            getAction(this.url.detail, {id: id}).then(res => {
                that.activeKey = that.pageData.groups[0].groupCode
                if(res.success) {
                    this.$emit('loadSuccess',  { res: res })
                    that.form = res.result
                    that.dealStatusLog(res.result)
                    that.pageData.groups.forEach(group => {
                        if(group.type == 'grid') {
                            let ref = group.custom.ref
                            that.$refs[ref][0].loadData(res.result[ref])
                            if(group.custom.expandColumnsMethod) {
                                let expandColumns = group.custom.expandColumnsMethod()
                                group.custom.columns = group.custom.columns.concat(expandColumns)
                                delete group.custom.expandColumnsMethod
                            }
                            if(Object.keys(group).includes('showDadgeDot')){//判断tab是否需要加红点
                                group.showDadgeDot=res.result[ref].length>0
                            }
                        }
                    })
                }else {
                    that.$message.warning(res.message)
                }
                cb && cb(res.result)
            }).finally(() => {
                that.confirmLoading = false
            })
        },
        rowClassName ({ row, rowIndex }) {
            if (row.updateType && row.updateType===3){
                return 'row-delete-type'
            }
            if (row.updateType && row.updateType===2){
                return 'row-add-type'
            }
        },
        cellClassName ({ row, rowIndex, column, columnIndex }) {
            if (row[column.property+'_new']) {
                return 'row-add-type'
            }
        },
        getPageData () {
            const that = this
            let params = {...this.form}
            this.pageData.groups.forEach(group => {
                if(group.type == 'grid') {
                    let ref = group.custom.ref
                    params[ref] = that.$refs[ref][0].getTableData().fullData
                }
            })
            return params
        },
        setPromise () {
            let that = this
            let promise = this.pageData.groups.map(group => {
                if(group.type == 'grid') {
                    return that.$refs[group.custom.ref][0].validate(true)
                }else {
                    return that.$refs[group.groupCode][0].validate()
                }
            })
            return promise
        },
        handValidate (url, params, callback){
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.currentStep = i
                        return
                    }
                }
                if (flag) return callback && callback(url, params, this)
            }).catch(err => {
                console.log(err)
            })
        },
        handleSend (type = 'public', callback) {
            const handlePromise = (list = []) => list.map(promise => promise.then(res => ({
                status: 'success',
                res
            }), err => ({
                status: 'error',
                err
            })))
            let promise = this.setPromise()
            Promise.all(handlePromise(promise)).then(result => {
                let flag = false
                for (let i = 0; i < result.length; i++) {
                    if (result[i].status === 'success') {
                        flag = true
                    } else {
                        this.currentStep = i
                        return
                    }
                }
                if (flag) this.postData(type, callback)
            }).catch(err => {
                console.log(err)
            })
        },
        postData (type, callback) {
            let params = this.getPageData()
            let url =  type === 'public'
                ? this.url.public
                : this.voucherId
                    ? this.url.edit
                    : this.url.add
            this.confirmLoading = true
            postAction(url, params).then(res => {
                const messageType = res.success ? 'success' : 'error'
                this.$message[messageType](res.message)
                if (res.success && this.refresh) {
                    this.queryDetail()
                }
                if (res.success && type === 'public') {
                    this.$parent.goBack()
                } else {
                    // 自定义回调
                    return callback && callback(params, this)
                }
            }).finally(() => {
                this.confirmLoading = false
            })
        },
        showLoading () {
            this.confirmLoading = true
        },
        hideLoading () {
            this.confirmLoading = false
        }
    },
    created () {
        const clientHeight = document.documentElement.clientHeight
        this.minHeight = clientHeight - 260
    }
}
</script>

<style lang="less" scoped>
@primary-color: #1890ff;
.detail-page {
    .step-status {
      padding:36px 0px 30px 0px
    }
    .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // margin: 1px 0px 0 1px;
        padding: 8px 14px;
        background: #fff;
        .btnGroups {
            text-align: right;
            .ant-btn {
                & +.ant-btn {
                    margin-left: 10px;
                }
            }
        }
    }
    .content {
        margin: 0px 1px;
        padding: 8px;
        background: #fff;
        .preview{
            cursor: pointer;
        }
    }
    :deep(.row-delete-type ){
        color: rgba(0, 0, 0, 0.25);
        background-color: #f5f5f5;
    }
    :deep(.row-add-type){
        /*background-color: #00B9FF;*/
        /*color: #00B9FF;*/
        background-color: #fff3e0;
        /*color: #fff;*/
    }

    :deep(.ant-descriptions-bordered .ant-descriptions-item-label){
      background: #F5F6F7;
    }

    :deep(.ant-descriptions-item-content ){
      width: 16.66%;
      max-width: 16.66%;
    }
    :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title),
    :deep(.sub-top .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-description){
      color: @primary-color;
    }
    :deep(.rich-editor-display-box p){
        margin-bottom: 0px;
    }
    :deep(.vxe-body--column.required-col) {
        background-color: #f6ebe8;
        background-image: linear-gradient(#fff9f7,#fff9f7),linear-gradient(#fff9f7,#fff9f7);
    }
    :deep(.vxe-header--column.required-col) {
        background-color: #f6ebe8;
        background-image: linear-gradient(#fff9f7,#fff9f7),linear-gradient(#fff9f7,#fff9f7);
    }
}
</style>

